import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from app.database.db import Base, get_db
from main import app
from app.models.user import User
from app.core.security import get_password_hash

# Create test database
SQLALCHEMY_DATABASE_URL = "sqlite:///./test_payment.db"
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

Base.metadata.create_all(bind=engine)

def override_get_db():
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()

app.dependency_overrides[get_db] = override_get_db
client = TestClient(app)

@pytest.fixture
def test_user():
    """Create a test user with KYC approved"""
    db = TestingSessionLocal()
    user = User(
        id="test-payment-user",
        username="paymentuser",
        email="<EMAIL>",
        hashed_password=get_password_hash("testpassword"),
        balance=1000.0,
        is_active=True,
        is_verified=True,
        kyc_status="approved"
    )
    db.add(user)
    db.commit()
    db.refresh(user)
    yield user
    db.delete(user)
    db.commit()
    db.close()

@pytest.fixture
def authenticated_client(test_user):
    """Get authenticated test client"""
    response = client.post(
        "/api/v1/auth/login",
        data={"username": test_user.email, "password": "testpassword"}
    )
    token = response.json()["access_token"]
    client.headers = {"Authorization": f"Bearer {token}"}
    return client

def test_get_payment_methods(authenticated_client):
    """Test getting available payment methods"""
    response = authenticated_client.get("/api/v1/payment/payment-methods")
    assert response.status_code == 200
    data = response.json()
    assert "payment_methods" in data
    assert len(data["payment_methods"]) > 0
    
    # Check payment method structure
    method = data["payment_methods"][0]
    assert "id" in method
    assert "type" in method
    assert "is_default" in method

def test_process_deposit(authenticated_client, test_user):
    """Test processing a deposit through payment gateway"""
    response = authenticated_client.post(
        "/api/v1/payment/deposit",
        json={
            "amount": 100.0,
            "payment_method": {
                "type": "card",
                "last_four": "4242",
                "card_brand": "Visa"
            },
            "description": "Test deposit"
        }
    )
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "completed"
    assert data["amount"] == 100.0
    assert "processing_fee" in data
    assert "net_amount" in data
    assert data["net_amount"] == data["amount"] - data["processing_fee"]

def test_process_withdrawal(authenticated_client, test_user):
    """Test processing a withdrawal through payment gateway"""
    response = authenticated_client.post(
        "/api/v1/payment/withdraw",
        json={
            "amount": 50.0,
            "payment_method": {
                "type": "bank_transfer",
                "bank_name": "Test Bank",
                "last_four": "6789"
            },
            "description": "Test withdrawal"
        }
    )
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "completed"
    assert data["amount"] == 50.0
    assert data["processing_fee"] == 2.0  # Fixed $2 fee for withdrawals
    assert data["net_amount"] == 50.0  # User receives full amount

def test_deposit_processing_fees(authenticated_client):
    """Test different processing fees for different payment methods"""
    payment_methods = [
        {"type": "card", "expected_fee_rate": 0.025},
        {"type": "bank_transfer", "expected_fee_rate": 0.01},
        {"type": "crypto", "expected_fee_rate": 0.005}
    ]
    
    for method in payment_methods:
        response = authenticated_client.post(
            "/api/v1/payment/deposit",
            json={
                "amount": 1000.0,
                "payment_method": method,
                "description": f"Test {method['type']} deposit"
            }
        )
        assert response.status_code == 200
        data = response.json()
        expected_fee = 1000.0 * method["expected_fee_rate"]
        assert data["processing_fee"] == expected_fee

def test_withdrawal_insufficient_balance(authenticated_client, test_user):
    """Test withdrawal with insufficient balance including fee"""
    # User has 1000, trying to withdraw 1000 + $2 fee = 1002
    response = authenticated_client.post(
        "/api/v1/payment/withdraw",
        json={
            "amount": 1000.0,
            "payment_method": {
                "type": "bank_transfer",
                "bank_name": "Test Bank",
                "last_four": "6789"
            }
        }
    )
    assert response.status_code == 400
    assert "Insufficient balance" in response.json()["detail"]

def test_get_transaction_status(authenticated_client):
    """Test getting transaction status"""
    # First create a deposit
    deposit_response = authenticated_client.post(
        "/api/v1/payment/deposit",
        json={
            "amount": 100.0,
            "payment_method": {"type": "card"},
            "description": "Test deposit"
        }
    )
    transaction_id = deposit_response.json()["transaction_id"]
    
    # Get transaction status
    response = authenticated_client.get(f"/api/v1/payment/transaction/{transaction_id}")
    assert response.status_code == 200
    data = response.json()
    assert data["transaction_id"] == transaction_id
    assert data["type"] == "deposit"
    assert data["amount"] == deposit_response.json()["net_amount"]
    assert data["status"] == "completed"

def test_payment_failure_simulation(authenticated_client):
    """Test payment gateway failure simulation"""
    # The mock processor has a 5% failure rate
    # We'll make multiple attempts to ensure we get at least one failure
    failed = False
    for _ in range(50):  # Statistically should get at least one failure
        response = authenticated_client.post(
            "/api/v1/payment/deposit",
            json={
                "amount": 10.0,
                "payment_method": {"type": "card"},
                "description": "Test deposit"
            }
        )
        if response.status_code == 400:
            failed = True
            assert "Payment declined" in response.json()["detail"]
            break
    
    # With 5% failure rate, 50 attempts should have at least one failure
    # Probability of no failures = 0.95^50 ≈ 0.077 (7.7%)
    assert failed, "Expected at least one payment failure in 50 attempts"