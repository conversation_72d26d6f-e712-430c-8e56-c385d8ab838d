import pytest
import time
from fastapi.testclient import TestClient
from app.core.rate_limit import RateLimiter, rate_limiter
from main import app

client = TestClient(app)

def test_rate_limiter_check():
    """Test basic rate limiter functionality"""
    limiter = RateLimiter()
    
    # Mock request
    class MockRequest:
        def __init__(self, host, path):
            self.client = type('obj', (object,), {'host': host})
            self.url = type('obj', (object,), {'path': path})
    
    request = MockRequest("127.0.0.1", "/api/v1/wallet/deposit")
    
    # First 10 requests should pass (limit is 10 per minute)
    for i in range(10):
        is_allowed, retry_after = limiter.check_rate_limit(request, 10, 60)
        assert is_allowed is True
        assert retry_after is None
    
    # 11th request should fail
    is_allowed, retry_after = limiter.check_rate_limit(request, 10, 60)
    assert is_allowed is False
    assert retry_after is not None
    assert retry_after > 0

def test_rate_limit_login_endpoint():
    """Test rate limiting on login endpoint"""
    # Clear rate limiter state
    rate_limiter.requests.clear()
    
    # Attempt 6 login requests (limit is 5 per 5 minutes)
    for i in range(5):
        response = client.post(
            "/api/v1/auth/login",
            data={"username": f"test{i}@example.com", "password": "wrongpassword"}
        )
        # Should fail with 401 (invalid credentials) but not rate limited
        assert response.status_code == 401
    
    # 6th request should be rate limited
    response = client.post(
        "/api/v1/auth/login",
        data={"username": "<EMAIL>", "password": "wrongpassword"}
    )
    assert response.status_code == 429
    assert "RATE_LIMIT_EXCEEDED" in response.json()["error"]["code"]

def test_rate_limit_different_users():
    """Test that rate limiting is per-user when authenticated"""
    # Clear rate limiter state
    rate_limiter.requests.clear()
    
    # Create two different users
    user1_response = client.post(
        "/api/v1/auth/register",
        json={
            "username": "rateLimitUser1",
            "email": "<EMAIL>",
            "password": "TestPassword123!"
        }
    )
    user1_token = user1_response.json()["access_token"]
    
    user2_response = client.post(
        "/api/v1/auth/register",
        json={
            "username": "rateLimitUser2",
            "email": "<EMAIL>",
            "password": "TestPassword123!"
        }
    )
    user2_token = user2_response.json()["access_token"]
    
    # Each user should have their own rate limit
    # User 1 makes 10 requests
    for i in range(10):
        response = client.post(
            "/api/v1/wallet/deposit",
            json={"amount": 10.0, "payment_method": "mock"},
            headers={"Authorization": f"Bearer {user1_token}"}
        )
        assert response.status_code == 200
    
    # User 2 should still be able to make requests
    response = client.post(
        "/api/v1/wallet/deposit",
        json={"amount": 10.0, "payment_method": "mock"},
        headers={"Authorization": f"Bearer {user2_token}"}
    )
    assert response.status_code == 200
    
    # User 1's next request should be rate limited
    response = client.post(
        "/api/v1/wallet/deposit",
        json={"amount": 10.0, "payment_method": "mock"},
        headers={"Authorization": f"Bearer {user1_token}"}
    )
    assert response.status_code == 429

def test_rate_limit_window_reset():
    """Test that rate limit resets after window expires"""
    limiter = RateLimiter()
    
    class MockRequest:
        def __init__(self, host, path):
            self.client = type('obj', (object,), {'host': host})
            self.url = type('obj', (object,), {'path': path})
    
    request = MockRequest("127.0.0.1", "/test/path")
    
    # Use a very short window (1 second)
    window = 1
    limit = 2
    
    # First 2 requests should pass
    for i in range(limit):
        is_allowed, _ = limiter.check_rate_limit(request, limit, window)
        assert is_allowed is True
    
    # 3rd request should fail
    is_allowed, retry_after = limiter.check_rate_limit(request, limit, window)
    assert is_allowed is False
    
    # Wait for window to expire
    time.sleep(window + 0.1)
    
    # Should be allowed again
    is_allowed, _ = limiter.check_rate_limit(request, limit, window)
    assert is_allowed is True