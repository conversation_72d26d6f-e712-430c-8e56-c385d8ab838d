"""Test authentication endpoints."""
import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from app.database.db import Base, get_db
from main import app
from app.models.user import User
from app.core.config import settings

# Create test database
SQLALCHEMY_DATABASE_URL = "sqlite:///./test_auth.db"
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

Base.metadata.create_all(bind=engine)

def override_get_db():
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()

app.dependency_overrides[get_db] = override_get_db
client = TestClient(app)

# Test data
test_user = {
    "username": "testuser",
    "email": "<EMAIL>",
    "password": "TestPassword123!",
    "full_name": "Test User",
    "phone_number": "+1234567890"
}

def test_register_user():
    """Test user registration"""
    response = client.post(
        f"{settings.API_V1_STR}/auth/register",
        json=test_user
    )
    assert response.status_code == 200
    data = response.json()
    assert "access_token" in data
    assert "refresh_token" in data
    assert data["token_type"] == "bearer"
    assert data["user"]["email"] == test_user["email"]
    assert data["user"]["username"] == test_user["username"].lower()
    
    # Check that user was created in database
    db = TestingSessionLocal()
    user = db.query(User).filter(User.email == test_user["email"]).first()
    assert user is not None
    assert user.username == test_user["username"].lower()
    assert user.full_name == test_user["full_name"]
    db.close()

def test_register_duplicate_email():
    """Test registering with duplicate email"""
    # First registration
    client.post(
        f"{settings.API_V1_STR}/auth/register",
        json={
            **test_user,
            "username": "user1",
            "email": "<EMAIL>"
        }
    )
    
    # Attempt duplicate registration
    response = client.post(
        f"{settings.API_V1_STR}/auth/register",
        json={
            **test_user,
            "username": "user2",
            "email": "<EMAIL>"
        }
    )
    assert response.status_code == 400
    assert "Email already registered" in response.json()["detail"]

def test_register_duplicate_username():
    """Test registering with duplicate username"""
    # First registration
    client.post(
        f"{settings.API_V1_STR}/auth/register",
        json={
            **test_user,
            "username": "duplicateuser",
            "email": "<EMAIL>"
        }
    )
    
    # Attempt duplicate registration
    response = client.post(
        f"{settings.API_V1_STR}/auth/register",
        json={
            **test_user,
            "username": "duplicateuser",
            "email": "<EMAIL>"
        }
    )
    assert response.status_code == 400
    assert "Username already taken" in response.json()["detail"]

def test_login_valid_credentials():
    """Test login with valid credentials"""
    # First register a user
    client.post(
        f"{settings.API_V1_STR}/auth/register",
        json={
            **test_user,
            "username": "loginuser",
            "email": "<EMAIL>"
        }
    )
    
    # Login with email
    response = client.post(
        f"{settings.API_V1_STR}/auth/login",
        data={"username": "<EMAIL>", "password": test_user["password"]}
    )
    assert response.status_code == 200
    data = response.json()
    assert "access_token" in data
    assert "refresh_token" in data
    
    # Login with username
    response = client.post(
        f"{settings.API_V1_STR}/auth/login",
        data={"username": "loginuser", "password": test_user["password"]}
    )
    assert response.status_code == 200

def test_login_invalid_credentials():
    """Test login with invalid credentials"""
    response = client.post(
        f"{settings.API_V1_STR}/auth/login",
        data={"username": "<EMAIL>", "password": "wrongpassword"}
    )
    assert response.status_code == 401
    assert "Incorrect username/email or password" in response.json()["detail"]

def test_refresh_token():
    """Test token refresh"""
    # Register and get tokens
    response = client.post(
        f"{settings.API_V1_STR}/auth/register",
        json={
            **test_user,
            "username": "refreshuser",
            "email": "<EMAIL>"
        }
    )
    tokens = response.json()
    
    # Use refresh token
    response = client.post(
        f"{settings.API_V1_STR}/auth/refresh",
        json={"refresh_token": tokens["refresh_token"]}
    )
    assert response.status_code == 200
    data = response.json()
    assert "access_token" in data
    assert "refresh_token" in data

def test_logout():
    """Test logout functionality"""
    # Register and login
    response = client.post(
        f"{settings.API_V1_STR}/auth/register",
        json={
            **test_user,
            "username": "logoutuser",
            "email": "<EMAIL>"
        }
    )
    access_token = response.json()["access_token"]
    
    # Logout
    response = client.post(
        f"{settings.API_V1_STR}/auth/logout",
        json={},  # Empty body or with refresh token
        headers={"Authorization": f"Bearer {access_token}"}
    )
    assert response.status_code == 200

def test_get_current_user():
    """Test getting current user info"""
    # Register and login
    response = client.post(
        f"{settings.API_V1_STR}/auth/register",
        json={
            **test_user,
            "username": "currentuser",
            "email": "<EMAIL>"
        }
    )
    access_token = response.json()["access_token"]
    
    # Get current user
    response = client.get(
        f"{settings.API_V1_STR}/auth/me",
        headers={"Authorization": f"Bearer {access_token}"}
    )
    assert response.status_code == 200
    data = response.json()
    assert data["username"] == "currentuser"
    assert data["email"] == "<EMAIL>"

def test_change_password():
    """Test password change"""
    # Register and get tokens
    response = client.post(
        f"{settings.API_V1_STR}/auth/register",
        json={
            **test_user,
            "username": "changepass",
            "email": "<EMAIL>"
        }
    )
    access_token = response.json()["access_token"]
    
    # Change password
    new_password = "NewPassword123!"
    response = client.put(
        f"{settings.API_V1_STR}/auth/change-password",
        json={
            "current_password": test_user["password"],
            "new_password": new_password
        },
        headers={"Authorization": f"Bearer {access_token}"}
    )
    assert response.status_code == 200
    
    # Try to login with new password
    login_response = client.post(
        f"{settings.API_V1_STR}/auth/login",
        data={"username": "<EMAIL>", "password": new_password}
    )
    assert login_response.status_code == 200

def test_forgot_password():
    """Test forgot password endpoint"""
    response = client.post(
        f"{settings.API_V1_STR}/auth/forgot-password",
        json={"email": "<EMAIL>"}
    )
    assert response.status_code == 200
    assert "If the email exists" in response.json()["detail"]

def test_password_validation():
    """Test password validation requirements"""
    # Test weak password
    response = client.post(
        f"{settings.API_V1_STR}/auth/register",
        json={
            **test_user,
            "username": "weakpass",
            "email": "<EMAIL>",
            "password": "simple"  # Too short and no uppercase/numbers
        }
    )
    assert response.status_code == 422  # Validation error
    
    # Test password without uppercase
    response = client.post(
        f"{settings.API_V1_STR}/auth/register",
        json={
            **test_user,
            "username": "weakpass2",
            "email": "<EMAIL>",
            "password": "password123!"  # No uppercase
        }
    )
    assert response.status_code == 422

def test_username_validation():
    """Test username validation"""
    # Test username with special characters
    response = client.post(
        f"{settings.API_V1_STR}/auth/register",
        json={
            **test_user,
            "username": "user@name",  # Invalid character
            "email": "<EMAIL>"
        }
    )
    assert response.status_code == 422
    assert "Username must contain only letters" in str(response.json())