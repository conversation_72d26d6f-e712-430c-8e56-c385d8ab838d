import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from app.database.db import Base, get_db
from main import app
from app.models.user import User
from app.core.security import get_password_hash

# Create test database
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

Base.metadata.create_all(bind=engine)

def override_get_db():
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()

app.dependency_overrides[get_db] = override_get_db
client = TestClient(app)

@pytest.fixture
def test_user():
    """Create a test user for authentication"""
    db = TestingSessionLocal()
    user = User(
        id="test-user-1",
        username="testuser",
        email="<EMAIL>",
        hashed_password=get_password_hash("testpassword"),
        balance=1000.0,
        is_active=True,
        is_verified=True,
        kyc_status="approved"
    )
    db.add(user)
    db.commit()
    db.refresh(user)
    yield user
    db.delete(user)
    db.commit()
    db.close()

@pytest.fixture
def authenticated_client(test_user):
    """Get authenticated test client"""
    response = client.post(
        "/api/v1/auth/login",
        data={"username": test_user.email, "password": "testpassword"}
    )
    token = response.json()["access_token"]
    client.headers = {"Authorization": f"Bearer {token}"}
    return client

def test_get_balance(authenticated_client, test_user):
    """Test getting wallet balance"""
    response = authenticated_client.get("/api/v1/wallet/balance")
    assert response.status_code == 200
    data = response.json()
    assert data["balance"] == 1000.0
    assert data["user_id"] == test_user.id
    assert data["username"] == test_user.username

def test_deposit_funds(authenticated_client, test_user):
    """Test depositing funds"""
    response = authenticated_client.post(
        "/api/v1/wallet/deposit",
        json={"amount": 100.0, "payment_method": "mock"}
    )
    assert response.status_code == 200
    data = response.json()
    assert data["balance"] == 1100.0

def test_withdraw_funds(authenticated_client, test_user):
    """Test withdrawing funds"""
    response = authenticated_client.post(
        "/api/v1/wallet/withdraw",
        json={"amount": 50.0, "payment_method": "mock"}
    )
    assert response.status_code == 200
    data = response.json()
    assert data["balance"] == 950.0

def test_withdraw_insufficient_balance(authenticated_client, test_user):
    """Test withdrawing more than available balance"""
    response = authenticated_client.post(
        "/api/v1/wallet/withdraw",
        json={"amount": 2000.0, "payment_method": "mock"}
    )
    assert response.status_code == 400
    assert "INSUFFICIENT_BALANCE" in response.json()["error"]["code"]

def test_place_bet(authenticated_client, test_user):
    """Test placing a bet"""
    response = authenticated_client.post(
        "/api/v1/wallet/bet",
        json={
            "amount": 100.0,
            "game_id": "test-game-1",
            "description": "Test bet"
        }
    )
    assert response.status_code == 200
    data = response.json()
    assert data["balance"] == 900.0

def test_process_win(authenticated_client, test_user):
    """Test processing a win"""
    response = authenticated_client.post(
        "/api/v1/wallet/win",
        json={
            "amount": 200.0,
            "game_id": "test-game-1",
            "description": "Test win"
        }
    )
    assert response.status_code == 200
    data = response.json()
    assert data["balance"] == 1200.0

def test_get_transactions(authenticated_client, test_user):
    """Test getting transaction history"""
    # First create some transactions
    authenticated_client.post(
        "/api/v1/wallet/deposit",
        json={"amount": 100.0, "payment_method": "mock"}
    )
    authenticated_client.post(
        "/api/v1/wallet/bet",
        json={"amount": 50.0, "game_id": "test-game-1", "description": "Test bet"}
    )
    
    # Get transactions
    response = authenticated_client.get("/api/v1/wallet/transactions")
    assert response.status_code == 200
    data = response.json()
    assert len(data) >= 2
    assert any(tx["type"] == "deposit" for tx in data)
    assert any(tx["type"] == "bet" for tx in data)