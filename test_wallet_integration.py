import requests
import json

# Base URL
BASE_URL = "http://localhost:8000/api/v1"

# Test credentials
test_user = {
    "username": "testgamer",
    "email": "<EMAIL>",
    "password": "TestPassword123!"
}

def register_user():
    response = requests.post(f"{BASE_URL}/auth/register", json=test_user)
    print("Registration response:", response.status_code)
    if response.status_code == 200:
        return response.json()
    else:
        print("Registration error:", response.text)
        return None

def login_user():
    response = requests.post(f"{BASE_URL}/auth/login", data={
        "username": test_user["email"],
        "password": test_user["password"]
    })
    print("Login response:", response.status_code)
    if response.status_code == 200:
        return response.json()
    else:
        print("Login error:", response.text)
        return None

def test_wallet_operations(token):
    headers = {"Authorization": f"Bearer {token}"}
    
    # Get balance
    response = requests.get(f"{BASE_URL}/wallet/balance", headers=headers)
    print("Balance response:", response.status_code)
    if response.status_code == 200:
        balance_data = response.json()
        print("Current balance:", balance_data)
        
    # Deposit funds
    response = requests.post(f"{BASE_URL}/wallet/deposit", 
                           json={"amount": 100.0, "description": "Test deposit"},
                           headers=headers)
    print("Deposit response:", response.status_code)
    if response.status_code == 200:
        print("Deposit successful:", response.json())
        
    # Test bet
    response = requests.post(f"{BASE_URL}/wallet/bet",
                           json={"amount": 10.0, "game_id": "test-game-1", "description": "Test bet"},
                           headers=headers)
    print("Bet response:", response.status_code)
    if response.status_code == 200:
        print("Bet placed:", response.json())
        
    # Test win
    response = requests.post(f"{BASE_URL}/wallet/win",
                           json={"amount": 20.0, "game_id": "test-game-1", "description": "Test win"},
                           headers=headers)
    print("Win response:", response.status_code)
    if response.status_code == 200:
        print("Win processed:", response.json())
        
    # Get transaction history
    response = requests.get(f"{BASE_URL}/wallet/transactions", headers=headers)
    print("Transactions response:", response.status_code)
    if response.status_code == 200:
        transactions = response.json()
        print(f"\nTransaction history ({len(transactions)} items):")
        for tx in transactions:
            print(f"  - {tx['type']}: ${tx['amount']} - {tx['description']}")

if __name__ == "__main__":
    print("Testing wallet integration...\n")
    
    # Try to register (may fail if user exists)
    register_user()
    
    # Login
    auth_data = login_user()
    if auth_data:
        token = auth_data["access_token"]
        print(f"\nAuthentication successful! Token: {token[:20]}...\n")
        
        # Test wallet operations
        test_wallet_operations(token)
    else:
        print("Authentication failed!")