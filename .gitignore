# Claude settings
**/.claude/settings.local.json

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
ENV/
env/
.venv

# Database
*.db
*.sqlite
*.sqlite3

# Frontend
frontend/node_modules/
frontend/dist/
frontend/build/
frontend/.vite/

# Environment variables
.env
.env.local
.env.production.local
.env.development.local
.env.test.local

# IDE specific files
.vscode/
.idea/
*.swp
*.swo
*~

# OS specific
.DS_Store
Thumbs.db

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Testing
coverage/
.coverage
htmlcov/
.tox/
.pytest_cache/