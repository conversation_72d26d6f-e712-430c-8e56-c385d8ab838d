"""Create a test user with expert access for development."""
import sys
sys.path.append('.')

from sqlalchemy.orm import Session
from app.database.db import get_db, engine
from app.models.user import User
from app.models.expert_picks import ExpertProfile
from app.auth.jwt import get_password_hash
import uuid

def create_test_expert():
    db = next(get_db())
    
    # Create test user
    test_email = "<EMAIL>"
    test_password = "testpass123"
    
    # Check if user exists
    existing_user = db.query(User).filter(User.email == test_email).first()
    
    if not existing_user:
        test_user = User(
            id=str(uuid.uuid4()),
            username="testexpert",
            email=test_email,
            hashed_password=get_password_hash(test_password),
            is_active=True,
            is_verified=True,
            real_name="Test Expert"
        )
        db.add(test_user)
        db.commit()
        print(f"Created test user: {test_email}")
        user_id = test_user.id
    else:
        print(f"User already exists: {test_email}")
        user_id = existing_user.id
    
    # Create expert profile
    existing_profile = db.query(ExpertProfile).filter(ExpertProfile.user_id == user_id).first()
    
    if not existing_profile:
        expert_profile = ExpertProfile(
            user_id=user_id,
            is_expert=True,
            total_picks=0,
            win_rate=0.0,
            monthly_revenue=0.0,
            total_revenue=0.0,
            followers_count=0,
            avg_odds=0.0,
            roi=0.0,
            win_streak=0,
            rating=0.0
        )
        db.add(expert_profile)
        db.commit()
        print("Created expert profile")
    else:
        existing_profile.is_expert = True
        db.commit()
        print("Updated existing profile to expert")
    
    print("\nTest expert created successfully!")
    print(f"Email: {test_email}")
    print(f"Password: {test_password}")
    print("\nYou can now login and test the Expert Picks feature.")
    
    db.close()

if __name__ == "__main__":
    create_test_expert()