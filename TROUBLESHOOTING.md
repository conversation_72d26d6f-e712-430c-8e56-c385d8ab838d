# BetBet Troubleshooting Guide

## Common Issues and Solutions

### 1. Import Errors

**Error**: `Uncaught SyntaxError: The requested module doesn't provide an export named: 'useWalletStore'`

**Solution**: The wallet store uses default export. Change:
```javascript
import { useWalletStore } from '../../stores/walletStore';
```
to:
```javascript
import useWalletStore from '../../stores/walletStore';
```

### 2. Database Issues

**Error**: `sqlite3.OperationalError: no such column`

**Solution**: The database schema is out of sync. Delete the database and restart:
```bash
rm betbet.db
python -m uvicorn main:app --reload
```

### 3. Admin Access Issues

**Error**: Admin link not showing up

**Solution**:
1. Make sure you're logged in with admin credentials:
   - Email: `<EMAIL>`
   - Password: `admin123`
2. Check if the user has `is_superuser: true` in the database
3. Clear browser cache and restart

### 4. CORS Errors

**Error**: `Access to fetch at 'http://localhost:8000' from origin 'http://localhost:5174' has been blocked by CORS policy`

**Solution**: 
1. Make sure the backend is running
2. Check that CORS is configured in `main.py`:
```python
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
```

### 5. Authentication Issues

**Error**: 401 Unauthorized errors

**Solution**:
1. Check if the token is being sent in headers
2. Verify token hasn't expired
3. Login again to get a fresh token

### 6. Module Not Found Errors

**Error**: `Module not found` errors in frontend

**Solution**:
1. Install dependencies:
```bash
cd frontend
npm install
```
2. Check import paths use @ alias correctly
3. Restart the dev server

### 7. Rate Limiting Errors

**Error**: 429 Too Many Requests

**Solution**:
1. Wait for the retry-after period
2. Check rate limit configuration in `app/core/rate_limit.py`
3. Use different user accounts for testing

### 8. Payment Gateway Errors

**Error**: Payment processing fails

**Solution**:
1. The mock gateway has a 5% failure rate by design
2. Simply retry the payment
3. Check wallet balance for sufficient funds

### 9. Port Already in Use

**Error**: `Port 5173 is in use, trying another one...`

**Solution**:
1. Kill the process using the port:
```bash
lsof -i :5173
kill -9 <PID>
```
2. Or use the alternative port shown in the terminal

### 10. Pydantic Validation Errors

**Error**: Pydantic validation errors

**Solution**:
1. Check request/response data matches schema
2. Ensure all required fields are provided
3. Check data types match expectations

## Development Tips

1. **Always clear database when changing models**:
```bash
rm betbet.db
```

2. **Check both frontend and backend logs**:
- Backend: Terminal running uvicorn
- Frontend: Browser console

3. **Use development tools**:
- React DevTools
- Network tab in browser
- FastAPI docs at `http://localhost:8000/docs`

4. **Test with different users**:
- Create regular users for testing
- Keep admin account separate
- Test KYC flow with different states

5. **Common commands**:
```bash
# Backend
cd /Users/<USER>/PycharmProjects/BetBet
python -m uvicorn main:app --reload

# Frontend
cd /Users/<USER>/PycharmProjects/BetBet/frontend
npm run dev

# Tests
pytest

# Install dependencies
pip install -r requirements.txt
npm install
```