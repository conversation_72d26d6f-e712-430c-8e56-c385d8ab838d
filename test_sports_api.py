"""Test script for Sports API integration."""
import httpx
import asyncio
from app.core.config import settings

async def test_sports_api():
    """Test the sports API with your API key."""
    
    # Check if API key is set
    api_key = settings.SPORTS_API_KEY
    api_url = settings.SPORTS_API_URL
    
    print(f"API URL: {api_url}")
    print(f"API Key configured: {'Yes' if api_key and api_key != 'your_actual_api_key_here' else 'No'}")
    
    if not api_key or api_key == 'your_actual_api_key_here':
        print("\n⚠️  WARNING: API key not configured!")
        print("Please update the SPORTS_API_KEY in your .env file")
        return
    
    # Test making a direct request
    headers = {
        "X-API-Key": api_key,
        "Content-Type": "application/json"
    }
    
    try:
        async with httpx.AsyncClient() as client:
            # Test getting sports
            print("\n📊 Testing GET /sports endpoint...")
            response = await client.get(
                f"{api_url}/sports",
                headers=headers,
                timeout=30.0
            )
            
            print(f"Status Code: {response.status_code}")
            print(f"Response Headers: {dict(response.headers)}")
            
            if response.status_code == 200:
                print("✅ Success! Response:")
                print(response.json())
            else:
                print("❌ Error Response:")
                print(response.text)
            
            # Test upcoming games endpoint if sports worked
            if response.status_code == 200:
                print("\n📊 Testing GET /games/upcoming endpoint...")
                games_response = await client.get(
                    f"{api_url}/games/upcoming",
                    headers=headers,
                    params={"limit": 5},
                    timeout=30.0
                )
                
                print(f"Status Code: {games_response.status_code}")
                
                if games_response.status_code == 200:
                    print("✅ Success! Response:")
                    print(games_response.json())
                else:
                    print("❌ Error Response:")
                    print(games_response.text)
                    
    except httpx.HTTPError as e:
        print(f"❌ HTTP Error: {e}")
    except Exception as e:
        print(f"❌ Unexpected Error: {e}")

if __name__ == "__main__":
    print("🏈 Testing Sports API Integration...")
    asyncio.run(test_sports_api())