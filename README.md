# BetBet - Skill-Based Betting Platform

BetBet is a modern, skill-based betting platform that combines traditional sports betting with competitive gaming.

## Tech Stack

- **Backend**: FastAPI (Python)
- **Frontend**: React + TypeScript + Vite
- **Database**: SQLAlchemy ORM with SQLite (development)
- **State Management**: Zustand
- **Styling**: Tailwind CSS
- **UI Components**: Radix UI

## Project Structure

```
BetBet/
├── app/                     # Backend application
│   ├── api/                 # API endpoints
│   ├── auth/                # Authentication logic
│   ├── core/                # Core configuration and settings
│   ├── database/            # Database configuration
│   ├── models/              # SQLAlchemy models
│   └── schemas/             # Pydantic schemas
├── frontend/                # React frontend
│   ├── src/
│   │   ├── components/      # React components
│   │   ├── hooks/           # Custom React hooks
│   │   ├── services/        # API services
│   │   ├── stores/          # Zustand stores
│   │   ├── types/           # TypeScript types
│   │   └── utils/           # Utility functions
│   └── public/              # Static assets
├── docs/                    # Documentation
├── main.py                  # FastAPI application entry point
└── requirements.txt         # Python dependencies
```

## Features

- **User Authentication**: JWT-based authentication with secure login/signup
- **Marketplace**: Create and browse bets on sports and games
- **Live Games**: Play competitive games like chess and checkers
- **Wallet System**: Deposit, withdraw, and manage funds
- **Sports Betting**: Real-time sports betting with live odds
- **Admin Dashboard**: Manage users, KYC verification, and platform settings
- **WebSocket Support**: Real-time game updates and notifications

## Getting Started

### Backend Setup

1. Install Python dependencies:
```bash
pip install -r requirements.txt
```

2. Run the FastAPI server:
```bash
python main.py
```

The API will be available at `http://localhost:8000`

### Frontend Setup

1. Navigate to the frontend directory:
```bash
cd frontend
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

The frontend will be available at `http://localhost:5173`

## Development

### Environment Variables

Create a `.env` file in the root directory with the following variables:

```
SECRET_KEY=your-secret-key
DATABASE_URL=sqlite:///./betbet.db
FIRST_SUPERUSER_EMAIL=<EMAIL>
FIRST_SUPERUSER_USERNAME=admin
FIRST_SUPERUSER_PASSWORD=admin123
```

### API Documentation

When the backend is running, visit `http://localhost:8000/docs` for interactive API documentation.

## License

Copyright © 2024 BetBet. All rights reserved.