Core Game Features
Authentic Millionaire Experience

15-Level Prize Ladder: From $100 to $1,000,000 with safe havens at levels 5 and 10
Multiple Choice Questions: A, B, C, D format with realistic difficulty progression
Dramatic Presentation: Question reveals, answer locks, and suspenseful timing

Four Classic Lifelines (TV Show Authentic)

50:50: Removes 2 wrong answers (not guaranteed to help)
Phone a Friend: Simulated friend advice (70% accuracy, includes confidence levels)
Ask the Audience: Voting percentages (biased toward correct but not foolproof)
Expert Advice: Professional analysis (85% accuracy with reasoning)

Multiplayer Competition

1v1 Mode: Head-to-head elimination competition
Tournament Mode: Up to 8 players competing sequentially
Player Progression: Each player starts from level 1 when it's their turn
Elimination System: Wrong answers eliminate players, advancing to next contestant

⏰ Game Modes
Timed vs Untimed

Timed Mode: 15-60 second countdown per question
Untimed Mode: Think as long as needed (classic TV format)
Auto-elimination: No answer within time limit = elimination

Flexible Configuration

Question Count: 10 or 15 questions (full ladder or shortened)
Wager Amounts: $50, $100, $200 entry fees
Prize Pools: $500, $1000, $2000 jackpots for winners

💰 Live Spectator Betting
Real-time Wagering

Correct Answer Bets: 1.8x odds on player getting it right
Wrong Answer Bets: 2.2x odds on player elimination
Lifeline Usage Bets: 3.5x odds on player using a lifeline
Dynamic Odds: Adjust based on betting volume and player history

Betting Windows

Open: During question display and thinking time
Closed: When answer is locked or lifeline is activated
Instant Payouts: Immediate settlement after answer reveal

🎯 Strategic Elements
Risk vs Reward Decisions

Walk Away Option: Cash out current winnings anytime after question 1
Safe Havens: Guaranteed minimums at $1,000 and $32,000 levels
Lifeline Strategy: Save for harder questions vs use early for confidence

Realistic Lifeline Limitations

Phone a Friend: Can give wrong advice (30% chance)
Ask Audience: Can be misled by group think
50:50: Might eliminate options you were considering
Expert Advice: Very reliable but not infallible (15% error rate)

📊 Advanced Features
Question Database Categories

Geography: Capital cities, countries, landmarks
Science: Chemistry, physics, biology, astronomy
History: Wars, dates, historical figures
Literature: Authors, books, characters
Technology: Inventions, companies, innovations
Music: Composers, songs, instruments

Difficulty Progression

Easy (Levels 1-5): Basic knowledge questions
Medium (Levels 6-10): Moderate difficulty, some specialized knowledge
Hard (Levels 11-15): Expert level, obscure facts, detailed knowledge

Social Integration

Live Chat: Spectators can cheer, give encouragement, discuss answers
Player Status: See who's active, eliminated, and current winnings
Viewing Stats: 127 spectators watching, betting volume indicators

🎮 Gameplay Flow
Question Sequence

Question Reveal: Category, difficulty, and prize amount shown
Thinking Time: Player considers options (with timer if enabled)
Lifeline Usage: Optional help from available lifelines
Answer Selection: Choose A, B, C, or D
Final Answer: Lock in choice with dramatic confirmation
Answer Reveal: Suspenseful reveal of correct answer
Progression: Advance to next level or face elimination

Elimination Scenarios

Wrong Answer: Player eliminated, drops to last safe haven amount
Time Expiry: No answer selected, automatic elimination
Walk Away: Player chooses to quit and keep current winnings

🏁 Winning Conditions
Victory Scenarios

Million Dollar Winner: Answer all 15 questions correctly
Walk Away Champion: Strategic exit with substantial winnings
Tournament Victor: Last player standing in multiplayer mode

Payout Structure

Full Winner: Entire prize pool (up to $2,000)
High Achiever: 2x wager for reaching $32,000+ level
Participation: 1x wager return for reaching $1,000+ level
Elimination: No payout for early elimination

🔧 Technical Implementation
State Management

Game Phases: Setup, question display, lifeline usage, answer reveal
Player Tracking: Active player, elimination status, earnings
Timer System: Countdown timers with auto-progression
Lifeline Logic: Realistic simulation of TV show mechanics

UI/UX Design

TV Show Aesthetics: Gold/yellow color scheme, dramatic styling
Answer Highlighting: Clear visual feedback for selections
Prize Ladder Display: Prominent showing of current position and winnings
Mobile Responsive: Works on all screen sizes

The game captures the authentic "Who Wants to Be a Millionaire" experience while adding modern multiplayer competition and live betting elements. Players must balance risk and reward, use lifelines strategically, and make crucial decisions under pressure - just like the real TV show!

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Input } from '@/components/ui/input';
import {
  Trophy,
  Clock,
  MessageSquare,
  Users,
  Settings,
  Star,
  Target,
  Zap,
  RotateCcw,
  Check,
  X,
  AlertCircle,
  DollarSign,
  ChevronDown,
  Phone,
  UsersRound,
  Split,
  HelpCircle,
  Crown,
  Timer,
  Award,
  TrendingUp,
  Eye,
  Percent,
  Lock,
  Unlock,
  Flame,
  Brain,
  Lightbulb
} from 'lucide-react';

// Game status
const GAME_STATUS = {
  SETUP: 'setup',
  WAITING: 'waiting',
  PLAYING: 'playing',
  QUESTION: 'question',
  LIFELINE: 'lifeline',
  ANSWER_REVEAL: 'answer_reveal',
  ROUND_END: 'round_end',
  GAME_END: 'game_end'
};

// Lifeline types
const LIFELINES = {
  FIFTY_FIFTY: 'fifty_fifty',
  PHONE_FRIEND: 'phone_friend',
  ASK_AUDIENCE: 'ask_audience',
  EXPERT_ADVICE: 'expert_advice'
};

// Prize ladder (simplified version)
const PRIZE_LADDER = [
  { level: 1, amount: 100, safe: false },
  { level: 2, amount: 200, safe: false },
  { level: 3, amount: 300, safe: false },
  { level: 4, amount: 500, safe: false },
  { level: 5, amount: 1000, safe: true },
  { level: 6, amount: 2000, safe: false },
  { level: 7, amount: 4000, safe: false },
  { level: 8, amount: 8000, safe: false },
  { level: 9, amount: 16000, safe: false },
  { level: 10, amount: 32000, safe: true },
  { level: 11, amount: 64000, safe: false },
  { level: 12, amount: 125000, safe: false },
  { level: 13, amount: 250000, safe: false },
  { level: 14, amount: 500000, safe: false },
  { level: 15, amount: 1000000, safe: false }
];

// Sample questions database
const QUESTIONS_DB = {
  easy: [
    {
      id: 1,
      question: "What is the capital of France?",
      options: ["London", "Berlin", "Paris", "Madrid"],
      correct: 2,
      category: "Geography",
      difficulty: "easy"
    },
    {
      id: 2,
      question: "How many legs does a spider have?",
      options: ["6", "8", "10", "12"],
      correct: 1,
      category: "Science",
      difficulty: "easy"
    },
    {
      id: 3,
      question: "What year did World War II end?",
      options: ["1944", "1945", "1946", "1947"],
      correct: 1,
      category: "History",
      difficulty: "easy"
    }
  ],
  medium: [
    {
      id: 4,
      question: "Which planet is known as the Red Planet?",
      options: ["Venus", "Mars", "Jupiter", "Saturn"],
      correct: 1,
      category: "Science",
      difficulty: "medium"
    },
    {
      id: 5,
      question: "Who wrote the novel '1984'?",
      options: ["Aldous Huxley", "George Orwell", "Ray Bradbury", "Ernest Hemingway"],
      correct: 1,
      category: "Literature",
      difficulty: "medium"
    },
    {
      id: 6,
      question: "What is the largest ocean on Earth?",
      options: ["Atlantic", "Indian", "Arctic", "Pacific"],
      correct: 3,
      category: "Geography",
      difficulty: "medium"
    }
  ],
  hard: [
    {
      id: 7,
      question: "In which year was the first iPhone released?",
      options: ["2006", "2007", "2008", "2009"],
      correct: 1,
      category: "Technology",
      difficulty: "hard"
    },
    {
      id: 8,
      question: "What is the chemical symbol for gold?",
      options: ["Go", "Gd", "Au", "Ag"],
      correct: 2,
      category: "Science",
      difficulty: "hard"
    },
    {
      id: 9,
      question: "Who composed 'The Four Seasons'?",
      options: ["Bach", "Mozart", "Vivaldi", "Beethoven"],
      correct: 2,
      category: "Music",
      difficulty: "hard"
    }
  ]
};

// Quiz Arena Game Component
const QuizArenaGame = () => {
  // Game configuration
  const [config, setConfig] = useState({
    gameMode: '1v1', // '1v1' or '1vN'
    maxPlayers: 4,
    gameType: 'timed', // 'timed' or 'untimed'
    timePerQuestion: 30,
    questionCount: 15,
    wagerAmount: 100,
    prizePool: 500,
    allowLifelines: true,
    spectatorBetting: true
  });

  // Game state
  const [gameStatus, setGameStatus] = useState(GAME_STATUS.SETUP);
  const [currentLevel, setCurrentLevel] = useState(1);
  const [timeLeft, setTimeLeft] = useState(0);
  const [currentQuestion, setCurrentQuestion] = useState(null);
  const [selectedAnswer, setSelectedAnswer] = useState(null);
  const [isAnswerLocked, setIsAnswerLocked] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [lifelinesUsed, setLifelinesUsed] = useState({});
  const [activeLifeline, setActiveLifeline] = useState(null);
  const [audienceVotes, setAudienceVotes] = useState({});
  const [phoneAdvice, setPhoneAdvice] = useState(null);
  const [eliminatedOptions, setEliminatedOptions] = useState([]);

  // Players and competition
  const [players, setPlayers] = useState([
    { id: 1, name: 'You', level: 1, earnings: 0, isActive: true, isEliminated: false, lifelinesUsed: {} },
    { id: 2, name: 'Player 2', level: 1, earnings: 0, isActive: false, isEliminated: false, lifelinesUsed: {} },
    { id: 3, name: 'Player 3', level: 1, earnings: 0, isActive: false, isEliminated: false, lifelinesUsed: {} },
    { id: 4, name: 'Player 4', level: 1, earnings: 0, isActive: false, isEliminated: false, lifelinesUsed: {} }
  ]);

  // Spectator betting
  const [spectatorBets, setSpectatorBets] = useState({
    correct: { amount: 0, odds: 1.8 },
    wrong: { amount: 0, odds: 2.2 },
    lifeline: { amount: 0, odds: 3.5 }
  });

  // Game tracking
  const [gameHistory, setGameHistory] = useState([]);
  const [gameResult, setGameResult] = useState(null);
  const [totalGameTime, setTotalGameTime] = useState(0);

  // Refs
  const timerRef = useRef(null);
  const gameTimerRef = useRef(null);

  // Chat state
  const [messages, setMessages] = useState([
    { text: "Welcome to Quiz Arena! Test your knowledge and compete for the million-dollar prize!", type: "system" }
  ]);
  const [messageInput, setMessageInput] = useState('');
  const messageEndRef = useRef(null);

  // Effects

  // Scroll chat to bottom
  useEffect(() => {
    if (messageEndRef.current) {
      messageEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  // Clean up timers
  useEffect(() => {
    return () => {
      if (timerRef.current) clearInterval(timerRef.current);
      if (gameTimerRef.current) clearInterval(gameTimerRef.current);
    };
  }, []);

  // Get questions for difficulty
  const getQuestionsForDifficulty = (level) => {
    if (level <= 5) return QUESTIONS_DB.easy;
    if (level <= 10) return QUESTIONS_DB.medium;
    return QUESTIONS_DB.hard;
  };

  // Add message to chat
  const addMessage = (text, type = "player") => {
    setMessages(prev => [...prev, { text, type }]);
  };

  // Send chat message
  const sendMessage = () => {
    if (messageInput.trim()) {
      addMessage(`Spectator: ${messageInput}`);
      setMessageInput('');
    }
  };

  // Start the game
  const startGame = () => {
    // Reset game state
    setCurrentLevel(1);
    setGameHistory([]);
    setGameResult(null);
    setTotalGameTime(0);
    setLifelinesUsed({});

    // Reset players
    const activePlayerCount = config.gameMode === '1v1' ? 2 : config.maxPlayers;
    setPlayers(prev => prev.slice(0, activePlayerCount).map((player, index) => ({
      ...player,
      level: 1,
      earnings: 0,
      isActive: index === 0,
      isEliminated: false,
      lifelinesUsed: {}
    })));

    // Start game timer
    gameTimerRef.current = setInterval(() => {
      setTotalGameTime(prev => prev + 1);
    }, 1000);

    // Start first question
    loadQuestion(1);

    setGameStatus(GAME_STATUS.PLAYING);
    addMessage("Game started! Let's play for the million!", "system");
  };

  // Load a question for the current level
  const loadQuestion = (level) => {
    const questions = getQuestionsForDifficulty(level);
    const question = questions[Math.floor(Math.random() * questions.length)];

    setCurrentQuestion(question);
    setSelectedAnswer(null);
    setIsAnswerLocked(false);
    setActiveLifeline(null);
    setAudienceVotes({});
    setPhoneAdvice(null);
    setEliminatedOptions([]);

    // Start question timer if timed mode
    if (config.gameType === 'timed') {
      setTimeLeft(config.timePerQuestion);
      timerRef.current = setInterval(() => {
        setTimeLeft(prev => {
          if (prev <= 1) {
            clearInterval(timerRef.current);
            handleTimeUp();
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }

    setGameStatus(GAME_STATUS.QUESTION);
    addMessage(`Question ${level}: ${question.category} - ${question.difficulty}`, "system");
  };

  // Handle answer selection
  const selectAnswer = (answerIndex) => {
    if (isAnswerLocked || gameStatus !== GAME_STATUS.QUESTION) return;

    setSelectedAnswer(answerIndex);
  };

  // Lock in final answer
  const lockAnswer = () => {
    if (selectedAnswer === null) return;

    setIsAnswerLocked(true);

    // Clear timer
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }

    // Show answer reveal
    setTimeout(() => {
      revealAnswer();
    }, 2000);

    addMessage(`Answer locked: ${currentQuestion.options[selectedAnswer]}`, "system");
  };

  // Handle time up
  const handleTimeUp = () => {
    if (selectedAnswer !== null) {
      lockAnswer();
    } else {
      // No answer selected - player is eliminated
      eliminatePlayer();
    }
  };

  // Reveal the correct answer
  const revealAnswer = () => {
    const isCorrect = selectedAnswer === currentQuestion.correct;

    setGameStatus(GAME_STATUS.ANSWER_REVEAL);

    if (isCorrect) {
      // Update player level and earnings
      const newLevel = currentLevel + 1;
      const earnings = PRIZE_LADDER[currentLevel - 1].amount;

      setPlayers(prev => prev.map(player =>
        player.isActive ? {
          ...player,
          level: newLevel,
          earnings
        } : player
      ));

      setCurrentLevel(newLevel);

      addMessage(`Correct! You've won $${earnings.toLocaleString()}`, "system");

      // Check if game is complete
      if (newLevel > config.questionCount) {
        setTimeout(() => endGame('winner'), 3000);
      } else {
        setTimeout(() => proceedToNextQuestion(newLevel), 3000);
      }
    } else {
      addMessage(`Wrong answer! The correct answer was: ${currentQuestion.options[currentQuestion.correct]}`, "system");
      eliminatePlayer();
    }

    // Add to history
    setGameHistory(prev => [...prev, {
      level: currentLevel,
      question: currentQuestion.question,
      selectedAnswer: selectedAnswer !== null ? currentQuestion.options[selectedAnswer] : 'No answer',
      correctAnswer: currentQuestion.options[currentQuestion.correct],
      isCorrect,
      lifelinesUsed: Object.keys(lifelinesUsed).length
    }]);
  };

  // Eliminate current player
  const eliminatePlayer = () => {
    // Find safe level earnings
    const safeLevel = PRIZE_LADDER.slice(0, currentLevel - 1).reverse().find(level => level.safe);
    const finalEarnings = safeLevel ? safeLevel.amount : 0;

    setPlayers(prev => prev.map(player =>
      player.isActive ? {
        ...player,
        isEliminated: true,
        isActive: false,
        earnings: finalEarnings
      } : player
    ));

    addMessage(`Player eliminated! Final earnings: $${finalEarnings.toLocaleString()}`, "system");

    // Check if multiplayer - move to next player or end game
    setTimeout(() => {
      const remainingPlayers = players.filter(p => !p.isEliminated && p.id !== players.find(p => p.isActive)?.id);

      if (remainingPlayers.length > 0 && config.gameMode !== '1v1') {
        // Next player's turn
        setPlayers(prev => prev.map(player => ({
          ...player,
          isActive: player.id === remainingPlayers[0].id
        })));

        loadQuestion(1); // Start from level 1 for next player
        setCurrentLevel(1);
        setLifelinesUsed({});
      } else {
        endGame('eliminated');
      }
    }, 3000);
  };

  // Proceed to next question
  const proceedToNextQuestion = (level) => {
    loadQuestion(level);
  };

  // Use lifeline
  const useLifeline = (lifelineType) => {
    if (lifelinesUsed[lifelineType] || gameStatus !== GAME_STATUS.QUESTION) return;

    setLifelinesUsed(prev => ({ ...prev, [lifelineType]: true }));
    setActiveLifeline(lifelineType);
    setGameStatus(GAME_STATUS.LIFELINE);

    switch (lifelineType) {
      case LIFELINES.FIFTY_FIFTY:
        activateFiftyFifty();
        break;
      case LIFELINES.PHONE_FRIEND:
        activatePhoneFriend();
        break;
      case LIFELINES.ASK_AUDIENCE:
        activateAskAudience();
        break;
      case LIFELINES.EXPERT_ADVICE:
        activateExpertAdvice();
        break;
    }

    addMessage(`Lifeline used: ${lifelineType.replace('_', ' ').toUpperCase()}`, "system");
  };

  // 50:50 lifeline
  const activateFiftyFifty = () => {
    const correctIndex = currentQuestion.correct;
    const wrongOptions = [0, 1, 2, 3].filter(i => i !== correctIndex);

    // Randomly eliminate 2 wrong answers
    const toEliminate = wrongOptions.sort(() => 0.5 - Math.random()).slice(0, 2);
    setEliminatedOptions(toEliminate);

    setTimeout(() => {
      setGameStatus(GAME_STATUS.QUESTION);
      setActiveLifeline(null);
    }, 2000);
  };

  // Phone a Friend lifeline
  const activatePhoneFriend = () => {
    // Simulate friend's advice (not always correct)
    const correctIndex = currentQuestion.correct;
    const confidence = Math.random();

    let advice;
    if (confidence > 0.3) {
      // Friend gives correct answer 70% of the time
      advice = {
        option: currentQuestion.options[correctIndex],
        index: correctIndex,
        confidence: Math.floor(60 + Math.random() * 40) // 60-100% confidence
      };
    } else {
      // Friend gives wrong answer 30% of the time
      const wrongIndex = [0, 1, 2, 3].filter(i => i !== correctIndex)[Math.floor(Math.random() * 3)];
      advice = {
        option: currentQuestion.options[wrongIndex],
        index: wrongIndex,
        confidence: Math.floor(40 + Math.random() * 30) // 40-70% confidence
      };
    }

    setPhoneAdvice(advice);

    setTimeout(() => {
      setGameStatus(GAME_STATUS.QUESTION);
      setActiveLifeline(null);
    }, 4000);
  };

  // Ask the Audience lifeline
  const activateAskAudience = () => {
    const correctIndex = currentQuestion.correct;
    const votes = { 0: 0, 1: 0, 2: 0, 3: 0 };

    // Simulate audience voting (biased toward correct answer but not guaranteed)
    for (let i = 0; i < 100; i++) {
      let vote;
      if (Math.random() < 0.6) {
        // 60% chance to vote for correct answer
        vote = correctIndex;
      } else {
        // 40% chance to vote randomly
        vote = Math.floor(Math.random() * 4);
      }
      votes[vote]++;
    }

    setAudienceVotes(votes);

    setTimeout(() => {
      setGameStatus(GAME_STATUS.QUESTION);
      setActiveLifeline(null);
    }, 3000);
  };

  // Expert Advice lifeline (new addition)
  const activateExpertAdvice = () => {
    const correctIndex = currentQuestion.correct;
    const expertAccuracy = 0.85; // Expert is right 85% of the time

    let advice;
    if (Math.random() < expertAccuracy) {
      advice = {
        option: currentQuestion.options[correctIndex],
        index: correctIndex,
        reasoning: "Based on my expertise, I'm confident this is the correct answer.",
        confidence: "High"
      };
    } else {
      const wrongIndex = [0, 1, 2, 3].filter(i => i !== correctIndex)[Math.floor(Math.random() * 3)];
      advice = {
        option: currentQuestion.options[wrongIndex],
        index: wrongIndex,
        reasoning: "I believe this is correct, though I'm not entirely certain.",
        confidence: "Medium"
      };
    }

    setPhoneAdvice(advice);

    setTimeout(() => {
      setGameStatus(GAME_STATUS.QUESTION);
      setActiveLifeline(null);
    }, 4000);
  };

  // Walk away (cash out)
  const walkAway = () => {
    const currentEarnings = currentLevel > 1 ? PRIZE_LADDER[currentLevel - 2].amount : 0;

    setPlayers(prev => prev.map(player =>
      player.isActive ? {
        ...player,
        earnings: currentEarnings,
        isEliminated: true
      } : player
    ));

    addMessage(`Walked away with $${currentEarnings.toLocaleString()}`, "system");
    endGame('walkaway');
  };

  // End the game
  const endGame = (reason) => {
    setGameStatus(GAME_STATUS.GAME_END);

    // Clear timers
    if (timerRef.current) clearInterval(timerRef.current);
    if (gameTimerRef.current) clearInterval(gameTimerRef.current);

    // Calculate final results
    const activePlayer = players.find(p => p.isActive) || players.find(p => p.id === 1);
    const finalEarnings = activePlayer.earnings;

    let result = {
      reason,
      earnings: finalEarnings,
      level: currentLevel,
      questionsAnswered: gameHistory.length,
      lifelinesUsed: Object.keys(lifelinesUsed).length,
      payout: 0
    };

    // Calculate payout based on performance
    if (reason === 'winner') {
      result.payout = config.prizePool;
      addMessage("Congratulations! You've won the million dollar prize!", "system");
    } else if (finalEarnings >= 32000) {
      result.payout = config.wagerAmount * 2;
      addMessage(`Great performance! 2x payout: $${result.payout}`, "system");
    } else if (finalEarnings >= 1000) {
      result.payout = config.wagerAmount;
      addMessage(`Good effort! Wager returned: $${result.payout}`, "system");
    } else {
      addMessage("Better luck next time!", "system");
    }

    setGameResult(result);
  };

  // Reset the game
  const resetGame = () => {
    // Clear timers
    if (timerRef.current) clearInterval(timerRef.current);
    if (gameTimerRef.current) clearInterval(gameTimerRef.current);

    // Reset state
    setGameStatus(GAME_STATUS.SETUP);
    setCurrentLevel(1);
    setGameHistory([]);
    setGameResult(null);
    setCurrentQuestion(null);
    setLifelinesUsed({});
    setPlayers(prev => prev.map(player => ({
      ...player,
      level: 1,
      earnings: 0,
      isActive: player.id === 1,
      isEliminated: false,
      lifelinesUsed: {}
    })));

    addMessage("Game reset! Ready for another million-dollar challenge?", "system");
  };

  // Format time display
  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds < 10 ? '0' : ''}${remainingSeconds}`;
  };

  // Get current prize amount
  const getCurrentPrize = () => {
    return currentLevel > 1 ? PRIZE_LADDER[currentLevel - 2].amount : 0;
  };

  // Get next prize amount
  const getNextPrize = () => {
    return PRIZE_LADDER[currentLevel - 1]?.amount || 1000000;
  };

  // Render prize ladder
  const renderPrizeLadder = () => {
    return (
      <div className="bg-slate-900 border border-slate-800 rounded-sm p-2">
        <h3 className="text-sm font-medium text-white mb-2 flex items-center">
          <Trophy className="h-4 w-4 mr-2 text-yellow-500" />
          Prize Ladder
        </h3>

        <div className="space-y-1 max-h-64 overflow-auto">
          {PRIZE_LADDER.slice().reverse().map((prize, index) => {
            const isCurrentLevel = prize.level === currentLevel;
            const isCompleted = prize.level < currentLevel;

            return (
              <div
                key={prize.level}
                className={`flex justify-between items-center p-1.5 rounded-sm text-xs ${
                  isCurrentLevel ? 'bg-yellow-500 text-black font-bold' :
                  isCompleted ? 'bg-green-900/30 text-green-400' :
                  'bg-slate-800 text-slate-300'
                }`}
              >
                <span className="flex items-center">
                  <span className="w-6">{prize.level}</span>
                  {prize.safe && <Lock className="h-3 w-3 ml-1 text-blue-400" />}
                </span>
                <span className="font-bold">${prize.amount.toLocaleString()}</span>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  // Render lifelines
  const renderLifelines = () => {
    const lifelines = [
      {
        type: LIFELINES.FIFTY_FIFTY,
        name: '50:50',
        icon: <Split className="h-4 w-4" />,
        description: 'Remove 2 wrong answers'
      },
      {
        type: LIFELINES.PHONE_FRIEND,
        name: 'Phone',
        icon: <Phone className="h-4 w-4" />,
        description: 'Call a friend for help'
      },
      {
        type: LIFELINES.ASK_AUDIENCE,
        name: 'Audience',
        icon: <UsersRound className="h-4 w-4" />,
        description: 'Ask the audience to vote'
      },
      {
        type: LIFELINES.EXPERT_ADVICE,
        name: 'Expert',
        icon: <Brain className="h-4 w-4" />,
        description: 'Get expert analysis'
      }
    ];

    return (
      <div className="flex space-x-2 justify-center">
        {lifelines.map(lifeline => (
          <Button
            key={lifeline.type}
            variant={lifelinesUsed[lifeline.type] ? "outline" : "default"}
            size="sm"
            className={`h-12 w-16 flex flex-col items-center justify-center text-xs ${
              lifelinesUsed[lifeline.type]
                ? 'opacity-30 cursor-not-allowed bg-slate-800 border-slate-700'
                : 'bg-gradient-to-b from-blue-500 to-blue-700 hover:from-blue-400 hover:to-blue-600'
            }`}
            onClick={() => useLifeline(lifeline.type)}
            disabled={lifelinesUsed[lifeline.type] || gameStatus !== GAME_STATUS.QUESTION}
            title={lifeline.description}
          >
            {lifeline.icon}
            <span className="mt-1">{lifeline.name}</span>
          </Button>
        ))}
      </div>
    );
  };

  // Render question and answers
  const renderQuestion = () => {
    if (!currentQuestion) return null;

    return (
      <div className="text-center">
        {/* Question header */}
        <div className="mb-4">
          <Badge className="bg-purple-500 mb-2">
            Question {currentLevel} of {config.questionCount}
          </Badge>
          <h2 className="text-lg font-bold text-white mb-2">
            For ${getNextPrize().toLocaleString()}
          </h2>
          <div className="text-slate-400 text-sm mb-4">
            Category: {currentQuestion.category} • Difficulty: {currentQuestion.difficulty}
          </div>
        </div>

        {/* Question text */}
        <div className="bg-slate-800 p-4 rounded-sm mb-6">
          <h3 className="text-xl font-medium text-white leading-relaxed">
            {currentQuestion.question}
          </h3>
        </div>

        {/* Timer */}
        {config.gameType === 'timed' && gameStatus === GAME_STATUS.QUESTION && (
          <div className="mb-4">
            <div className="flex justify-center items-center mb-2">
              <Timer className="h-4 w-4 mr-2 text-red-500" />
              <span className="text-lg font-bold text-white">{timeLeft}s</span>
            </div>
            <Progress value={(timeLeft / config.timePerQuestion) * 100} className="h-2 max-w-xs mx-auto" />
          </div>
        )}

        {/* Answer options */}
        <div className="grid grid-cols-2 gap-3 mb-6">
          {currentQuestion.options.map((option, index) => {
            const isEliminated = eliminatedOptions.includes(index);
            const isSelected = selectedAnswer === index;
            const isRevealing = gameStatus === GAME_STATUS.ANSWER_REVEAL;
            const isCorrect = index === currentQuestion.correct;
            const isWrong = isRevealing && isSelected && !isCorrect;

            return (
              <Button
                key={index}
                variant="outline"
                className={`h-16 text-left justify-start p-4 text-sm transition-all ${
                  isEliminated
                    ? 'opacity-20 cursor-not-allowed bg-slate-900'
                    : isSelected && !isRevealing
                    ? 'bg-yellow-500 border-yellow-400 text-black'
                    : isRevealing && isCorrect
                    ? 'bg-green-500 border-green-400 text-white animate-pulse'
                    : isWrong
                    ? 'bg-red-500 border-red-400 text-white'
                    : 'bg-slate-800 border-slate-600 text-white hover:border-slate-500'
                }`}
                onClick={() => selectAnswer(index)}
                disabled={isEliminated || isAnswerLocked || gameStatus !== GAME_STATUS.QUESTION}
              >
                <span className="font-bold mr-3">{String.fromCharCode(65 + index)}:</span>
                <span>{option}</span>
              </Button>
            );
          })}
        </div>

        {/* Final answer button */}
        {gameStatus === GAME_STATUS.QUESTION && selectedAnswer !== null && !isAnswerLocked && (
          <Button
            className="h-12 px-8 text-lg bg-gradient-to-r from-green-500 to-green-600 hover:from-green-400 hover:to-green-500"
            onClick={lockAnswer}
          >
            <Lock className="h-5 w-5 mr-2" />
            Final Answer
          </Button>
        )}

        {/* Walk away button */}
        {gameStatus === GAME_STATUS.QUESTION && currentLevel > 1 && (
          <Button
            variant="outline"
            className="ml-4 h-12 px-6"
            onClick={walkAway}
          >
            Walk Away (${getCurrentPrize().toLocaleString()})
          </Button>
        )}
      </div>
    );
  };

  // Render lifeline effects
  const renderLifelineEffects = () => {
    if (gameStatus !== GAME_STATUS.LIFELINE) return null;

    return (
      <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-50">
        <div className="bg-slate-900 border border-slate-800 rounded-sm p-6 max-w-md w-full mx-4">
          <h3 className="text-lg font-bold text-white mb-4 text-center">
            {activeLifeline?.replace('_', ' ').toUpperCase()}
          </h3>

          {activeLifeline === LIFELINES.FIFTY_FIFTY && (
            <div className="text-center">
              <div className="text-white mb-4">
                Removing 2 incorrect answers...
              </div>
              <div className="animate-spin h-8 w-8 border-4 border-yellow-500 border-t-transparent rounded-full mx-auto"></div>
            </div>
          )}

          {activeLifeline === LIFELINES.PHONE_FRIEND && phoneAdvice && (
            <div>
              <div className="text-white mb-4">Your friend says:</div>
              <div className="bg-slate-800 p-3 rounded-sm mb-4">
                <div className="text-white mb-2">
                  "I think the answer is <strong>{phoneAdvice.option}</strong>"
                </div>
                <div className="text-slate-400 text-sm">
                  Confidence: {phoneAdvice.confidence}%
                </div>
              </div>
            </div>
          )}

          {activeLifeline === LIFELINES.ASK_AUDIENCE && Object.keys(audienceVotes).length > 0 && (
            <div>
              <div className="text-white mb-4">Audience votes:</div>
              <div className="space-y-2">
                {Object.entries(audienceVotes).map(([option, votes]) => (
                  <div key={option} className="flex items-center">
                    <span className="text-white w-8">{String.fromCharCode(65 + parseInt(option))}:</span>
                    <div className="flex-1 mx-2">
                      <Progress value={votes} className="h-4" />
                    </div>
                    <span className="text-white w-12 text-right">{votes}%</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeLifeline === LIFELINES.EXPERT_ADVICE && phoneAdvice && (
            <div>
              <div className="text-white mb-4">Expert analysis:</div>
              <div className="bg-slate-800 p-3 rounded-sm mb-4">
                <div className="text-white mb-2">
                  <strong>{phoneAdvice.option}</strong>
                </div>
                <div className="text-slate-400 text-sm mb-2">
                  {phoneAdvice.reasoning}
                </div>
                <div className="text-blue-400 text-sm">
                  Confidence: {phoneAdvice.confidence}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    );
  };

  // Render settings panel
  const renderSettings = () => {
    return (
      <div className="bg-slate-900 border border-slate-800 rounded-sm p-3 absolute top-0 left-0 right-0 z-10">
        <div className="flex justify-between items-center mb-3">
          <h3 className="text-sm font-medium text-white">Game Settings</h3>
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0"
            onClick={() => setShowSettings(false)}
          >
            <X className="h-3 w-3" />
          </Button>
        </div>

        <div className="space-y-3">
          <div>
            <label className="text-xs text-slate-400 block mb-1">Game Mode</label>
            <div className="flex space-x-2">
              {['1v1', '1vN'].map(mode => (
                <Button
                  key={mode}
                  variant={config.gameMode === mode ? "default" : "outline"}
                  size="sm"
                  className="flex-1 h-7 text-xs"
                  onClick={() => setConfig({ ...config, gameMode: mode })}
                >
                  {mode}
                </Button>
              ))}
            </div>
          </div>

          <div>
            <label className="text-xs text-slate-400 block mb-1">Game Type</label>
            <div className="flex space-x-2">
              {['timed', 'untimed'].map(type => (
                <Button
                  key={type}
                  variant={config.gameType === type ? "default" : "outline"}
                  size="sm"
                  className="flex-1 h-7 text-xs"
                  onClick={() => setConfig({ ...config, gameType: type })}
                >
                  {type}
                </Button>
              ))}
            </div>
          </div>

          {config.gameType === 'timed' && (
            <div>
              <label className="text-xs text-slate-400 block mb-1">Time per Question</label>
              <div className="flex space-x-2">
                {[15, 30, 45, 60].map(time => (
                  <Button
                    key={time}
                    variant={config.timePerQuestion === time ? "default" : "outline"}
                    size="sm"
                    className="flex-1 h-7 text-xs"
                    onClick={() => setConfig({ ...config, timePerQuestion: time })}
                  >
                    {time}s
                  </Button>
                ))}
              </div>
            </div>
          )}

          <div className="grid grid-cols-2 gap-3">
            <div>
              <label className="text-xs text-slate-400 block mb-1">Questions</label>
              <div className="flex space-x-1">
                {[10, 15].map(num => (
                  <Button
                    key={num}
                    variant={config.questionCount === num ? "default" : "outline"}
                    size="sm"
                    className="flex-1 h-7 text-xs"
                    onClick={() => setConfig({ ...config, questionCount: num })}
                  >
                    {num}
                  </Button>
                ))}
              </div>
            </div>

            <div>
              <label className="text-xs text-slate-400 block mb-1">Wager</label>
              <div className="flex space-x-1">
                {[50, 100, 200].map(amount => (
                  <Button
                    key={amount}
                    variant={config.wagerAmount === amount ? "default" : "outline"}
                    size="sm"
                    className="flex-1 h-7 text-xs"
                    onClick={() => setConfig({ ...config, wagerAmount: amount })}
                  >
                    ${amount}
                  </Button>
                ))}
              </div>
            </div>
          </div>

          <div className="flex items-center justify-between">
            <label className="text-xs text-slate-400">Lifelines</label>
            <Button
              variant={config.allowLifelines ? "default" : "outline"}
              size="sm"
              className="h-7 w-14 text-xs"
              onClick={() => setConfig({ ...config, allowLifelines: !config.allowLifelines })}
            >
              {config.allowLifelines ? "On" : "Off"}
            </Button>
          </div>

          <div className="flex items-center justify-between">
            <label className="text-xs text-slate-400">Spectator Betting</label>
            <Button
              variant={config.spectatorBetting ? "default" : "outline"}
              size="sm"
              className="h-7 w-14 text-xs"
              onClick={() => setConfig({ ...config, spectatorBetting: !config.spectatorBetting })}
            >
              {config.spectatorBetting ? "On" : "Off"}
            </Button>
          </div>
        </div>
      </div>
    );
  };

  // Render setup screen
  const renderSetupScreen = () => {
    return (
      <div className="flex flex-col items-center justify-center h-full bg-slate-900 border border-slate-800 rounded-sm p-4">
        <div className="text-6xl mb-4">🧠</div>
        <h1 className="text-3xl font-bold text-white mb-1">Quiz Arena</h1>
        <p className="text-lg text-yellow-500 mb-2">Who Wants to Be a Millionaire?</p>
        <p className="text-sm text-slate-400 mb-6 text-center">
          Answer questions correctly to climb the prize ladder!<br />
          Use lifelines wisely and compete for the ultimate prize.
        </p>

        <div className="w-full max-w-md mb-6">
          <div className="bg-slate-800 p-4 rounded-sm mb-4">
            <h3 className="text-sm font-medium text-white mb-3">Quick Setup</h3>

            <div className="space-y-3">
              <div>
                <label className="text-xs text-slate-400 block mb-1">Game Mode</label>
                <div className="flex space-x-2">
                  {['1v1', '1vN'].map(mode => (
                    <Button
                      key={mode}
                      variant={config.gameMode === mode ? "default" : "outline"}
                      size="sm"
                      className="flex-1 h-8 text-xs"
                      onClick={() => setConfig({ ...config, gameMode: mode })}
                    >
                      {mode === '1v1' ? '1 vs 1' : 'Tournament'}
                    </Button>
                  ))}
                </div>
              </div>

              <div>
                <label className="text-xs text-slate-400 block mb-1">Timer</label>
                <div className="flex space-x-2">
                  {['timed', 'untimed'].map(type => (
                    <Button
                      key={type}
                      variant={config.gameType === type ? "default" : "outline"}
                      size="sm"
                      className="flex-1 h-8 text-xs"
                      onClick={() => setConfig({ ...config, gameType: type })}
                    >
                      <div className="flex items-center">
                        {type === 'timed' ? <Timer className="h-3 w-3 mr-1" /> : <Unlock className="h-3 w-3 mr-1" />}
                        {type}
                      </div>
                    </Button>
                  ))}
                </div>
              </div>

              <div className="grid grid-cols-2 gap-3">
                <div>
                  <label className="text-xs text-slate-400 block mb-1">Wager</label>
                  <div className="flex space-x-1">
                    {[50, 100, 200].map(amount => (
                      <Button
                        key={amount}
                        variant={config.wagerAmount === amount ? "default" : "outline"}
                        size="sm"
                        className="flex-1 h-7 text-xs"
                        onClick={() => setConfig({ ...config, wagerAmount: amount })}
                      >
                        ${amount}
                      </Button>
                    ))}
                  </div>
                </div>

                <div>
                  <label className="text-xs text-slate-400 block mb-1">Prize Pool</label>
                  <div className="flex space-x-1">
                    {[500, 1000, 2000].map(amount => (
                      <Button
                        key={amount}
                        variant={config.prizePool === amount ? "default" : "outline"}
                        size="sm"
                        className="flex-1 h-7 text-xs"
                        onClick={() => setConfig({ ...config, prizePool: amount })}
                      >
                        ${amount}
                      </Button>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="flex space-x-3">
          <Button
            variant="outline"
            className="w-32 h-10 text-sm rounded-sm"
            onClick={() => setShowSettings(true)}
          >
            <Settings className="h-4 w-4 mr-2" />
            Advanced
          </Button>

          <Button
            className="w-32 h-10 text-sm rounded-sm bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-400 hover:to-yellow-500 text-black font-bold"
            onClick={startGame}
          >
            <Crown className="h-4 w-4 mr-2" />
            Start Quiz
          </Button>
        </div>
      </div>
    );
  };

  // Render game info
  const renderGameInfo = () => {
    const activePlayer = players.find(p => p.isActive) || players[0];

    return (
      <div className="bg-slate-900 border border-slate-800 rounded-sm p-2">
        <div className="flex justify-between items-center mb-2">
          <h3 className="text-sm font-medium text-white">
            {gameStatus === GAME_STATUS.SETUP ? "Quiz Setup" :
             gameStatus === GAME_STATUS.GAME_END ? "Quiz Complete" :
             `Level ${currentLevel}`}
          </h3>
          <Badge className={`
            ${gameStatus === GAME_STATUS.SETUP ? 'bg-yellow-500' :
              gameStatus === GAME_STATUS.GAME_END ? 'bg-blue-500' :
              'bg-red-500 animate-pulse'}
          `}>
            {gameStatus === GAME_STATUS.SETUP ? 'SETUP' :
             gameStatus === GAME_STATUS.GAME_END ? 'FINISHED' :
             'LIVE'}
          </Badge>
        </div>

        <div className="grid grid-cols-2 gap-2">
          <div className="bg-slate-800 p-2 rounded-sm">
            <div className="text-xs text-slate-400">Current Winnings</div>
            <div className="text-sm font-bold text-green-500">
              ${activePlayer?.earnings?.toLocaleString() || '0'}
            </div>
          </div>

          <div className="bg-slate-800 p-2 rounded-sm">
            <div className="text-xs text-slate-400">Playing For</div>
            <div className="text-sm font-bold text-yellow-500">
              ${getNextPrize().toLocaleString()}
            </div>
          </div>
        </div>
      </div>
    );
  };

  // Main game layout
  return (
    <div className="grid grid-cols-12 gap-2 p-2 h-full">
      {/* Game Information - Left column */}
      <div className="col-span-3 flex flex-col gap-2 h-full">
        {renderGameInfo()}

        {/* Prize Ladder */}
        {gameStatus !== GAME_STATUS.SETUP && renderPrizeLadder()}

        {/* Game Result */}
        {gameStatus === GAME_STATUS.GAME_END && gameResult && (
          <div className="bg-slate-900 border border-slate-800 rounded-sm p-2">
            <h3 className="text-sm font-medium text-white mb-2">Final Results</h3>

            <div className="text-center mb-2">
              <div className="text-2xl mb-1">
                {gameResult.reason === 'winner' ? '🏆' :
                 gameResult.reason === 'walkaway' ? '💰' : '😔'}
              </div>
              <div className={`text-lg font-bold ${
                gameResult.reason === 'winner' ? 'text-yellow-500' :
                gameResult.earnings > 0 ? 'text-green-500' : 'text-red-500'
              }`}>
                ${gameResult.earnings.toLocaleString()}
              </div>
              <div className="text-xs text-slate-400">
                Level {gameResult.level} • {gameResult.questionsAnswered} questions
              </div>
            </div>

            {gameResult.payout > 0 && (
              <div className="bg-slate-800 p-2 rounded-sm mb-2">
                <div className="text-center">
                  <div className="text-xs text-slate-400">Payout</div>
                  <div className="text-lg font-bold text-green-500">
                    ${gameResult.payout.toFixed(2)}
                  </div>
                </div>
              </div>
            )}

            <Button
              onClick={resetGame}
              className="w-full h-7 text-xs rounded-sm bg-gradient-to-r from-purple-500 to-pink-500"
            >
              <RotateCcw className="h-3 w-3 mr-1" />
              Play Again
            </Button>
          </div>
        )}
      </div>

      {/* Game Board - Middle column */}
      <div className="col-span-6 flex flex-col h-full">
        {/* Game board */}
        <div className="flex-1 bg-slate-900 border border-slate-800 rounded-sm p-3 flex flex-col relative">
          {gameStatus === GAME_STATUS.SETUP ? (
            renderSetupScreen()
          ) : (
            <div className="flex-1 flex flex-col justify-center">
              {/* Current player indicator */}
              {config.gameMode === '1vN' && (
                <div className="text-center mb-4">
                  <Badge className="bg-blue-500">
                    Current Player: {players.find(p => p.isActive)?.name}
                  </Badge>
                </div>
              )}

              {/* Question display */}
              {renderQuestion()}
            </div>
          )}

          {/* Settings overlay */}
          {showSettings && renderSettings()}

          {/* Lifeline effects overlay */}
          {renderLifelineEffects()}
        </div>

        {/* Lifelines */}
        {gameStatus !== GAME_STATUS.SETUP && config.allowLifelines && (
          <div className="mt-2 bg-slate-900 border border-slate-800 rounded-sm p-2">
            <div className="flex justify-between items-center mb-2">
              <h3 className="text-sm font-medium text-white">Lifelines</h3>
              <Badge className="bg-blue-500">
                {4 - Object.keys(lifelinesUsed).length} remaining
              </Badge>
            </div>

            {renderLifelines()}
          </div>
        )}

        {/* Game controls */}
        <div className="flex justify-between items-center mt-2">
          <Button
            variant="outline"
            size="sm"
            className="h-8 text-xs"
            onClick={resetGame}
          >
            <RotateCcw className="h-3 w-3 mr-1" />
            Reset Quiz
          </Button>

          <div className="flex items-center space-x-2">
            <Badge className={config.gameType === 'timed' ? 'bg-red-500' : 'bg-green-500'}>
              {config.gameType === 'timed' ? 'Timed' : 'Untimed'}
            </Badge>
          </div>

          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              className={`h-8 text-xs ${showSettings ? 'bg-slate-800' : ''}`}
              onClick={() => setShowSettings(!showSettings)}
            >
              <Settings className="h-3 w-3 mr-1" />
              Settings
            </Button>

            <Button
              variant={gameStatus === GAME_STATUS.SETUP ? 'default' : 'outline'}
              size="sm"
              className="h-8 text-xs"
              disabled={gameStatus !== GAME_STATUS.SETUP}
              onClick={startGame}
            >
              {gameStatus === GAME_STATUS.SETUP ? (
                <>
                  <Zap className="h-3 w-3 mr-1" />
                  Start Quiz
                </>
              ) : (
                <>
                  <Trophy className="h-3 w-3 mr-1" />
                  Quiz Active
                </>
              )}
            </Button>
          </div>
        </div>
      </div>

      {/* Spectator & Betting - Right column */}
      <div className="col-span-3 flex flex-col gap-2 h-full">
        {/* Live Spectator Betting */}
        {config.spectatorBetting && (
          <div className="bg-slate-900 border border-slate-800 rounded-sm p-2">
            <div className="flex justify-between items-center mb-2">
              <h3 className="text-sm font-medium text-white">Live Betting</h3>
              <Badge className={gameStatus === GAME_STATUS.QUESTION ? "bg-green-500" : "bg-red-500"}>
                {gameStatus === GAME_STATUS.QUESTION ? "OPEN" : "CLOSED"}
              </Badge>
            </div>

            <div className="space-y-2">
              <div className="bg-slate-800 p-2 rounded-sm">
                <div className="flex justify-between items-center mb-1">
                  <span className="text-xs text-slate-400">Correct Answer</span>
                  <span className="text-xs font-bold text-white">{spectatorBets.correct.odds}x</span>
                </div>
                <Button
                  className="w-full h-6 text-[10px] rounded-sm bg-gradient-to-r from-green-500 to-green-600"
                  disabled={gameStatus !== GAME_STATUS.QUESTION}
                >
                  Bet Correct
                </Button>
              </div>

              <div className="bg-slate-800 p-2 rounded-sm">
                <div className="flex justify-between items-center mb-1">
                  <span className="text-xs text-slate-400">Wrong Answer</span>
                  <span className="text-xs font-bold text-white">{spectatorBets.wrong.odds}x</span>
                </div>
                <Button
                  className="w-full h-6 text-[10px] rounded-sm bg-gradient-to-r from-red-500 to-red-600"
                  disabled={gameStatus !== GAME_STATUS.QUESTION}
                >
                  Bet Wrong
                </Button>
              </div>

              <div className="bg-slate-800 p-2 rounded-sm">
                <div className="flex justify-between items-center mb-1">
                  <span className="text-xs text-slate-400">Uses Lifeline</span>
                  <span className="text-xs font-bold text-white">{spectatorBets.lifeline.odds}x</span>
                </div>
                <Button
                  className="w-full h-6 text-[10px] rounded-sm bg-gradient-to-r from-blue-500 to-blue-600"
                  disabled={gameStatus !== GAME_STATUS.QUESTION}
                >
                  Bet Lifeline
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Live Stats */}
        <div className="bg-slate-900 border border-slate-800 rounded-sm p-2">
          <div className="flex justify-between items-center mb-2">
            <h3 className="text-sm font-medium text-white">Live Stats</h3>
            <Badge className="bg-purple-500">
              <Eye className="h-3 w-3 mr-1" />
              127 watching
            </Badge>
          </div>

          <div className="space-y-2">
            <div className="flex justify-between text-xs">
              <span className="text-slate-400">Quiz Time</span>
              <span className="text-white">{formatTime(totalGameTime)}</span>
            </div>

            <div className="flex justify-between text-xs">
              <span className="text-slate-400">Lifelines Used</span>
              <span className="text-white">{Object.keys(lifelinesUsed).length}/4</span>
            </div>

            <div className="flex justify-between text-xs">
              <span className="text-slate-400">Questions Left</span>
              <span className="text-white">{Math.max(0, config.questionCount - currentLevel + 1)}</span>
            </div>

            {gameStatus === GAME_STATUS.QUESTION && config.gameType === 'timed' && (
              <div>
                <div className="flex justify-between text-xs mb-1">
                  <span className="text-slate-400">Time Remaining</span>
                  <span className="text-white">{timeLeft}s</span>
                </div>
                <Progress value={(timeLeft / config.timePerQuestion) * 100} className="h-1.5" />
              </div>
            )}
          </div>
        </div>

        {/* Players Status (for multiplayer) */}
        {config.gameMode === '1vN' && gameStatus !== GAME_STATUS.SETUP && (
          <div className="bg-slate-900 border border-slate-800 rounded-sm p-2">
            <div className="flex justify-between items-center mb-2">
              <h3 className="text-sm font-medium text-white">Players</h3>
              <Badge className="bg-blue-500">
                {players.filter(p => !p.isEliminated).length} active
              </Badge>
            </div>

            <div className="space-y-1">
              {players.slice(0, config.maxPlayers).map(player => (
                <div
                  key={player.id}
                  className={`flex items-center justify-between p-1.5 rounded-sm text-xs ${
                    player.isActive ? 'bg-yellow-500/20 border border-yellow-500' :
                    player.isEliminated ? 'bg-red-900/20 opacity-50' :
                    'bg-slate-800'
                  }`}
                >
                  <div className="flex items-center">
                    <div className={`h-4 w-4 rounded-full mr-1.5 flex items-center justify-center text-[10px] ${
                      player.isActive ? 'bg-yellow-500 text-black' :
                      player.isEliminated ? 'bg-red-500' :
                      'bg-slate-600'
                    }`}>
                      {player.id}
                    </div>
                    <span className="text-white">{player.name}</span>
                    {player.isActive && <Crown className="h-3 w-3 ml-1 text-yellow-500" />}
                  </div>

                  <div className="text-right">
                    <div className="text-white">${player.earnings.toLocaleString()}</div>
                    <div className="text-slate-400">L{player.level}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Chat */}
        <div className="flex-1 bg-slate-900 border border-slate-800 rounded-sm flex flex-col overflow-hidden">
          <div className="p-2 border-b border-slate-800 flex justify-between items-center">
            <div className="flex items-center">
              <MessageSquare className="h-4 w-4 text-slate-400 mr-1" />
              <h3 className="text-sm font-medium text-white">Quiz Chat</h3>
            </div>
            <div className="flex items-center">
              <Badge className="h-5 bg-green-500 mr-1 text-[10px]">
                <Users className="h-3 w-3 mr-1" />
                127
              </Badge>
              <Button
                variant="ghost"
                size="sm"
                className="h-5 w-5 p-0"
              >
                <ChevronDown className="h-3 w-3 text-slate-400" />
              </Button>
            </div>
          </div>

          {/* Chat messages - scrollable */}
          <div className="flex-1 overflow-auto p-2 space-y-1">
            {messages.map((msg, idx) => (
              <div
                key={idx}
                className={`text-xs p-1 rounded-sm ${
                  msg.type === 'system' ? 'text-yellow-500 italic font-medium' : 'text-white'
                }`}
              >
                {msg.text}
              </div>
            ))}
            <div ref={messageEndRef} />
          </div>

          {/* Chat input */}
          <div className="p-2 border-t border-slate-800">
            <div className="flex space-x-2">
              <Input
                placeholder="Cheer on the contestants..."
                className="h-8 text-xs bg-slate-800 border-slate-700"
                value={messageInput}
                onChange={(e) => setMessageInput(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    sendMessage();
                  }
                }}
              />
              <Button
                onClick={sendMessage}
                className="h-8 px-3 bg-gradient-to-r from-purple-500 to-pink-500"
              >
                Send
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default QuizArenaGame;