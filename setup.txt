🎮 Core Gameplay Features
Dual Input Modes

Drag & Drop: Players drag letters from the scrambled pile to answer boxes
Typing Mode: Players type their words directly (with letter validation)
Touch-Friendly: Click letters to auto-place them in the first available box

Multiplayer Support

1v1 Mode: Head-to-head competition between two players
1vN Mode: Up to 8 players competing simultaneously
Real-time Updates: See other players' progress and when they're ready

Smart Scoring System

Base Points: Length-based scoring (3 letters = 30pts, 4 letters = 40pts, etc.)
Target Word Bonus: 2x multiplier for finding the designated target word
Valid Word Bonus: 1.5x multiplier for any word in the solutions list
Length Bonuses: Extra multipliers for 6+ and 8+ letter words

⚙️ Game Configuration
Difficulty Levels

Easy: 3-letter words (CAT, DOG, SUN)
Medium: 4-letter words (FLOW, WORK, TIME)
Hard: 5+ letter words (COMPUTER, READING, ANIMALS)

Customizable Settings

Game Modes: 1v1 or 1vN (up to 8 players)
Round Count: 3, 5, 7, or 10 rounds
Time Limits: 30s, 60s, 90s, or 120s per round
Wager Amounts: $25, $50, $100, $200
Minimum Word Length: Configurable (default 3 letters)

🎯 Advanced Features
Interactive Gameplay

Letter Shuffling: Reorganize scrambled letters to spot new patterns
RetryTMContinueEdit
Answer Box Management: Click filled boxes to remove letters and return them to the pool
Real-time Validation: Immediate feedback on word validity and point calculation
Auto-submission: Round ends when all players submit or time expires

Multiplayer Mechanics

Player Status Tracking: See who's ready, current words, and scores
AI Opponents: Simulated players with realistic timing and word selection
Round Winners: Clear indication of who won each round and why
Cumulative Scoring: Points accumulate across all rounds

🏆 Scoring & Payouts
Performance-Based Payouts

3x Payout: Dominant victory (100+ point margin)
2x Payout: Clear victory (50+ point margin)
1.5x Payout: Close victory (any margin)
No Payout: Loss to other players

Scoring Strategy

Risk vs Reward: Submit early with shorter words vs wait for longer words
Target Word Hunt: Finding the target word gives massive bonus points
Speed Matters: Other players can end the round by submitting first

📱 User Interface
Responsive Design

Visual Feedback: Drag states, hover effects, and smooth transitions
Color Coding: Different states for available, used, and selected letters
Progress Tracking: Round progress, player readiness, and time remaining

Accessibility Features

Multiple Input Methods: Both drag-and-drop and keyboard input
Clear Visual Hierarchy: Easy to distinguish between game elements
Touch Optimization: Large click targets for mobile devices

🔧 Technical Implementation
Drag & Drop System
javascript// Handles both desktop drag-and-drop and mobile click-to-place
const handleLetterClick = (letter, index) => {
  const emptyBoxIndex = answerBoxes.findIndex(box => box === '');
  if (emptyBoxIndex !== -1) {
    // Auto-place in first empty box
    updateAnswerBoxes(emptyBoxIndex, letter);
  }
};
Word Validation
javascriptconst calculateWordScore = (word) => {
  const baseScore = word.length * 10;
  let multiplier = 1;

  if (currentWordSet.solutions.includes(word)) multiplier = 1.5;
  if (word === currentWordSet.target) multiplier = 2;
  if (word.length >= 6) multiplier += 0.5;

  return Math.round(baseScore * multiplier);
};
Real-time Game State

Timer Management: Separate timers for rounds and overall game time
State Synchronization: All players see the same scrambled letters
Collision Prevention: Letters can't be used twice simultaneously

🎲 Game Flow

Setup Phase: Choose game mode, difficulty, rounds, and wager
Round Start: Letters are scrambled and displayed to all players
Word Building: Players arrange letters using drag-and-drop or typing
Submission: Players submit words when satisfied (or time runs out)
Scoring: Points awarded based on word length, validity, and bonuses
Round End: Winner announced, scores updated
Next Round: New letters presented (or game ends)
Final Results: Overall winner determined, payouts calculated

🌟 Sample Word Sets
The game includes carefully curated word sets for each difficulty:
Easy Examples:

TCA → CAT, ACT, TAC (target: CAT)
GOD → DOG, GOD (target: DOG)

Medium Examples:

OWLF → FLOW, FOWL, WOLF (target: FLOW)
KROW → WORK (target: WORK)

Hard Examples:

POTMCER → COMPUTER, COMPETE, COME (target: COMPUTER)
RINGDEA → READING, GARDEN, GRADE (target: READING)

🔄 Integration
To integrate into your platform:

Import the component: import WordJumbleGame from './WordJumbleGame'
Add to your layout: <WordJumbleGame />
Customize word sets: Expand the WORD_SETS object with more categories
Connect multiplayer: Hook into your real-time communication system

📈 Extensibility
Easy Expansion Points

Theme Categories: Sports words, technology terms, movie titles
Power-ups: Letter hints, time extensions, bonus multipliers
Tournament Mode: Bracket-style elimination competitions
Achievements: Unlock rewards for word streaks, perfect rounds
Custom Dictionaries: Player-submitted word sets

Advanced Features

Team Mode: Players work together to form words
Blitz Mode: Rapid-fire rounds with shorter time limits
Daily Challenges: Special themed word sets with bonus rewards
Leaderboards: Global rankings and seasonal competitions

The game maintains consistency with your platform's design language while providing engaging, competitive word-based gameplay that scales from casual 1v1 matches to tournament-style multiplayer competitions.

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Input } from '@/components/ui/input';
import {
  Trophy,
  Clock,
  MessageSquare,
  Users,
  Settings,
  Star,
  Target,
  Zap,
  RotateCcw,
  Check,
  X,
  AlertCircle,
  DollarSign,
  ChevronDown,
  Shuffle,
  Type,
  MousePointer,
  Award,
  Timer,
  Keyboard,
  Hand,
  Crown,
  Flame
} from 'lucide-react';

// Game status
const GAME_STATUS = {
  SETUP: 'setup',
  WAITING: 'waiting',
  PLAYING: 'playing',
  ROUND_END: 'round_end',
  GAME_END: 'game_end'
};

// Input modes
const INPUT_MODE = {
  DRAG: 'drag',
  TYPE: 'type'
};

// Sample word sets for different difficulties
const WORD_SETS = {
  easy: [
    { letters: 'TCA', solutions: ['CAT', 'ACT', 'TAC'], target: 'CAT' },
    { letters: 'GOD', solutions: ['DOG', 'GOD'], target: 'DOG' },
    { letters: 'UNS', solutions: ['SUN', 'NUS'], target: 'SUN' },
    { letters: 'RAT', solutions: ['TAR', 'RAT', 'ART'], target: 'TAR' },
    { letters: 'TEN', solutions: ['NET', 'TEN'], target: 'NET' }
  ],
  medium: [
    { letters: 'OWLF', solutions: ['FLOW', 'FOWL', 'WOLF'], target: 'FLOW' },
    { letters: 'KROW', solutions: ['WORK', 'KROW'], target: 'WORK' },
    { letters: 'MITE', solutions: ['TIME', 'ITEM', 'EMIT', 'MITE'], target: 'TIME' },
    { letters: 'POTS', solutions: ['STOP', 'POST', 'TOPS', 'POTS', 'SPOT', 'OPTS'], target: 'STOP' },
    { letters: 'ATRE', solutions: ['TEAR', 'RATE', 'TARE', 'LATE'], target: 'TEAR' }
  ],
  hard: [
    { letters: 'LESTT', solutions: ['SETTLE', 'LETTERS', 'LEST', 'LETS', 'TEST'], target: 'LETTERS' },
    { letters: 'NGAECH', solutions: ['CHANGE', 'CHANCE', 'HANG', 'CAGE', 'EACH'], target: 'CHANGE' },
    { letters: 'POTMCER', solutions: ['COMPUTER', 'COMPETE', 'COME', 'PORT', 'MORE'], target: 'COMPUTER' },
    { letters: 'RINGDEA', solutions: ['READING', 'GARDEN', 'GRADE', 'DEAR', 'GEAR'], target: 'READING' },
    { letters: 'MALISAN', solutions: ['ANIMALS', 'ANIMAL', 'SNAIL', 'NAIL', 'MAIL'], target: 'ANIMALS' }
  ]
};

// Word Jumble Game Component
const WordJumbleGame = () => {
  // Game configuration
  const [config, setConfig] = useState({
    gameMode: '1v1', // '1v1' or '1vN'
    maxPlayers: 4,
    difficulty: 'medium',
    rounds: 5,
    timePerRound: 60, // seconds
    wagerAmount: 50,
    inputMode: INPUT_MODE.DRAG,
    allowPartialWords: true,
    minWordLength: 3
  });

  // Game state
  const [gameStatus, setGameStatus] = useState(GAME_STATUS.SETUP);
  const [currentRound, setCurrentRound] = useState(1);
  const [timeLeft, setTimeLeft] = useState(0);
  const [currentLetters, setCurrentLetters] = useState([]);
  const [availableLetters, setAvailableLetters] = useState([]);
  const [answerBoxes, setAnswerBoxes] = useState([]);
  const [currentWord, setCurrentWord] = useState('');
  const [typedInput, setTypedInput] = useState('');
  const [showSettings, setShowSettings] = useState(false);
  const [currentWordSet, setCurrentWordSet] = useState(null);
  const [draggedLetter, setDraggedLetter] = useState(null);
  const [dragOverIndex, setDragOverIndex] = useState(null);
  const [selectedLetterIndex, setSelectedLetterIndex] = useState(null);

  // Players and scores
  const [players, setPlayers] = useState([
    { id: 1, name: 'You', score: 0, currentWord: '', isReady: false, lastWordPoints: 0 },
    { id: 2, name: 'Player 2', score: 0, currentWord: '', isReady: false, lastWordPoints: 0 },
    { id: 3, name: 'Player 3', score: 0, currentWord: '', isReady: false, lastWordPoints: 0 },
    { id: 4, name: 'Player 4', score: 0, currentWord: '', isReady: false, lastWordPoints: 0 }
  ]);

  // Round tracking
  const [roundHistory, setRoundHistory] = useState([]);
  const [gameResult, setGameResult] = useState(null);
  const [totalGameTime, setTotalGameTime] = useState(0);
  const [roundWinner, setRoundWinner] = useState(null);

  // Refs
  const timerRef = useRef(null);
  const gameTimerRef = useRef(null);

  // Chat state
  const [messages, setMessages] = useState([
    { text: "Welcome to Word Jumble! Unscramble letters to form words and compete for the highest score.", type: "system" }
  ]);
  const [messageInput, setMessageInput] = useState('');
  const messageEndRef = useRef(null);

  // Effects

  // Scroll chat to bottom
  useEffect(() => {
    if (messageEndRef.current) {
      messageEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  // Clean up timers
  useEffect(() => {
    return () => {
      if (timerRef.current) clearInterval(timerRef.current);
      if (gameTimerRef.current) clearInterval(gameTimerRef.current);
    };
  }, []);

  // Update typed input when current word changes
  useEffect(() => {
    if (config.inputMode === INPUT_MODE.TYPE) {
      setTypedInput(currentWord);
    }
  }, [currentWord, config.inputMode]);

  // Get word sets for current difficulty
  const getWordSets = (difficulty) => {
    return WORD_SETS[difficulty] || WORD_SETS.medium;
  };

  // Add message to chat
  const addMessage = (text, type = "player") => {
    setMessages(prev => [...prev, { text, type }]);
  };

  // Send chat message
  const sendMessage = () => {
    if (messageInput.trim()) {
      addMessage(`You: ${messageInput}`);
      setMessageInput('');
    }
  };

  // Shuffle array
  const shuffleArray = (array) => {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  };

  // Start the game
  const startGame = () => {
    // Reset game state
    setCurrentRound(1);
    setRoundHistory([]);
    setGameResult(null);
    setTotalGameTime(0);

    // Reset player scores
    setPlayers(prev => prev.slice(0, config.gameMode === '1v1' ? 2 : config.maxPlayers).map(player => ({
      ...player,
      score: 0,
      currentWord: '',
      isReady: false,
      lastWordPoints: 0
    })));

    // Start game timer
    gameTimerRef.current = setInterval(() => {
      setTotalGameTime(prev => prev + 1);
    }, 1000);

    // Start first round
    startRound(1);

    setGameStatus(GAME_STATUS.PLAYING);
    addMessage(`Game started! ${config.gameMode === '1v1' ? '1v1' : `1v${config.maxPlayers}`} mode, ${config.rounds} rounds.`, "system");
  };

  // Start a new round
  const startRound = (roundNumber) => {
    const wordSets = getWordSets(config.difficulty);
    const wordSet = wordSets[(roundNumber - 1) % wordSets.length];

    setCurrentWordSet(wordSet);

    // Shuffle the letters
    const shuffledLetters = shuffleArray(wordSet.letters.split(''));
    setCurrentLetters(shuffledLetters);
    setAvailableLetters(shuffledLetters.map((letter, index) => ({ letter, id: index, used: false })));

    // Initialize answer boxes
    const maxLength = Math.max(...wordSet.solutions.map(word => word.length));
    setAnswerBoxes(new Array(maxLength).fill(''));
    setCurrentWord('');
    setTypedInput('');

    // Reset player states
    setPlayers(prev => prev.map(player => ({
      ...player,
      currentWord: '',
      isReady: false,
      lastWordPoints: 0
    })));

    // Start round timer
    setTimeLeft(config.timePerRound);
    timerRef.current = setInterval(() => {
      setTimeLeft(prev => {
        if (prev <= 1) {
          clearInterval(timerRef.current);
          endRound();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    addMessage(`Round ${roundNumber} started! Letters: ${wordSet.letters}`, "system");

    // Simulate other players thinking/typing
    simulateOtherPlayers();
  };

  // Simulate other players making words
  const simulateOtherPlayers = () => {
    const activePlayerCount = config.gameMode === '1v1' ? 2 : config.maxPlayers;

    // Simulate other players with random delays
    for (let i = 1; i < activePlayerCount; i++) {
      const delay = Math.random() * (config.timePerRound * 0.8) * 1000; // Random delay up to 80% of round time

      setTimeout(() => {
        if (gameStatus === GAME_STATUS.PLAYING && currentWordSet) {
          const possibleWords = currentWordSet.solutions.filter(word => word.length >= config.minWordLength);
          const randomWord = possibleWords[Math.floor(Math.random() * possibleWords.length)];

          setPlayers(prev => prev.map(player =>
            player.id === i + 1 ? {
              ...player,
              currentWord: randomWord,
              isReady: true,
              lastWordPoints: calculateWordScore(randomWord)
            } : player
          ));

          addMessage(`${players[i]?.name || `Player ${i + 1}`} formed: "${randomWord}"`, "system");

          // Check if this ends the round (if all players are ready)
          const updatedPlayers = players.map(player =>
            player.id === i + 1 ? { ...player, isReady: true } : player
          );

          if (updatedPlayers.slice(0, activePlayerCount).every(player => player.isReady)) {
            setTimeout(() => endRound(), 1000);
          }
        }
      }, delay);
    }
  };

  // Handle drag start
  const handleDragStart = (e, letter, index) => {
    if (config.inputMode !== INPUT_MODE.DRAG) return;

    setDraggedLetter({ letter, index });
    e.dataTransfer.effectAllowed = 'move';
  };

  // Handle drag over
  const handleDragOver = (e, boxIndex) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
    setDragOverIndex(boxIndex);
  };

  // Handle drag leave
  const handleDragLeave = () => {
    setDragOverIndex(null);
  };

  // Handle drop
  const handleDrop = (e, boxIndex) => {
    e.preventDefault();

    if (!draggedLetter) return;

    // Remove letter from available letters
    setAvailableLetters(prev => prev.map(item =>
      item.id === draggedLetter.index ? { ...item, used: true } : item
    ));

    // Add letter to answer box
    const newAnswerBoxes = [...answerBoxes];
    newAnswerBoxes[boxIndex] = draggedLetter.letter;
    setAnswerBoxes(newAnswerBoxes);

    // Update current word
    const newWord = newAnswerBoxes.join('');
    setCurrentWord(newWord);

    setDraggedLetter(null);
    setDragOverIndex(null);
  };

  // Handle letter click (for mobile/touch)
  const handleLetterClick = (letter, index) => {
    if (config.inputMode !== INPUT_MODE.DRAG) return;

    // Find first empty box
    const emptyBoxIndex = answerBoxes.findIndex(box => box === '');
    if (emptyBoxIndex !== -1) {
      // Remove letter from available letters
      setAvailableLetters(prev => prev.map(item =>
        item.id === index ? { ...item, used: true } : item
      ));

      // Add letter to answer box
      const newAnswerBoxes = [...answerBoxes];
      newAnswerBoxes[emptyBoxIndex] = letter;
      setAnswerBoxes(newAnswerBoxes);

      // Update current word
      const newWord = newAnswerBoxes.join('');
      setCurrentWord(newWord);
    }
  };

  // Handle answer box click (to remove letter)
  const handleAnswerBoxClick = (boxIndex) => {
    if (answerBoxes[boxIndex] && config.inputMode === INPUT_MODE.DRAG) {
      const letter = answerBoxes[boxIndex];

      // Find the original letter index and mark as available
      const originalLetterIndex = currentLetters.findIndex((l, i) =>
        l === letter && availableLetters[i]?.used
      );

      if (originalLetterIndex !== -1) {
        setAvailableLetters(prev => prev.map(item =>
          item.id === originalLetterIndex ? { ...item, used: false } : item
        ));
      }

      // Remove letter from answer box
      const newAnswerBoxes = [...answerBoxes];
      newAnswerBoxes[boxIndex] = '';
      setAnswerBoxes(newAnswerBoxes);

      // Update current word
      const newWord = newAnswerBoxes.join('');
      setCurrentWord(newWord);
    }
  };

  // Handle typed input
  const handleTypedInputChange = (e) => {
    if (config.inputMode !== INPUT_MODE.TYPE) return;

    const value = e.target.value.toUpperCase();
    const validLetters = currentLetters.join('');

    // Check if all letters in the input are available
    const letterCount = {};
    for (const letter of validLetters) {
      letterCount[letter] = (letterCount[letter] || 0) + 1;
    }

    let isValid = true;
    const inputLetterCount = {};
    for (const letter of value) {
      inputLetterCount[letter] = (inputLetterCount[letter] || 0) + 1;
      if (inputLetterCount[letter] > (letterCount[letter] || 0)) {
        isValid = false;
        break;
      }
    }

    if (isValid) {
      setTypedInput(value);
      setCurrentWord(value);

      // Update answer boxes to show typed letters
      const newAnswerBoxes = new Array(answerBoxes.length).fill('');
      for (let i = 0; i < value.length && i < newAnswerBoxes.length; i++) {
        newAnswerBoxes[i] = value[i];
      }
      setAnswerBoxes(newAnswerBoxes);
    }
  };

  // Calculate word score
  const calculateWordScore = (word) => {
    if (!currentWordSet || !word) return 0;

    const baseScore = word.length * 10;
    let multiplier = 1;

    // Bonus for valid words
    if (currentWordSet.solutions.includes(word)) {
      multiplier = 1.5;
    }

    // Bonus for target word
    if (word === currentWordSet.target) {
      multiplier = 2;
    }

    // Length bonus
    if (word.length >= 6) multiplier += 0.5;
    if (word.length >= 8) multiplier += 0.5;

    return Math.round(baseScore * multiplier);
  };

  // Submit current word
  const submitWord = () => {
    if (!currentWord || currentWord.length < config.minWordLength) {
      addMessage(`Word must be at least ${config.minWordLength} letters long!`, "system");
      return;
    }

    const score = calculateWordScore(currentWord);

    // Update player score and mark as ready
    setPlayers(prev => prev.map(player =>
      player.id === 1 ? {
        ...player,
        currentWord,
        isReady: true,
        lastWordPoints: score,
        score: player.score + score
      } : player
    ));

    addMessage(`You submitted: "${currentWord}" for ${score} points`, "system");

    // Check if all players are ready
    const activePlayerCount = config.gameMode === '1v1' ? 2 : config.maxPlayers;
    const readyPlayers = players.filter(p => p.id <= activePlayerCount && (p.id === 1 || p.isReady));

    if (readyPlayers.length >= activePlayerCount - 1) { // -1 because we just marked current player as ready
      setTimeout(() => endRound(), 2000);
    }
  };

  // Clear current word
  const clearWord = () => {
    setCurrentWord('');
    setTypedInput('');
    setAnswerBoxes(new Array(answerBoxes.length).fill(''));
    setAvailableLetters(prev => prev.map(item => ({ ...item, used: false })));
  };

  // Shuffle available letters
  const shuffleLetters = () => {
    const availableOnly = availableLetters.filter(item => !item.used);
    const shuffled = shuffleArray(availableOnly.map(item => item.letter));

    let shuffledIndex = 0;
    setAvailableLetters(prev => prev.map(item =>
      item.used ? item : { ...item, letter: shuffled[shuffledIndex++] }
    ));

    // Also update currentLetters for consistency
    const newCurrentLetters = [...currentLetters];
    shuffledIndex = 0;
    for (let i = 0; i < newCurrentLetters.length; i++) {
      if (!availableLetters[i]?.used) {
        newCurrentLetters[i] = shuffled[shuffledIndex++];
      }
    }
    setCurrentLetters(newCurrentLetters);
  };

  // End current round
  const endRound = () => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }

    setGameStatus(GAME_STATUS.ROUND_END);

    // Calculate round results
    const activePlayerCount = config.gameMode === '1v1' ? 2 : config.maxPlayers;
    const roundPlayers = players.slice(0, activePlayerCount);

    // Find round winner
    const winner = roundPlayers.reduce((prev, current) =>
      current.lastWordPoints > prev.lastWordPoints ? current : prev
    );

    setRoundWinner(winner);

    // Add to round history
    setRoundHistory(prev => [...prev, {
      round: currentRound,
      letters: currentLetters.join(''),
      players: roundPlayers.map(p => ({
        name: p.name,
        word: p.currentWord,
        points: p.lastWordPoints
      })),
      winner: winner.name,
      targetWord: currentWordSet?.target
    }]);

    addMessage(`Round ${currentRound} ended! Winner: ${winner.name} with "${winner.currentWord}" (${winner.lastWordPoints} points)`, "system");

    // Check if game is over
    if (currentRound >= config.rounds) {
      setTimeout(() => endGame(), 3000);
    } else {
      setTimeout(() => proceedToNextRound(), 3000);
    }
  };

  // Proceed to next round
  const proceedToNextRound = () => {
    const nextRound = currentRound + 1;
    setCurrentRound(nextRound);
    setRoundWinner(null);
    setGameStatus(GAME_STATUS.PLAYING);
    startRound(nextRound);
  };

  // End the game
  const endGame = () => {
    setGameStatus(GAME_STATUS.GAME_END);

    // Clear timers
    if (timerRef.current) clearInterval(timerRef.current);
    if (gameTimerRef.current) clearInterval(gameTimerRef.current);

    // Calculate final results
    const activePlayerCount = config.gameMode === '1v1' ? 2 : config.maxPlayers;
    const finalPlayers = players.slice(0, activePlayerCount);
    const gameWinner = finalPlayers.reduce((prev, current) =>
      current.score > prev.score ? current : prev
    );

    let result = {
      winner: gameWinner,
      players: finalPlayers,
      totalRounds: config.rounds,
      payout: 0
    };

    // Calculate payout (only for player 1)
    if (gameWinner.id === 1) {
      const winMargin = gameWinner.score - Math.max(...finalPlayers.filter(p => p.id !== 1).map(p => p.score));
      if (winMargin > 100) {
        result.payout = config.wagerAmount * 3;
      } else if (winMargin > 50) {
        result.payout = config.wagerAmount * 2;
      } else {
        result.payout = config.wagerAmount * 1.5;
      }
      addMessage(`Congratulations! You won! Payout: $${result.payout.toFixed(2)}`, "system");
    } else {
      addMessage(`Game over! ${gameWinner.name} wins with ${gameWinner.score} points.`, "system");
    }

    setGameResult(result);
  };

  // Reset the game
  const resetGame = () => {
    // Clear timers
    if (timerRef.current) clearInterval(timerRef.current);
    if (gameTimerRef.current) clearInterval(gameTimerRef.current);

    // Reset state
    setGameStatus(GAME_STATUS.SETUP);
    setCurrentRound(1);
    setRoundHistory([]);
    setGameResult(null);
    setCurrentWordSet(null);
    setCurrentWord('');
    setTypedInput('');
    setPlayers(prev => prev.map(player => ({ ...player, score: 0, currentWord: '', isReady: false, lastWordPoints: 0 })));

    addMessage("Game reset! Configure your settings and start a new word challenge.", "system");
  };

  // Format time display
  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds < 10 ? '0' : ''}${remainingSeconds}`;
  };

  // Get difficulty color
  const getDifficultyColor = (difficulty) => {
    switch (difficulty) {
      case 'easy': return 'text-green-500';
      case 'medium': return 'text-yellow-500';
      case 'hard': return 'text-red-500';
      default: return 'text-slate-400';
    }
  };

  // Render scrambled letters
  const renderScrambledLetters = () => {
    return (
      <div className="mb-4">
        <h3 className="text-sm font-medium text-white mb-2 flex items-center">
          <Shuffle className="h-4 w-4 mr-2" />
          Scrambled Letters
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0 ml-2"
            onClick={shuffleLetters}
            disabled={gameStatus !== GAME_STATUS.PLAYING}
          >
            <Shuffle className="h-3 w-3" />
          </Button>
        </h3>

        <div className="flex flex-wrap gap-2 justify-center">
          {availableLetters.map((item, index) => (
            <div
              key={index}
              className={`
                w-12 h-12 bg-slate-800 border-2 border-slate-600 rounded-sm
                flex items-center justify-center text-xl font-bold text-white
                cursor-pointer transition-all hover:border-slate-500
                ${item.used ? 'opacity-30 cursor-not-allowed' : 'hover:bg-slate-700'}
                ${draggedLetter?.index === index ? 'opacity-50' : ''}
              `}
              draggable={!item.used && config.inputMode === INPUT_MODE.DRAG}
              onDragStart={(e) => handleDragStart(e, item.letter, index)}
              onClick={() => !item.used && handleLetterClick(item.letter, index)}
            >
              {item.letter}
            </div>
          ))}
        </div>
      </div>
    );
  };

  // Render answer boxes
  const renderAnswerBoxes = () => {
    return (
      <div className="mb-4">
        <h3 className="text-sm font-medium text-white mb-2 flex items-center">
          <Target className="h-4 w-4 mr-2" />
          Your Word
          <span className="ml-2 text-xs text-slate-400">
            ({currentWord.length} letters, {calculateWordScore(currentWord)} points)
          </span>
        </h3>

        <div className="flex flex-wrap gap-2 justify-center mb-3">
          {answerBoxes.map((letter, index) => (
            <div
              key={index}
              className={`
                w-12 h-12 bg-slate-900 border-2 border-slate-700 rounded-sm
                flex items-center justify-center text-xl font-bold text-white
                transition-all cursor-pointer
                ${letter ? 'border-purple-500 bg-purple-900/30' : 'border-dashed'}
                ${dragOverIndex === index ? 'border-yellow-500 bg-yellow-900/30' : ''}
                ${letter ? 'hover:border-red-500' : ''}
              `}
              onDragOver={(e) => handleDragOver(e, index)}
              onDragLeave={handleDragLeave}
              onDrop={(e) => handleDrop(e, index)}
              onClick={() => handleAnswerBoxClick(index)}
            >
              {letter}
            </div>
          ))}
        </div>

        {config.inputMode === INPUT_MODE.TYPE && (
          <div className="flex justify-center mb-3">
            <Input
              type="text"
              placeholder="Type your word here..."
              className="h-12 text-center text-xl font-bold bg-slate-800 border-slate-700 max-w-md"
              value={typedInput}
              onChange={handleTypedInputChange}
              disabled={gameStatus !== GAME_STATUS.PLAYING}
            />
          </div>
        )}

        <div className="flex justify-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            className="h-8 text-xs"
            onClick={clearWord}
            disabled={!currentWord || gameStatus !== GAME_STATUS.PLAYING}
          >
            <X className="h-3 w-3 mr-1" />
            Clear
          </Button>

          <Button
            className="h-8 text-xs bg-gradient-to-r from-green-500 to-green-600"
            onClick={submitWord}
            disabled={!currentWord || currentWord.length < config.minWordLength || gameStatus !== GAME_STATUS.PLAYING || players[0]?.isReady}
          >
            <Check className="h-3 w-3 mr-1" />
            Submit Word
          </Button>
        </div>
      </div>
    );
  };

  // Render player scores
  const renderPlayerScores = () => {
    const activePlayerCount = config.gameMode === '1v1' ? 2 : config.maxPlayers;
    const activePlayers = players.slice(0, activePlayerCount);

    return (
      <div className="mb-4">
        <h3 className="text-sm font-medium text-white mb-2">Player Scores</h3>

        <div className="space-y-2">
          {activePlayers.map((player) => (
            <div
              key={player.id}
              className={`flex items-center justify-between p-2 rounded-sm ${
                player.id === 1 ? 'bg-blue-900/30 border border-blue-700' : 'bg-slate-800'
              }`}
            >
              <div className="flex items-center">
                <div className={`h-6 w-6 rounded-full mr-2 flex items-center justify-center text-xs font-bold ${
                  player.id === 1 ? 'bg-blue-500' : 'bg-slate-600'
                }`}>
                  {player.id}
                </div>
                <span className="text-sm text-white">{player.name}</span>
                {player.isReady && gameStatus === GAME_STATUS.PLAYING && (
                  <Badge className="ml-2 h-4 bg-green-500">Ready</Badge>
                )}
              </div>

              <div className="text-right">
                <div className="text-sm font-bold text-white">{player.score} pts</div>
                {player.currentWord && (
                  <div className="text-xs text-slate-400">"{player.currentWord}" (+{player.lastWordPoints})</div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  // Render settings panel
  const renderSettings = () => {
    return (
      <div className="bg-slate-900 border border-slate-800 rounded-sm p-3 absolute top-0 left-0 right-0 z-10">
        <div className="flex justify-between items-center mb-3">
          <h3 className="text-sm font-medium text-white">Game Settings</h3>
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0"
            onClick={() => setShowSettings(false)}
          >
            <X className="h-3 w-3" />
          </Button>
        </div>

        <div className="space-y-3">
          <div>
            <label className="text-xs text-slate-400 block mb-1">Game Mode</label>
            <div className="flex space-x-2">
              {['1v1', '1vN'].map(mode => (
                <Button
                  key={mode}
                  variant={config.gameMode === mode ? "default" : "outline"}
                  size="sm"
                  className="flex-1 h-7 text-xs"
                  onClick={() => setConfig({ ...config, gameMode: mode })}
                >
                  {mode}
                </Button>
              ))}
            </div>
          </div>

          {config.gameMode === '1vN' && (
            <div>
              <label className="text-xs text-slate-400 block mb-1">Max Players</label>
              <div className="flex space-x-2">
                {[3, 4, 6, 8].map(num => (
                  <Button
                    key={num}
                    variant={config.maxPlayers === num ? "default" : "outline"}
                    size="sm"
                    className="flex-1 h-7 text-xs"
                    onClick={() => setConfig({ ...config, maxPlayers: num })}
                  >
                    {num}
                  </Button>
                ))}
              </div>
            </div>
          )}

          <div>
            <label className="text-xs text-slate-400 block mb-1">Difficulty</label>
            <div className="flex space-x-2">
              {['easy', 'medium', 'hard'].map(diff => (
                <Button
                  key={diff}
                  variant={config.difficulty === diff ? "default" : "outline"}
                  size="sm"
                  className="flex-1 h-7 text-xs"
                  onClick={() => setConfig({ ...config, difficulty: diff })}
                >
                  {diff}
                </Button>
              ))}
            </div>
          </div>

          <div className="grid grid-cols-2 gap-3">
            <div>
              <label className="text-xs text-slate-400 block mb-1">Rounds</label>
              <div className="flex space-x-1">
                {[3, 5, 7, 10].map(num => (
                  <Button
                    key={num}
                    variant={config.rounds === num ? "default" : "outline"}
                    size="sm"
                    className="flex-1 h-7 text-xs"
                    onClick={() => setConfig({ ...config, rounds: num })}
                  >
                    {num}
                  </Button>
                ))}
              </div>
            </div>

            <div>
              <label className="text-xs text-slate-400 block mb-1">Time/Round</label>
              <div className="flex space-x-1">
                {[30, 60, 90, 120].map(time => (
                  <Button
                    key={time}
                    variant={config.timePerRound === time ? "default" : "outline"}
                    size="sm"
                    className="flex-1 h-7 text-xs"
                    onClick={() => setConfig({ ...config, timePerRound: time })}
                  >
                    {time}s
                  </Button>
                ))}
              </div>
            </div>
          </div>

          <div>
            <label className="text-xs text-slate-400 block mb-1">Input Mode</label>
            <div className="flex space-x-2">
              <Button
                variant={config.inputMode === INPUT_MODE.DRAG ? "default" : "outline"}
                size="sm"
                className="flex-1 h-7 text-xs"
                onClick={() => setConfig({ ...config, inputMode: INPUT_MODE.DRAG })}
              >
                <MousePointer className="h-3 w-3 mr-1" />
                Drag & Drop
              </Button>
              <Button
                variant={config.inputMode === INPUT_MODE.TYPE ? "default" : "outline"}
                size="sm"
                className="flex-1 h-7 text-xs"
                onClick={() => setConfig({ ...config, inputMode: INPUT_MODE.TYPE })}
              >
                <Keyboard className="h-3 w-3 mr-1" />
                Typing
              </Button>
            </div>
          </div>

          <div>
            <label className="text-xs text-slate-400 block mb-1">Wager Amount</label>
            <div className="flex space-x-2">
              {[25, 50, 100, 200].map(amount => (
                <Button
                  key={amount}
                  variant={config.wagerAmount === amount ? "default" : "outline"}
                  size="sm"
                  className="flex-1 h-7 text-xs"
                  onClick={() => setConfig({ ...config, wagerAmount: amount })}
                >
                  ${amount}
                </Button>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  };

  // Render setup screen
  const renderSetupScreen = () => {
    return (
      <div className="flex flex-col items-center justify-center h-full bg-slate-900 border border-slate-800 rounded-sm p-4">
        <div className="text-6xl mb-4">🔤</div>
        <h1 className="text-2xl font-bold text-white mb-1">Word Jumble</h1>
        <p className="text-sm text-slate-400 mb-6 text-center">
          Unscramble letters to form words and compete against other players!<br />
          The longer the word, the more points you earn.
        </p>

        <div className="w-full max-w-md mb-6">
          <div className="bg-slate-800 p-4 rounded-sm mb-4">
            <h3 className="text-sm font-medium text-white mb-3">Quick Setup</h3>

            <div className="space-y-3">
              <div>
                <label className="text-xs text-slate-400 block mb-1">Game Mode</label>
                <div className="flex space-x-2">
                  {['1v1', '1vN'].map(mode => (
                    <Button
                      key={mode}
                      variant={config.gameMode === mode ? "default" : "outline"}
                      size="sm"
                      className="flex-1 h-8 text-xs"
                      onClick={() => setConfig({ ...config, gameMode: mode })}
                    >
                      {mode === '1v1' ? '1 vs 1' : '1 vs Many'}
                    </Button>
                  ))}
                </div>
              </div>

              <div className="grid grid-cols-2 gap-3">
                <div>
                  <label className="text-xs text-slate-400 block mb-1">Difficulty</label>
                  <div className="flex space-x-1">
                    {['easy', 'medium', 'hard'].map(diff => (
                      <Button
                        key={diff}
                        variant={config.difficulty === diff ? "default" : "outline"}
                        size="sm"
                        className="flex-1 h-7 text-xs"
                        onClick={() => setConfig({ ...config, difficulty: diff })}
                      >
                        {diff}
                      </Button>
                    ))}
                  </div>
                </div>

                <div>
                  <label className="text-xs text-slate-400 block mb-1">Rounds</label>
                  <div className="flex space-x-1">
                    {[3, 5, 7].map(num => (
                      <Button
                        key={num}
                        variant={config.rounds === num ? "default" : "outline"}
                        size="sm"
                        className="flex-1 h-7 text-xs"
                        onClick={() => setConfig({ ...config, rounds: num })}
                      >
                        {num}
                      </Button>
                    ))}
                  </div>
                </div>
              </div>

              <div>
                <label className="text-xs text-slate-400 block mb-1">Input Method</label>
                <div className="flex space-x-2">
                  <Button
                    variant={config.inputMode === INPUT_MODE.DRAG ? "default" : "outline"}
                    size="sm"
                    className="flex-1 h-8 text-xs"
                    onClick={() => setConfig({ ...config, inputMode: INPUT_MODE.DRAG })}
                  >
                    <Hand className="h-3 w-3 mr-1" />
                    Drag & Drop
                  </Button>
                  <Button
                    variant={config.inputMode === INPUT_MODE.TYPE ? "default" : "outline"}
                    size="sm"
                    className="flex-1 h-8 text-xs"
                    onClick={() => setConfig({ ...config, inputMode: INPUT_MODE.TYPE })}
                  >
                    <Type className="h-3 w-3 mr-1" />
                    Typing
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="flex space-x-3">
          <Button
            variant="outline"
            className="w-32 h-10 text-sm rounded-sm"
            onClick={() => setShowSettings(true)}
          >
            <Settings className="h-4 w-4 mr-2" />
            Advanced
          </Button>

          <Button
            className="w-32 h-10 text-sm rounded-sm bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600"
            onClick={startGame}
          >
            <Zap className="h-4 w-4 mr-2" />
            Start Game
          </Button>
        </div>
      </div>
    );
  };

  // Render game info
  const renderGameInfo = () => {
    return (
      <div className="bg-slate-900 border border-slate-800 rounded-sm p-2">
        <div className="flex justify-between items-center mb-2">
          <h3 className="text-sm font-medium text-white">
            {gameStatus === GAME_STATUS.SETUP ? "Game Setup" :
             gameStatus === GAME_STATUS.GAME_END ? "Game Complete" :
             `Round ${currentRound}/${config.rounds}`}
          </h3>
          <Badge className={`
            ${gameStatus === GAME_STATUS.SETUP ? 'bg-yellow-500' :
              gameStatus === GAME_STATUS.GAME_END ? 'bg-blue-500' :
              'bg-green-500'}
          `}>
            {gameStatus === GAME_STATUS.SETUP ? 'SETUP' :
             gameStatus === GAME_STATUS.GAME_END ? 'FINISHED' :
             'LIVE'}
          </Badge>
        </div>

        <div className="grid grid-cols-2 gap-2">
          <div className="bg-slate-800 p-2 rounded-sm">
            <div className="text-xs text-slate-400">Mode</div>
            <div className="text-sm font-bold text-white">{config.gameMode}</div>
          </div>

          <div className="bg-slate-800 p-2 rounded-sm">
            <div className="text-xs text-slate-400">Wager</div>
            <div className="text-sm font-bold text-white">${config.wagerAmount}</div>
          </div>
        </div>

        {gameStatus === GAME_STATUS.PLAYING && (
          <div className="mt-2">
            <div className="flex justify-between items-center mb-1">
              <span className="text-xs text-slate-400">Time Left</span>
              <span className="text-xs font-bold text-white">{timeLeft}s</span>
            </div>
            <Progress value={(timeLeft / config.timePerRound) * 100} className="h-2" />
          </div>
        )}
      </div>
    );
  };

  // Render round history
  const renderRoundHistory = () => {
    if (roundHistory.length === 0) {
      return <div className="text-xs text-slate-400 p-2">No rounds played yet</div>;
    }

    return (
      <div className="text-xs p-2 overflow-auto max-h-full">
        {roundHistory.map((round, index) => (
          <div key={index} className="mb-2 p-2 bg-slate-800 rounded-sm">
            <div className="flex justify-between items-center mb-1">
              <span className="font-medium text-white">Round {round.round}</span>
              <span className="text-green-500">Winner: {round.winner}</span>
            </div>

            <div className="text-slate-300 mb-1 text-[10px]">
              Letters: {round.letters}
            </div>

            <div className="space-y-1">
              {round.players.map((player, pIndex) => (
                <div key={pIndex} className="flex justify-between text-[10px]">
                  <span className="text-slate-400">{player.name}:</span>
                  <span className="text-white">"{player.word}" ({player.points}pts)</span>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
    );
  };

  // Main game layout
  return (
    <div className="grid grid-cols-12 gap-2 p-2 h-full">
      {/* Game Information - Left column */}
      <div className="col-span-3 flex flex-col gap-2 h-full">
        {renderGameInfo()}

        {/* Player Scores */}
        {gameStatus !== GAME_STATUS.SETUP && (
          <div className="bg-slate-900 border border-slate-800 rounded-sm p-2">
            {renderPlayerScores()}
          </div>
        )}

        {/* Round History */}
        <div className="bg-slate-900 border border-slate-800 rounded-sm flex-1 flex flex-col overflow-hidden">
          <div className="p-2 border-b border-slate-800 flex justify-between items-center">
            <h3 className="text-sm font-medium text-white">Round History</h3>
            <span className="text-xs text-slate-400">{roundHistory.length} rounds</span>
          </div>

          <div className="flex-1 overflow-auto">
            {renderRoundHistory()}
          </div>
        </div>

        {/* Game Result */}
        {gameStatus === GAME_STATUS.GAME_END && gameResult && (
          <div className="bg-slate-900 border border-slate-800 rounded-sm p-2">
            <h3 className="text-sm font-medium text-white mb-2">Final Results</h3>

            <div className="text-center mb-2">
              <div className="text-2xl mb-1">
                {gameResult.winner.id === 1 ? '🏆' : '🥈'}
              </div>
              <div className={`text-lg font-bold ${gameResult.winner.id === 1 ? 'text-green-500' : 'text-slate-300'}`}>
                {gameResult.winner.name} Wins!
              </div>
              <div className="text-sm text-slate-400">
                {gameResult.winner.score} points
              </div>
            </div>

            {gameResult.payout > 0 && (
              <div className="bg-slate-800 p-2 rounded-sm mb-2">
                <div className="text-center">
                  <div className="text-xs text-slate-400">Your Payout</div>
                  <div className="text-lg font-bold text-green-500">
                    ${gameResult.payout.toFixed(2)}
                  </div>
                </div>
              </div>
            )}

            <Button
              onClick={resetGame}
              className="w-full h-7 text-xs rounded-sm bg-gradient-to-r from-purple-500 to-pink-500"
            >
              <RotateCcw className="h-3 w-3 mr-1" />
              Play Again
            </Button>
          </div>
        )}
      </div>

      {/* Game Board - Middle column */}
      <div className="col-span-6 flex flex-col h-full">
        {/* Game board */}
        <div className="flex-1 bg-slate-900 border border-slate-800 rounded-sm p-3 flex flex-col relative">
          {gameStatus === GAME_STATUS.SETUP ? (
            renderSetupScreen()
          ) : (
            <div className="flex-1 flex flex-col justify-center">
              {/* Current round info */}
              {currentWordSet && (
                <div className="text-center mb-6">
                  <h2 className="text-lg font-bold text-white mb-1">
                    Round {currentRound} of {config.rounds}
                  </h2>
                  <div className="flex items-center justify-center space-x-4">
                    <Badge className={getDifficultyColor(config.difficulty)}>
                      {config.difficulty}
                    </Badge>
                    <Badge className="bg-purple-500">
                      Target: {currentWordSet.target}
                    </Badge>
                    {gameStatus === GAME_STATUS.PLAYING && (
                      <Badge className="bg-red-500 animate-pulse">
                        <Timer className="h-3 w-3 mr-1" />
                        {timeLeft}s
                      </Badge>
                    )}
                  </div>
                </div>
              )}

              {/* Round end message */}
              {gameStatus === GAME_STATUS.ROUND_END && roundWinner && (
                <div className="text-center mb-6 p-4 bg-slate-800 rounded-sm">
                  <h3 className="text-lg font-bold text-white mb-2">
                    Round {currentRound} Complete!
                  </h3>
                  <div className="text-green-500 font-bold">
                    🏆 {roundWinner.name} wins with "{roundWinner.currentWord}"
                  </div>
                  <div className="text-sm text-slate-400 mt-1">
                    {roundWinner.lastWordPoints} points earned
                  </div>
                </div>
              )}

              {/* Scrambled letters */}
              {gameStatus === GAME_STATUS.PLAYING && renderScrambledLetters()}

              {/* Answer boxes */}
              {gameStatus === GAME_STATUS.PLAYING && renderAnswerBoxes()}

              {/* Possible words hint */}
              {gameStatus === GAME_STATUS.PLAYING && currentWordSet && (
                <div className="text-center">
                  <div className="text-xs text-slate-400">
                    Possible words: {currentWordSet.solutions.join(', ')}
                  </div>
                  <div className="text-xs text-slate-500 mt-1">
                    Points: 3-letter words = 30pts, 4-letter = 40pts, target word = 2x bonus
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Settings overlay */}
          {showSettings && renderSettings()}
        </div>

        {/* Game controls */}
        <div className="flex justify-between items-center mt-2">
          <Button
            variant="outline"
            size="sm"
            className="h-8 text-xs"
            onClick={resetGame}
          >
            <RotateCcw className="h-3 w-3 mr-1" />
            Reset Game
          </Button>

          <div className="flex items-center space-x-2">
            <Badge className={`${config.inputMode === INPUT_MODE.DRAG ? 'bg-blue-500' : 'bg-slate-600'}`}>
              {config.inputMode === INPUT_MODE.DRAG ? 'Drag Mode' : 'Type Mode'}
            </Badge>
          </div>

          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              className={`h-8 text-xs ${showSettings ? 'bg-slate-800' : ''}`}
              onClick={() => setShowSettings(!showSettings)}
            >
              <Settings className="h-3 w-3 mr-1" />
              Settings
            </Button>

            <Button
              variant={gameStatus === GAME_STATUS.SETUP ? 'default' : 'outline'}
              size="sm"
              className="h-8 text-xs"
              disabled={gameStatus !== GAME_STATUS.SETUP}
              onClick={startGame}
            >
              {gameStatus === GAME_STATUS.SETUP ? (
                <>
                  <Zap className="h-3 w-3 mr-1" />
                  Start Game
                </>
              ) : (
                <>
                  <Trophy className="h-3 w-3 mr-1" />
                  Game Active
                </>
              )}
            </Button>
          </div>
        </div>
      </div>

      {/* Chat and Stats - Right column */}
      <div className="col-span-3 flex flex-col gap-2 h-full">
        {/* Live Stats */}
        <div className="bg-slate-900 border border-slate-800 rounded-sm p-2">
          <div className="flex justify-between items-center mb-2">
            <h3 className="text-sm font-medium text-white">Live Stats</h3>
            <Badge className="bg-green-500">
              <Users className="h-3 w-3 mr-1" />
              {config.gameMode === '1v1' ? 2 : config.maxPlayers}
            </Badge>
          </div>

          <div className="space-y-2">
            <div className="flex justify-between text-xs">
              <span className="text-slate-400">Game Time</span>
              <span className="text-white">{formatTime(totalGameTime)}</span>
            </div>

            <div className="flex justify-between text-xs">
              <span className="text-slate-400">Current Leader</span>
              <span className="text-white">
                {players.reduce((prev, current) =>
                  current.score > prev.score ? current : prev
                ).name}
              </span>
            </div>

            {gameStatus === GAME_STATUS.PLAYING && (
              <div>
                <div className="flex justify-between text-xs mb-1">
                  <span className="text-slate-400">Round Progress</span>
                  <span className="text-white">
                    {players.filter(p => p.isReady).length}/{config.gameMode === '1v1' ? 2 : config.maxPlayers} ready
                  </span>
                </div>
                <Progress
                  value={(players.filter(p => p.isReady).length / (config.gameMode === '1v1' ? 2 : config.maxPlayers)) * 100}
                  className="h-1.5"
                />
              </div>
            )}
          </div>
        </div>

        {/* Word Building Tips */}
        <div className="bg-slate-900 border border-slate-800 rounded-sm p-2">
          <div className="flex justify-between items-center mb-2">
            <h3 className="text-sm font-medium text-white">Tips</h3>
            <Badge className="bg-blue-500">
              <Star className="h-3 w-3 mr-1" />
              Pro
            </Badge>
          </div>

          <div className="space-y-2 text-xs text-slate-300">
            <div className="flex items-center">
              <Crown className="h-3 w-3 text-yellow-500 mr-1" />
              <span>Target word gives 2x points</span>
            </div>
            <div className="flex items-center">
              <Flame className="h-3 w-3 text-orange-500 mr-1" />
              <span>Longer words = more points</span>
            </div>
            <div className="flex items-center">
              <Shuffle className="h-3 w-3 text-purple-500 mr-1" />
              <span>Use shuffle to see new patterns</span>
            </div>
            <div className="flex items-center">
              <Timer className="h-3 w-3 text-red-500 mr-1" />
              <span>Submit early to secure points</span>
            </div>
          </div>
        </div>

        {/* Chat */}
        <div className="flex-1 bg-slate-900 border border-slate-800 rounded-sm flex flex-col overflow-hidden">
          <div className="p-2 border-b border-slate-800 flex justify-between items-center">
            <div className="flex items-center">
              <MessageSquare className="h-4 w-4 text-slate-400 mr-1" />
              <h3 className="text-sm font-medium text-white">Game Chat</h3>
            </div>
            <div className="flex items-center">
              <Badge className="h-5 bg-green-500 mr-1 text-[10px]">
                <Users className="h-3 w-3 mr-1" />
                {config.gameMode === '1v1' ? 2 : config.maxPlayers}
              </Badge>
              <Button
                variant="ghost"
                size="sm"
                className="h-5 w-5 p-0"
              >
                <ChevronDown className="h-3 w-3 text-slate-400" />
              </Button>
            </div>
          </div>

          {/* Chat messages - scrollable */}
          <div className="flex-1 overflow-auto p-2 space-y-1">
            {messages.map((msg, idx) => (
              <div
                key={idx}
                className={`text-xs p-1 rounded-sm ${
                  msg.type === 'system' ? 'text-slate-400 italic' : 'text-white'
                }`}
              >
                {msg.text}
              </div>
            ))}
            <div ref={messageEndRef} />
          </div>

          {/* Chat input */}
          <div className="p-2 border-t border-slate-800">
            <div className="flex space-x-2">
              <Input
                placeholder="Type a message..."
                className="h-8 text-xs bg-slate-800 border-slate-700"
                value={messageInput}
                onChange={(e) => setMessageInput(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    sendMessage();
                  }
                }}
              />
              <Button
                onClick={sendMessage}
                className="h-8 px-3 bg-gradient-to-r from-purple-500 to-pink-500"
              >
                Send
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WordJumbleGame;