Core Game Features
Complete Deck Management

Multiple Decks: Support for 1-3 decks (52 cards each)
Proper Shuffling: Fisher-Yates shuffle algorithm for randomness
Card Images: Expects card images in /public/cards/ directory with format {value}_of_{suit}.png
Automatic Reshuffling: Deck reshuffles from discard pile when empty

Flexible Player Configuration

2-6 Players: Scalable player count with AI opponents
Cards per Player: Choose 5, 7, or 9 starting cards
Turn-based Gameplay: Clear turn indicators and timers

Authentic Crazy Eights Rules

Basic Rules: Match suit or rank to play cards
Eights are Wild: Can be played anytime, player chooses new suit
Draw Mechanism: Must draw if no playable cards available

🎮 Special Rules & Variations
Optional Special Cards (Configurable)

Twos = Draw Two: Next player draws 2 cards and skips turn
Queens = Skip Turn: Next player loses their turn
Jacks = Reverse: Changes direction of play (clockwise ↔ counterclockwise)

Advanced Features

Turn Timer: 15-60 second time limits per turn
Auto-Draw on Timeout: Players automatically draw if time expires
Direction Indicators: Visual arrow showing clockwise/counterclockwise play
Penalty Tracking: Shows active draw penalties

🤖 AI Player Behavior
Smart AI Strategy

Card Selection: Prefers non-8s, saves 8s for strategic moments
Suit Selection: Chooses suits based on most cards in hand
Realistic Timing: Random delays (1-3 seconds) for human-like play
Auto-Draw: AI draws cards when no valid plays available

💰 Scoring & Wagering
Traditional Scoring System

Number Cards: Face value (1-10)
Face Cards: J, Q, K = 10 points each
Aces: 1 point each
Eights: 50 points each (penalty for holding)

Game End Conditions

Round Winner: First to empty hand wins round
Game Winner: Lowest total score when someone reaches 100+ points
Payout System: Winner takes pot based on number of players

🎨 Visual Design
Card Display System

Human Player: Full card faces visible with hover effects
AI Players: Card backs with card count indicators
Interactive Cards: Green border on playable cards, click to play
Fallback Graphics: Text representation if images fail to load

Game Board Elements

Discard Pile: Shows top card with suit/rank
Deck Counter: Shows remaining cards in deck
Current Suit Indicator: Visual display of active suit
Direction Arrow: Shows turn order direction

📁 Expected Card Images
Place card images in /public/cards/ with these filenames:
a_of_hearts.png, 2_of_hearts.png, ..., k_of_hearts.png
a_of_diamonds.png, 2_of_diamonds.png, ..., k_of_diamonds.png
a_of_clubs.png, 2_of_clubs.png, ..., k_of_clubs.png
a_of_spades.png, 2_of_spades.png, ..., k_of_spades.png
back.png (for card backs)
🔧 Configuration Options
Game Setup

Deck Count: 1-3 decks for longer games
Player Count: 2-6 players (human + AI)
Starting Cards: 5, 7, or 9 cards per player
Turn Timer: 15-60 seconds per turn
Wager Amount: $10-$100 per game

Rule Variations

Basic Mode: Standard Crazy Eights rules only
Special Rules: Enable/disable individual special card effects
Penalty Cards: Toggle draw-two, skip, and reverse effects

🎯 Gameplay Flow
Game Sequence

Setup: Configure players, decks, and rules
Deal: Cards distributed automatically
Play: Players take turns matching suit/rank or playing 8s
Draw: Players draw cards when no valid plays
Score: Round ends when someone empties hand
Win: Game ends when player reaches score threshold

Turn Actions

Play Card: Click playable cards (green border)
Draw Card: Click "Draw Card" button when stuck
Choose Suit: Select new suit after playing an 8
Auto-Skip: Timer handles turn progression

🔄 Integration Features

Chat System: Real-time game commentary and player interaction
Progress Tracking: Round history and scoring display
Responsive Design: Works on desktop and mobile devices
Error Handling: Graceful fallbacks for missing card images

The game provides an authentic Crazy Eights experience with modern UI/UX, flexible configuration, and intelligent AI opponents. Players can enjoy classic card game mechanics with optional rule variations and competitive wagering.


import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Input } from '@/components/ui/input';
import {
  Trophy,
  Clock,
  MessageSquare,
  Users,
  Settings,
  Star,
  Target,
  Zap,
  RotateCcw,
  Check,
  X,
  AlertCircle,
  DollarSign,
  ChevronDown,
  Shuffle,
  Plus,
  Minus,
  Play,
  Pause,
  Crown,
  Award,
  Hand,
  Eye,
  EyeOff,
  RefreshCw,
  Spade,
  Heart,
  Diamond,
  Club
} from 'lucide-react';

// Card suits and values
const SUITS = {
  HEARTS: 'hearts',
  DIAMONDS: 'diamonds',
  CLUBS: 'clubs',
  SPADES: 'spades'
};

const VALUES = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K'];

// Game status
const GAME_STATUS = {
  SETUP: 'setup',
  DEALING: 'dealing',
  PLAYING: 'playing',
  CHOOSING_SUIT: 'choosing_suit',
  ROUND_END: 'round_end',
  GAME_END: 'game_end'
};

// Card directions
const DIRECTIONS = {
  CLOCKWISE: 'clockwise',
  COUNTERCLOCKWISE: 'counterclockwise'
};

// Crazy Eights Game Component
const CrazyEightsGame = () => {
  // Game configuration
  const [config, setConfig] = useState({
    numDecks: 1,
    numPlayers: 4,
    cardsPerPlayer: 7,
    wagerAmount: 25,
    enableSpecialRules: true,
    drawTwoPenalty: true,
    skipTurnOnQueen: true,
    reverseOnJack: true,
    timeLimit: 30 // seconds per turn
  });

  // Game state
  const [gameStatus, setGameStatus] = useState(GAME_STATUS.SETUP);
  const [deck, setDeck] = useState([]);
  const [discardPile, setDiscardPile] = useState([]);
  const [currentPlayer, setCurrentPlayer] = useState(0);
  const [direction, setDirection] = useState(DIRECTIONS.CLOCKWISE);
  const [currentSuit, setCurrentSuit] = useState(null);
  const [mustDraw, setMustDraw] = useState(0); // For draw-two penalty
  const [showSettings, setShowSettings] = useState(false);
  const [timeLeft, setTimeLeft] = useState(0);
  const [gameHistory, setGameHistory] = useState([]);
  const [gameResult, setGameResult] = useState(null);
  const [suitSelection, setSuitSelection] = useState(false);
  const [selectedCard, setSelectedCard] = useState(null);

  // Players
  const [players, setPlayers] = useState([
    { id: 1, name: 'You', hand: [], isAI: false, score: 0, isWinner: false },
    { id: 2, name: 'Player 2', hand: [], isAI: true, score: 0, isWinner: false },
    { id: 3, name: 'Player 3', hand: [], isAI: true, score: 0, isWinner: false },
    { id: 4, name: 'Player 4', hand: [], isAI: true, score: 0, isWinner: false }
  ]);

  // Refs
  const timerRef = useRef(null);
  const aiTimeoutRef = useRef(null);

  // Chat state
  const [messages, setMessages] = useState([
    { text: "Welcome to Crazy Eights! Match the suit or rank, and play your 8s wisely!", type: "system" }
  ]);
  const [messageInput, setMessageInput] = useState('');
  const messageEndRef = useRef(null);

  // Effects

  // Scroll chat to bottom
  useEffect(() => {
    if (messageEndRef.current) {
      messageEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  // Clean up timers
  useEffect(() => {
    return () => {
      if (timerRef.current) clearInterval(timerRef.current);
      if (aiTimeoutRef.current) clearTimeout(aiTimeoutRef.current);
    };
  }, []);

  // Create a standard deck of cards
  const createDeck = useCallback(() => {
    const newDeck = [];

    for (let deckNum = 0; deckNum < config.numDecks; deckNum++) {
      Object.values(SUITS).forEach(suit => {
        VALUES.forEach(value => {
          newDeck.push({
            id: `${suit}_${value}_${deckNum}`,
            suit,
            value,
            deckNumber: deckNum
          });
        });
      });
    }

    return newDeck;
  }, [config.numDecks]);

  // Shuffle deck
  const shuffleDeck = useCallback((deckToShuffle) => {
    const shuffled = [...deckToShuffle];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  }, []);

  // Add message to chat
  const addMessage = (text, type = "player") => {
    setMessages(prev => [...prev, { text, type }]);
  };

  // Send chat message
  const sendMessage = () => {
    if (messageInput.trim()) {
      addMessage(`You: ${messageInput}`);
      setMessageInput('');
    }
  };

  // Get card image path
  const getCardImagePath = (card) => {
    if (!card) return '/cards/back.png';

    // Convert suit names to match image file names
    const suitMap = {
      hearts: 'hearts',
      diamonds: 'diamonds',
      clubs: 'clubs',
      spades: 'spades'
    };

    return `/cards/${card.value.toLowerCase()}_of_${suitMap[card.suit]}.png`;
  };

  // Get suit icon
  const getSuitIcon = (suit) => {
    switch (suit) {
      case SUITS.HEARTS:
        return <Heart className="h-4 w-4 text-red-500 fill-current" />;
      case SUITS.DIAMONDS:
        return <Diamond className="h-4 w-4 text-red-500 fill-current" />;
      case SUITS.CLUBS:
        return <Club className="h-4 w-4 text-black fill-current" />;
      case SUITS.SPADES:
        return <Spade className="h-4 w-4 text-black fill-current" />;
      default:
        return null;
    }
  };

  // Start the game
  const startGame = () => {
    // Create and shuffle deck
    const newDeck = shuffleDeck(createDeck());

    // Reset players
    const activePlayers = players.slice(0, config.numPlayers).map((player, index) => ({
      ...player,
      hand: [],
      score: 0,
      isWinner: false
    }));

    setPlayers(activePlayers);
    setDeck(newDeck);
    setDiscardPile([]);
    setCurrentPlayer(0);
    setDirection(DIRECTIONS.CLOCKWISE);
    setCurrentSuit(null);
    setMustDraw(0);
    setGameHistory([]);
    setGameResult(null);
    setSuitSelection(false);
    setSelectedCard(null);

    // Deal cards
    dealCards(newDeck, activePlayers);

    setGameStatus(GAME_STATUS.DEALING);
    addMessage("Game started! Cards are being dealt...", "system");
  };

  // Deal cards to players
  const dealCards = (deckToDeal, playersToServe) => {
    let currentDeck = [...deckToDeal];
    const updatedPlayers = [...playersToServe];

    // Deal cards to each player
    for (let round = 0; round < config.cardsPerPlayer; round++) {
      for (let playerIndex = 0; playerIndex < playersToServe.length; playerIndex++) {
        if (currentDeck.length > 0) {
          const card = currentDeck.pop();
          updatedPlayers[playerIndex].hand.push(card);
        }
      }
    }

    // Place first card on discard pile
    if (currentDeck.length > 0) {
      const firstCard = currentDeck.pop();
      setDiscardPile([firstCard]);
      setCurrentSuit(firstCard.suit);

      // If first card is an 8, let first player choose suit
      if (firstCard.value === '8') {
        setSuitSelection(true);
        setGameStatus(GAME_STATUS.CHOOSING_SUIT);
        addMessage("First card is an 8! Player 1, choose a suit.", "system");
      } else {
        setGameStatus(GAME_STATUS.PLAYING);
        startPlayerTurn();
      }
    }

    setDeck(currentDeck);
    setPlayers(updatedPlayers);

    addMessage(`Cards dealt! Each player has ${config.cardsPerPlayer} cards.`, "system");
  };

  // Start a player's turn
  const startPlayerTurn = () => {
    setTimeLeft(config.timeLimit);

    if (timerRef.current) {
      clearInterval(timerRef.current);
    }

    const activePlayer = players[currentPlayer];
    addMessage(`${activePlayer.name}'s turn!`, "system");

    // Handle draw penalty
    if (mustDraw > 0) {
      drawCards(currentPlayer, mustDraw);
      setMustDraw(0);
      addMessage(`${activePlayer.name} drew ${mustDraw} cards due to Draw Two penalty.`, "system");
      setTimeout(() => nextPlayer(), 1500);
      return;
    }

    // Start timer
    timerRef.current = setInterval(() => {
      setTimeLeft(prev => {
        if (prev <= 1) {
          clearInterval(timerRef.current);
          handleTurnTimeout();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    // Handle AI turn
    if (activePlayer.isAI) {
      aiTimeoutRef.current = setTimeout(() => {
        playAITurn();
      }, 1000 + Math.random() * 2000); // Random delay for realism
    }
  };

  // Handle turn timeout
  const handleTurnTimeout = () => {
    const activePlayer = players[currentPlayer];
    addMessage(`${activePlayer.name} timed out and drew a card.`, "system");
    drawCards(currentPlayer, 1);
    nextPlayer();
  };

  // Draw cards for a player
  const drawCards = (playerIndex, numCards) => {
    setPlayers(prev => {
      const updated = [...prev];
      let currentDeck = [...deck];

      for (let i = 0; i < numCards; i++) {
        if (currentDeck.length === 0) {
          // Reshuffle discard pile if deck is empty
          if (discardPile.length > 1) {
            const topCard = discardPile[discardPile.length - 1];
            const cardsToShuffle = discardPile.slice(0, -1);
            currentDeck = shuffleDeck(cardsToShuffle);
            setDiscardPile([topCard]);
            addMessage("Deck empty! Reshuffling discard pile...", "system");
          } else {
            break; // No more cards available
          }
        }

        if (currentDeck.length > 0) {
          const card = currentDeck.pop();
          updated[playerIndex].hand.push(card);
        }
      }

      setDeck(currentDeck);
      return updated;
    });
  };

  // Check if card can be played
  const canPlayCard = (card) => {
    const topCard = discardPile[discardPile.length - 1];
    if (!topCard) return false;

    // 8s can always be played
    if (card.value === '8') return true;

    // Match suit or value
    return card.suit === currentSuit || card.value === topCard.value;
  };

  // Play a card
  const playCard = (playerIndex, cardIndex) => {
    if (gameStatus !== GAME_STATUS.PLAYING) return;
    if (playerIndex !== currentPlayer) return;

    const card = players[playerIndex].hand[cardIndex];
    if (!canPlayCard(card)) {
      addMessage("That card cannot be played!", "system");
      return;
    }

    // Clear timer
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }

    // Remove card from player's hand
    setPlayers(prev => {
      const updated = [...prev];
      updated[playerIndex].hand.splice(cardIndex, 1);
      return updated;
    });

    // Add card to discard pile
    setDiscardPile(prev => [...prev, card]);

    // Handle special cards
    handleSpecialCard(card);

    addMessage(`${players[playerIndex].name} played ${card.value} of ${card.suit}`, "system");

    // Check for win condition
    if (players[playerIndex].hand.length === 0) {
      endRound(playerIndex);
      return;
    }

    // Check for "Crazy Eights" call (1 card left)
    if (players[playerIndex].hand.length === 1) {
      addMessage(`${players[playerIndex].name} has one card left!`, "system");
    }

    // If played an 8, choose suit
    if (card.value === '8') {
      if (playerIndex === 0) {
        setSuitSelection(true);
        setGameStatus(GAME_STATUS.CHOOSING_SUIT);
      } else {
        // AI chooses suit
        const aiChosenSuit = chooseAISuit(playerIndex);
        setCurrentSuit(aiChosenSuit);
        addMessage(`${players[playerIndex].name} changed the suit to ${aiChosenSuit}`, "system");
        nextPlayer();
      }
    } else {
      setCurrentSuit(card.suit);
      nextPlayer();
    }
  };

  // Handle special card effects
  const handleSpecialCard = (card) => {
    if (!config.enableSpecialRules) return;

    switch (card.value) {
      case '2':
        if (config.drawTwoPenalty) {
          setMustDraw(2);
          addMessage("Next player must draw 2 cards!", "system");
        }
        break;
      case 'Q':
        if (config.skipTurnOnQueen) {
          // Skip next player
          const nextPlayerIndex = getNextPlayerIndex();
          addMessage(`${players[nextPlayerIndex].name}'s turn is skipped!`, "system");
          setCurrentPlayer(getPlayerAfterSkip());
          return; // Don't call nextPlayer() as we've already moved
        }
        break;
      case 'J':
        if (config.reverseOnJack) {
          setDirection(prev =>
            prev === DIRECTIONS.CLOCKWISE ? DIRECTIONS.COUNTERCLOCKWISE : DIRECTIONS.CLOCKWISE
          );
          addMessage("Direction reversed!", "system");
        }
        break;
    }
  };

  // Get next player index
  const getNextPlayerIndex = () => {
    const numPlayers = players.length;
    if (direction === DIRECTIONS.CLOCKWISE) {
      return (currentPlayer + 1) % numPlayers;
    } else {
      return (currentPlayer - 1 + numPlayers) % numPlayers;
    }
  };

  // Get player index after skip
  const getPlayerAfterSkip = () => {
    const numPlayers = players.length;
    if (direction === DIRECTIONS.CLOCKWISE) {
      return (currentPlayer + 2) % numPlayers;
    } else {
      return (currentPlayer - 2 + numPlayers) % numPlayers;
    }
  };

  // Move to next player
  const nextPlayer = () => {
    setCurrentPlayer(getNextPlayerIndex());
    setTimeout(() => {
      startPlayerTurn();
    }, 500);
  };

  // Choose suit (for 8s)
  const chooseSuit = (suit) => {
    setCurrentSuit(suit);
    setSuitSelection(false);
    setGameStatus(GAME_STATUS.PLAYING);
    addMessage(`Suit changed to ${suit}`, "system");
    nextPlayer();
  };

  // AI chooses suit
  const chooseAISuit = (playerIndex) => {
    const hand = players[playerIndex].hand;
    const suitCounts = {
      [SUITS.HEARTS]: 0,
      [SUITS.DIAMONDS]: 0,
      [SUITS.CLUBS]: 0,
      [SUITS.SPADES]: 0
    };

    // Count cards by suit
    hand.forEach(card => {
      if (card.value !== '8') {
        suitCounts[card.suit]++;
      }
    });

    // Choose suit with most cards
    return Object.keys(suitCounts).reduce((a, b) =>
      suitCounts[a] > suitCounts[b] ? a : b
    );
  };

  // AI plays a turn
  const playAITurn = () => {
    const aiPlayer = players[currentPlayer];
    const playableCards = aiPlayer.hand
      .map((card, index) => ({ card, index }))
      .filter(({ card }) => canPlayCard(card));

    if (playableCards.length > 0) {
      // AI strategy: prefer non-8s, then 8s as last resort
      const non8s = playableCards.filter(({ card }) => card.value !== '8');
      const cardToPlay = non8s.length > 0 ? non8s[0] : playableCards[0];

      playCard(currentPlayer, cardToPlay.index);
    } else {
      // Draw a card
      drawCards(currentPlayer, 1);
      addMessage(`${aiPlayer.name} drew a card.`, "system");

      // Check if the drawn card can be played
      const newHand = [...aiPlayer.hand];
      const drawnCard = newHand[newHand.length - 1];

      if (canPlayCard(drawnCard)) {
        setTimeout(() => {
          playCard(currentPlayer, newHand.length - 1);
        }, 1000);
      } else {
        nextPlayer();
      }
    }
  };

  // Player draws a card
  const playerDrawCard = () => {
    if (currentPlayer !== 0 || gameStatus !== GAME_STATUS.PLAYING) return;

    drawCards(0, 1);
    addMessage("You drew a card.", "system");

    // Check if drawn card can be played
    const playerHand = players[0].hand;
    const drawnCard = playerHand[playerHand.length - 1];

    if (canPlayCard(drawnCard)) {
      addMessage("You can play the card you just drew!", "system");
    } else {
      nextPlayer();
    }
  };

  // End round
  const endRound = (winnerIndex) => {
    setGameStatus(GAME_STATUS.ROUND_END);

    // Clear timers
    if (timerRef.current) clearInterval(timerRef.current);
    if (aiTimeoutRef.current) clearTimeout(aiTimeoutRef.current);

    // Calculate scores
    const roundScores = players.map(player => {
      return player.hand.reduce((score, card) => {
        if (card.value === '8') return score + 50;
        if (['J', 'Q', 'K'].includes(card.value)) return score + 10;
        if (card.value === 'A') return score + 1;
        return score + parseInt(card.value) || 0;
      }, 0);
    });

    // Update player scores
    setPlayers(prev => prev.map((player, index) => ({
      ...player,
      score: player.score + (index === winnerIndex ? 0 : roundScores[index]),
      isWinner: index === winnerIndex
    })));

    const winner = players[winnerIndex];
    addMessage(`${winner.name} wins the round!`, "system");

    // Check for game end (first to reach target score loses)
    const maxScore = Math.max(...players.map((p, i) => p.score + (i === winnerIndex ? 0 : roundScores[i])));

    if (maxScore >= 100) {
      endGame();
    } else {
      setTimeout(() => {
        startNewRound();
      }, 3000);
    }
  };

  // Start new round
  const startNewRound = () => {
    // Reset for new round but keep scores
    const newDeck = shuffleDeck(createDeck());

    setPlayers(prev => prev.map(player => ({
      ...player,
      hand: [],
      isWinner: false
    })));

    setDeck(newDeck);
    setDiscardPile([]);
    setCurrentPlayer(0);
    setDirection(DIRECTIONS.CLOCKWISE);
    setCurrentSuit(null);
    setMustDraw(0);
    setSuitSelection(false);

    dealCards(newDeck, players);
    addMessage("New round started!", "system");
  };

  // End game
  const endGame = () => {
    setGameStatus(GAME_STATUS.GAME_END);

    // Find winner (lowest score)
    const winnerIndex = players.reduce((minIndex, player, index) =>
      player.score < players[minIndex].score ? index : minIndex
    , 0);

    const result = {
      winner: players[winnerIndex],
      finalScores: players.map(p => ({ name: p.name, score: p.score })),
      payout: 0
    };

    // Calculate payout for human player
    if (winnerIndex === 0) {
      result.payout = config.wagerAmount * (config.numPlayers - 1);
      addMessage(`Congratulations! You won $${result.payout}!`, "system");
    } else {
      addMessage(`${result.winner.name} wins the game!`, "system");
    }

    setGameResult(result);
  };

  // Reset game
  const resetGame = () => {
    // Clear timers
    if (timerRef.current) clearInterval(timerRef.current);
    if (aiTimeoutRef.current) clearTimeout(aiTimeoutRef.current);

    setGameStatus(GAME_STATUS.SETUP);
    setPlayers(prev => prev.map(player => ({ ...player, hand: [], score: 0, isWinner: false })));
    setDeck([]);
    setDiscardPile([]);
    setCurrentPlayer(0);
    setGameResult(null);
    setGameHistory([]);

    addMessage("Game reset! Ready for another round of Crazy Eights?", "system");
  };

  // Render player hand
  const renderPlayerHand = (player, playerIndex) => {
    const isCurrentPlayer = playerIndex === currentPlayer && gameStatus === GAME_STATUS.PLAYING;
    const isHumanPlayer = playerIndex === 0;

    return (
      <div className={`bg-slate-900 border border-slate-800 rounded-sm p-2 ${
        isCurrentPlayer ? 'ring-2 ring-yellow-500' : ''
      }`}>
        <div className="flex justify-between items-center mb-2">
          <div className="flex items-center">
            <h3 className="text-sm font-medium text-white">{player.name}</h3>
            {isCurrentPlayer && (
              <Badge className="ml-2 bg-yellow-500 text-black animate-pulse">
                Current Turn
              </Badge>
            )}
            {player.isWinner && (
              <Crown className="h-4 w-4 ml-2 text-yellow-500" />
            )}
          </div>
          <div className="flex items-center space-x-2">
            <Badge className="bg-blue-500">{player.hand.length} cards</Badge>
            <Badge className="bg-purple-500">{player.score} pts</Badge>
          </div>
        </div>

        <div className="flex flex-wrap gap-1">
          {player.hand.map((card, cardIndex) => (
            <div
              key={card.id}
              className={`relative w-12 h-16 bg-slate-800 border rounded cursor-pointer transition-all ${
                isHumanPlayer
                  ? canPlayCard(card) && isCurrentPlayer
                    ? 'border-green-500 hover:border-green-400 hover:-translate-y-1'
                    : 'border-slate-600'
                  : 'border-slate-600'
              }`}
              onClick={() => {
                if (isHumanPlayer && isCurrentPlayer && canPlayCard(card)) {
                  playCard(playerIndex, cardIndex);
                }
              }}
            >
              {isHumanPlayer ? (
                <img
                  src={getCardImagePath(card)}
                  alt={`${card.value} of ${card.suit}`}
                  className="w-full h-full object-cover rounded"
                  onError={(e) => {
                    // Fallback to text representation
                    e.target.style.display = 'none';
                  }}
                />
              ) : (
                <div className="w-full h-full bg-blue-900 border border-blue-700 rounded flex items-center justify-center">
                  <div className="text-white text-xs font-bold transform rotate-12">?</div>
                </div>
              )}

              {/* Text fallback for human player cards */}
              {isHumanPlayer && (
                <div className="absolute inset-0 flex flex-col items-center justify-center text-xs font-bold text-white bg-slate-800 rounded opacity-0 hover:opacity-100 transition-opacity">
                  <div className={card.suit === SUITS.HEARTS || card.suit === SUITS.DIAMONDS ? 'text-red-500' : 'text-black'}>
                    {card.value}
                  </div>
                  <div className="mt-1">
                    {getSuitIcon(card.suit)}
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Draw button for human player */}
        {isHumanPlayer && isCurrentPlayer && gameStatus === GAME_STATUS.PLAYING && (
          <Button
            className="w-full mt-2 h-8 text-xs bg-gradient-to-r from-blue-500 to-blue-600"
            onClick={playerDrawCard}
          >
            <Plus className="h-3 w-3 mr-1" />
            Draw Card
          </Button>
        )}
      </div>
    );
  };

  // Render discard pile and current suit
  const renderGameBoard = () => {
    const topCard = discardPile[discardPile.length - 1];

    return (
      <div className="text-center">
        <div className="mb-4">
          <h2 className="text-lg font-bold text-white mb-2">Game Board</h2>
          <div className="flex items-center justify-center space-x-4">
            <Badge className="bg-green-500">
              Current Suit: {currentSuit}
              {currentSuit && getSuitIcon(currentSuit)}
            </Badge>
            <Badge className="bg-blue-500">
              Direction: {direction === DIRECTIONS.CLOCKWISE ? '→' : '←'}
            </Badge>
            {mustDraw > 0 && (
              <Badge className="bg-red-500 animate-pulse">
                Draw {mustDraw} Penalty
              </Badge>
            )}
          </div>
        </div>

        <div className="flex justify-center items-center space-x-6">
          {/* Deck */}
          <div className="text-center">
            <div className="text-xs text-slate-400 mb-1">Deck</div>
            <div className="w-16 h-20 bg-blue-900 border-2 border-blue-700 rounded flex items-center justify-center">
              <div className="text-white text-sm font-bold">{deck.length}</div>
            </div>
          </div>

          {/* Discard pile */}
          <div className="text-center">
            <div className="text-xs text-slate-400 mb-1">Discard Pile</div>
            <div className="w-16 h-20 bg-slate-800 border-2 border-slate-600 rounded relative">
              {topCard ? (
                <>
                  <img
                    src={getCardImagePath(topCard)}
                    alt={`${topCard.value} of ${topCard.suit}`}
                    className="w-full h-full object-cover rounded"
                    onError={(e) => {
                      e.target.style.display = 'none';
                    }}
                  />
                  <div className="absolute inset-0 flex flex-col items-center justify-center text-xs font-bold text-white bg-slate-800 rounded opacity-0 hover:opacity-100 transition-opacity">
                    <div className={topCard.suit === SUITS.HEARTS || topCard.suit === SUITS.DIAMONDS ? 'text-red-500' : 'text-white'}>
                      {topCard.value}
                    </div>
                    <div className="mt-1">
                      {getSuitIcon(topCard.suit)}
                    </div>
                  </div>
                </>
              ) : (
                <div className="w-full h-full flex items-center justify-center text-slate-500">
                  Empty
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Turn timer */}
        {gameStatus === GAME_STATUS.PLAYING && (
          <div className="mt-4">
            <div className="flex justify-center items-center mb-2">
              <Clock className="h-4 w-4 mr-2 text-yellow-500" />
              <span className="text-sm font-bold text-white">{timeLeft}s</span>
            </div>
            <Progress value={(timeLeft / config.timeLimit) * 100} className="h-2 max-w-xs mx-auto" />
          </div>
        )}
      </div>
    );
  };

  // Render suit selection
  const renderSuitSelection = () => {
    if (!suitSelection) return null;

    return (
      <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-50">
        <div className="bg-slate-900 border border-slate-800 rounded-sm p-6 text-center">
          <h3 className="text-lg font-bold text-white mb-4">Choose a Suit</h3>
          <div className="grid grid-cols-2 gap-4">
            {Object.values(SUITS).map(suit => (
              <Button
                key={suit}
                className="h-16 w-16 bg-slate-800 hover:bg-slate-700 border-2 border-slate-600 hover:border-slate-500"
                onClick={() => chooseSuit(suit)}
              >
                <div className="text-2xl">
                  {getSuitIcon(suit)}
                </div>
              </Button>
            ))}
          </div>
        </div>
      </div>
    );
  };

  // Render settings panel
  const renderSettings = () => {
    return (
      <div className="bg-slate-900 border border-slate-800 rounded-sm p-3 absolute top-0 left-0 right-0 z-10">
        <div className="flex justify-between items-center mb-3">
          <h3 className="text-sm font-medium text-white">Game Settings</h3>
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0"
            onClick={() => setShowSettings(false)}
          >
            <X className="h-3 w-3" />
          </Button>
        </div>

        <div className="space-y-3">
          <div>
            <label className="text-xs text-slate-400 block mb-1">Number of Decks</label>
            <div className="flex space-x-2">
              {[1, 2, 3].map(num => (
                <Button
                  key={num}
                  variant={config.numDecks === num ? "default" : "outline"}
                  size="sm"
                  className="flex-1 h-7 text-xs"
                  onClick={() => setConfig({ ...config, numDecks: num })}
                >
                  {num}
                </Button>
              ))}
            </div>
          </div>

          <div>
            <label className="text-xs text-slate-400 block mb-1">Number of Players</label>
            <div className="flex space-x-2">
              {[2, 3, 4, 5, 6].map(num => (
                <Button
                  key={num}
                  variant={config.numPlayers === num ? "default" : "outline"}
                  size="sm"
                  className="flex-1 h-7 text-xs"
                  onClick={() => setConfig({ ...config, numPlayers: num })}
                >
                  {num}
                </Button>
              ))}
            </div>
          </div>

          <div>
            <label className="text-xs text-slate-400 block mb-1">Cards per Player</label>
            <div className="flex space-x-2">
              {[5, 7, 9].map(num => (
                <Button
                  key={num}
                  variant={config.cardsPerPlayer === num ? "default" : "outline"}
                  size="sm"
                  className="flex-1 h-7 text-xs"
                  onClick={() => setConfig({ ...config, cardsPerPlayer: num })}
                >
                  {num}
                </Button>
              ))}
            </div>
          </div>

          <div>
            <label className="text-xs text-slate-400 block mb-1">Turn Time Limit</label>
            <div className="flex space-x-2">
              {[15, 30, 45, 60].map(time => (
                <Button
                  key={time}
                  variant={config.timeLimit === time ? "default" : "outline"}
                  size="sm"
                  className="flex-1 h-7 text-xs"
                  onClick={() => setConfig({ ...config, timeLimit: time })}
                >
                  {time}s
                </Button>
              ))}
            </div>
          </div>

          <div>
            <label className="text-xs text-slate-400 block mb-1">Wager Amount</label>
            <div className="flex space-x-2">
              {[10, 25, 50, 100].map(amount => (
                <Button
                  key={amount}
                  variant={config.wagerAmount === amount ? "default" : "outline"}
                  size="sm"
                  className="flex-1 h-7 text-xs"
                  onClick={() => setConfig({ ...config, wagerAmount: amount })}
                >
                  ${amount}
                </Button>
              ))}
            </div>
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <label className="text-xs text-slate-400">Special Rules</label>
              <Button
                variant={config.enableSpecialRules ? "default" : "outline"}
                size="sm"
                className="h-7 w-14 text-xs"
                onClick={() => setConfig({ ...config, enableSpecialRules: !config.enableSpecialRules })}
              >
                {config.enableSpecialRules ? "On" : "Off"}
              </Button>
            </div>

            {config.enableSpecialRules && (
              <>
                <div className="flex items-center justify-between">
                  <label className="text-xs text-slate-400">2 = Draw Two</label>
                  <Button
                    variant={config.drawTwoPenalty ? "default" : "outline"}
                    size="sm"
                    className="h-7 w-14 text-xs"
                    onClick={() => setConfig({ ...config, drawTwoPenalty: !config.drawTwoPenalty })}
                  >
                    {config.drawTwoPenalty ? "On" : "Off"}
                  </Button>
                </div>

                <div className="flex items-center justify-between">
                  <label className="text-xs text-slate-400">Q = Skip Turn</label>
                  <Button
                    variant={config.skipTurnOnQueen ? "default" : "outline"}
                    size="sm"
                    className="h-7 w-14 text-xs"
                    onClick={() => setConfig({ ...config, skipTurnOnQueen: !config.skipTurnOnQueen })}
                  >
                    {config.skipTurnOnQueen ? "On" : "Off"}
                  </Button>
                </div>

                <div className="flex items-center justify-between">
                  <label className="text-xs text-slate-400">J = Reverse</label>
                  <Button
                    variant={config.reverseOnJack ? "default" : "outline"}
                    size="sm"
                    className="h-7 w-14 text-xs"
                    onClick={() => setConfig({ ...config, reverseOnJack: !config.reverseOnJack })}
                  >
                    {config.reverseOnJack ? "On" : "Off"}
                  </Button>
                </div>
              </>
            )}
          </div>
        </div>
      </div>
    );
  };

  // Render setup screen
  const renderSetupScreen = () => {
    return (
      <div className="flex flex-col items-center justify-center h-full bg-slate-900 border border-slate-800 rounded-sm p-4">
        <div className="text-6xl mb-4">🃏</div>
        <h1 className="text-2xl font-bold text-white mb-1">Crazy Eights</h1>
        <p className="text-sm text-slate-400 mb-6 text-center">
          Match the suit or rank to play your cards.<br />
          First to empty their hand wins the round!
        </p>

        <div className="w-full max-w-md mb-6">
          <div className="bg-slate-800 p-4 rounded-sm mb-4">
            <h3 className="text-sm font-medium text-white mb-3">Quick Setup</h3>

            <div className="space-y-3">
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <label className="text-xs text-slate-400 block mb-1">Players</label>
                  <div className="flex space-x-1">
                    {[2, 3, 4].map(num => (
                      <Button
                        key={num}
                        variant={config.numPlayers === num ? "default" : "outline"}
                        size="sm"
                        className="flex-1 h-7 text-xs"
                        onClick={() => setConfig({ ...config, numPlayers: num })}
                      >
                        {num}
                      </Button>
                    ))}
                  </div>
                </div>

                <div>
                  <label className="text-xs text-slate-400 block mb-1">Cards</label>
                  <div className="flex space-x-1">
                    {[5, 7, 9].map(num => (
                      <Button
                        key={num}
                        variant={config.cardsPerPlayer === num ? "default" : "outline"}
                        size="sm"
                        className="flex-1 h-7 text-xs"
                        onClick={() => setConfig({ ...config, cardsPerPlayer: num })}
                      >
                        {num}
                      </Button>
                    ))}
                  </div>
                </div>
              </div>

              <div>
                <label className="text-xs text-slate-400 block mb-1">Wager Amount</label>
                <div className="flex space-x-2">
                  {[10, 25, 50].map(amount => (
                    <Button
                      key={amount}
                      variant={config.wagerAmount === amount ? "default" : "outline"}
                      size="sm"
                      className="flex-1 h-7 text-xs"
                      onClick={() => setConfig({ ...config, wagerAmount: amount })}
                    >
                      ${amount}
                    </Button>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="flex space-x-3">
          <Button
            variant="outline"
            className="w-32 h-10 text-sm rounded-sm"
            onClick={() => setShowSettings(true)}
          >
            <Settings className="h-4 w-4 mr-2" />
            Settings
          </Button>

          <Button
            className="w-32 h-10 text-sm rounded-sm bg-gradient-to-r from-red-500 to-red-600 hover:from-red-400 hover:to-red-500"
            onClick={startGame}
          >
            <Shuffle className="h-4 w-4 mr-2" />
            Deal Cards
          </Button>
        </div>
      </div>
    );
  };

  // Main game layout
  return (
    <div className="grid grid-cols-12 gap-2 p-2 h-full">
      {/* Player Info - Left column */}
      <div className="col-span-3 flex flex-col gap-2 h-full">
        {/* Game Info */}
        <div className="bg-slate-900 border border-slate-800 rounded-sm p-2">
          <div className="flex justify-between items-center mb-2">
            <h3 className="text-sm font-medium text-white">
              {gameStatus === GAME_STATUS.SETUP ? "Game Setup" :
               gameStatus === GAME_STATUS.GAME_END ? "Game Complete" :
               "Crazy Eights"}
            </h3>
            <Badge className={`
              ${gameStatus === GAME_STATUS.SETUP ? 'bg-yellow-500' :
                gameStatus === GAME_STATUS.GAME_END ? 'bg-blue-500' :
                'bg-green-500'}
            `}>
              {gameStatus === GAME_STATUS.SETUP ? 'SETUP' :
               gameStatus === GAME_STATUS.GAME_END ? 'FINISHED' :
               'PLAYING'}
            </Badge>
          </div>

          <div className="grid grid-cols-2 gap-2">
            <div className="bg-slate-800 p-2 rounded-sm">
              <div className="text-xs text-slate-400">Players</div>
              <div className="text-sm font-bold text-white">{config.numPlayers}</div>
            </div>

            <div className="bg-slate-800 p-2 rounded-sm">
              <div className="text-xs text-slate-400">Wager</div>
              <div className="text-sm font-bold text-white">${config.wagerAmount}</div>
            </div>
          </div>
        </div>

        {/* Player List */}
        {gameStatus !== GAME_STATUS.SETUP && (
          <div className="bg-slate-900 border border-slate-800 rounded-sm flex-1 flex flex-col overflow-hidden">
            <div className="p-2 border-b border-slate-800">
              <h3 className="text-sm font-medium text-white">Players</h3>
            </div>

            <div className="flex-1 overflow-auto p-2 space-y-2">
              {players.slice(0, config.numPlayers).map((player, index) => (
                <div
                  key={player.id}
                  className={`p-2 rounded-sm ${
                    index === currentPlayer ? 'bg-yellow-500/20 border border-yellow-500' : 'bg-slate-800'
                  }`}
                >
                  <div className="flex justify-between items-center">
                    <div className="flex items-center">
                      <div className={`h-6 w-6 rounded-full mr-2 flex items-center justify-center text-xs font-bold ${
                        index === currentPlayer ? 'bg-yellow-500 text-black' : 'bg-slate-600 text-white'
                      }`}>
                        {index + 1}
                      </div>
                      <span className="text-sm text-white">{player.name}</span>
                      {player.isWinner && <Crown className="h-4 w-4 ml-1 text-yellow-500" />}
                    </div>

                    <div className="text-right">
                      <div className="text-sm font-bold text-white">{player.hand.length} cards</div>
                      <div className="text-xs text-slate-400">{player.score} pts</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Game Result */}
        {gameStatus === GAME_STATUS.GAME_END && gameResult && (
          <div className="bg-slate-900 border border-slate-800 rounded-sm p-2">
            <h3 className="text-sm font-medium text-white mb-2">Game Results</h3>

            <div className="text-center mb-2">
              <div className="text-2xl mb-1">
                {gameResult.winner.name === 'You' ? '🏆' : '🎯'}
              </div>
              <div className={`text-lg font-bold ${
                gameResult.winner.name === 'You' ? 'text-green-500' : 'text-red-500'
              }`}>
                {gameResult.winner.name} Wins!
              </div>
              <div className="text-xs text-slate-400">
                Score: {gameResult.winner.score}
              </div>
            </div>

            {gameResult.payout > 0 && (
              <div className="bg-slate-800 p-2 rounded-sm mb-2">
                <div className="text-center">
                  <div className="text-xs text-slate-400">Your Payout</div>
                  <div className="text-lg font-bold text-green-500">
                    ${gameResult.payout}
                  </div>
                </div>
              </div>
            )}

            <Button
              onClick={resetGame}
              className="w-full h-7 text-xs rounded-sm bg-gradient-to-r from-purple-500 to-pink-500"
            >
              <RotateCcw className="h-3 w-3 mr-1" />
              New Game
            </Button>
          </div>
        )}
      </div>

      {/* Game Board - Middle column */}
      <div className="col-span-6 flex flex-col h-full">
        {/* Game board */}
        <div className="flex-1 bg-slate-900 border border-slate-800 rounded-sm p-3 flex flex-col relative">
          {gameStatus === GAME_STATUS.SETUP ? (
            renderSetupScreen()
          ) : (
            <div className="flex-1 flex flex-col justify-center">
              {renderGameBoard()}
            </div>
          )}

          {/* Settings overlay */}
          {showSettings && renderSettings()}

          {/* Suit selection overlay */}
          {renderSuitSelection()}
        </div>

        {/* Game controls */}
        <div className="flex justify-between items-center mt-2">
          <Button
            variant="outline"
            size="sm"
            className="h-8 text-xs"
            onClick={resetGame}
          >
            <RotateCcw className="h-3 w-3 mr-1" />
            Reset Game
          </Button>

          <div className="flex items-center space-x-2">
            <Badge className={config.enableSpecialRules ? 'bg-green-500' : 'bg-slate-500'}>
              {config.enableSpecialRules ? 'Special Rules On' : 'Basic Rules'}
            </Badge>
          </div>

          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              className={`h-8 text-xs ${showSettings ? 'bg-slate-800' : ''}`}
              onClick={() => setShowSettings(!showSettings)}
            >
              <Settings className="h-3 w-3 mr-1" />
              Settings
            </Button>

            <Button
              variant={gameStatus === GAME_STATUS.SETUP ? 'default' : 'outline'}
              size="sm"
              className="h-8 text-xs"
              disabled={gameStatus !== GAME_STATUS.SETUP}
              onClick={startGame}
            >
              {gameStatus === GAME_STATUS.SETUP ? (
                <>
                  <Shuffle className="h-3 w-3 mr-1" />
                  Deal Cards
                </>
              ) : (
                <>
                  <Play className="h-3 w-3 mr-1" />
                  Game Active
                </>
              )}
            </Button>
          </div>
        </div>
      </div>

      {/* Player Hands - Right column */}
      <div className="col-span-3 flex flex-col gap-2 h-full">
        {/* Current Player Hand (You) */}
        {gameStatus !== GAME_STATUS.SETUP && players.length > 0 && (
          renderPlayerHand(players[0], 0)
        )}

        {/* Other Players */}
        {gameStatus !== GAME_STATUS.SETUP && (
          <div className="flex-1 overflow-auto space-y-2">
            {players.slice(1, config.numPlayers).map((player, index) => (
              <div key={player.id}>
                {renderPlayerHand(player, index + 1)}
              </div>
            ))}
          </div>
        )}

        {/* Chat */}
        <div className="bg-slate-900 border border-slate-800 rounded-sm flex flex-col overflow-hidden h-64">
          <div className="p-2 border-b border-slate-800 flex justify-between items-center">
            <div className="flex items-center">
              <MessageSquare className="h-4 w-4 text-slate-400 mr-1" />
              <h3 className="text-sm font-medium text-white">Game Chat</h3>
            </div>
            <Badge className="h-5 bg-green-500 text-[10px]">
              <Users className="h-3 w-3 mr-1" />
              {config.numPlayers}
            </Badge>
          </div>

          {/* Chat messages - scrollable */}
          <div className="flex-1 overflow-auto p-2 space-y-1">
            {messages.map((msg, idx) => (
              <div
                key={idx}
                className={`text-xs p-1 rounded-sm ${
                  msg.type === 'system' ? 'text-yellow-500 italic' : 'text-white'
                }`}
              >
                {msg.text}
              </div>
            ))}
            <div ref={messageEndRef} />
          </div>

          {/* Chat input */}
          <div className="p-2 border-t border-slate-800">
            <div className="flex space-x-2">
              <Input
                placeholder="Type a message..."
                className="h-8 text-xs bg-slate-800 border-slate-700"
                value={messageInput}
                onChange={(e) => setMessageInput(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    sendMessage();
                  }
                }}
              />
              <Button
                onClick={sendMessage}
                className="h-8 px-3 bg-gradient-to-r from-purple-500 to-pink-500"
              >
                Send
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CrazyEightsGame;