import requests
import json

# Test Expert Picks API
BASE_URL = "http://localhost:8000/api"

# First, login to get a token
login_data = {
    "username": "<EMAIL>",
    "password": "testpass123"
}

print("1. Logging in...")
login_response = requests.post(
    f"{BASE_URL}/auth/login",
    data=login_data,
    headers={"Content-Type": "application/x-www-form-urlencoded"}
)

if login_response.status_code == 200:
    tokens = login_response.json()
    access_token = tokens["access_token"]
    print(f"✓ Login successful! Token: {access_token[:20]}...")
    
    # Headers with auth token
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }
    
    # Test creating expert profile
    print("\n2. Creating expert profile...")
    profile_data = {"is_expert": True}
    profile_response = requests.post(
        f"{BASE_URL}/expert-picks/profile",
        json=profile_data,
        headers=headers
    )
    
    if profile_response.status_code == 200:
        profile = profile_response.json()
        print("✓ Expert profile created/updated!")
        print(json.dumps(profile, indent=2))
    else:
        print(f"✗ Failed to create profile: {profile_response.status_code}")
        print(profile_response.text)
        
    # Test getting profile
    print("\n3. Getting expert profile...")
    user_response = requests.get(f"{BASE_URL}/users/me", headers=headers)
    if user_response.status_code == 200:
        user = user_response.json()
        user_id = user["id"]
        
        get_profile_response = requests.get(
            f"{BASE_URL}/expert-picks/profile/{user_id}",
            headers=headers
        )
        
        if get_profile_response.status_code == 200:
            profile = get_profile_response.json()
            print("✓ Expert profile retrieved!")
            print(json.dumps(profile, indent=2))
        else:
            print(f"✗ Failed to get profile: {get_profile_response.status_code}")
            print(get_profile_response.text)
else:
    print(f"✗ Login failed: {login_response.status_code}")
    print(login_response.text)