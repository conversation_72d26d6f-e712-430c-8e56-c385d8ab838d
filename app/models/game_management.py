"""Enhanced game models for BetBet platform."""
from sqlalchemy import <PERSON>umn, String, Float, DateTime, JSON, Foreign<PERSON>ey, <PERSON><PERSON>an, <PERSON>teger, UniqueConstraint
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.database.db import Base
import uuid


class GamePlayer(Base):
    """Game player participation model."""
    __tablename__ = "game_players"
    
    id = Column(Integer, primary_key=True)
    game_id = Column(String, ForeignKey("games.id"), nullable=False)
    user_id = Column(String, ForeignKey("users.id"), nullable=False)
    
    # Player info
    player_number = Column(Integer, nullable=False)  # 1, 2, etc.
    is_winner = Column(Boolean, nullable=True)
    final_score = Column(Integer, nullable=True)
    time_remaining = Column(Integer, nullable=True)  # seconds, for timed games
    
    # Financial
    amount_wagered = Column(Float, default=0.0)
    amount_won = Column(Float, default=0.0)
    
    # Status
    status = Column(String, default="active")  # active, disconnected, forfeited
    joined_at = Column(DateTime(timezone=True), server_default=func.now())
    left_at = Column(DateTime(timezone=True), nullable=True)
    
    # Relationships
    game = relationship("Game", back_populates="players")
    user = relationship("User", back_populates="games")
    
    __table_args__ = (UniqueConstraint('game_id', 'user_id'),)


class GameChat(Base):
    """In-game chat messages."""
    __tablename__ = "game_chat"
    
    id = Column(Integer, primary_key=True)
    game_id = Column(String, ForeignKey("games.id"), nullable=False)
    user_id = Column(String, ForeignKey("users.id"), nullable=False)
    
    message = Column(String, nullable=False)
    is_system_message = Column(Boolean, default=False)
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    game = relationship("Game", back_populates="chat_messages")
    user = relationship("User")


class GameInvite(Base):
    """Game invitations model."""
    __tablename__ = "game_invites"
    
    id = Column(Integer, primary_key=True)
    game_id = Column(String, ForeignKey("games.id"), nullable=False)
    inviter_id = Column(String, ForeignKey("users.id"), nullable=False)
    invitee_id = Column(String, ForeignKey("users.id"), nullable=False)
    
    status = Column(String, default="pending")  # pending, accepted, declined, expired
    message = Column(String, nullable=True)
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    responded_at = Column(DateTime(timezone=True), nullable=True)
    expires_at = Column(DateTime(timezone=True), nullable=False)
    
    # Relationships
    game = relationship("Game")
    inviter = relationship("User", foreign_keys=[inviter_id])
    invitee = relationship("User", foreign_keys=[invitee_id])
    
    __table_args__ = (UniqueConstraint('game_id', 'invitee_id'),)


class GameTournament(Base):
    """Tournament model for multiple games."""
    __tablename__ = "game_tournaments"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    name = Column(String, nullable=False)
    description = Column(String, nullable=True)
    game_type = Column(String, nullable=False)
    
    # Tournament settings
    max_players = Column(Integer, nullable=False)
    current_players = Column(Integer, default=0)
    entry_fee = Column(Float, default=0.0)
    prize_pool = Column(Float, default=0.0)
    
    # Prize distribution
    prize_distribution = Column(JSON, nullable=True)  # {1: 50%, 2: 30%, 3: 20%}
    
    # Status
    status = Column(String, default="registration")  # registration, in_progress, completed
    started_at = Column(DateTime(timezone=True), nullable=True)
    ended_at = Column(DateTime(timezone=True), nullable=True)
    
    # Tournament format
    format = Column(String, default="single_elimination")  # single_elimination, double_elimination, round_robin
    current_round = Column(Integer, default=0)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    registration_closes_at = Column(DateTime(timezone=True), nullable=False)
    
    # Relationships
    # games = relationship("Game", back_populates="tournament")  # Temporarily disabled until tournament_id is added back
    participants = relationship("TournamentParticipant", back_populates="tournament")
    

class TournamentParticipant(Base):
    """Tournament participant model."""
    __tablename__ = "tournament_participants"
    
    id = Column(Integer, primary_key=True)
    tournament_id = Column(String, ForeignKey("game_tournaments.id"), nullable=False)
    user_id = Column(String, ForeignKey("users.id"), nullable=False)
    
    # Status
    status = Column(String, default="active")  # active, eliminated, withdrawn
    seed = Column(Integer, nullable=True)  # Seeding for bracket
    
    # Performance
    games_played = Column(Integer, default=0)
    games_won = Column(Integer, default=0)
    final_position = Column(Integer, nullable=True)
    prize_won = Column(Float, default=0.0)
    
    # Timestamps
    registered_at = Column(DateTime(timezone=True), server_default=func.now())
    eliminated_at = Column(DateTime(timezone=True), nullable=True)
    
    # Relationships
    tournament = relationship("GameTournament", back_populates="participants")
    user = relationship("User")
    
    __table_args__ = (UniqueConstraint('tournament_id', 'user_id'),)