from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from datetime import datetime
import json
import uuid
import random
from pydantic import BaseModel

from app.database.db import get_db
from app.models.game import Game, GameStatus, GameType, GameMove
from app.models.game_management import GamePlayer
from app.models.user import User
from app.auth.jwt import get_current_user
from app.schemas.game import GameCreate, GameUpdate, GameResponse, GameMoveCreate
from app.core.websocket_auth import get_current_user_ws
from app.api.websocket import manager as game_manager

router = APIRouter(tags=["game-instances"])

def generate_game_id(game_type: GameType) -> str:
    """Generate a unique game ID with game type prefix"""
    prefix = game_type.value[:3].upper()  # First 3 chars of game type
    unique_id = str(uuid.uuid4())[:8]  # First 8 chars of UUID
    return f"{prefix}_{unique_id}"

# Helper function to validate game state
def validate_game_state(game_type: GameType, state: Dict[str, Any]) -> bool:
    """Validate game state based on game type"""
    if game_type == GameType.CHESS:
        # Chess state should have board, turn, castling rights, etc.
        required_fields = ["board", "turn", "castlingRights", "enPassant", "halfMoveClock", "fullMoveNumber"]
        return all(field in state for field in required_fields)
    elif game_type == GameType.CHECKERS:
        # Checkers state should have board and turn
        required_fields = ["board", "turn"]
        return all(field in state for field in required_fields)
    elif game_type == GameType.ROCK_PAPER_SCISSORS:
        # RPS state should have rounds and current choices
        required_fields = ["rounds", "currentRound", "player1Choice", "player2Choice"]
        return all(field in state for field in required_fields)
    elif game_type == GameType.HIGHLIGHT_HERO:
        # Highlight Hero state
        required_fields = ["currentHighlight", "scores", "round", "timeRemaining"]
        return all(field in state for field in required_fields)
    return True

@router.post("/create", response_model=GameResponse)
async def create_game(
    game_data: GameCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create a new game instance"""
    try:
        # Create game
        game = Game(
            id=str(uuid.uuid4()),
            game_type=game_data.game_type,
            player1_id=current_user.id,
            wager_amount=game_data.wager_amount,
            total_pot=game_data.wager_amount,  # Initial pot from player 1
            settings=game_data.settings or {},
            state=get_initial_state(game_data.game_type),
            status=GameStatus.WAITING
        )

        db.add(game)
        db.commit()
        db.refresh(game)

        # Add player to game_players table
        game_player = GamePlayer(
            game_id=game.id,
            user_id=current_user.id,
            player_number=1,  # First player
            amount_wagered=game_data.wager_amount,
            status="active"
        )
        db.add(game_player)
        db.commit()

        return GameResponse.from_orm(game)
    except Exception as e:
        db.rollback()
        print(f"Error creating game: {str(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to create game: {str(e)}")

@router.get("/{game_id}", response_model=GameResponse)
async def get_game(
    game_id: str,
    current_user: Optional[User] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get game details including current state"""
    game = db.query(Game).filter(Game.id == game_id).first()
    if not game:
        raise HTTPException(status_code=404, detail="Game not found")

    return GameResponse.from_orm(game)

class JoinGameData(BaseModel):
    stake_amount: float

@router.post("/{game_id}/join")
async def join_game(
    game_id: str,
    join_data: JoinGameData,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Join an existing game"""
    game = db.query(Game).filter(Game.id == game_id).first()
    if not game:
        raise HTTPException(status_code=404, detail="Game not found")

    if game.status != GameStatus.WAITING:
        raise HTTPException(status_code=400, detail="Game already started or completed")

    # Check if user is already in the game
    existing_player = db.query(GamePlayer).filter(
        GamePlayer.game_id == game_id,
        GamePlayer.user_id == current_user.id
    ).first()

    if existing_player:
        raise HTTPException(status_code=400, detail="Already in this game")

    # Add as second player
    game.player2_id = current_user.id
    game.total_pot = game.total_pot + join_data.stake_amount  # Add to pot
    game_player = GamePlayer(
        game_id=game.id,
        user_id=current_user.id,
        player_number=2,  # Second player
        amount_wagered=join_data.stake_amount,
        status="active"
    )
    db.add(game_player)

    # Check if we can start the game
    players_count = db.query(GamePlayer).filter(GamePlayer.game_id == game_id).count()
    if players_count >= 2:
        game.status = GameStatus.IN_PROGRESS
        game.started_at = datetime.utcnow()
        game.current_player_id = game.player1_id

    db.commit()

    # Notify through WebSocket
    await game_manager.broadcast_to_all_in_game({
        "type": "player_joined",
        "player": {
            "id": current_user.id,
            "username": current_user.username,
            "player_number": 2
        },
        "game_status": game.status.value
    }, game_id)

    return {"message": "Joined game successfully", "game_id": game_id}

@router.post("/{game_id}/move")
async def make_move(
    game_id: str,
    move_data: GameMoveCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Make a move in the game"""
    game = db.query(Game).filter(Game.id == game_id).first()
    if not game:
        raise HTTPException(status_code=404, detail="Game not found")

    if game.status != GameStatus.IN_PROGRESS:
        raise HTTPException(status_code=400, detail="Game is not in progress")

    if game.current_player_id != current_user.id:
        raise HTTPException(status_code=400, detail="Not your turn")

    # Validate move based on game type
    is_valid, new_state = validate_and_apply_move(
        game.game_type,
        game.state,
        move_data.move_data,
        current_user.id
    )

    if not is_valid:
        raise HTTPException(status_code=400, detail="Invalid move")

    # Update game state
    game.state = new_state
    game.last_move_at = datetime.utcnow()

    # Create move record
    move_count = db.query(GameMove).filter(GameMove.game_id == game_id).count()
    game_move = GameMove(
        game_id=game_id,
        player_id=current_user.id,
        move_number=move_count + 1,
        move_data=move_data.move_data
    )
    db.add(game_move)

    # Switch turns
    game.current_player_id = game.player2_id if game.current_player_id == game.player1_id else game.player1_id

    # Check for game end
    is_ended, winner_id = check_game_end(game.game_type, new_state)
    if is_ended:
        game.status = GameStatus.COMPLETED
        game.winner_id = winner_id
        game.completed_at = datetime.utcnow()

    db.commit()

    # Notify through WebSocket
    await game_manager.broadcast_to_all_in_game({
        "type": "move_made",
        "player_id": current_user.id,
        "move": move_data.move_data,
        "new_state": new_state,
        "current_turn": game.current_player_id,
        "game_status": game.status.value,
        "winner_id": game.winner_id
    }, game_id)

    return {
        "message": "Move made successfully",
        "new_state": new_state,
        "current_turn": game.current_player_id,
        "game_status": game.status.value
    }

@router.get("/active", response_model=List[GameResponse])
async def get_active_games(
    game_type: Optional[GameType] = None,
    db: Session = Depends(get_db)
):
    """Get list of active games (waiting or in progress)"""
    query = db.query(Game).filter(
        Game.status.in_([GameStatus.WAITING, GameStatus.IN_PROGRESS])
    )

    if game_type:
        query = query.filter(Game.game_type == game_type)

    games = query.order_by(Game.created_at.desc()).limit(50).all()
    return [GameResponse.from_orm(game) for game in games]

@router.get("/user/{user_id}/games", response_model=List[GameResponse])
async def get_user_games(
    user_id: str,
    status: Optional[GameStatus] = None,
    db: Session = Depends(get_db)
):
    """Get games for a specific user"""
    query = db.query(Game).filter(
        (Game.player1_id == user_id) | (Game.player2_id == user_id)
    )

    if status:
        query = query.filter(Game.status == status)

    games = query.order_by(Game.created_at.desc()).limit(50).all()
    return [GameResponse.from_orm(game) for game in games]

# Helper functions
def get_initial_state(game_type: GameType) -> Dict[str, Any]:
    """Get initial state for a game type"""
    if game_type == GameType.CHESS:
        return {
            "board": get_initial_chess_board(),
            "turn": "white",
            "castlingRights": {"white": {"kingside": True, "queenside": True}, "black": {"kingside": True, "queenside": True}},
            "enPassant": None,
            "halfMoveClock": 0,
            "fullMoveNumber": 1
        }
    elif game_type == GameType.CHECKERS:
        return {
            "board": get_initial_checkers_board(),
            "turn": "red",
            "captureSequence": []
        }
    elif game_type == GameType.ROCK_PAPER_SCISSORS:
        return {
            "rounds": [],
            "currentRound": 1,
            "player1Choice": None,
            "player2Choice": None,
            "maxRounds": 3
        }
    elif game_type == GameType.HIGHLIGHT_HERO:
        return {
            "currentHighlight": None,
            "scores": {},
            "round": 1,
            "timeRemaining": 60
        }
    elif game_type == GameType.WORD_JUMBLE:
        return {
            "currentRound": 1,
            "maxRounds": 5,
            "currentLetters": get_random_word_set(),
            "timePerRound": 60,
            "scores": {},
            "foundWords": {},
            "roundHistory": []
        }
    return {}

def get_initial_chess_board():
    """Get initial chess board setup"""
    return [
        ["bR", "bN", "bB", "bQ", "bK", "bB", "bN", "bR"],
        ["bP", "bP", "bP", "bP", "bP", "bP", "bP", "bP"],
        [None, None, None, None, None, None, None, None],
        [None, None, None, None, None, None, None, None],
        [None, None, None, None, None, None, None, None],
        [None, None, None, None, None, None, None, None],
        ["wP", "wP", "wP", "wP", "wP", "wP", "wP", "wP"],
        ["wR", "wN", "wB", "wQ", "wK", "wB", "wN", "wR"]
    ]

def get_initial_checkers_board():
    """Get initial checkers board setup"""
    board = [[None for _ in range(8)] for _ in range(8)]
    # Place red pieces
    for row in range(3):
        for col in range(8):
            if (row + col) % 2 == 1:
                board[row][col] = "r"
    # Place black pieces
    for row in range(5, 8):
        for col in range(8):
            if (row + col) % 2 == 1:
                board[row][col] = "b"
    return board

def get_random_word_set():
    """Get a random word set for Word Jumble"""
    word_sets = [
        {"letters": "TAEC", "solutions": ["TACE", "CATE", "ACTE"], "target": "TACE"},
        {"letters": "RDWO", "solutions": ["WORD", "DROW"], "target": "WORD"},
        {"letters": "AELP", "solutions": ["LEAP", "PEAL", "PALE", "PLEA"], "target": "LEAP"},
        {"letters": "OWLF", "solutions": ["FLOW", "FOWL", "WOLF"], "target": "FLOW"},
        {"letters": "KROW", "solutions": ["WORK"], "target": "WORK"},
        {"letters": "MITE", "solutions": ["TIME", "ITEM", "EMIT", "MITE"], "target": "TIME"},
        {"letters": "POTS", "solutions": ["STOP", "POST", "TOPS", "POTS", "SPOT", "OPTS"], "target": "STOP"},
        {"letters": "ATRE", "solutions": ["TEAR", "RATE", "TARE"], "target": "TEAR"}
    ]
    return random.choice(word_sets)

def validate_and_apply_move(game_type: GameType, current_state: Dict, move_data: Dict, player_id: str):
    """Validate and apply a move to the game state"""
    # This is a simplified version - in production, you'd have full game logic
    new_state = current_state.copy()

    if game_type == GameType.CHESS:
        # Apply chess move
        from_pos = move_data.get("from")
        to_pos = move_data.get("to")
        if from_pos and to_pos:
            # Update board
            board = new_state["board"]
            piece = board[from_pos[0]][from_pos[1]]
            board[to_pos[0]][to_pos[1]] = piece
            board[from_pos[0]][from_pos[1]] = None
            # Switch turn
            new_state["turn"] = "black" if new_state["turn"] == "white" else "white"
            return True, new_state

    elif game_type == GameType.ROCK_PAPER_SCISSORS:
        # Apply RPS choice
        choice = move_data.get("choice")
        if choice in ["rock", "paper", "scissors"]:
            if new_state["player1Choice"] is None:
                new_state["player1Choice"] = choice
            else:
                new_state["player2Choice"] = choice
            return True, new_state

    elif game_type == GameType.WORD_JUMBLE:
        # Apply Word Jumble submission
        word = move_data.get("word", "").upper()
        player_key = f"player_{player_id}"

        if word and word not in new_state.get("foundWords", {}).get(player_key, []):
            # Calculate score
            word_set = new_state.get("currentLetters", {})
            base_score = len(word) * 10
            multiplier = 1.0

            if word in word_set.get("solutions", []):
                multiplier = 1.5
            if word == word_set.get("target"):
                multiplier = 2.0
            if len(word) >= 6:
                multiplier += 0.5

            score = int(base_score * multiplier)

            # Update state
            if player_key not in new_state["scores"]:
                new_state["scores"][player_key] = 0
            if player_key not in new_state["foundWords"]:
                new_state["foundWords"][player_key] = []

            new_state["scores"][player_key] += score
            new_state["foundWords"][player_key].append(word)

            return True, new_state

    # Add other game types...
    return True, new_state

def check_game_end(game_type: GameType, state: Dict):
    """Check if the game has ended and determine winner"""
    # Simplified end game checks
    if game_type == GameType.ROCK_PAPER_SCISSORS:
        if state["player1Choice"] and state["player2Choice"]:
            # Determine winner of round
            p1 = state["player1Choice"]
            p2 = state["player2Choice"]
            if p1 == p2:
                return False, None  # Tie, continue
            elif (p1 == "rock" and p2 == "scissors") or \
                 (p1 == "paper" and p2 == "rock") or \
                 (p1 == "scissors" and p2 == "paper"):
                # Player 1 wins round
                return state["currentRound"] >= state["maxRounds"], "player1"
            else:
                # Player 2 wins round
                return state["currentRound"] >= state["maxRounds"], "player2"

    elif game_type == GameType.WORD_JUMBLE:
        # Check if round time is up or max rounds reached
        if state.get("currentRound", 1) >= state.get("maxRounds", 5):
            # Determine winner by score
            scores = state.get("scores", {})
            if scores:
                winner = max(scores.items(), key=lambda x: x[1])
                return True, winner[0].replace("player_", "")

    return False, None