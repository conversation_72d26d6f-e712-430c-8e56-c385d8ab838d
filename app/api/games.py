from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime
from app.database.db import get_db
from app.models.user import User
from app.models.game import Game, GameStatus, GameType, GameSpectator
from app.core.dependencies import get_current_user
from app.schemas.game import GameCreate, GameResponse, GameUpdate, GameListResponse
from app.services.wallet import WalletService

router = APIRouter()

@router.post("/create", response_model=GameResponse)
async def create_game(
    game_data: GameCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Create a new game"""
    # Check if user has sufficient balance
    wallet_service = WalletService(db)
    if current_user.balance < game_data.wager_amount:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Insufficient balance"
        )
    
    # Create the game
    game = Game(
        game_type=game_data.game_type,
        player1_id=current_user.id,
        wager_amount=game_data.wager_amount,
        settings=game_data.settings.dict() if game_data.settings else {},
        status=GameStatus.WAITING
    )
    
    # Place bet for player 1
    wallet_service.place_bet(
        user=current_user,
        amount=game_data.wager_amount,
        game_id=game.id,
        description=f"{game_data.game_type.value} game - Player 1"
    )
    
    # Update total pot
    game.total_pot = game_data.wager_amount
    
    db.add(game)
    db.commit()
    db.refresh(game)
    
    return game

@router.post("/{game_id}/join", response_model=GameResponse)
async def join_game(
    game_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Join an existing game as player 2"""
    game = db.query(Game).filter(Game.id == game_id).first()
    if not game:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Game not found"
        )
    
    # Check if game is available to join
    if game.status != GameStatus.WAITING:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Game is not available to join"
        )
    
    if game.player2_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Game already has two players"
        )
    
    if game.player1_id == current_user.id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="You cannot join your own game"
        )
    
    # Check if user has sufficient balance
    wallet_service = WalletService(db)
    if current_user.balance < game.wager_amount:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Insufficient balance"
        )
    
    # Place bet for player 2
    wallet_service.place_bet(
        user=current_user,
        amount=game.wager_amount,
        game_id=game.id,
        description=f"{game.game_type.value} game - Player 2"
    )
    
    # Update game
    game.player2_id = current_user.id
    game.status = GameStatus.IN_PROGRESS
    game.started_at = datetime.utcnow()
    game.current_player_id = game.player1_id
    game.total_pot = game.wager_amount * 2
    
    db.commit()
    db.refresh(game)
    
    return game

@router.get("/available", response_model=List[GameListResponse])
async def get_available_games(
    game_type: Optional[GameType] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get list of available games to join"""
    query = db.query(Game).filter(
        Game.status == GameStatus.WAITING,
        Game.player1_id != current_user.id
    )
    
    if game_type:
        query = query.filter(Game.game_type == game_type)
    
    games = query.all()
    return games

@router.get("/my-games", response_model=List[GameListResponse])
async def get_my_games(
    status: Optional[GameStatus] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get user's games"""
    query = db.query(Game).filter(
        (Game.player1_id == current_user.id) | (Game.player2_id == current_user.id)
    )
    
    if status:
        query = query.filter(Game.status == status)
    
    games = query.order_by(Game.created_at.desc()).all()
    return games

@router.get("/{game_id}", response_model=GameResponse)
async def get_game(
    game_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get game details"""
    game = db.query(Game).filter(Game.id == game_id).first()
    if not game:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Game not found"
        )
    
    # Check if user can access this game
    is_player = current_user.id in [game.player1_id, game.player2_id]
    is_spectator = db.query(GameSpectator).filter(
        GameSpectator.game_id == game_id,
        GameSpectator.user_id == current_user.id
    ).first() is not None
    
    if not is_player and not is_spectator and game.status != GameStatus.WAITING:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You don't have access to this game"
        )
    
    return game

@router.post("/{game_id}/spectate")
async def spectate_game(
    game_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Join a game as a spectator"""
    game = db.query(Game).filter(Game.id == game_id).first()
    if not game:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Game not found"
        )
    
    # Check if already spectating
    existing = db.query(GameSpectator).filter(
        GameSpectator.game_id == game_id,
        GameSpectator.user_id == current_user.id
    ).first()
    
    if existing:
        return {"message": "Already spectating this game"}
    
    # Check if user is a player
    if current_user.id in [game.player1_id, game.player2_id]:
        return {"message": "You are a player in this game"}
    
    # Add as spectator
    spectator = GameSpectator(
        game_id=game_id,
        user_id=current_user.id
    )
    db.add(spectator)
    db.commit()
    
    return {"message": "Now spectating the game"}

@router.post("/{game_id}/complete")
async def complete_game(
    game_id: str,
    winner_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Complete a game and distribute winnings"""
    game = db.query(Game).filter(Game.id == game_id).first()
    if not game:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Game not found"
        )
    
    # Verify the request is from a player
    if current_user.id not in [game.player1_id, game.player2_id]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only players can complete the game"
        )
    
    # Verify winner is a player
    if winner_id not in [game.player1_id, game.player2_id]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid winner"
        )
    
    # Update game status
    game.status = GameStatus.COMPLETED
    game.winner_id = winner_id
    game.completed_at = datetime.utcnow()
    
    # Distribute winnings
    wallet_service = WalletService(db)
    winner = db.query(User).filter(User.id == winner_id).first()
    
    # Winner gets 95% of the pot (5% house fee)
    winnings = game.total_pot * 0.95
    wallet_service.process_win(
        user=winner,
        amount=winnings,
        game_id=game.id,
        description=f"Won {game.game_type.value} game"
    )
    
    db.commit()
    
    return {"message": "Game completed", "winner_id": winner_id, "winnings": winnings}