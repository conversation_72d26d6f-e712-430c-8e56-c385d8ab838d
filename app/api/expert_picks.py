from fastapi import APIRout<PERSON>, Depends, HTTPException, Query
from sqlalchemy.orm import Session, joinedload
from typing import List, Optional
from datetime import datetime, timedelta
import secrets
import string

from app.database.db import get_db
from app.models.expert_picks import (
    ExpertProfile, ExpertPick, PickPack, PickPackItem, 
    PickPurchase, PickPackPurchase, ExpertFollower,
    PickStatus, PickType
)
from app.models.user import User
from app.schemas.expert_picks import (
    ExpertProfileCreate, ExpertProfileUpdate, ExpertProfile as ExpertProfileSchema,
    ExpertPickCreate, ExpertPickUpdate, ExpertPick as ExpertPickSchema,
    PickPackCreate, PickPackUpdate, PickPack as PickPackSchema,
    PickPurchaseCreate, PickP<PERSON>chase as PickPurchaseSchema,
    PickPackPurchaseCreate, PickPackPurchase as PickPackPurchaseSchema,
    ExpertFollowerCreate, ExpertFollower as ExpertFollowerSchema,
    ExpertStats
)
from app.core.dependencies import get_current_user
from app.services.wallet import WalletService


router = APIRouter(prefix="/expert-picks", tags=["expert-picks"])


def generate_booking_code(prefix: str = "") -> str:
    """Generate a unique booking code."""
    random_part = ''.join(secrets.choice(string.ascii_uppercase + string.digits) for _ in range(8))
    return f"{prefix}-{random_part}" if prefix else random_part


def generate_universal_code() -> str:
    """Generate a universal booking code."""
    return f"BK-{''.join(secrets.choice(string.digits) for _ in range(10))}"


@router.post("/profile", response_model=ExpertProfileSchema)
async def create_expert_profile(
    profile: ExpertProfileCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create or update expert profile for current user."""
    try:
        existing = db.query(ExpertProfile).filter(ExpertProfile.user_id == current_user.id).first()
        
        if existing:
            existing.is_expert = profile.is_expert
            db.commit()
            db.refresh(existing)
            return existing
        
        db_profile = ExpertProfile(
            user_id=current_user.id,
            is_expert=profile.is_expert
        )
        db.add(db_profile)
        db.commit()
        db.refresh(db_profile)
        return db_profile
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Failed to create expert profile: {str(e)}")


@router.get("/profile/{user_id}", response_model=ExpertProfileSchema)
async def get_expert_profile(
    user_id: str,
    db: Session = Depends(get_db)
):
    """Get expert profile by user ID."""
    profile = db.query(ExpertProfile).filter(ExpertProfile.user_id == user_id).first()
    if not profile:
        raise HTTPException(status_code=404, detail="Expert profile not found")
    return profile


@router.get("/stats", response_model=ExpertStats)
async def get_expert_stats(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get current user's expert statistics."""
    profile = db.query(ExpertProfile).filter(ExpertProfile.user_id == current_user.id).first()
    if not profile:
        raise HTTPException(status_code=404, detail="Expert profile not found")
    
    return ExpertStats(
        total_picks=profile.total_picks,
        win_rate=profile.win_rate,
        monthly_revenue=profile.monthly_revenue,
        followers=profile.followers_count,
        avg_odds=profile.avg_odds,
        roi=profile.roi,
        streak=profile.win_streak,
        rating=profile.rating
    )


@router.post("/picks", response_model=ExpertPickSchema)
async def create_pick(
    pick: ExpertPickCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create a new expert pick."""
    # Ensure user has expert profile
    profile = db.query(ExpertProfile).filter(ExpertProfile.user_id == current_user.id).first()
    if not profile or not profile.is_expert:
        raise HTTPException(status_code=403, detail="Expert access required")
    
    # Generate booking codes
    sport_prefix = pick.sport[:3].upper()
    booking_code = generate_booking_code(sport_prefix)
    universal_code = generate_universal_code()
    
    db_pick = ExpertPick(
        expert_id=profile.id,
        booking_code=booking_code,
        universal_code=universal_code,
        status=PickStatus.DRAFT,
        **pick.dict()
    )
    
    db.add(db_pick)
    db.commit()
    db.refresh(db_pick)
    
    # Update expert stats
    profile.total_picks += 1
    db.commit()
    
    return db_pick


@router.put("/picks/{pick_id}", response_model=ExpertPickSchema)
async def update_pick(
    pick_id: int,
    pick_update: ExpertPickUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Update an expert pick."""
    pick = db.query(ExpertPick).join(ExpertProfile).filter(
        ExpertPick.id == pick_id,
        ExpertProfile.user_id == current_user.id
    ).first()
    
    if not pick:
        raise HTTPException(status_code=404, detail="Pick not found or access denied")
    
    update_data = pick_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(pick, field, value)
    
    db.commit()
    db.refresh(pick)
    return pick


@router.get("/picks", response_model=List[ExpertPickSchema])
async def get_picks(
    expert_id: Optional[int] = None,
    sport: Optional[str] = None,
    status: Optional[PickStatus] = None,
    limit: int = Query(20, le=100),
    offset: int = 0,
    db: Session = Depends(get_db)
):
    """Get expert picks with filters."""
    query = db.query(ExpertPick)
    
    if expert_id:
        query = query.filter(ExpertPick.expert_id == expert_id)
    if sport:
        query = query.filter(ExpertPick.sport == sport)
    if status:
        query = query.filter(ExpertPick.status == status)
    
    # Only show active picks to non-owners
    query = query.filter(ExpertPick.status == PickStatus.ACTIVE)
    
    picks = query.order_by(ExpertPick.created_at.desc()).offset(offset).limit(limit).all()
    return picks


@router.get("/picks/{pick_id}", response_model=ExpertPickSchema)
async def get_pick(
    pick_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get a specific pick (full details only for purchasers)."""
    pick = db.query(ExpertPick).filter(ExpertPick.id == pick_id).first()
    if not pick:
        raise HTTPException(status_code=404, detail="Pick not found")
    
    # Check if user has purchased this pick
    purchase = db.query(PickPurchase).filter(
        PickPurchase.user_id == current_user.id,
        PickPurchase.pick_id == pick_id
    ).first()
    
    # Check if user is the expert
    is_expert = db.query(ExpertProfile).filter(
        ExpertProfile.id == pick.expert_id,
        ExpertProfile.user_id == current_user.id
    ).first() is not None
    
    if not purchase and not is_expert:
        # Hide sensitive information
        pick.analysis = "Purchase to view full analysis"
        pick.booking_code = "****"
        pick.universal_code = "****"
    
    return pick


@router.post("/picks/{pick_id}/purchase", response_model=PickPurchaseSchema)
async def purchase_pick(
    pick_id: int,
    purchase: PickPurchaseCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Purchase an expert pick."""
    pick = db.query(ExpertPick).filter(
        ExpertPick.id == pick_id,
        ExpertPick.status == PickStatus.ACTIVE
    ).first()
    
    if not pick:
        raise HTTPException(status_code=404, detail="Pick not found or not available")
    
    # Check if already purchased
    existing = db.query(PickPurchase).filter(
        PickPurchase.user_id == current_user.id,
        PickPurchase.pick_id == pick_id
    ).first()
    
    if existing:
        raise HTTPException(status_code=400, detail="Pick already purchased")
    
    # Determine price
    price = pick.vip_price if purchase.is_vip and pick.vip_price else pick.price
    
    # Process payment through wallet
    wallet_service = WalletService(db)
    try:
        wallet_service.process_transaction(
            user_id=current_user.id,
            amount=-price,
            transaction_type="pick_purchase",
            description=f"Purchase pick: {pick.match}"
        )
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    
    # Create purchase record
    db_purchase = PickPurchase(
        user_id=current_user.id,
        pick_id=pick_id,
        price_paid=price,
        is_vip=purchase.is_vip
    )
    
    db.add(db_purchase)
    
    # Update pick sales count
    pick.sales_count += 1
    
    # Update expert revenue
    expert_profile = db.query(ExpertProfile).filter(ExpertProfile.id == pick.expert_id).first()
    expert_profile.monthly_revenue += price
    expert_profile.total_revenue += price
    
    # Transfer funds to expert
    expert_user = db.query(User).filter(User.id == expert_profile.user_id).first()
    wallet_service.process_transaction(
        user_id=expert_user.id,
        amount=price * 0.8,  # 80% to expert, 20% platform fee
        transaction_type="pick_sale",
        description=f"Sale of pick: {pick.match}"
    )
    
    db.commit()
    db.refresh(db_purchase)
    
    # Load full pick details
    db_purchase.pick = pick
    return db_purchase


@router.post("/packs", response_model=PickPackSchema)
async def create_pack(
    pack: PickPackCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create a new pick pack."""
    profile = db.query(ExpertProfile).filter(ExpertProfile.user_id == current_user.id).first()
    if not profile or not profile.is_expert:
        raise HTTPException(status_code=403, detail="Expert access required")
    
    # Verify all picks belong to the expert
    picks = db.query(ExpertPick).filter(
        ExpertPick.id.in_(pack.pick_ids),
        ExpertPick.expert_id == profile.id
    ).all()
    
    if len(picks) != len(pack.pick_ids):
        raise HTTPException(status_code=400, detail="Invalid pick IDs")
    
    pack_data = pack.dict(exclude={'pick_ids'})
    db_pack = PickPack(expert_id=profile.id, **pack_data)
    db.add(db_pack)
    db.flush()
    
    # Add picks to pack
    for pick_id in pack.pick_ids:
        pack_item = PickPackItem(pack_id=db_pack.id, pick_id=pick_id)
        db.add(pack_item)
    
    db.commit()
    db.refresh(db_pack)
    
    # Load picks
    db_pack.picks = picks
    return db_pack


@router.get("/packs", response_model=List[PickPackSchema])
async def get_packs(
    expert_id: Optional[int] = None,
    limit: int = Query(20, le=100),
    offset: int = 0,
    db: Session = Depends(get_db)
):
    """Get pick packs."""
    query = db.query(PickPack).options(joinedload(PickPack.picks))
    
    if expert_id:
        query = query.filter(PickPack.expert_id == expert_id)
    
    # Only show valid packs
    query = query.filter(PickPack.valid_until > datetime.utcnow())
    
    packs = query.order_by(PickPack.created_at.desc()).offset(offset).limit(limit).all()
    return packs


@router.post("/follow", response_model=ExpertFollowerSchema)
async def follow_expert(
    follow: ExpertFollowerCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Follow an expert."""
    # Get follower's expert profile
    follower_profile = db.query(ExpertProfile).filter(
        ExpertProfile.user_id == current_user.id
    ).first()
    
    if not follower_profile:
        # Create basic profile for follower
        follower_profile = ExpertProfile(user_id=current_user.id)
        db.add(follower_profile)
        db.flush()
    
    # Check if already following
    existing = db.query(ExpertFollower).filter(
        ExpertFollower.expert_id == follow.expert_id,
        ExpertFollower.follower_id == follower_profile.id
    ).first()
    
    if existing:
        raise HTTPException(status_code=400, detail="Already following this expert")
    
    # Create follow relationship
    db_follow = ExpertFollower(
        expert_id=follow.expert_id,
        follower_id=follower_profile.id
    )
    
    db.add(db_follow)
    
    # Update follower count
    expert = db.query(ExpertProfile).filter(ExpertProfile.id == follow.expert_id).first()
    if expert:
        expert.followers_count += 1
    
    db.commit()
    db.refresh(db_follow)
    return db_follow


@router.delete("/follow/{expert_id}")
async def unfollow_expert(
    expert_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Unfollow an expert."""
    follower_profile = db.query(ExpertProfile).filter(
        ExpertProfile.user_id == current_user.id
    ).first()
    
    if not follower_profile:
        raise HTTPException(status_code=404, detail="Profile not found")
    
    follow = db.query(ExpertFollower).filter(
        ExpertFollower.expert_id == expert_id,
        ExpertFollower.follower_id == follower_profile.id
    ).first()
    
    if not follow:
        raise HTTPException(status_code=404, detail="Not following this expert")
    
    db.delete(follow)
    
    # Update follower count
    expert = db.query(ExpertProfile).filter(ExpertProfile.id == expert_id).first()
    if expert:
        expert.followers_count -= 1
    
    db.commit()
    return {"message": "Unfollowed successfully"}


@router.get("/my-picks", response_model=List[ExpertPickSchema])
async def get_my_picks(
    status: Optional[PickStatus] = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get current user's expert picks."""
    profile = db.query(ExpertProfile).filter(ExpertProfile.user_id == current_user.id).first()
    if not profile:
        return []
    
    query = db.query(ExpertPick).filter(ExpertPick.expert_id == profile.id)
    
    if status:
        query = query.filter(ExpertPick.status == status)
    
    picks = query.order_by(ExpertPick.created_at.desc()).all()
    return picks


@router.get("/purchased", response_model=List[PickPurchaseSchema])
async def get_purchased_picks(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get picks purchased by current user."""
    purchases = db.query(PickPurchase).options(
        joinedload(PickPurchase.pick)
    ).filter(
        PickPurchase.user_id == current_user.id
    ).order_by(PickPurchase.purchased_at.desc()).all()
    
    return purchases