from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import Optional
from datetime import datetime
import uuid
import random

from app.database.db import get_db
from app.models.user import User
from app.models.payment import Transaction
from app.auth.jwt import get_current_user
from pydantic import BaseModel, Field

router = APIRouter()

# Mock payment processor response times (in seconds)
PAYMENT_PROCESSING_TIME = 2

# Pydantic models for payment gateway
class PaymentMethod(BaseModel):
    type: str  # card, bank_transfer, crypto
    last_four: Optional[str] = None
    card_brand: Optional[str] = None
    bank_name: Optional[str] = None
    crypto_address: Optional[str] = None

class DepositRequest(BaseModel):
    amount: float = Field(gt=0)
    payment_method: PaymentMethod
    description: Optional[str] = "Account deposit"

class WithdrawRequest(BaseModel):
    amount: float = Field(gt=0)
    payment_method: PaymentMethod
    description: Optional[str] = "Account withdrawal"

class PaymentResponse(BaseModel):
    transaction_id: str
    status: str  # pending, completed, failed
    amount: float
    processing_fee: float
    net_amount: float
    created_at: datetime
    payment_method: PaymentMethod
    message: str

# Mock payment processor
class MockPaymentProcessor:
    @staticmethod
    def process_deposit(amount: float, payment_method: PaymentMethod) -> dict:
        """Simulate payment processing with 95% success rate"""
        # Simulate processing time
        import time
        time.sleep(PAYMENT_PROCESSING_TIME)
        
        # Random failure for testing
        if random.random() < 0.05:  # 5% failure rate
            return {
                "success": False,
                "error": "Payment declined by issuer",
                "code": "PAYMENT_DECLINED"
            }
        
        # Calculate processing fee (2.5% for cards, 1% for bank, 0.5% for crypto)
        fee_rate = 0.025 if payment_method.type == "card" else 0.01 if payment_method.type == "bank_transfer" else 0.005
        processing_fee = round(amount * fee_rate, 2)
        
        return {
            "success": True,
            "transaction_id": f"dep_{uuid.uuid4().hex[:8]}",
            "processing_fee": processing_fee,
            "net_amount": amount - processing_fee
        }
    
    @staticmethod
    def process_withdrawal(amount: float, payment_method: PaymentMethod) -> dict:
        """Simulate withdrawal processing with 98% success rate"""
        # Simulate processing time
        import time
        time.sleep(PAYMENT_PROCESSING_TIME)
        
        # Random failure for testing
        if random.random() < 0.02:  # 2% failure rate
            return {
                "success": False,
                "error": "Withdrawal temporarily unavailable",
                "code": "WITHDRAWAL_UNAVAILABLE"
            }
        
        # Calculate processing fee ($2 flat fee for withdrawals)
        processing_fee = 2.0
        
        return {
            "success": True,
            "transaction_id": f"wd_{uuid.uuid4().hex[:8]}",
            "processing_fee": processing_fee,
            "net_amount": amount + processing_fee  # User pays the fee
        }

@router.post("/deposit", response_model=PaymentResponse)
async def process_deposit(
    request: DepositRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Process a deposit through mock payment gateway"""
    
    # Process payment through mock gateway
    result = MockPaymentProcessor.process_deposit(request.amount, request.payment_method)
    
    if not result["success"]:
        raise HTTPException(
            status_code=400,
            detail=result["error"]
        )
    
    # Create transaction record
    transaction = Transaction(
        user_id=current_user.id,
        type="deposit",
        amount=result["net_amount"],
        status="completed",
        description=request.description,
        gateway_transaction_id=result["transaction_id"],
        processing_fee=result["processing_fee"]
    )
    db.add(transaction)
    
    # Update user balance
    current_user.balance += result["net_amount"]
    
    db.commit()
    db.refresh(transaction)
    
    return PaymentResponse(
        transaction_id=transaction.id,
        status="completed",
        amount=request.amount,
        processing_fee=result["processing_fee"],
        net_amount=result["net_amount"],
        created_at=transaction.created_at,
        payment_method=request.payment_method,
        message=f"Deposit successful. ${result['net_amount']} added to your account."
    )

@router.post("/withdraw", response_model=PaymentResponse)
async def process_withdrawal(
    request: WithdrawRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Process a withdrawal through mock payment gateway"""
    
    # Check if user has sufficient balance (including fee)
    total_amount = request.amount + 2.0  # $2 withdrawal fee
    if current_user.balance < total_amount:
        raise HTTPException(
            status_code=400,
            detail=f"Insufficient balance. You need ${total_amount} (including $2 fee)"
        )
    
    # Process withdrawal through mock gateway
    result = MockPaymentProcessor.process_withdrawal(request.amount, request.payment_method)
    
    if not result["success"]:
        raise HTTPException(
            status_code=400,
            detail=result["error"]
        )
    
    # Create transaction record
    transaction = Transaction(
        user_id=current_user.id,
        type="withdrawal",
        amount=request.amount,
        status="completed",
        description=request.description,
        gateway_transaction_id=result["transaction_id"],
        processing_fee=result["processing_fee"]
    )
    db.add(transaction)
    
    # Update user balance (deduct amount + fee)
    current_user.balance -= total_amount
    
    db.commit()
    db.refresh(transaction)
    
    return PaymentResponse(
        transaction_id=transaction.id,
        status="completed",
        amount=request.amount,
        processing_fee=result["processing_fee"],
        net_amount=request.amount,  # User receives the full amount
        created_at=transaction.created_at,
        payment_method=request.payment_method,
        message=f"Withdrawal successful. ${request.amount} will be sent to your {request.payment_method.type}."
    )

@router.get("/payment-methods")
def get_payment_methods(current_user: User = Depends(get_current_user)):
    """Get available payment methods for the user"""
    # Mock saved payment methods
    return {
        "payment_methods": [
            {
                "id": "pm_1",
                "type": "card",
                "last_four": "4242",
                "card_brand": "Visa",
                "is_default": True
            },
            {
                "id": "pm_2",
                "type": "bank_transfer",
                "bank_name": "Chase Bank",
                "last_four": "6789",
                "is_default": False
            },
            {
                "id": "pm_3",
                "type": "crypto",
                "crypto_address": "1A2B3C...XYZ",
                "currency": "BTC",
                "is_default": False
            }
        ]
    }

@router.get("/transaction/{transaction_id}")
def get_transaction_status(
    transaction_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get status of a specific transaction"""
    transaction = db.query(Transaction).filter(
        Transaction.id == transaction_id,
        Transaction.user_id == current_user.id
    ).first()
    
    if not transaction:
        raise HTTPException(status_code=404, detail="Transaction not found")
    
    return {
        "transaction_id": transaction.id,
        "type": transaction.type,
        "amount": transaction.amount,
        "status": transaction.status,
        "created_at": transaction.created_at,
        "description": transaction.description
    }