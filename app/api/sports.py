from fastapi import APIRouter, Query, Depends, HTTPException, Request
from typing import List, Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel

from app.services.sports_api import sports_api
from app.auth.jwt import get_current_user, get_current_active_user
from app.models.user import User
from app.core.rate_limit import limiter, RateLimits

router = APIRouter()

# Response models
class Sport(BaseModel):
    id: str
    name: str
    icon: str
    active: bool = True

class Team(BaseModel):
    name: str
    logo: Optional[str] = None

class Odds(BaseModel):
    home: float
    away: float
    draw: Optional[float] = None

class SportEvent(BaseModel):
    id: str
    sport: str
    league: str
    home_team: Team
    away_team: Team
    start_time: datetime
    status: str  # "upcoming", "live", "finished"
    odds: Odds
    minute: Optional[str] = None  # For live games
    score: Optional[str] = None   # For live games
    viewers: Optional[int] = None # For live games
    featured: bool = False
    trending: bool = False

# Test endpoint to verify API connection
@router.get("/api-test")
@limiter.limit("10 per minute")
async def test_sports_api(request: Request):
    """Test the sports API connection and return raw response."""
    try:
        # Try to get sports data
        sports_data = await sports_api.get_sports()
        
        if sports_data is None:
            return {
                "status": "error",
                "message": "Failed to connect to sports API",
                "api_configured": bool(sports_api.api_key),
                "api_url": sports_api.base_url
            }
        
        return {
            "status": "success",
            "message": "Successfully connected to sports API",
            "data": sports_data,
            "api_configured": bool(sports_api.api_key),
            "api_url": sports_api.base_url
        }
    except Exception as e:
        return {
            "status": "error",
            "message": str(e),
            "api_configured": bool(sports_api.api_key),
            "api_url": sports_api.base_url
        }

# Real API endpoints
@router.get("/sports-live", response_model=List[Dict[str, Any]])
@limiter.limit("30 per minute")
async def get_sports_from_api(request: Request):
    """Get all available sports from the external API."""
    sports_data = await sports_api.get_sports()
    
    if not sports_data:
        # Fallback to mock data if API is not available
        return get_mock_sports()
    
    return sports_data

@router.get("/leagues-live/{sport_id}")
@limiter.limit("30 per minute")
async def get_leagues_from_api(
    request: Request,
    sport_id: str
):
    """Get leagues for a specific sport from the external API."""
    leagues_data = await sports_api.get_leagues(sport_id)
    
    if not leagues_data:
        raise HTTPException(status_code=404, detail="Sport not found or API unavailable")
    
    return leagues_data

@router.get("/games-live/upcoming")
@limiter.limit("30 per minute")
async def get_upcoming_games_from_api(
    request: Request,
    sport_id: Optional[str] = None,
    league_id: Optional[str] = None,
    limit: int = Query(20, ge=1, le=100),
    current_user: User = Depends(get_current_active_user)
):
    """Get upcoming games from the external API."""
    games_data = await sports_api.get_upcoming_games(
        sport_id=sport_id,
        league_id=league_id,
        limit=limit
    )
    
    if not games_data:
        # Return empty list if API is not available
        return []
    
    return games_data

@router.get("/games-live/{game_id}/odds")
@limiter.limit("60 per minute")
async def get_game_odds_from_api(
    request: Request,
    game_id: str,
    current_user: User = Depends(get_current_active_user)
):
    """Get odds for a specific game from the external API."""
    odds_data = await sports_api.get_game_odds(game_id)
    
    if not odds_data:
        raise HTTPException(status_code=404, detail="Game not found or API unavailable")
    
    return odds_data

@router.get("/games-live/live")
@limiter.limit("30 per minute")
async def get_live_games_from_api(
    request: Request,
    sport_id: Optional[str] = None,
    current_user: User = Depends(get_current_active_user)
):
    """Get currently live games from the external API."""
    live_games = await sports_api.get_live_games(sport_id=sport_id)
    
    if not live_games:
        return []
    
    return live_games

# Original mock endpoints (keeping for backward compatibility)
@router.get("/sports", response_model=List[Sport])
async def get_sports():
    """Get all available sports (mock data)."""
    return get_mock_sports()

def get_mock_sports():
    """Return mock sports data."""
    return [
        Sport(id="football", name="Football", icon="🏈"),
        Sport(id="basketball", name="Basketball", icon="🏀"),
        Sport(id="baseball", name="Baseball", icon="⚾"),
        Sport(id="soccer", name="Soccer", icon="⚽"),
        Sport(id="mma", name="MMA / UFC", icon="🥊"),
        Sport(id="tennis", name="Tennis", icon="🎾"),
        Sport(id="hockey", name="Hockey", icon="🏒"),
        Sport(id="motorsports", name="Motor Sports", icon="🏎️"),
        Sport(id="boxing", name="Boxing", icon="🥊"),
    ]

@router.get("/events", response_model=List[SportEvent])
async def get_events(
    sport: Optional[str] = None,
    status: Optional[str] = Query(None, regex="^(upcoming|live|finished)$"),
    featured: Optional[bool] = None,
    league: Optional[str] = None,
    limit: int = Query(20, ge=1, le=100)
):
    """Get sports events with optional filters (mock data)."""
    # For now, return sample data - replace with real data source
    events = [
        SportEvent(
            id="nfl-game-1",
            sport="football",
            league="NFL",
            home_team=Team(name="Chiefs"),
            away_team=Team(name="Bills"),
            start_time=datetime.now(),
            status="live",
            odds=Odds(home=-110, away=+105),
            minute="Q3 8:45",
            score="21-17",
            viewers=250000,
            featured=True,
            trending=True
        ),
        SportEvent(
            id="nba-game-1",
            sport="basketball",
            league="NBA",
            home_team=Team(name="Lakers"),
            away_team=Team(name="Warriors"),
            start_time=datetime.now(),
            status="upcoming",
            odds=Odds(home=-120, away=+110),
            featured=True
        ),
    ]
    
    # Apply filters
    if sport:
        events = [e for e in events if e.sport == sport]
    if status:
        events = [e for e in events if e.status == status]
    if featured is not None:
        events = [e for e in events if e.featured == featured]
    if league:
        events = [e for e in events if e.league == league]
        
    return events[:limit]

@router.get("/events/{event_id}", response_model=SportEvent)
async def get_event_details(event_id: str):
    """Get details for a specific event (mock data)."""
    # Mock implementation - replace with real data
    return SportEvent(
        id=event_id,
        sport="football",
        league="NFL",
        home_team=Team(name="Chiefs"),
        away_team=Team(name="Bills"),
        start_time=datetime.now(),
        status="live",
        odds=Odds(home=-110, away=+105),
        minute="Q3 8:45",
        score="21-17",
        viewers=250000,
        featured=True,
        trending=True
    )

@router.get("/leagues")
async def get_leagues(sport: Optional[str] = None):
    """Get available leagues (mock data)."""
    leagues = {
        "football": ["NFL", "NCAAF"],
        "basketball": ["NBA", "NCAAB"],
        "baseball": ["MLB"],
        "soccer": ["Premier League", "Champions League", "MLS"],
        "hockey": ["NHL"],
        "tennis": ["ATP", "WTA"],
        "mma": ["UFC"],
    }
    
    if sport:
        return {sport: leagues.get(sport, [])}
    return leagues