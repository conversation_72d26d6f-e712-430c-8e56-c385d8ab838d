from fastapi import APIRouter, Query, Depends
from typing import List, Optional
from pydantic import BaseModel
from sqlalchemy.orm import Session
from sqlalchemy import func, desc

from app.database.db import get_db
from app.models.user import User
from app.models.wallet import Wallet
from app.core.exceptions import NotFoundError

router = APIRouter()

class LeaderboardEntry(BaseModel):
    id: str
    username: str
    winnings: float
    win_rate: float
    total_games: int
    position: int
    is_current_user: bool = False
    preferred_category: Optional[str] = None

class LeaderboardResponse(BaseModel):
    entries: List[LeaderboardEntry]
    user_position: Optional[int] = None
    period: str

@router.get("/", response_model=LeaderboardResponse)
async def get_leaderboard(
    period: str = Query("all-time", regex="^(all-time|monthly|weekly|daily)$"),
    limit: int = Query(10, ge=1, le=100),
    current_user_id: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """Get leaderboard entries"""
    
    # Query based on period
    query = db.query(
        User.id,
        User.username,
        func.coalesce(Wallet.balance, 0).label('winnings'),
        func.count(User.id).label('total_games'),  # Would need actual game data
        func.avg(1).label('win_rate')  # Placeholder - would calculate from actual wins
    ).outerjoin(Wallet, User.id == Wallet.user_id).filter(User.is_active == True)
    
    if period == "monthly":
        # Filter by current month
        pass
    elif period == "weekly":
        # Filter by current week
        pass
    elif period == "daily":
        # Filter by today
        pass
    
    # Order by winnings
    query = query.group_by(User.id).order_by(desc('winnings')).limit(limit)
    
    results = query.all()
    
    entries = []
    user_position = None
    
    for idx, result in enumerate(results):
        position = idx + 1
        entry = LeaderboardEntry(
            id=result.id,
            username=result.username,
            winnings=float(result.winnings) if result.winnings else 0.0,
            win_rate=75.0,  # Placeholder - would calculate from actual data
            total_games=50,  # Placeholder
            position=position,
            is_current_user=(result.id == current_user_id) if current_user_id else False,
            preferred_category="Sports"  # Placeholder
        )
        entries.append(entry)
        
        if current_user_id and result.id == current_user_id:
            user_position = position
    
    # If current user not in top entries, find their position
    if current_user_id and not user_position:
        user_wallet = db.query(Wallet).filter(Wallet.user_id == current_user_id).first()
        user_balance = user_wallet.balance if user_wallet else 0
        
        user_rank = db.query(func.count(User.id)).outerjoin(
            Wallet, User.id == Wallet.user_id
        ).filter(
            func.coalesce(Wallet.balance, 0) > user_balance
        ).scalar()
        user_position = user_rank + 1
    
    return LeaderboardResponse(
        entries=entries,
        user_position=user_position,
        period=period
    )

@router.get("/stats")
async def get_user_stats(
    user_id: str,
    db: Session = Depends(get_db)
):
    """Get detailed stats for a specific user"""
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise NotFoundError("User not found")
    
    return {
        "user_id": user.id,
        "username": user.username,
        "total_wins": 0,  # Would need actual game data
        "total_games": 0,
        "win_rate": 0,
        "current_streak": 0,
        "best_streak": 0,
        "total_winnings": user.wallet.balance if user.wallet else 0,
        "monthly_winnings": 0,
        "weekly_winnings": 0
    }