from fastapi import APIRouter, Depends, HTTPException, UploadFile, File
from sqlalchemy.orm import Session
from datetime import datetime
import os
import uuid
from typing import Optional, List

from app.database.db import get_db
from app.models.user import User
from app.auth.jwt import get_current_user
from app.schemas.user import UserKYCUpdate, UserDetailResponse
# Remove unused import

router = APIRouter()

# Configure upload directory
UPLOAD_DIR = "uploads/kyc"
os.makedirs(UPLOAD_DIR, exist_ok=True)
ALLOWED_EXTENSIONS = {'jpg', 'jpeg', 'png', 'pdf'}

def allowed_file(filename: str) -> bool:
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def require_admin(current_user: User = Depends(get_current_user)):
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=403,
            detail="Admin privileges required"
        )
    return current_user

@router.post("/upload-id", response_model=dict)
async def upload_id_card(
    file: UploadFile = File(...),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Upload ID card for KYC verification"""
    if not allowed_file(file.filename):
        raise HTTPException(
            status_code=400,
            detail=f"Invalid file type. Allowed types: {', '.join(ALLOWED_EXTENSIONS)}"
        )
    
    # Generate unique filename
    file_extension = file.filename.rsplit('.', 1)[1].lower()
    new_filename = f"{current_user.id}_{uuid.uuid4()}.{file_extension}"
    file_path = os.path.join(UPLOAD_DIR, new_filename)
    
    # Save file
    try:
        contents = await file.read()
        with open(file_path, 'wb') as f:
            f.write(contents)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"File upload failed: {str(e)}")
    
    # Update user record
    current_user.id_card_url = file_path
    current_user.kyc_submitted_at = datetime.utcnow()
    db.commit()
    
    return {
        "message": "ID card uploaded successfully",
        "file_path": file_path
    }

@router.put("/update", response_model=UserDetailResponse)
def update_kyc_info(
    kyc_data: UserKYCUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Update KYC information (real name)"""
    current_user.real_name = kyc_data.real_name
    if kyc_data.id_card_url:
        current_user.id_card_url = kyc_data.id_card_url
    
    current_user.kyc_submitted_at = datetime.utcnow()
    current_user.kyc_status = "pending"  
    
    db.commit()
    db.refresh(current_user)
    
    return current_user

@router.get("/status", response_model=dict)
def get_kyc_status(current_user: User = Depends(get_current_user)):
    """Get current KYC status"""
    return {
        "kyc_status": current_user.kyc_status,
        "real_name": current_user.real_name,
        "id_card_uploaded": bool(current_user.id_card_url),
        "submitted_at": current_user.kyc_submitted_at,
        "verified_at": current_user.kyc_verified_at
    }

# Admin endpoints
@router.get("/admin/pending", response_model=List[UserDetailResponse])
def get_all_kyc_submissions(
    status: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_admin)
):
    """Get all users for KYC verification (Admin only)"""
    query = db.query(User)
    if status:
        query = query.filter(User.kyc_status == status)
    else:
        # Return all users, not just pending
        query = query.filter(User.real_name.isnot(None))
    
    users = query.all()
    return users

@router.put("/admin/verify/{user_id}", response_model=dict)
def verify_user_kyc(
    user_id: str,
    approved: bool = True,
    reason: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_admin)
):
    """Admin endpoint to approve KYC"""
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    
    user.kyc_status = "approved"
    user.kyc_verified_at = datetime.utcnow()
    
    db.commit()
    
    return {
        "message": "KYC approved",
        "user_id": user_id,
        "status": user.kyc_status
    }

@router.put("/admin/reject/{user_id}", response_model=dict)
def reject_user_kyc(
    user_id: str,
    reason: str = "Documents not clear",
    db: Session = Depends(get_db),
    current_user: User = Depends(require_admin)
):
    """Admin endpoint to reject KYC"""
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    
    user.kyc_status = "rejected"
    user.kyc_verified_at = None
    
    db.commit()
    
    return {
        "message": "KYC rejected",
        "user_id": user_id,
        "reason": reason,
        "status": user.kyc_status
    }