from fastapi import APIRouter, Depends, HTTPException, Query, Path
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func
from typing import List, Optional
from datetime import datetime, timezone

from app.database.db import get_db
from app.models.user import User
from app.models.marketplace import Bet, BetParticipation, BetStatus, BetResult, BetType, BetVisibility
from app.schemas.bet import (
    BetCreate, BetUpdate, CounterBetCreate, BetResponse, 
    BetDetailResponse, BetListResponse
)
from app.auth.jwt import get_current_user

router = APIRouter()

# Create a new bet
@router.post("/", response_model=BetResponse)
async def create_bet(
    bet_data: BetCreate, 
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Create a new bet in the marketplace
    """
    # Check user has sufficient balance
    if current_user.balance < bet_data.total_stake:
        raise HTTPException(status_code=400, detail="Insufficient balance")
    
    # Create new bet and creator's participation
    new_bet = Bet(
        creator_id=current_user.id,
        title=bet_data.title,
        description=bet_data.description,
        category=bet_data.category,
        subcategory=bet_data.subcategory,
        bet_type=bet_data.bet_type,
        visibility=bet_data.visibility,
        total_stake=bet_data.total_stake,
        min_counter_stake=bet_data.min_counter_stake,
        expiry_time=bet_data.expiry_time,
        settlement_source=bet_data.settlement_source,
        status=BetStatus.OPEN,
        current_stake=0  # Will be updated after participation is created
    )
    
    db.add(new_bet)
    db.flush()  # Flush to get the ID without committing
    
    # Create creator's participation
    participation = BetParticipation(
        bet_id=new_bet.id,
        user_id=current_user.id,
        position="for",  # Creator is always supporting the bet
        stake_amount=bet_data.total_stake,
        is_creator=True
    )
    
    db.add(participation)
    
    # Update bet's current stake
    new_bet.current_stake = bet_data.total_stake
    
    # Deduct from user's balance
    current_user.balance -= bet_data.total_stake
    
    db.commit()
    db.refresh(new_bet)
    
    return new_bet

# Get bet by ID
@router.get("/{bet_id}", response_model=BetDetailResponse)
async def get_bet(
    bet_id: str = Path(...),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get detailed information about a specific bet
    """
    bet = db.query(Bet).filter(Bet.id == bet_id).first()
    if not bet:
        raise HTTPException(status_code=404, detail="Bet not found")
    
    # If bet is private, check that user is creator or participant
    if bet.visibility == BetVisibility.PRIVATE:
        participation = db.query(BetParticipation).filter(
            and_(
                BetParticipation.bet_id == bet_id,
                BetParticipation.user_id == current_user.id
            )
        ).first()
        
        if not participation and bet.creator_id != current_user.id:
            raise HTTPException(status_code=403, detail="You don't have access to this bet")
    
    # Get participations
    participations = db.query(BetParticipation).filter(BetParticipation.bet_id == bet_id).all()
    
    return BetDetailResponse(
        **{k: v for k, v in bet.__dict__.items() if not k.startswith('_')},
        participations=participations
    )

# List bets with filters
@router.get("/", response_model=BetListResponse)
async def list_bets(
    status: Optional[str] = Query(None, description="Filter by bet status"),
    category: Optional[str] = Query(None, description="Filter by category"),
    subcategory: Optional[str] = Query(None, description="Filter by subcategory"),
    min_stake: Optional[float] = Query(None, description="Filter by minimum total stake"),
    max_stake: Optional[float] = Query(None, description="Filter by maximum total stake"),
    only_mine: bool = Query(False, description="Show only my bets (created or participated)"),
    page: int = Query(1, gt=0, description="Page number"),
    size: int = Query(10, gt=0, le=100, description="Page size"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    List bets with various filters
    """
    # Build query
    query = db.query(Bet)
    
    # Only show public bets unless the user is the creator or participant
    if not only_mine:
        query = query.filter(Bet.visibility == BetVisibility.PUBLIC)
    else:
        # Get bet IDs where user is a participant
        participation_bet_ids = db.query(BetParticipation.bet_id).filter(
            BetParticipation.user_id == current_user.id
        ).all()
        participation_bet_ids = [bid[0] for bid in participation_bet_ids]
        
        # Show bets where user is creator or participant
        query = query.filter(
            or_(
                Bet.creator_id == current_user.id,
                Bet.id.in_(participation_bet_ids)
            )
        )
    
    # Apply filters
    if status:
        query = query.filter(Bet.status == status)
    
    if category:
        query = query.filter(Bet.category == category)
        
    if subcategory:
        query = query.filter(Bet.subcategory == subcategory)
    
    if min_stake is not None:
        query = query.filter(Bet.total_stake >= min_stake)
        
    if max_stake is not None:
        query = query.filter(Bet.total_stake <= max_stake)
    
    # Count total for pagination
    total = query.count()
    
    # Apply pagination
    query = query.offset((page - 1) * size).limit(size)
    
    # Get results
    items = query.all()
    
    return {
        "items": items,
        "total": total,
        "page": page,
        "size": size
    }

# Counter a bet (join the opposing side)
@router.post("/{bet_id}/counter", response_model=BetResponse)
async def counter_bet(
    counter_data: CounterBetCreate,
    bet_id: str = Path(...),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Counter an existing bet by staking on the opposing side
    """
    # Check that the bet exists and is open for counters
    bet = db.query(Bet).filter(Bet.id == bet_id).first()
    if not bet:
        raise HTTPException(status_code=404, detail="Bet not found")
    
    if bet.status not in [BetStatus.OPEN, BetStatus.PARTIALLY_FILLED]:
        raise HTTPException(status_code=400, detail="This bet is not open for counters")
    
    # Check that the bet hasn't expired
    if bet.expiry_time and bet.expiry_time < datetime.now(timezone.utc):
        raise HTTPException(status_code=400, detail="This bet has expired")
    
    # Calculate how much more stake is needed to fill the bet
    remaining_stake = bet.total_stake - bet.current_stake
    
    # Validate the counter amount
    if counter_data.amount < bet.min_counter_stake:
        raise HTTPException(
            status_code=400, 
            detail=f"Counter amount must be at least {bet.min_counter_stake}"
        )
    
    if counter_data.amount > remaining_stake:
        raise HTTPException(
            status_code=400, 
            detail=f"Maximum counter amount is {remaining_stake}"
        )
    
    # Check user has sufficient balance
    if current_user.balance < counter_data.amount:
        raise HTTPException(status_code=400, detail="Insufficient balance")
    
    # Check if user is not the creator
    if bet.creator_id == current_user.id:
        raise HTTPException(status_code=400, detail="You cannot counter your own bet")
    
    # Check if user already has a counter position
    existing_participation = db.query(BetParticipation).filter(
        and_(
            BetParticipation.bet_id == bet_id,
            BetParticipation.user_id == current_user.id
        )
    ).first()
    
    if existing_participation:
        raise HTTPException(status_code=400, detail="You already have a position in this bet")
    
    # Create counter participation
    participation = BetParticipation(
        bet_id=bet_id,
        user_id=current_user.id,
        position=counter_data.position,
        stake_amount=counter_data.amount,
        is_creator=False
    )
    
    db.add(participation)
    
    # Update bet's current stake and status
    bet.current_stake += counter_data.amount
    
    # Check if the bet is now fully filled
    if bet.current_stake >= bet.total_stake:
        bet.status = BetStatus.LOCKED
        bet.locked_at = datetime.now(timezone.utc)
    else:
        bet.status = BetStatus.PARTIALLY_FILLED
    
    # Deduct from user's balance
    current_user.balance -= counter_data.amount
    
    db.commit()
    db.refresh(bet)
    
    return bet

# Admin/creator only - settle a bet with a result
@router.post("/{bet_id}/settle", response_model=BetResponse)
async def settle_bet(
    result: BetResult,
    bet_id: str = Path(...),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Settle a bet with a result (admin or creator only)
    """
    # Check that the bet exists
    bet = db.query(Bet).filter(Bet.id == bet_id).first()
    if not bet:
        raise HTTPException(status_code=404, detail="Bet not found")
    
    # Check user is admin or creator
    if bet.creator_id != current_user.id and not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="Only the creator or admin can settle this bet")
    
    # Check if bet is ready to be settled
    if bet.status != BetStatus.LOCKED:
        raise HTTPException(status_code=400, detail="Only locked bets can be settled")
    
    # Update bet status
    bet.status = BetStatus.COMPLETED
    bet.result = result
    bet.completed_at = datetime.now(timezone.utc)
    
    # Handle payouts
    participations = db.query(BetParticipation).filter(BetParticipation.bet_id == bet_id).all()
    
    # Determine winning position
    winning_position = None
    if result == BetResult.FOR_WIN:
        winning_position = "for"
    elif result == BetResult.AGAINST_WIN:
        winning_position = "against"
    elif result == BetResult.DRAW:
        # On draw, return stakes to all participants
        for p in participations:
            user = db.query(User).filter(User.id == p.user_id).first()
            user.balance += p.stake_amount
            p.is_paid = True
            p.payout_amount = p.stake_amount
    elif result == BetResult.CANCELLED:
        # On cancel, return stakes to all participants
        for p in participations:
            user = db.query(User).filter(User.id == p.user_id).first()
            user.balance += p.stake_amount
            p.is_paid = True
            p.payout_amount = p.stake_amount
    
    # Process winner payouts
    if winning_position:
        # Calculate total stakes
        total_for_stakes = sum(p.stake_amount for p in participations if p.position == "for")
        total_against_stakes = sum(p.stake_amount for p in participations if p.position == "against")
        total_pool = total_for_stakes + total_against_stakes
        
        # Calculate platform fee (2%)
        platform_fee = total_pool * 0.02
        payout_pool = total_pool - platform_fee
        
        # Pay winners
        for p in participations:
            if p.position == winning_position:
                user = db.query(User).filter(User.id == p.user_id).first()
                
                # Calculate payout proportional to stake
                if winning_position == "for":
                    payout_proportion = p.stake_amount / total_for_stakes
                else:  # against
                    payout_proportion = p.stake_amount / total_against_stakes
                
                payout = payout_proportion * payout_pool
                user.balance += payout
                p.is_paid = True
                p.payout_amount = payout
    
    db.commit()
    db.refresh(bet)
    
    return bet

# Get categories for filtering
@router.get("/categories", response_model=List[str])
async def get_categories(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get list of all available bet categories
    """
    categories = db.query(Bet.category).distinct().all()
    return [c[0] for c in categories if c[0]]

# Get subcategories for a specific category
@router.get("/categories/{category}/subcategories", response_model=List[str])
async def get_subcategories(
    category: str = Path(...),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get list of all subcategories for a specific category
    """
    subcategories = db.query(Bet.subcategory).filter(
        and_(
            Bet.category == category,
            Bet.subcategory.isnot(None)
        )
    ).distinct().all()
    
    return [sc[0] for sc in subcategories if sc[0]]