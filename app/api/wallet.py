from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime

from app.database.db import get_db
from app.models.user import User
from app.models.payment import Transaction
from app.auth.jwt import get_current_user
from app.core.exceptions import (
    InsufficientBalanceError,
    PaymentError,
    NotFoundError,
    ValidationError
)

router = APIRouter()

# Request/Response schemas
class BalanceResponse(BaseModel):
    balance: float
    user_id: str
    username: str

class DepositRequest(BaseModel):
    amount: float
    payment_method: str = "mock"

class WithdrawalRequest(BaseModel):
    amount: float
    payment_method: str = "mock"

class TransactionResponse(BaseModel):
    id: str
    type: str
    amount: float
    status: str
    description: Optional[str]
    created_at: datetime
    
    class Config:
        from_attributes = True

class TransactionListResponse(BaseModel):
    transactions: List[TransactionResponse]
    total: int

class BetRequest(BaseModel):
    amount: float
    game_id: str
    description: str = "Bet placed"

class WinRequest(BaseModel):
    amount: float
    game_id: str
    description: str = "Game win"

# Endpoints
@router.get("/balance", response_model=BalanceResponse)
def get_balance(current_user: User = Depends(get_current_user)):
    """Get current user's balance and info"""
    return {
        "balance": current_user.balance,
        "user_id": current_user.id,
        "username": current_user.username
    }

@router.post("/deposit", response_model=BalanceResponse)
def deposit_funds(
    deposit_data: DepositRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Deposit funds to user wallet"""
    if deposit_data.amount <= 0:
        raise HTTPException(status_code=400, detail="Amount must be positive")
    
    # Create transaction record
    transaction = Transaction(
        user_id=current_user.id,
        type="deposit",
        amount=deposit_data.amount,
        status="completed",  # Mock payment always succeeds
        description=f"Deposit via {deposit_data.payment_method}",
        payment_method=deposit_data.payment_method
    )
    
    # Update user balance
    current_user.balance += deposit_data.amount
    
    # Save to database
    db.add(transaction)
    db.commit()
    db.refresh(current_user)
    
    return {
        "balance": current_user.balance,
        "user_id": current_user.id,
        "username": current_user.username
    }

@router.post("/withdraw", response_model=BalanceResponse)
def withdraw_funds(
    withdrawal_data: WithdrawalRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Withdraw funds from user wallet"""
    if withdrawal_data.amount <= 0:
        raise ValidationError("Amount must be positive", details={"amount": withdrawal_data.amount})
    
    if current_user.balance < withdrawal_data.amount:
        raise InsufficientBalanceError(
            required=withdrawal_data.amount,
            available=current_user.balance
        )
    
    # KYC check for withdrawals
    if current_user.kyc_status != "approved":
        raise ValidationError(
            "KYC verification required for withdrawals",
            details={"kyc_status": current_user.kyc_status}
        )
    
    # Create transaction record
    transaction = Transaction(
        user_id=current_user.id,
        type="withdrawal",
        amount=withdrawal_data.amount,
        status="completed",  # Mock payment always succeeds
        description=f"Withdrawal via {withdrawal_data.payment_method}",
        payment_method=withdrawal_data.payment_method
    )
    
    # Update user balance
    current_user.balance -= withdrawal_data.amount
    
    # Save to database
    db.add(transaction)
    db.commit()
    db.refresh(current_user)
    
    return {
        "balance": current_user.balance,
        "user_id": current_user.id,
        "username": current_user.username
    }

@router.post("/bet", response_model=BalanceResponse)
def place_bet(
    bet_data: BetRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Place a bet and deduct from balance"""
    amount = bet_data.amount
    game_id = bet_data.game_id
    description = bet_data.description
    
    if amount <= 0:
        raise ValidationError("Bet amount must be positive", details={"amount": amount})
    
    if current_user.balance < amount:
        raise InsufficientBalanceError(
            required=amount,
            available=current_user.balance
        )
    
    # Create transaction record
    transaction = Transaction(
        user_id=current_user.id,
        type="bet",
        amount=amount,
        status="completed",
        description=description,
        reference_id=game_id
    )
    
    # Update user balance
    current_user.balance -= amount
    
    # Save to database
    db.add(transaction)
    db.commit()
    db.refresh(current_user)
    
    return {
        "balance": current_user.balance,
        "user_id": current_user.id,
        "username": current_user.username
    }

@router.post("/win", response_model=BalanceResponse)
def process_win(
    win_data: WinRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Process a win and add to balance"""
    amount = win_data.amount
    game_id = win_data.game_id
    description = win_data.description
    
    if amount <= 0:
        raise HTTPException(status_code=400, detail="Win amount must be positive")
    
    # Create transaction record
    transaction = Transaction(
        user_id=current_user.id,
        type="win",
        amount=amount,
        status="completed",
        description=description,
        reference_id=game_id
    )
    
    # Update user balance
    current_user.balance += amount
    
    # Save to database
    db.add(transaction)
    db.commit()
    db.refresh(current_user)
    
    return {
        "balance": current_user.balance,
        "user_id": current_user.id,
        "username": current_user.username
    }

@router.get("/transactions", response_model=TransactionListResponse)
def get_transactions(
    limit: int = 50,
    offset: int = 0,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get user's transaction history"""
    query = db.query(Transaction).filter(Transaction.user_id == current_user.id)
    total = query.count()
    
    transactions = query.order_by(Transaction.created_at.desc()).offset(offset).limit(limit).all()
    
    return {
        "transactions": transactions,
        "total": total
    }