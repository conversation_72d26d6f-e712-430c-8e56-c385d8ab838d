from fastapi import APIRouter, Depends, HTTPException, status, Request
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session
from app.database.db import get_db
from app.models.user import User
from app.models.wallet import Wallet
from app.models.user_profile import UserStats
from app.schemas.auth import (
    UserRegister, UserLogin, TokenResponse, TokenRefresh,
    UpdatePassword, ForgotPassword, ResetPassword, UserLogout
)
from app.schemas.user import UserResponse
from app.auth.jwt import (
    verify_password, get_password_hash, create_token_response,
    decode_token, get_current_user, invalidate_session
)
from app.core.config import settings
from app.core.rate_limit import limiter, RateLimits
from datetime import datetime, timedelta
import uuid
import secrets

router = APIRouter(tags=["auth"])

@router.post("/register", response_model=TokenResponse)
@limiter.limit(RateLimits.REGISTER)
async def register(
    request: Request,
    user_data: UserRegister, 
    db: Session = Depends(get_db)
):
    """Register a new user."""
    # Check if email already exists
    existing_email = db.query(User).filter(User.email == user_data.email).first()
    if existing_email:
        raise HTTPException(
            status_code=400,
            detail="Email already registered"
        )
    
    # Check if username already exists
    existing_username = db.query(User).filter(User.username == user_data.username).first()
    if existing_username:
        raise HTTPException(
            status_code=400,
            detail="Username already taken"
        )
    
    # Create new user
    hashed_password = get_password_hash(user_data.password)
    db_user = User(
        id=str(uuid.uuid4()),
        username=user_data.username.lower(),
        email=user_data.email,
        hashed_password=hashed_password,
        real_name=user_data.full_name or user_data.username,  # Use username as fallback
        is_active=True,
        is_verified=False,
        is_superuser=False,
        created_at=datetime.utcnow(),
        status="Rookie",  # Changed from "active" to match default
        kyc_status="pending",
        level=1,
        xp=0,
        next_level_xp=100,
        open_to_challenges=True
    )
    
    db.add(db_user)
    db.flush()  # Flush to get the user ID
    
    # Create wallet for the user
    wallet = Wallet(
        user_id=db_user.id,
        balance=0.0,
        bonus_tokens=100,  # Welcome bonus
        daily_limit=500.0,
        daily_spent=0.0,
        weekly_limit=2000.0,
        weekly_spent=0.0,
        monthly_limit=5000.0,
        monthly_spent=0.0
    )
    db.add(wallet)
    
    # Create user stats
    user_stats = UserStats(
        user_id=db_user.id
    )
    db.add(user_stats)
    
    db.commit()
    db.refresh(db_user)
    
    # Create token response
    device_info = request.headers.get("User-Agent")
    ip_address = request.client.host
    
    return create_token_response(db_user, db, device_info, ip_address)

@router.post("/login", response_model=TokenResponse)
@limiter.limit(RateLimits.LOGIN)
async def login(
    request: Request,
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: Session = Depends(get_db)
):
    """Login with username/email and password."""
    # Try to find user by email first, then by username
    user = db.query(User).filter(
        (User.email == form_data.username) | (User.username == form_data.username.lower())
    ).first()
    
    if not user or not verify_password(form_data.password, user.hashed_password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username/email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Account is not active"
        )
    
    if user.status == "banned":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Account has been banned"
        )
    
    # Create token response
    device_info = request.headers.get("User-Agent")
    ip_address = request.client.host
    
    return create_token_response(user, db, device_info, ip_address)

@router.post("/refresh", response_model=TokenResponse)
@limiter.limit(RateLimits.REFRESH_TOKEN)
async def refresh_token(
    request: Request,
    token_data: TokenRefresh,
    db: Session = Depends(get_db)
):
    """Refresh access token using refresh token."""
    payload = decode_token(token_data.refresh_token)
    
    if not payload:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid refresh token"
        )
    
    # Check if this is a refresh token
    if payload.get("type") != "refresh":
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token type"
        )
    
    user_id = payload.get("sub")
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token payload"
        )
    
    # Get user
    user = db.query(User).filter(User.id == user_id).first()
    if not user or not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User not found or inactive"
        )
    
    # Create new token response
    device_info = request.headers.get("User-Agent")
    ip_address = request.client.host
    
    return create_token_response(user, db, device_info, ip_address)

@router.post("/logout")
async def logout(
    logout_data: UserLogout,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Logout user and invalidate session."""
    # Invalidate current session
    # In a real implementation, you'd invalidate the JWT token
    # For now, we'll just return success
    
    if logout_data.refresh_token:
        # You could store invalidated tokens in Redis
        pass
    
    return {"detail": "Successfully logged out"}

@router.put("/change-password")
async def change_password(
    password_data: UpdatePassword,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Change user password."""
    # Verify current password
    if not verify_password(password_data.current_password, current_user.hashed_password):
        raise HTTPException(
            status_code=400,
            detail="Current password is incorrect"
        )
    
    # Update password
    current_user.hashed_password = get_password_hash(password_data.new_password)
    current_user.updated_at = datetime.utcnow()
    
    db.commit()
    
    return {"detail": "Password updated successfully"}

@router.post("/forgot-password")
@limiter.limit(RateLimits.PASSWORD_RESET)
async def forgot_password(
    request: Request,
    forgot_data: ForgotPassword,
    db: Session = Depends(get_db)
):
    """Request password reset email."""
    user = db.query(User).filter(User.email == forgot_data.email).first()
    
    # Don't reveal if email exists or not
    if not user:
        return {"detail": "If the email exists, a password reset link has been sent"}
    
    # Generate reset token
    reset_token = secrets.token_urlsafe(32)
    
    # In a real implementation, you would:
    # 1. Store the reset token with expiration in Redis
    # 2. Send email with reset link
    
    return {"detail": "If the email exists, a password reset link has been sent"}

@router.post("/reset-password")
async def reset_password(
    reset_data: ResetPassword,
    db: Session = Depends(get_db)
):
    """Reset password with token."""
    # In a real implementation, verify the reset token from Redis
    
    # For demonstration, let's assume token is valid
    # You'd need to implement token verification
    
    # Mock implementation
    user_id = "mock_user_id"  # This would come from the verified token
    
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(
            status_code=400,
            detail="Invalid or expired reset token"
        )
    
    # Update password
    user.hashed_password = get_password_hash(reset_data.new_password)
    user.updated_at = datetime.utcnow()
    
    db.commit()
    
    return {"detail": "Password has been reset successfully"}

@router.get("/me", response_model=UserResponse)
async def get_current_user_info(current_user: User = Depends(get_current_user)):
    """Get current user information."""
    return current_user