from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List
from datetime import datetime, timedelta

from app.database.db import get_db
from app.models.user import User
from app.models.payment import Transaction
from app.auth.jwt import get_current_user
from app.schemas.user import UserResponse

router = APIRouter()

def require_admin(current_user: User = Depends(get_current_user)):
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=403,
            detail="Admin privileges required"
        )
    return current_user

@router.get("/stats")
def get_admin_stats(
    db: Session = Depends(get_db),
    current_user: User = Depends(require_admin)
):
    """Get admin dashboard statistics"""
    
    # Total users
    total_users = db.query(User).count()
    
    # Active users (logged in within last 24 hours - simplified)
    active_users = db.query(User).filter(User.is_active == True).count()
    
    # Pending KYC
    pending_kyc = db.query(User).filter(User.kyc_status == "pending").count()
    
    # Total revenue (sum of all winning transactions)
    total_revenue = db.query(Transaction).filter(
        Transaction.type.in_(["bet", "deposit"])
    ).with_entities(
        db.func.sum(Transaction.amount)
    ).scalar() or 0
    
    # Daily active users (simplified - using created_at for demo)
    today = datetime.utcnow().date()
    daily_active = db.query(User).filter(
        db.func.date(User.created_at) == today
    ).count()
    
    # Weekly growth (simplified calculation)
    week_ago = datetime.utcnow() - timedelta(days=7)
    users_week_ago = db.query(User).filter(
        User.created_at < week_ago
    ).count()
    
    weekly_growth = 0
    if users_week_ago > 0:
        weekly_growth = ((total_users - users_week_ago) / users_week_ago) * 100
    
    return {
        "totalUsers": total_users,
        "activeUsers": active_users,
        "pendingKYC": pending_kyc,
        "totalRevenue": float(total_revenue),
        "dailyActive": daily_active,
        "weeklyGrowth": round(weekly_growth, 2)
    }

@router.get("/users")
def get_all_users(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_admin)
):
    """Get all users with pagination"""
    users = db.query(User).offset(skip).limit(limit).all()
    total = db.query(User).count()
    
    return {
        "items": users,
        "total": total,
        "skip": skip,
        "limit": limit
    }

@router.put("/users/{user_id}/status")
def update_user_status(
    user_id: str,
    is_active: bool,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_admin)
):
    """Enable or disable a user account"""
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    
    user.is_active = is_active
    db.commit()
    db.refresh(user)
    
    return {"message": f"User {'activated' if is_active else 'deactivated'} successfully"}