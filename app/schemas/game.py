from pydantic import BaseModel, Field
from typing import Optional, Dict, Any
from datetime import datetime
from enum import Enum

class GameStatus(str, Enum):
    WAITING = "waiting"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    CANCELLED = "cancelled"

class GameType(str, Enum):
    CHECKERS = "checkers"
    ROCK_PAPER_SCISSORS = "rock_paper_scissors"
    CHESS = "chess"
    HIGHLIGHT_HERO = "highlight_hero"
    BLUR_DETECTIVE = "blur_detective"
    WORD_JUMBLE = "word_jumble"

class GameSettings(BaseModel):
    """Base game settings"""
    time_limit: Optional[int] = Field(default=300, description="Time limit per player in seconds")
    is_timed: Optional[bool] = Field(default=False, description="Whether the game has time limit")

class CheckersSettings(GameSettings):
    """Checkers-specific settings"""
    board_size: Optional[int] = Field(default=8, description="Board size (8x8)")
    king_row_capture: Optional[bool] = Field(default=True, description="Allow king row capture")

class RPSSettings(GameSettings):
    """Rock Paper Scissors specific settings"""
    rounds: int = Field(default=3, description="Number of rounds")
    tie_breaker: Optional[bool] = Field(default=True, description="Enable tie breaker")

class ChessSettings(GameSettings):
    """Chess-specific settings"""
    time_control: str = Field(default="Rapid", description="Time control type: Bullet, Blitz, Rapid, or Classical")
    time_limit: Optional[int] = Field(default=600, description="Initial time per player in seconds")
    increment: Optional[int] = Field(default=10, description="Time increment per move in seconds")

class GameCreate(BaseModel):
    game_type: GameType
    wager_amount: float = Field(gt=0, description="Wager amount per player")
    settings: Optional[Dict[str, Any]] = Field(default_factory=dict)

class GameUpdate(BaseModel):
    status: Optional[GameStatus]
    state: Optional[Dict[str, Any]]
    current_player_id: Optional[str]
    winner_id: Optional[str]
    last_move_at: Optional[datetime]

class GameResponse(BaseModel):
    id: str
    game_type: GameType
    status: GameStatus
    player1_id: str
    player2_id: Optional[str]
    current_player_id: Optional[str]
    winner_id: Optional[str]
    wager_amount: float
    total_pot: float
    settings: Dict[str, Any]
    state: Dict[str, Any]
    created_at: datetime
    started_at: Optional[datetime]
    completed_at: Optional[datetime]
    last_move_at: Optional[datetime]
    
    class Config:
        from_attributes = True

class GameListResponse(BaseModel):
    id: str
    game_type: GameType
    status: GameStatus
    player1_id: str
    player2_id: Optional[str]
    wager_amount: float
    total_pot: float
    created_at: datetime
    player1_username: Optional[str] = None
    player2_username: Optional[str] = None
    
    class Config:
        from_attributes = True

class GameMoveCreate(BaseModel):
    """Create a new game move"""
    move_data: Dict[str, Any]

class GameMoveData(BaseModel):
    """Base move data"""
    move_type: str

class CheckersMoveData(GameMoveData):
    """Checkers move data"""
    from_row: int
    from_col: int
    to_row: int
    to_col: int
    is_jump: bool
    captured_pieces: Optional[list] = []

class RPSMoveData(GameMoveData):
    """Rock Paper Scissors move data"""
    choice: str  # rock, paper, scissors
    round_number: int

class ChessMoveData(GameMoveData):
    """Chess move data"""
    from_square: str  # e.g., "e2"
    to_square: str    # e.g., "e4"
    piece: str        # piece type
    promotion: Optional[str] = None  # for pawn promotion
    is_castle: bool = False
    is_en_passant: bool = False
    is_check: bool = False
    is_checkmate: bool = False
    is_stalemate: bool = False
    captured_piece: Optional[str] = None

class GameMoveResponse(BaseModel):
    id: str
    game_id: str
    player_id: str
    move_number: int
    move_data: Dict[str, Any]
    timestamp: datetime
    
    class Config:
        from_attributes = True