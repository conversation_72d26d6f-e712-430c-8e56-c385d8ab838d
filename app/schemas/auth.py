"""Authentication schemas for BetBet platform."""
from pydantic import BaseModel, EmailStr, Field, validator
from typing import Optional
from datetime import datetime
import re

class UserRegister(BaseModel):
    """User registration schema."""
    username: str = Field(..., min_length=3, max_length=50)
    email: EmailStr
    password: str = Field(..., min_length=8, max_length=100)
    full_name: Optional[str] = Field(None, min_length=2, max_length=100)
    phone_number: Optional[str] = Field(None, max_length=15)
    
    @validator('username')
    def validate_username(cls, v):
        if not re.match(r'^[a-zA-Z0-9_-]+$', v):
            raise ValueError('Username must contain only letters, numbers, underscores, and hyphens')
        return v.lower()
    
    @validator('password')
    def validate_password(cls, v):
        if not re.match(r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{8,}$', v):
            raise ValueError('Password must contain at least one uppercase letter, one lowercase letter, and one number')
        return v

class UserLogin(BaseModel):
    """User login schema."""
    username: str
    password: str
    device_info: Optional[str] = None
    
    @validator('username')
    def normalize_username(cls, v):
        return v.lower()

class TokenRefresh(BaseModel):
    """Token refresh schema."""
    refresh_token: str

class TokenResponse(BaseModel):
    """Token response schema."""
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int
    user: dict

class UpdatePassword(BaseModel):
    """Update password schema."""
    current_password: str
    new_password: str = Field(..., min_length=8, max_length=100)
    
    @validator('new_password')
    def validate_password(cls, v):
        if not re.match(r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{8,}$', v):
            raise ValueError('Password must contain at least one uppercase letter, one lowercase letter, and one number')
        return v

class ForgotPassword(BaseModel):
    """Forgot password schema."""
    email: EmailStr

class ResetPassword(BaseModel):
    """Reset password schema."""
    token: str
    new_password: str = Field(..., min_length=8, max_length=100)
    
    @validator('new_password')
    def validate_password(cls, v):
        if not re.match(r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{8,}$', v):
            raise ValueError('Password must contain at least one uppercase letter, one lowercase letter, and one number')
        return v

class UserSession(BaseModel):
    """User session info."""
    id: str
    user_id: str
    device_info: Optional[str]
    ip_address: Optional[str]
    created_at: datetime
    last_active: datetime
    is_active: bool

class UserLogout(BaseModel):
    """User logout schema."""
    refresh_token: Optional[str] = None