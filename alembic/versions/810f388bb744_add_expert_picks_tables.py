"""Add expert picks tables

Revision ID: 810f388bb744
Revises: 0eec1bd418d5
Create Date: 2025-05-23 09:50:02.547369

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '810f388bb744'
down_revision = '0eec1bd418d5'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Create expert_profiles table
    op.create_table('expert_profiles',
        sa.<PERSON>umn('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.String(), nullable=False),
        sa.Column('is_expert', sa.<PERSON>(), default=False),
        sa.<PERSON>umn('total_picks', sa.Integer(), default=0),
        sa.Column('win_rate', sa.Float(), default=0.0),
        sa.Column('monthly_revenue', sa.Float(), default=0.0),
        sa.Column('total_revenue', sa.Float(), default=0.0),
        sa.Column('followers_count', sa.Integer(), default=0),
        sa.Column('avg_odds', sa.Float(), default=0.0),
        sa.Column('roi', sa.Float(), default=0.0),
        sa.Column('win_streak', sa.Integer(), default=0),
        sa.Column('rating', sa.Float(), default=0.0),
        sa.Column('created_at', sa.DateTime(), default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(), default=sa.func.now(), onupdate=sa.func.now()),
        sa.ForeignKeyConstraint(['user_id'], ['users.id']),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('user_id')
    )

    # Create pick status enum
    pick_status_enum = sa.Enum('draft', 'active', 'completed', 'cancelled', name='pickstatus')
    pick_status_enum.create(op.get_bind())

    # Create pick type enum
    pick_type_enum = sa.Enum('match_winner', 'over_under', 'both_teams_score', 'handicap', 'correct_score', 'custom', name='picktype')
    pick_type_enum.create(op.get_bind())

    # Create expert_picks table
    op.create_table('expert_picks',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('expert_id', sa.Integer(), nullable=False),
        sa.Column('sport', sa.String(50), nullable=False),
        sa.Column('league', sa.String(100), nullable=False),
        sa.Column('match', sa.String(200), nullable=False),
        sa.Column('event_date', sa.DateTime(), nullable=False),
        sa.Column('pick_type', pick_type_enum, nullable=False),
        sa.Column('pick_description', sa.String(500), nullable=False),
        sa.Column('odds', sa.Float(), nullable=False),
        sa.Column('confidence', sa.Integer(), nullable=False),
        sa.Column('price', sa.Float(), nullable=False),
        sa.Column('vip_price', sa.Float()),
        sa.Column('status', pick_status_enum, default='draft'),
        sa.Column('booking_code', sa.String(50), unique=True),
        sa.Column('universal_code', sa.String(50), unique=True),
        sa.Column('analysis', sa.Text()),
        sa.Column('media_urls', sa.JSON(), default=[]),
        sa.Column('sales_count', sa.Integer(), default=0),
        sa.Column('result', sa.String(20)),
        sa.Column('created_at', sa.DateTime(), default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(), default=sa.func.now(), onupdate=sa.func.now()),
        sa.ForeignKeyConstraint(['expert_id'], ['expert_profiles.id']),
        sa.PrimaryKeyConstraint('id')
    )

    # Create pick_packs table
    op.create_table('pick_packs',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('expert_id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(200), nullable=False),
        sa.Column('description', sa.Text()),
        sa.Column('price', sa.Float(), nullable=False),
        sa.Column('confidence', sa.Integer(), nullable=False),
        sa.Column('expected_roi', sa.Float()),
        sa.Column('valid_until', sa.DateTime(), nullable=False),
        sa.Column('image_url', sa.String(500)),
        sa.Column('sales_count', sa.Integer(), default=0),
        sa.Column('rating', sa.Float(), default=0.0),
        sa.Column('created_at', sa.DateTime(), default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(), default=sa.func.now(), onupdate=sa.func.now()),
        sa.ForeignKeyConstraint(['expert_id'], ['expert_profiles.id']),
        sa.PrimaryKeyConstraint('id')
    )

    # Create pick_pack_items table
    op.create_table('pick_pack_items',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('pack_id', sa.Integer(), nullable=False),
        sa.Column('pick_id', sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(['pack_id'], ['pick_packs.id']),
        sa.ForeignKeyConstraint(['pick_id'], ['expert_picks.id']),
        sa.PrimaryKeyConstraint('id')
    )

    # Create pick_purchases table
    op.create_table('pick_purchases',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.String(), nullable=False),
        sa.Column('pick_id', sa.Integer(), nullable=False),
        sa.Column('price_paid', sa.Float(), nullable=False),
        sa.Column('is_vip', sa.Boolean(), default=False),
        sa.Column('purchased_at', sa.DateTime(), default=sa.func.now()),
        sa.ForeignKeyConstraint(['user_id'], ['users.id']),
        sa.ForeignKeyConstraint(['pick_id'], ['expert_picks.id']),
        sa.PrimaryKeyConstraint('id')
    )

    # Create pick_pack_purchases table
    op.create_table('pick_pack_purchases',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.String(), nullable=False),
        sa.Column('pack_id', sa.Integer(), nullable=False),
        sa.Column('price_paid', sa.Float(), nullable=False),
        sa.Column('purchased_at', sa.DateTime(), default=sa.func.now()),
        sa.ForeignKeyConstraint(['user_id'], ['users.id']),
        sa.ForeignKeyConstraint(['pack_id'], ['pick_packs.id']),
        sa.PrimaryKeyConstraint('id')
    )

    # Create expert_followers table
    op.create_table('expert_followers',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('expert_id', sa.Integer(), nullable=False),
        sa.Column('follower_id', sa.Integer(), nullable=False),
        sa.Column('followed_at', sa.DateTime(), default=sa.func.now()),
        sa.ForeignKeyConstraint(['expert_id'], ['expert_profiles.id']),
        sa.ForeignKeyConstraint(['follower_id'], ['expert_profiles.id']),
        sa.PrimaryKeyConstraint('id')
    )

    # Create indexes
    op.create_index('ix_expert_picks_sport', 'expert_picks', ['sport'])
    op.create_index('ix_expert_picks_status', 'expert_picks', ['status'])
    op.create_index('ix_expert_picks_expert_id', 'expert_picks', ['expert_id'])
    op.create_index('ix_pick_purchases_user_id', 'pick_purchases', ['user_id'])
    op.create_index('ix_pick_purchases_pick_id', 'pick_purchases', ['pick_id'])


def downgrade() -> None:
    # Drop indexes
    op.drop_index('ix_pick_purchases_pick_id', 'pick_purchases')
    op.drop_index('ix_pick_purchases_user_id', 'pick_purchases')
    op.drop_index('ix_expert_picks_expert_id', 'expert_picks')
    op.drop_index('ix_expert_picks_status', 'expert_picks')
    op.drop_index('ix_expert_picks_sport', 'expert_picks')

    # Drop tables
    op.drop_table('expert_followers')
    op.drop_table('pick_pack_purchases')
    op.drop_table('pick_purchases')
    op.drop_table('pick_pack_items')
    op.drop_table('pick_packs')
    op.drop_table('expert_picks')
    op.drop_table('expert_profiles')

    # Drop enums
    op.execute('DROP TYPE IF EXISTS picktype')
    op.execute('DROP TYPE IF EXISTS pickstatus')