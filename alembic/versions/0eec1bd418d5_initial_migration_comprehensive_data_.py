"""Initial migration - comprehensive data models

Revision ID: 0eec1bd418d5
Revises: 
Create Date: 2025-05-19 00:12:57.090732

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '0eec1bd418d5'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('achievements',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('description', sa.Text(), nullable=False),
    sa.Column('icon', sa.String(), nullable=True),
    sa.Column('category', sa.String(), nullable=False),
    sa.Column('requirement_type', sa.String(), nullable=False),
    sa.Column('requirement_value', sa.Integer(), nullable=False),
    sa.Column('xp_reward', sa.Integer(), nullable=True),
    sa.Column('token_reward', sa.Integer(), nullable=True),
    sa.Column('badge_color', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    op.create_table('bet_templates',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('category', sa.String(), nullable=False),
    sa.Column('bet_type', sa.String(), nullable=False),
    sa.Column('default_amount', sa.Float(), nullable=True),
    sa.Column('default_odds', sa.Float(), nullable=True),
    sa.Column('template_data', sa.JSON(), nullable=False),
    sa.Column('times_used', sa.Integer(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('is_featured', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('game_tournaments',
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('description', sa.String(), nullable=True),
    sa.Column('game_type', sa.String(), nullable=False),
    sa.Column('max_players', sa.Integer(), nullable=False),
    sa.Column('current_players', sa.Integer(), nullable=True),
    sa.Column('entry_fee', sa.Float(), nullable=True),
    sa.Column('prize_pool', sa.Float(), nullable=True),
    sa.Column('prize_distribution', sa.JSON(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('started_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('ended_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('format', sa.String(), nullable=True),
    sa.Column('current_round', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('registration_closes_at', sa.DateTime(timezone=True), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('users',
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('username', sa.String(), nullable=False),
    sa.Column('email', sa.String(), nullable=False),
    sa.Column('hashed_password', sa.String(), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('is_verified', sa.Boolean(), nullable=True),
    sa.Column('is_superuser', sa.Boolean(), nullable=True),
    sa.Column('avatar_url', sa.String(), nullable=True),
    sa.Column('cover_photo_url', sa.String(), nullable=True),
    sa.Column('bio', sa.Text(), nullable=True),
    sa.Column('country', sa.String(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('level', sa.Integer(), nullable=True),
    sa.Column('xp', sa.Integer(), nullable=True),
    sa.Column('next_level_xp', sa.Integer(), nullable=True),
    sa.Column('last_active', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('open_to_challenges', sa.Boolean(), nullable=True),
    sa.Column('real_name', sa.String(), nullable=True),
    sa.Column('id_card_url', sa.String(), nullable=True),
    sa.Column('kyc_status', sa.String(), nullable=True),
    sa.Column('kyc_submitted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('kyc_verified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_users_email'), ['email'], unique=True)
        batch_op.create_index(batch_op.f('ix_users_id'), ['id'], unique=False)
        batch_op.create_index(batch_op.f('ix_users_username'), ['username'], unique=True)

    op.create_table('friendships',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('friend_id', sa.Integer(), nullable=False),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('accepted_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['friend_id'], ['users.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('user_id', 'friend_id')
    )
    op.create_table('games',
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('game_type', sa.Enum('CHECKERS', 'ROCK_PAPER_SCISSORS', 'CHESS', name='gametype'), nullable=False),
    sa.Column('status', sa.Enum('WAITING', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED', name='gamestatus'), nullable=True),
    sa.Column('player1_id', sa.String(), nullable=False),
    sa.Column('player2_id', sa.String(), nullable=True),
    sa.Column('current_player_id', sa.String(), nullable=True),
    sa.Column('winner_id', sa.String(), nullable=True),
    sa.Column('settings', sa.JSON(), nullable=True),
    sa.Column('state', sa.JSON(), nullable=True),
    sa.Column('wager_amount', sa.Float(), nullable=True),
    sa.Column('total_pot', sa.Float(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('started_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('completed_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('last_move_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['current_player_id'], ['users.id'], ),
    sa.ForeignKeyConstraint(['player1_id'], ['users.id'], ),
    sa.ForeignKeyConstraint(['player2_id'], ['users.id'], ),
    sa.ForeignKeyConstraint(['winner_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('games', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_games_id'), ['id'], unique=False)

    op.create_table('leaderboard_entries',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('global_rank', sa.Integer(), nullable=True),
    sa.Column('weekly_rank', sa.Integer(), nullable=True),
    sa.Column('monthly_rank', sa.Integer(), nullable=True),
    sa.Column('total_earnings', sa.Float(), nullable=True),
    sa.Column('win_rate', sa.Float(), nullable=True),
    sa.Column('games_played', sa.Integer(), nullable=True),
    sa.Column('week_earnings', sa.Float(), nullable=True),
    sa.Column('month_earnings', sa.Float(), nullable=True),
    sa.Column('last_updated', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('user_id')
    )
    op.create_table('tournament_participants',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('tournament_id', sa.String(), nullable=False),
    sa.Column('user_id', sa.String(), nullable=False),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('seed', sa.Integer(), nullable=True),
    sa.Column('games_played', sa.Integer(), nullable=True),
    sa.Column('games_won', sa.Integer(), nullable=True),
    sa.Column('final_position', sa.Integer(), nullable=True),
    sa.Column('prize_won', sa.Float(), nullable=True),
    sa.Column('registered_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('eliminated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['tournament_id'], ['game_tournaments.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('tournament_id', 'user_id')
    )
    op.create_table('user_achievements',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('achievement_id', sa.Integer(), nullable=False),
    sa.Column('current_progress', sa.Integer(), nullable=True),
    sa.Column('earned', sa.Boolean(), nullable=True),
    sa.Column('earned_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['achievement_id'], ['achievements.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('user_id', 'achievement_id')
    )
    op.create_table('user_sessions',
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('user_id', sa.String(), nullable=False),
    sa.Column('session_token', sa.String(), nullable=False),
    sa.Column('user_agent', sa.String(), nullable=True),
    sa.Column('ip_address', sa.String(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('last_activity', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('expires_at', sa.DateTime(timezone=True), nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('user_sessions', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_user_sessions_id'), ['id'], unique=False)
        batch_op.create_index(batch_op.f('ix_user_sessions_session_token'), ['session_token'], unique=True)

    op.create_table('user_stats',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('games_played', sa.Integer(), nullable=True),
    sa.Column('games_won', sa.Integer(), nullable=True),
    sa.Column('win_rate', sa.Float(), nullable=True),
    sa.Column('win_streak', sa.Integer(), nullable=True),
    sa.Column('longest_win_streak', sa.Integer(), nullable=True),
    sa.Column('total_wagered', sa.Float(), nullable=True),
    sa.Column('total_earned', sa.Float(), nullable=True),
    sa.Column('biggest_win', sa.Float(), nullable=True),
    sa.Column('biggest_loss', sa.Float(), nullable=True),
    sa.Column('challenges_issued', sa.Integer(), nullable=True),
    sa.Column('challenges_accepted', sa.Integer(), nullable=True),
    sa.Column('challenges_won', sa.Integer(), nullable=True),
    sa.Column('challenges_declined', sa.Integer(), nullable=True),
    sa.Column('friends_count', sa.Integer(), nullable=True),
    sa.Column('most_played_game', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('user_id')
    )
    op.create_table('wallets',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.String(), nullable=False),
    sa.Column('balance', sa.Float(), nullable=True),
    sa.Column('bonus_tokens', sa.Integer(), nullable=True),
    sa.Column('daily_limit', sa.Float(), nullable=True),
    sa.Column('weekly_limit', sa.Float(), nullable=True),
    sa.Column('monthly_limit', sa.Float(), nullable=True),
    sa.Column('daily_spent', sa.Float(), nullable=True),
    sa.Column('weekly_spent', sa.Float(), nullable=True),
    sa.Column('monthly_spent', sa.Float(), nullable=True),
    sa.Column('is_locked', sa.Boolean(), nullable=True),
    sa.Column('locked_reason', sa.String(), nullable=True),
    sa.Column('locked_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('last_transaction_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('daily_reset_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('weekly_reset_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('monthly_reset_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('user_id')
    )
    op.create_table('bets',
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('user_id', sa.String(), nullable=False),
    sa.Column('bet_type', sa.String(), nullable=False),
    sa.Column('title', sa.String(), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('category', sa.String(), nullable=False),
    sa.Column('subcategory', sa.String(), nullable=True),
    sa.Column('amount', sa.Float(), nullable=False),
    sa.Column('odds', sa.Float(), nullable=True),
    sa.Column('potential_payout', sa.Float(), nullable=True),
    sa.Column('allows_counter', sa.Boolean(), nullable=True),
    sa.Column('min_counter_amount', sa.Float(), nullable=True),
    sa.Column('max_counter_amount', sa.Float(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('result', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('matched_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('settled_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('expires_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('sport_type', sa.String(), nullable=True),
    sa.Column('match_id', sa.String(), nullable=True),
    sa.Column('team_or_player', sa.String(), nullable=True),
    sa.Column('bet_market', sa.String(), nullable=True),
    sa.Column('line', sa.Float(), nullable=True),
    sa.Column('game_id', sa.String(), nullable=True),
    sa.Column('predicted_winner_id', sa.String(), nullable=True),
    sa.Column('opponent_id', sa.String(), nullable=True),
    sa.Column('judge_id', sa.String(), nullable=True),
    sa.Column('is_public', sa.Boolean(), nullable=True),
    sa.Column('is_featured', sa.Boolean(), nullable=True),
    sa.Column('extra_data', sa.JSON(), nullable=True),
    sa.ForeignKeyConstraint(['game_id'], ['games.id'], ),
    sa.ForeignKeyConstraint(['judge_id'], ['users.id'], ),
    sa.ForeignKeyConstraint(['opponent_id'], ['users.id'], ),
    sa.ForeignKeyConstraint(['predicted_winner_id'], ['users.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('game_chat',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('game_id', sa.String(), nullable=False),
    sa.Column('user_id', sa.String(), nullable=False),
    sa.Column('message', sa.String(), nullable=False),
    sa.Column('is_system_message', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.ForeignKeyConstraint(['game_id'], ['games.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('game_invites',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('game_id', sa.String(), nullable=False),
    sa.Column('inviter_id', sa.String(), nullable=False),
    sa.Column('invitee_id', sa.String(), nullable=False),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('message', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('responded_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('expires_at', sa.DateTime(timezone=True), nullable=False),
    sa.ForeignKeyConstraint(['game_id'], ['games.id'], ),
    sa.ForeignKeyConstraint(['invitee_id'], ['users.id'], ),
    sa.ForeignKeyConstraint(['inviter_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('game_id', 'invitee_id')
    )
    op.create_table('game_moves',
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('game_id', sa.String(), nullable=False),
    sa.Column('player_id', sa.String(), nullable=False),
    sa.Column('move_number', sa.Integer(), nullable=False),
    sa.Column('move_data', sa.JSON(), nullable=False),
    sa.Column('timestamp', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.ForeignKeyConstraint(['game_id'], ['games.id'], ),
    sa.ForeignKeyConstraint(['player_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('game_moves', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_game_moves_id'), ['id'], unique=False)

    op.create_table('game_players',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('game_id', sa.String(), nullable=False),
    sa.Column('user_id', sa.String(), nullable=False),
    sa.Column('player_number', sa.Integer(), nullable=False),
    sa.Column('is_winner', sa.Boolean(), nullable=True),
    sa.Column('final_score', sa.Integer(), nullable=True),
    sa.Column('time_remaining', sa.Integer(), nullable=True),
    sa.Column('amount_wagered', sa.Float(), nullable=True),
    sa.Column('amount_won', sa.Float(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('joined_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('left_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['game_id'], ['games.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('game_id', 'user_id')
    )
    op.create_table('game_spectators',
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('game_id', sa.String(), nullable=False),
    sa.Column('user_id', sa.String(), nullable=False),
    sa.Column('joined_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.ForeignKeyConstraint(['game_id'], ['games.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('game_spectators', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_game_spectators_id'), ['id'], unique=False)

    op.create_table('payment_methods',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('wallet_id', sa.Integer(), nullable=False),
    sa.Column('type', sa.String(), nullable=False),
    sa.Column('is_default', sa.Boolean(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('last4', sa.String(length=4), nullable=True),
    sa.Column('card_type', sa.String(), nullable=True),
    sa.Column('card_holder_name', sa.String(), nullable=True),
    sa.Column('expiry_month', sa.Integer(), nullable=True),
    sa.Column('expiry_year', sa.Integer(), nullable=True),
    sa.Column('email', sa.String(), nullable=True),
    sa.Column('wallet_address', sa.String(), nullable=True),
    sa.Column('token', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('last_used_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['wallet_id'], ['wallets.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('bet_invites',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('bet_id', sa.String(), nullable=False),
    sa.Column('inviter_id', sa.String(), nullable=False),
    sa.Column('invitee_id', sa.String(), nullable=False),
    sa.Column('message', sa.Text(), nullable=True),
    sa.Column('suggested_amount', sa.Float(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('responded_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('expires_at', sa.DateTime(timezone=True), nullable=False),
    sa.ForeignKeyConstraint(['bet_id'], ['bets.id'], ),
    sa.ForeignKeyConstraint(['invitee_id'], ['users.id'], ),
    sa.ForeignKeyConstraint(['inviter_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('bet_participations',
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('bet_id', sa.String(), nullable=False),
    sa.Column('user_id', sa.String(), nullable=False),
    sa.Column('position', sa.String(), nullable=False),
    sa.Column('stake_amount', sa.Float(), nullable=False),
    sa.Column('is_creator', sa.Boolean(), nullable=True),
    sa.Column('payout_amount', sa.Float(), nullable=True),
    sa.Column('is_paid', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['bet_id'], ['bets.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('bet_participations', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_bet_participations_id'), ['id'], unique=False)

    op.create_table('counter_bets',
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('original_bet_id', sa.String(), nullable=False),
    sa.Column('user_id', sa.String(), nullable=False),
    sa.Column('amount', sa.Float(), nullable=False),
    sa.Column('odds', sa.Float(), nullable=True),
    sa.Column('potential_payout', sa.Float(), nullable=True),
    sa.Column('message', sa.Text(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('responded_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('expires_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['original_bet_id'], ['bets.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('notifications',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('type', sa.String(), nullable=False),
    sa.Column('title', sa.String(), nullable=False),
    sa.Column('message', sa.Text(), nullable=False),
    sa.Column('data', sa.JSON(), nullable=True),
    sa.Column('related_user_id', sa.Integer(), nullable=True),
    sa.Column('related_game_id', sa.String(), nullable=True),
    sa.Column('related_bet_id', sa.Integer(), nullable=True),
    sa.Column('is_read', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('read_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['related_bet_id'], ['bets.id'], ),
    sa.ForeignKeyConstraint(['related_game_id'], ['games.id'], ),
    sa.ForeignKeyConstraint(['related_user_id'], ['users.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('transactions',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('wallet_id', sa.Integer(), nullable=False),
    sa.Column('type', sa.String(), nullable=False),
    sa.Column('amount', sa.Float(), nullable=False),
    sa.Column('fee', sa.Float(), nullable=True),
    sa.Column('net_amount', sa.Float(), nullable=False),
    sa.Column('balance_before', sa.Float(), nullable=False),
    sa.Column('balance_after', sa.Float(), nullable=False),
    sa.Column('game_id', sa.String(), nullable=True),
    sa.Column('bet_id', sa.Integer(), nullable=True),
    sa.Column('payment_method_id', sa.Integer(), nullable=True),
    sa.Column('recipient_wallet_id', sa.Integer(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('processed_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('completed_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('failed_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('reference_number', sa.String(), nullable=False),
    sa.Column('processor_transaction_id', sa.String(), nullable=True),
    sa.Column('failure_reason', sa.Text(), nullable=True),
    sa.Column('extra_data', sa.Text(), nullable=True),
    sa.ForeignKeyConstraint(['bet_id'], ['bets.id'], ),
    sa.ForeignKeyConstraint(['game_id'], ['games.id'], ),
    sa.ForeignKeyConstraint(['payment_method_id'], ['payment_methods.id'], ),
    sa.ForeignKeyConstraint(['recipient_wallet_id'], ['wallets.id'], ),
    sa.ForeignKeyConstraint(['wallet_id'], ['wallets.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('reference_number')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('transactions')
    op.drop_table('notifications')
    op.drop_table('counter_bets')
    with op.batch_alter_table('bet_participations', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_bet_participations_id'))

    op.drop_table('bet_participations')
    op.drop_table('bet_invites')
    op.drop_table('payment_methods')
    with op.batch_alter_table('game_spectators', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_game_spectators_id'))

    op.drop_table('game_spectators')
    op.drop_table('game_players')
    with op.batch_alter_table('game_moves', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_game_moves_id'))

    op.drop_table('game_moves')
    op.drop_table('game_invites')
    op.drop_table('game_chat')
    op.drop_table('bets')
    op.drop_table('wallets')
    op.drop_table('user_stats')
    with op.batch_alter_table('user_sessions', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_user_sessions_session_token'))
        batch_op.drop_index(batch_op.f('ix_user_sessions_id'))

    op.drop_table('user_sessions')
    op.drop_table('user_achievements')
    op.drop_table('tournament_participants')
    op.drop_table('leaderboard_entries')
    with op.batch_alter_table('games', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_games_id'))

    op.drop_table('games')
    op.drop_table('friendships')
    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_users_username'))
        batch_op.drop_index(batch_op.f('ix_users_id'))
        batch_op.drop_index(batch_op.f('ix_users_email'))

    op.drop_table('users')
    op.drop_table('game_tournaments')
    op.drop_table('bet_templates')
    op.drop_table('achievements')
    # ### end Alembic commands ###
