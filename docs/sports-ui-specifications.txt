integrate the ui design and style it according to the already established theme. this will be the sports section for live data which
users will find bets and create for the marketplace. it will also feed the marketplace data so users can set up based on the real world events.

Key Features of the Sports Platform
Navigation & Layout

Global Top Navigation Bar with BetBet logo, main navigation menu, notifications, and wallet
Sub-Navigation Tabs for filtering sports content (All Sports, Live Now, Upcoming, Popular Leagues, My Favorites, Exotics)
3-Column Responsive Layout that collapses to stack on mobile
Mobile-Optimized Interface with bottom navigation and collapsible sections

Left Sidebar - Filters

Sport Filter Accordion with all major sports categories
Date & Time Filter (Today, Tomorrow, This Week, Custom Range)
Odds Type selection (American, Decimal, Fractional)
Market Type filters (Spreads, Moneylines, Over/Unders, Futures, Props)
View Mode options (Compact, Detail)

Center Content - Main Feed

Featured Match/Hot Pick with highlighted styling and trending tags
Sports Events Grid with expandable event cards
Event Cards showing:

League information with icon
Team/player matchups
Live status and timing
Dynamic odds display
Quick bet buttons
Expandable details for more betting options
Interactive elements (favorite, share, expand)



Right Sidebar - Dynamic Widgets

My Bets Widget with tabs for Active, Pending, Won, and Lost bets
Trending Leagues showing popular betting volumes
Watchlist/Favorites for saved events
Live Commentary chat room for user engagement

Event Details Modal

Comprehensive Event View with tabs for:

Overview with match info and market odds
Live Scores (placeholder for future implementation)
Detailed Odds display
Stats, Head-to-Head, and Line Movement sections
Bet placement interface with customizable stake inputs
Custom bet creation option



Interactive Features

Real-time Updates for odds and match statuses (simulated in this component)
Favorites and Watchlist Management
Filtering System across multiple dimensions (sport, date, status, etc.)
Search Functionality for teams, leagues, and events
Expandable Cards for detailed information
Betting Interface with odds selection and stake input
Custom Bet Creation

Visual Design Elements

Consistent dark theme matching your chess and checkers games
Rainbow-themed gradients for emphasis on key elements
Responsive design for all screen sizes
Animated elements for live events and trending items
Clear visual hierarchy for information display

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Avatar } from '@/components/ui/avatar';
import {
  Search,
  Home,
  BarChart2,
  Trophy,
  Wallet,
  Bell,
  User,
  Calendar,
  Filter,
  Star,
  Clock,
  TrendingUp,
  Globe,
  Menu,
  ChevronDown,
  ChevronRight,
  ArrowRight,
  Fire,
  ExternalLink,
  MessageSquare,
  MoreHorizontal,
  Bookmark,
  Share2,
  Plus,
  AlertCircle,
  X,
  Check,
  DollarSign,
  Flame
} from 'lucide-react';

// Sample data for sports
const SPORTS = [
  { id: 'football', name: 'Football', icon: '🏈', active: true },
  { id: 'basketball', name: 'Basketball', icon: '🏀', active: true },
  { id: 'baseball', name: 'Baseball', icon: '⚾', active: true },
  { id: 'soccer', name: 'Soccer', icon: '⚽', active: true },
  { id: 'mma', name: 'MMA / UFC', icon: '🥊', active: true },
  { id: 'tennis', name: 'Tennis', icon: '🎾', active: true },
  { id: 'hockey', name: 'Hockey', icon: '🏒', active: false },
  { id: 'golf', name: 'Golf', icon: '🏌️', active: false },
  { id: 'cricket', name: 'Cricket', icon: '🏏', active: false },
  { id: 'esports', name: 'Esports', icon: '🎮', active: false },
  { id: 'non-sports', name: 'Non-Sports', icon: '🎲', active: true }
];

// Sample data for featured events
const FEATURED_EVENTS = [
  {
    id: 'ev1',
    league: 'NFL',
    leagueIcon: '🏈',
    teamHome: 'Kansas City Chiefs',
    teamAway: 'Baltimore Ravens',
    homeScore: 24,
    awayScore: 21,
    status: 'LIVE',
    period: '4th Quarter',
    time: '2:15',
    odds: {
      home: '-110',
      away: '+105',
      draw: '+450',
      spread: '-2.5',
      total: 'O/U 46.5'
    },
    betVolume: 78500,
    trending: true,
    favorite: true,
    startTime: 'Live Now'
  },
  {
    id: 'ev2',
    league: 'NBA',
    leagueIcon: '🏀',
    teamHome: 'Los Angeles Lakers',
    teamAway: 'Boston Celtics',
    homeScore: 98,
    awayScore: 102,
    status: 'LIVE',
    period: '3rd Quarter',
    time: '4:22',
    odds: {
      home: '+115',
      away: '-125',
      draw: '+1200',
      spread: '+3.5',
      total: 'O/U 215.5'
    },
    betVolume: 65200,
    trending: true,
    favorite: false,
    startTime: 'Live Now'
  },
  {
    id: 'ev3',
    league: 'UEFA Champions League',
    leagueIcon: '⚽',
    teamHome: 'Manchester City',
    teamAway: 'Real Madrid',
    homeScore: null,
    awayScore: null,
    status: 'UPCOMING',
    period: '',
    time: '',
    odds: {
      home: '+130',
      away: '+210',
      draw: '+240',
      spread: '-0.5',
      total: 'O/U 2.5'
    },
    betVolume: 102300,
    trending: true,
    favorite: true,
    startTime: 'Today, 7:00 PM'
  },
  {
    id: 'ev4',
    league: 'MLB',
    leagueIcon: '⚾',
    teamHome: 'New York Yankees',
    teamAway: 'Boston Red Sox',
    homeScore: null,
    awayScore: null,
    status: 'UPCOMING',
    period: '',
    time: '',
    odds: {
      home: '-140',
      away: '+120',
      draw: '+900',
      spread: '-1.5',
      total: 'O/U 8.5'
    },
    betVolume: 45800,
    trending: false,
    favorite: false,
    startTime: 'Tomorrow, 4:05 PM'
  },
  {
    id: 'ev5',
    league: 'UFC 290',
    leagueIcon: '🥊',
    teamHome: 'Jon Jones',
    teamAway: 'Stipe Miocic',
    homeScore: null,
    awayScore: null,
    status: 'UPCOMING',
    period: '',
    time: '',
    odds: {
      home: '-175',
      away: '+150',
      draw: '+2500',
      spread: '',
      total: ''
    },
    betVolume: 83600,
    trending: true,
    favorite: true,
    startTime: 'Sat, 10:00 PM'
  },
  {
    id: 'ev6',
    league: 'Tennis - Wimbledon',
    leagueIcon: '🎾',
    teamHome: 'Novak Djokovic',
    teamAway: 'Carlos Alcaraz',
    homeScore: null,
    awayScore: null,
    status: 'UPCOMING',
    period: '',
    time: '',
    odds: {
      home: '-110',
      away: '-110',
      draw: '+3300',
      spread: '-1.5 sets',
      total: 'O/U 39.5 games'
    },
    betVolume: 56400,
    trending: false,
    favorite: false,
    startTime: 'Sun, 9:00 AM'
  }
];

// Sample data for user bets
const USER_BETS = [
  {
    id: 'bet1',
    event: 'Chiefs vs Ravens',
    selection: 'Chiefs -2.5',
    odds: '-110',
    amount: 100,
    potentialWin: 190.91,
    status: 'ACTIVE'
  },
  {
    id: 'bet2',
    event: 'Lakers vs Celtics',
    selection: 'Over 215.5',
    odds: '-105',
    amount: 50,
    potentialWin: 97.62,
    status: 'ACTIVE'
  },
  {
    id: 'bet3',
    event: 'Man City vs Real Madrid',
    selection: 'Draw',
    odds: '+240',
    amount: 20,
    potentialWin: 68.00,
    status: 'PENDING'
  },
  {
    id: 'bet4',
    event: 'Packers vs Vikings',
    selection: 'Vikings +3.5',
    odds: '-105',
    amount: 75,
    potentialWin: 146.43,
    status: 'WON'
  },
  {
    id: 'bet5',
    event: 'Yankees vs Red Sox',
    selection: 'Red Sox ML',
    odds: '+120',
    amount: 40,
    potentialWin: 88.00,
    status: 'LOST'
  }
];

// Sample data for trending leagues
const TRENDING_LEAGUES = [
  { id: 'nfl', name: 'NFL', icon: '🏈', percentage: 28, volume: '$2.4M' },
  { id: 'nba', name: 'NBA', icon: '🏀', percentage: 23, volume: '$1.9M' },
  { id: 'ucl', name: 'UEFA Champions League', icon: '⚽', percentage: 18, volume: '$1.5M' },
  { id: 'mlb', name: 'MLB', icon: '⚾', percentage: 14, volume: '$1.2M' },
  { id: 'ufc', name: 'UFC', icon: '🥊', percentage: 9, volume: '$720K' }
];

// Main Sports Platform Component
const SportsPlatform = () => {
  // State variables
  const [activeTab, setActiveTab] = useState('all-sports');
  const [activeSport, setActiveSport] = useState('all');
  const [dateFilter, setDateFilter] = useState('today');
  const [oddsType, setOddsType] = useState('american');
  const [marketType, setMarketType] = useState('all');
  const [viewMode, setViewMode] = useState('detail');
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredEvents, setFilteredEvents] = useState(FEATURED_EVENTS);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState(null);
  const [activeBetsTab, setActiveBetsTab] = useState('active');
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);
  const [showMobileNav, setShowMobileNav] = useState(false);
  const [showDetailView, setShowDetailView] = useState(null);

  // Filter events based on search query and active sport
  useEffect(() => {
    let filtered = [...FEATURED_EVENTS];

    // Filter by sport
    if (activeSport !== 'all') {
      const sportName = SPORTS.find(s => s.id === activeSport)?.name || '';
      filtered = filtered.filter(event => event.league.includes(sportName));
    }

    // Filter by search query
    if (searchQuery) {
      filtered = filtered.filter(event =>
        event.league.toLowerCase().includes(searchQuery.toLowerCase()) ||
        event.teamHome.toLowerCase().includes(searchQuery.toLowerCase()) ||
        event.teamAway.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Filter by date/time
    if (dateFilter === 'today') {
      filtered = filtered.filter(event =>
        event.status === 'LIVE' || event.startTime.includes('Today')
      );
    } else if (dateFilter === 'tomorrow') {
      filtered = filtered.filter(event =>
        event.startTime.includes('Tomorrow')
      );
    } else if (dateFilter === 'week') {
      // Include all events as sample data is within a week
    }

    // Filter by active tab
    if (activeTab === 'live-now') {
      filtered = filtered.filter(event => event.status === 'LIVE');
    } else if (activeTab === 'upcoming') {
      filtered = filtered.filter(event => event.status === 'UPCOMING');
    } else if (activeTab === 'favorites') {
      filtered = filtered.filter(event => event.favorite);
    }

    setFilteredEvents(filtered);
  }, [searchQuery, activeSport, dateFilter, activeTab]);

  // Handle window resize for responsive design
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // Open event detail modal
  const handleEventClick = (event) => {
    setSelectedEvent(event);
    setShowDetailModal(true);
  };

  // Format odds based on selected odds type
  const formatOdds = (odds) => {
    if (!odds) return '';

    // Convert American odds to other formats
    if (oddsType === 'american') {
      return odds;
    } else if (oddsType === 'decimal') {
      // Simplified conversion
      if (odds.startsWith('+')) {
        return (parseFloat(odds.substring(1)) / 100 + 1).toFixed(2);
      } else if (odds.startsWith('-')) {
        return (100 / parseFloat(odds.substring(1)) + 1).toFixed(2);
      }
    } else if (oddsType === 'fractional') {
      // Simplified conversion
      if (odds.startsWith('+')) {
        const decimal = parseFloat(odds.substring(1)) / 100;
        return `${decimal}/1`;
      } else if (odds.startsWith('-')) {
        const decimal = 100 / parseFloat(odds.substring(1));
        return `1/${decimal.toFixed(1)}`;
      }
    }

    return odds;
  };

  // Toggle event in favorites
  const toggleFavorite = (event, e) => {
    e.stopPropagation();
    // In a real app, this would update the state and API
    console.log(`Toggled favorite for event: ${event.id}`);
  };

  // Place a bet
  const placeBet = (event, selection, odds, e) => {
    e.stopPropagation();
    // In a real app, this would open a bet slip or navigate to bet placement
    console.log(`Placing bet on ${event.id}, selection: ${selection}, odds: ${odds}`);
  };

  // Render sport filters in left sidebar
  const renderSportFilters = () => {
    return (
      <Accordion type="single" collapsible defaultValue="sports" className="w-full">
        <AccordionItem value="sports">
          <AccordionTrigger className="py-2 text-sm font-medium text-white">
            Sports
          </AccordionTrigger>
          <AccordionContent>
            <div className="space-y-1">
              <Button
                variant={activeSport === 'all' ? 'default' : 'ghost'}
                size="sm"
                className="w-full justify-start text-xs h-8"
                onClick={() => setActiveSport('all')}
              >
                <Globe className="w-3.5 h-3.5 mr-2" /> All Sports
              </Button>

              {SPORTS.filter(sport => sport.active).map(sport => (
                <Button
                  key={sport.id}
                  variant={activeSport === sport.id ? 'default' : 'ghost'}
                  size="sm"
                  className="w-full justify-start text-xs h-8"
                  onClick={() => setActiveSport(sport.id)}
                >
                  <span className="mr-2">{sport.icon}</span> {sport.name}
                </Button>
              ))}

              <Button
                variant="ghost"
                size="sm"
                className="w-full justify-start text-xs h-8 text-slate-400"
              >
                <ChevronRight className="w-3.5 h-3.5 mr-2" /> View All Sports
              </Button>
            </div>
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="date">
          <AccordionTrigger className="py-2 text-sm font-medium text-white">
            Date & Time
          </AccordionTrigger>
          <AccordionContent>
            <div className="flex flex-col space-y-1">
              <Button
                variant={dateFilter === 'today' ? 'default' : 'ghost'}
                size="sm"
                className="justify-start text-xs h-8"
                onClick={() => setDateFilter('today')}
              >
                Today
              </Button>
              <Button
                variant={dateFilter === 'tomorrow' ? 'default' : 'ghost'}
                size="sm"
                className="justify-start text-xs h-8"
                onClick={() => setDateFilter('tomorrow')}
              >
                Tomorrow
              </Button>
              <Button
                variant={dateFilter === 'week' ? 'default' : 'ghost'}
                size="sm"
                className="justify-start text-xs h-8"
                onClick={() => setDateFilter('week')}
              >
                This Week
              </Button>
              <Button
                variant={dateFilter === 'custom' ? 'default' : 'ghost'}
                size="sm"
                className="justify-start text-xs h-8"
                onClick={() => setDateFilter('custom')}
              >
                Custom Range
              </Button>
            </div>
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="odds">
          <AccordionTrigger className="py-2 text-sm font-medium text-white">
            Odds Type
          </AccordionTrigger>
          <AccordionContent>
            <div className="flex flex-col space-y-1">
              <Button
                variant={oddsType === 'american' ? 'default' : 'ghost'}
                size="sm"
                className="justify-start text-xs h-8"
                onClick={() => setOddsType('american')}
              >
                American (+100/-110)
              </Button>
              <Button
                variant={oddsType === 'decimal' ? 'default' : 'ghost'}
                size="sm"
                className="justify-start text-xs h-8"
                onClick={() => setOddsType('decimal')}
              >
                Decimal (2.00)
              </Button>
              <Button
                variant={oddsType === 'fractional' ? 'default' : 'ghost'}
                size="sm"
                className="justify-start text-xs h-8"
                onClick={() => setOddsType('fractional')}
              >
                Fractional (1/1)
              </Button>
            </div>
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="market">
          <AccordionTrigger className="py-2 text-sm font-medium text-white">
            Market Type
          </AccordionTrigger>
          <AccordionContent>
            <div className="flex flex-col space-y-1">
              <Button
                variant={marketType === 'all' ? 'default' : 'ghost'}
                size="sm"
                className="justify-start text-xs h-8"
                onClick={() => setMarketType('all')}
              >
                All Markets
              </Button>
              <Button
                variant={marketType === 'spread' ? 'default' : 'ghost'}
                size="sm"
                className="justify-start text-xs h-8"
                onClick={() => setMarketType('spread')}
              >
                Spreads
              </Button>
              <Button
                variant={marketType === 'moneyline' ? 'default' : 'ghost'}
                size="sm"
                className="justify-start text-xs h-8"
                onClick={() => setMarketType('moneyline')}
              >
                Moneylines
              </Button>
              <Button
                variant={marketType === 'totals' ? 'default' : 'ghost'}
                size="sm"
                className="justify-start text-xs h-8"
                onClick={() => setMarketType('totals')}
              >
                Over/Unders
              </Button>
              <Button
                variant={marketType === 'futures' ? 'default' : 'ghost'}
                size="sm"
                className="justify-start text-xs h-8"
                onClick={() => setMarketType('futures')}
              >
                Futures
              </Button>
              <Button
                variant={marketType === 'props' ? 'default' : 'ghost'}
                size="sm"
                className="justify-start text-xs h-8"
                onClick={() => setMarketType('props')}
              >
                Props
              </Button>
            </div>
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="view">
          <AccordionTrigger className="py-2 text-sm font-medium text-white">
            View Mode
          </AccordionTrigger>
          <AccordionContent>
            <div className="flex flex-col space-y-1">
              <Button
                variant={viewMode === 'compact' ? 'default' : 'ghost'}
                size="sm"
                className="justify-start text-xs h-8"
                onClick={() => setViewMode('compact')}
              >
                Compact View
              </Button>
              <Button
                variant={viewMode === 'detail' ? 'default' : 'ghost'}
                size="sm"
                className="justify-start text-xs h-8"
                onClick={() => setViewMode('detail')}
              >
                Detail View
              </Button>
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    );
  };

  // Render event cards in center content
  const renderEventCard = (event, isFeatured = false) => {
    const isExpanded = showDetailView === event.id;

    return (
      <Card
        key={event.id}
        className={`w-full mb-2 bg-slate-900 border-slate-800 hover:border-slate-700 transition-all overflow-hidden cursor-pointer ${isFeatured ? 'border-l-4 border-l-amber-500' : ''}`}
        onClick={() => handleEventClick(event)}
      >
        <CardHeader className={`p-3 ${event.status === 'LIVE' ? 'bg-slate-800 bg-opacity-50' : ''}`}>
          <div className="flex justify-between items-center">
            <div className="flex items-center">
              <span className="mr-1">{event.leagueIcon}</span>
              <span className="text-xs font-medium text-slate-300">{event.league}</span>
              {event.status === 'LIVE' && (
                <Badge className="ml-2 bg-red-500 animate-pulse text-[10px] py-0">LIVE</Badge>
              )}
              {event.trending && (
                <Badge className="ml-2 bg-orange-500 text-[10px] py-0">
                  <Fire className="h-3 w-3 mr-1" /> Trending
                </Badge>
              )}
            </div>
            <div className="flex items-center space-x-1">
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0"
                onClick={(e) => toggleFavorite(event, e)}
              >
                <Star className={`h-3.5 w-3.5 ${event.favorite ? 'text-yellow-400 fill-yellow-400' : 'text-slate-400'}`} />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0"
                onClick={(e) => {
                  e.stopPropagation();
                  setShowDetailView(isExpanded ? null : event.id);
                }}
              >
                <ChevronDown className={`h-3.5 w-3.5 transition-transform ${isExpanded ? 'rotate-180' : ''}`} />
              </Button>
            </div>
          </div>
        </CardHeader>

        <CardContent className="p-3">
          <div className="flex justify-between items-center">
            <div className="flex-1">
              <div className="font-medium text-white">{event.teamHome}</div>
              <div className="font-medium text-white">{event.teamAway}</div>
            </div>

            <div className="flex-shrink-0 w-12 text-center">
              {event.status === 'LIVE' ? (
                <>
                  <div className="font-bold text-white">{event.homeScore}</div>
                  <div className="font-bold text-white">{event.awayScore}</div>
                </>
              ) : (
                <div className="text-xs text-slate-400">{event.startTime}</div>
              )}
            </div>

            <div className="flex-shrink-0 ml-2">
              <Button
                variant="outline"
                size="sm"
                className="h-8 px-2 text-xs mb-1 justify-between w-24"
                onClick={(e) => placeBet(event, `${event.teamHome} ML`, event.odds.home, e)}
              >
                <span>{event.teamHome.split(' ').pop()}</span>
                <span className={event.odds.home.startsWith('+') ? "text-green-400" : "text-red-400"}>
                  {formatOdds(event.odds.home)}
                </span>
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="h-8 px-2 text-xs justify-between w-24"
                onClick={(e) => placeBet(event, `${event.teamAway} ML`, event.odds.away, e)}
              >
                <span>{event.teamAway.split(' ').pop()}</span>
                <span className={event.odds.away.startsWith('+') ? "text-green-400" : "text-red-400"}>
                  {formatOdds(event.odds.away)}
                </span>
              </Button>
            </div>
          </div>

          {event.status === 'LIVE' && (
            <div className="mt-2 text-xs text-red-400 font-medium flex items-center">
              <Clock className="h-3 w-3 mr-1" />
              {event.period} • {event.time}
            </div>
          )}

          {isExpanded && (
            <div className="mt-3 border-t border-slate-800 pt-3">
              <div className="grid grid-cols-3 gap-2 mb-3">
                <Button
                  variant="outline"
                  size="sm"
                  className="h-8 text-xs justify-between"
                  onClick={(e) => placeBet(event, event.odds.spread, '-110', e)}
                >
                  <span>Spread</span>
                  <span className="text-slate-400">{event.odds.spread}</span>
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="h-8 text-xs justify-between"
                  onClick={(e) => placeBet(event, event.odds.total, '-110', e)}
                >
                  <span>Total</span>
                  <span className="text-slate-400">{event.odds.total}</span>
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="h-8 text-xs justify-between"
                  onClick={(e) => placeBet(event, 'Draw', event.odds.draw, e)}
                >
                  <span>Draw</span>
                  <span className="text-green-400">{formatOdds(event.odds.draw)}</span>
                </Button>
              </div>

              <div className="flex space-x-2">
                <Button
                  variant="default"
                  size="sm"
                  className="h-8 text-xs bg-gradient-to-r from-purple-500 to-pink-500"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleEventClick(event);
                  }}
                >
                  View All Odds
                  <ExternalLink className="h-3 w-3 ml-1" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="h-8 text-xs"
                  onClick={(e) => {
                    e.stopPropagation();
                    console.log('Open bet slip');
                  }}
                >
                  <Plus className="h-3 w-3 mr-1" />
                  Add to Bet Slip
                </Button>
              </div>

              <div className="mt-3 text-xs text-slate-400 flex items-center justify-between">
                <div>
                  <TrendingUp className="h-3 w-3 inline mr-1" />
                  Betting Volume: ${(event.betVolume / 1000).toFixed(1)}K
                </div>
                <div className="flex space-x-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0"
                    onClick={(e) => {
                      e.stopPropagation();
                      console.log('Share event');
                    }}
                  >
                    <Share2 className="h-3 w-3" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0"
                    onClick={(e) => {
                      e.stopPropagation();
                      console.log('More options');
                    }}
                  >
                    <MoreHorizontal className="h-3 w-3" />
                  </Button>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  // Render user bets in the right sidebar
  const renderUserBets = () => {
    const filteredBets = USER_BETS.filter(bet => {
      if (activeBetsTab === 'active') return bet.status === 'ACTIVE';
      if (activeBetsTab === 'pending') return bet.status === 'PENDING';
      if (activeBetsTab === 'won') return bet.status === 'WON';
      if (activeBetsTab === 'lost') return bet.status === 'LOST';
      return true;
    });

    if (filteredBets.length === 0) {
      return (
        <div className="text-center py-4 text-xs text-slate-400">
          No {activeBetsTab.toLowerCase()} bets found
        </div>
      );
    }

    return (
      <div className="space-y-2">
        {filteredBets.map(bet => (
          <Card key={bet.id} className="bg-slate-800 border-slate-700">
            <CardContent className="p-2">
              <div className="flex justify-between items-start">
                <div>
                  <div className="text-xs font-medium text-white">{bet.event}</div>
                  <div className="text-xs text-slate-400">{bet.selection}</div>
                </div>
                <Badge
                  className={`
                    ${bet.status === 'ACTIVE' ? 'bg-blue-500' :
                      bet.status === 'PENDING' ? 'bg-yellow-500' :
                      bet.status === 'WON' ? 'bg-green-500' : 'bg-red-500'}
                  `}
                >
                  {bet.status}
                </Badge>
              </div>
              <div className="mt-2 flex justify-between text-xs">
                <div>
                  <div className="text-slate-400">Stake</div>
                  <div className="text-white">${bet.amount.toFixed(2)}</div>
                </div>
                <div>
                  <div className="text-slate-400">Odds</div>
                  <div className="text-white">{formatOdds(bet.odds)}</div>
                </div>
                <div>
                  <div className="text-slate-400">To Win</div>
                  <div className="text-green-400">${bet.potentialWin.toFixed(2)}</div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  };

  // Render trending leagues in the right sidebar
  const renderTrendingLeagues = () => {
    return (
      <div className="space-y-2">
        {TRENDING_LEAGUES.map(league => (
          <div
            key={league.id}
            className="flex justify-between items-center p-2 bg-slate-800 rounded-sm hover:bg-slate-700 cursor-pointer"
          >
            <div className="flex items-center">
              <span className="text-xl mr-2">{league.icon}</span>
              <div>
                <div className="text-xs font-medium text-white">{league.name}</div>
                <div className="text-xs text-slate-400">Volume: {league.volume}</div>
              </div>
            </div>
            <div>
              <Badge className="bg-green-500/20 text-green-400">
                {league.percentage}%
              </Badge>
            </div>
          </div>
        ))}
      </div>
    );
  };

  // Render watchlist in the right sidebar
  const renderWatchlist = () => {
    const favoriteEvents = FEATURED_EVENTS.filter(event => event.favorite);

    if (favoriteEvents.length === 0) {
      return (
        <div className="text-center py-4 text-xs text-slate-400">
          No favorite events yet.
          <Button
            variant="ghost"
            size="sm"
            className="w-full h-8 text-xs mt-2"
          >
            <Star className="h-3.5 w-3.5 mr-1" />
            Add Favorites
          </Button>
        </div>
      );
    }

    return (
      <div className="space-y-2">
        {favoriteEvents.map(event => (
          <div
            key={event.id}
            className="flex justify-between items-center p-2 bg-slate-800 rounded-sm hover:bg-slate-700 cursor-pointer"
            onClick={() => handleEventClick(event)}
          >
            <div>
              <div className="text-xs font-medium text-white">
                {event.teamHome} vs {event.teamAway}
              </div>
              <div className="text-xs text-slate-400">
                {event.league} • {event.status === 'LIVE' ? 'LIVE' : event.startTime}
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0"
              onClick={(e) => toggleFavorite(event, e)}
            >
              <Star className="h-3.5 w-3.5 text-yellow-400 fill-yellow-400" />
            </Button>
          </div>
        ))}
      </div>
    );
  };

  // Render event detail modal
  const renderEventDetailModal = () => {
    if (!selectedEvent) return null;

    return (
      <div className="fixed inset-0 bg-black bg-opacity-80 z-50 flex items-center justify-center p-4 overflow-auto">
        <div className="bg-slate-900 border border-slate-800 rounded-md w-full max-w-4xl max-h-full overflow-auto">
          <div className="p-4 border-b border-slate-800 flex justify-between items-center sticky top-0 bg-slate-900 z-10">
            <div className="flex items-center">
              <span className="mr-2">{selectedEvent.leagueIcon}</span>
              <h2 className="text-lg font-bold text-white">{selectedEvent.league}</h2>
              {selectedEvent.status === 'LIVE' && (
                <Badge className="ml-2 bg-red-500 animate-pulse">LIVE</Badge>
              )}
            </div>
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0"
              onClick={() => setShowDetailModal(false)}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>

          <div className="p-4">
            <div className="flex justify-between items-center mb-4">
              <div className="text-2xl font-bold text-white">{selectedEvent.teamHome}</div>
              <div className="text-4xl font-bold">
                {selectedEvent.status === 'LIVE' ? (
                  <span className="text-white">{selectedEvent.homeScore} - {selectedEvent.awayScore}</span>
                ) : (
                  <span className="text-slate-600">VS</span>
                )}
              </div>
              <div className="text-2xl font-bold text-white">{selectedEvent.teamAway}</div>
            </div>

            {selectedEvent.status === 'LIVE' && (
              <div className="mb-4 text-sm text-red-400 font-medium text-center">
                {selectedEvent.period} • {selectedEvent.time}
              </div>
            )}

            <Tabs defaultValue="overview">
              <TabsList className="w-full mb-4 grid grid-cols-7">
                <TabsTrigger value="overview" className="text-xs">Overview</TabsTrigger>
                <TabsTrigger value="live" className="text-xs">Live Scores</TabsTrigger>
                <TabsTrigger value="odds" className="text-xs">Odds</TabsTrigger>
                <TabsTrigger value="stats" className="text-xs">Stats</TabsTrigger>
                <TabsTrigger value="h2h" className="text-xs">H2H</TabsTrigger>
                <TabsTrigger value="line" className="text-xs">Line Movement</TabsTrigger>
                <TabsTrigger value="bet" className="text-xs">Place Bet</TabsTrigger>
              </TabsList>

              <TabsContent value="overview">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Card className="bg-slate-800 border-slate-700">
                    <CardHeader className="p-3">
                      <CardTitle className="text-sm">Match Info</CardTitle>
                    </CardHeader>
                    <CardContent className="p-3 pt-0">
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-slate-400">Status:</span>
                          <span className="text-white">{selectedEvent.status}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-slate-400">Start Time:</span>
                          <span className="text-white">{selectedEvent.startTime}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-slate-400">League:</span>
                          <span className="text-white">{selectedEvent.league}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-slate-400">Bet Volume:</span>
                          <span className="text-white">${(selectedEvent.betVolume / 1000).toFixed(1)}K</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="bg-slate-800 border-slate-700">
                    <CardHeader className="p-3">
                      <CardTitle className="text-sm">Market Odds</CardTitle>
                    </CardHeader>
                    <CardContent className="p-3 pt-0">
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-slate-400">{selectedEvent.teamHome} Moneyline:</span>
                          <span className={selectedEvent.odds.home.startsWith('+') ? "text-green-400" : "text-red-400"}>
                            {formatOdds(selectedEvent.odds.home)}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-slate-400">{selectedEvent.teamAway} Moneyline:</span>
                          <span className={selectedEvent.odds.away.startsWith('+') ? "text-green-400" : "text-red-400"}>
                            {formatOdds(selectedEvent.odds.away)}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-slate-400">Draw:</span>
                          <span className="text-green-400">{formatOdds(selectedEvent.odds.draw)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-slate-400">Spread:</span>
                          <span className="text-white">{selectedEvent.odds.spread}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-slate-400">Total:</span>
                          <span className="text-white">{selectedEvent.odds.total}</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                <Card className="mt-4 bg-slate-800 border-slate-700">
                  <CardHeader className="p-3">
                    <CardTitle className="text-sm">Popular Bets</CardTitle>
                  </CardHeader>
                  <CardContent className="p-3 pt-0">
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        className="h-10 text-xs justify-between"
                      >
                        <div className="text-left">
                          <div>{selectedEvent.teamHome} ML</div>
                          <div className="text-slate-400 text-[10px]">43% of bets</div>
                        </div>
                        <span className={selectedEvent.odds.home.startsWith('+') ? "text-green-400" : "text-red-400"}>
                          {formatOdds(selectedEvent.odds.home)}
                        </span>
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="h-10 text-xs justify-between"
                      >
                        <div className="text-left">
                          <div>{selectedEvent.teamAway} ML</div>
                          <div className="text-slate-400 text-[10px]">38% of bets</div>
                        </div>
                        <span className={selectedEvent.odds.away.startsWith('+') ? "text-green-400" : "text-red-400"}>
                          {formatOdds(selectedEvent.odds.away)}
                        </span>
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="h-10 text-xs justify-between"
                      >
                        <div className="text-left">
                          <div>Over {selectedEvent.odds.total.split(' ')[1]}</div>
                          <div className="text-slate-400 text-[10px]">65% of bets</div>
                        </div>
                        <span className="text-red-400">-110</span>
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="h-10 text-xs justify-between"
                      >
                        <div className="text-left">
                          <div>Under {selectedEvent.odds.total.split(' ')[1]}</div>
                          <div className="text-slate-400 text-[10px]">35% of bets</div>
                        </div>
                        <span className="text-red-400">-110</span>
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="h-10 text-xs justify-between"
                      >
                        <div className="text-left">
                          <div>{selectedEvent.teamHome} {selectedEvent.odds.spread}</div>
                          <div className="text-slate-400 text-[10px]">52% of bets</div>
                        </div>
                        <span className="text-red-400">-110</span>
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="h-10 text-xs justify-between"
                      >
                        <div className="text-left">
                          <div>Draw</div>
                          <div className="text-slate-400 text-[10px]">19% of bets</div>
                        </div>
                        <span className="text-green-400">{formatOdds(selectedEvent.odds.draw)}</span>
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                <div className="mt-6 flex justify-center space-x-3">
                  <Button
                    variant="default"
                    className="bg-gradient-to-r from-purple-500 to-pink-500"
                  >
                    <Plus className="h-4 w-4 mr-1" />
                    Open Bet Slip
                  </Button>
                  <Button variant="outline">
                    <Share2 className="h-4 w-4 mr-1" />
                    Share Event
                  </Button>
                </div>
              </TabsContent>

              <TabsContent value="bet">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Card className="bg-slate-800 border-slate-700">
                      <CardHeader className="p-3">
                        <CardTitle className="text-sm">Bet Options</CardTitle>
                      </CardHeader>
                      <CardContent className="p-3 pt-0">
                        <div className="space-y-2">
                          <div className="flex justify-between items-center p-2 bg-slate-700 rounded-sm">
                            <div>
                              <div className="text-sm text-white">{selectedEvent.teamHome} ML</div>
                              <div className="text-xs text-slate-400">Moneyline</div>
                            </div>
                            <Button
                              variant="outline"
                              size="sm"
                              className="h-8 w-20"
                              onClick={() => {
                                // Add to bet slip
                              }}
                            >
                              <span className={selectedEvent.odds.home.startsWith('+') ? "text-green-400" : "text-red-400"}>
                                {formatOdds(selectedEvent.odds.home)}
                              </span>
                            </Button>
                          </div>

                          <div className="flex justify-between items-center p-2 bg-slate-700 rounded-sm">
                            <div>
                              <div className="text-sm text-white">{selectedEvent.teamAway} ML</div>
                              <div className="text-xs text-slate-400">Moneyline</div>
                            </div>
                            <Button
                              variant="outline"
                              size="sm"
                              className="h-8 w-20"
                              onClick={() => {
                                // Add to bet slip
                              }}
                            >
                              <span className={selectedEvent.odds.away.startsWith('+') ? "text-green-400" : "text-red-400"}>
                                {formatOdds(selectedEvent.odds.away)}
                              </span>
                            </Button>
                          </div>

                          <div className="flex justify-between items-center p-2 bg-slate-700 rounded-sm">
                            <div>
                              <div className="text-sm text-white">Draw</div>
                              <div className="text-xs text-slate-400">Moneyline</div>
                            </div>
                            <Button
                              variant="outline"
                              size="sm"
                              className="h-8 w-20"
                              onClick={() => {
                                // Add to bet slip
                              }}
                            >
                              <span className="text-green-400">{formatOdds(selectedEvent.odds.draw)}</span>
                            </Button>
                          </div>

                          <div className="flex justify-between items-center p-2 bg-slate-700 rounded-sm">
                            <div>
                              <div className="text-sm text-white">{selectedEvent.teamHome} {selectedEvent.odds.spread}</div>
                              <div className="text-xs text-slate-400">Spread</div>
                            </div>
                            <Button
                              variant="outline"
                              size="sm"
                              className="h-8 w-20"
                              onClick={() => {
                                // Add to bet slip
                              }}
                            >
                              <span className="text-red-400">-110</span>
                            </Button>
                          </div>

                          <div className="flex justify-between items-center p-2 bg-slate-700 rounded-sm">
                            <div>
                              <div className="text-sm text-white">Over {selectedEvent.odds.total.split(' ')[1]}</div>
                              <div className="text-xs text-slate-400">Total</div>
                            </div>
                            <Button
                              variant="outline"
                              size="sm"
                              className="h-8 w-20"
                              onClick={() => {
                                // Add to bet slip
                              }}
                            >
                              <span className="text-red-400">-110</span>
                            </Button>
                          </div>

                          <div className="flex justify-between items-center p-2 bg-slate-700 rounded-sm">
                            <div>
                              <div className="text-sm text-white">Under {selectedEvent.odds.total.split(' ')[1]}</div>
                              <div className="text-xs text-slate-400">Total</div>
                            </div>
                            <Button
                              variant="outline"
                              size="sm"
                              className="h-8 w-20"
                              onClick={() => {
                                // Add to bet slip
                              }}
                            >
                              <span className="text-red-400">-110</span>
                            </Button>
                          </div>

                          <Button
                            variant="ghost"
                            size="sm"
                            className="w-full h-8 text-xs text-blue-400"
                          >
                            View More Bet Options
                            <ChevronDown className="h-3.5 w-3.5 ml-1" />
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  <div>
                    <Card className="bg-slate-800 border-slate-700">
                      <CardHeader className="p-3">
                        <CardTitle className="text-sm">Bet Slip</CardTitle>
                      </CardHeader>
                      <CardContent className="p-3 pt-0">
                        <div className="h-52 flex flex-col items-center justify-center border border-dashed border-slate-700 rounded-sm mb-3">
                          <DollarSign className="h-8 w-8 text-slate-600 mb-2" />
                          <div className="text-sm text-slate-400 text-center">
                            Your bet slip is empty.<br />
                            Select odds to add bets.
                          </div>
                        </div>

                        <div className="space-y-3">
                          <div className="flex items-center">
                            <div className="text-sm text-white mr-2">Enter stake:</div>
                            <div className="relative flex-grow">
                              <DollarSign className="h-4 w-4 text-slate-400 absolute left-2 top-2" />
                              <Input
                                className="pl-8 bg-slate-700 border-slate-600 h-8"
                                placeholder="0.00"
                              />
                            </div>
                          </div>

                          <div className="flex space-x-2">
                            <Button
                              variant="outline"
                              size="sm"
                              className="flex-1 h-8 text-xs"
                            >
                              $10
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              className="flex-1 h-8 text-xs"
                            >
                              $25
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              className="flex-1 h-8 text-xs"
                            >
                              $50
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              className="flex-1 h-8 text-xs"
                            >
                              $100
                            </Button>
                          </div>

                          <Button
                            variant="default"
                            className="w-full bg-gradient-to-r from-green-500 to-emerald-600"
                            disabled
                          >
                            Place Bet
                          </Button>

                          <div className="text-xs text-slate-500 text-center">
                            Select odds and enter stake to place bet
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    <Card className="mt-4 bg-slate-800 border-slate-700">
                      <CardHeader className="p-3">
                        <CardTitle className="text-sm">Create Custom Bet</CardTitle>
                      </CardHeader>
                      <CardContent className="p-3 pt-0">
                        <div className="text-xs text-slate-400 mb-3">
                          Can't find the bet you want? Create a custom bet and share it with the community.
                        </div>
                        <Button
                          variant="default"
                          size="sm"
                          className="w-full h-8 text-xs bg-gradient-to-r from-purple-500 to-pink-500"
                        >
                          <Plus className="h-3.5 w-3.5 mr-1" />
                          Create Custom Bet
                        </Button>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="live">
                <div className="flex justify-center items-center h-40 text-slate-400">
                  Live match data will be shown here
                </div>
              </TabsContent>

              <TabsContent value="odds">
                <div className="flex justify-center items-center h-40 text-slate-400">
                  Detailed odds will be shown here
                </div>
              </TabsContent>

              <TabsContent value="stats">
                <div className="flex justify-center items-center h-40 text-slate-400">
                  Match statistics will be shown here
                </div>
              </TabsContent>

              <TabsContent value="h2h">
                <div className="flex justify-center items-center h-40 text-slate-400">
                  Head-to-head record will be shown here
                </div>
              </TabsContent>

              <TabsContent value="line">
                <div className="flex justify-center items-center h-40 text-slate-400">
                  Odds movement chart will be shown here
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
    );
  };

  // Main render function
  return (
    <div className="w-full h-full bg-slate-950 text-white overflow-hidden flex flex-col">
      {/* Top Navigation Bar */}
      <div className="flex justify-between items-center p-2 border-b border-slate-800 bg-slate-900">
        <div className="flex items-center">
          {isMobile && (
            <Button
              variant="ghost"
              size="sm"
              className="mr-2 h-8 w-8 p-0"
              onClick={() => setShowMobileNav(!showMobileNav)}
            >
              <Menu className="h-4 w-4" />
            </Button>
          )}
          <div className="font-bold text-lg bg-gradient-to-r from-purple-500 to-pink-500 bg-clip-text text-transparent">
            BetBet
          </div>
        </div>

        <div className={`hidden md:flex items-center space-x-1`}>
          <Button variant="ghost" size="sm" className="h-8">
            <Home className="h-4 w-4 mr-1" />
            Home
          </Button>
          <Button variant="ghost" size="sm" className="h-8">
            <BarChart2 className="h-4 w-4 mr-1" />
            Dashboard
          </Button>
          <Button variant="default" size="sm" className="h-8 bg-gradient-to-r from-purple-500 to-pink-500">
            <Globe className="h-4 w-4 mr-1" />
            Sports
          </Button>
          <Button variant="ghost" size="sm" className="h-8">
            <Trophy className="h-4 w-4 mr-1" />
            Challenges
          </Button>
          <Button variant="ghost" size="sm" className="h-8">
            <Wallet className="h-4 w-4 mr-1" />
            Wallet
          </Button>
        </div>

        <div className="flex items-center space-x-1">
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0 relative">
            <Bell className="h-4 w-4" />
            <span className="absolute top-1 right-1 h-2 w-2 rounded-full bg-red-500"></span>
          </Button>
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
            <User className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="sm" className="h-8 pl-2 pr-3">
            <Wallet className="h-4 w-4 mr-1" />
            <span className="text-green-400">$1,250</span>
          </Button>
        </div>
      </div>

      {/* Mobile Navigation Drawer */}
      {isMobile && showMobileNav && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-40" onClick={() => setShowMobileNav(false)}>
          <div className="bg-slate-900 w-64 h-full p-4 border-r border-slate-800" onClick={e => e.stopPropagation()}>
            <div className="flex justify-between items-center mb-4">
              <div className="font-bold text-lg bg-gradient-to-r from-purple-500 to-pink-500 bg-clip-text text-transparent">
                BetBet
              </div>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0"
                onClick={() => setShowMobileNav(false)}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            <div className="space-y-1">
              <Button variant="ghost" size="sm" className="w-full justify-start h-10">
                <Home className="h-4 w-4 mr-2" />
                Home
              </Button>
              <Button variant="ghost" size="sm" className="w-full justify-start h-10">
                <BarChart2 className="h-4 w-4 mr-2" />
                Dashboard
              </Button>
              <Button variant="default" size="sm" className="w-full justify-start h-10 bg-gradient-to-r from-purple-500 to-pink-500">
                <Globe className="h-4 w-4 mr-2" />
                Sports
              </Button>
              <Button variant="ghost" size="sm" className="w-full justify-start h-10">
                <Trophy className="h-4 w-4 mr-2" />
                Challenges
              </Button>
              <Button variant="ghost" size="sm" className="w-full justify-start h-10">
                <Wallet className="h-4 w-4 mr-2" />
                Wallet
              </Button>
            </div>

            <div className="mt-4 pt-4 border-t border-slate-800">
              {renderSportFilters()}
            </div>
          </div>
        </div>
      )}

      {/* Sub-Navigation Tabs */}
      <div className="border-b border-slate-800 bg-slate-900 overflow-x-auto">
        <div className="flex p-1 min-w-max">
          <Button
            variant={activeTab === 'all-sports' ? 'default' : 'ghost'}
            size="sm"
            className="h-8 text-xs"
            onClick={() => setActiveTab('all-sports')}
          >
            All Sports
          </Button>
          <Button
            variant={activeTab === 'live-now' ? 'default' : 'ghost'}
            size="sm"
            className="h-8 text-xs"
            onClick={() => setActiveTab('live-now')}
          >
            <span className="relative">
              Live Now
              <span className="absolute -top-1 -right-1 h-2 w-2 rounded-full bg-red-500 animate-pulse"></span>
            </span>
          </Button>
          <Button
            variant={activeTab === 'upcoming' ? 'default' : 'ghost'}
            size="sm"
            className="h-8 text-xs"
            onClick={() => setActiveTab('upcoming')}
          >
            Upcoming
          </Button>
          <Button
            variant={activeTab === 'popular' ? 'default' : 'ghost'}
            size="sm"
            className="h-8 text-xs"
            onClick={() => setActiveTab('popular')}
          >
            Popular Leagues
          </Button>
          <Button
            variant={activeTab === 'favorites' ? 'default' : 'ghost'}
            size="sm"
            className="h-8 text-xs"
            onClick={() => setActiveTab('favorites')}
          >
            <Star className="h-3.5 w-3.5 mr-1" />
            My Favorites
          </Button>
          <Button
            variant={activeTab === 'exotics' ? 'default' : 'ghost'}
            size="sm"
            className="h-8 text-xs"
            onClick={() => setActiveTab('exotics')}
          >
            Exotics / Non-Sports
          </Button>

          <div className="ml-auto relative">
            <Search className="absolute top-2 left-2 h-4 w-4 text-slate-400" />
            <Input
              className="h-8 bg-slate-800 border-slate-700 pl-8 text-xs w-48"
              placeholder="Search teams, leagues, events..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex-1 overflow-hidden">
        <div className="h-full grid grid-cols-12 gap-2 p-2">
          {/* Left Sidebar - Filters and Shortcuts */}
          <div className={`${isMobile ? 'hidden' : 'col-span-2'} flex flex-col gap-2 h-full overflow-hidden`}>
            <div className="bg-slate-900 border border-slate-800 rounded-sm flex-1 overflow-auto p-2">
              {renderSportFilters()}
            </div>
          </div>

          {/* Center Content - Main Feed */}
          <div className={`${isMobile ? 'col-span-12' : 'col-span-7'} flex flex-col gap-2 h-full overflow-hidden`}>
            {/* Featured Event - Hero Section */}
            {filteredEvents.length > 0 && filteredEvents[0].trending && (
              <div className="bg-slate-900 border border-slate-800 rounded-sm p-3">
                <div className="flex justify-between items-center mb-2">
                  <div className="flex items-center">
                    <Fire className="h-4 w-4 text-orange-500 mr-1" />
                    <h3 className="text-sm font-medium text-white">Featured Match</h3>
                  </div>
                  <Badge className="bg-gradient-to-r from-orange-500 to-pink-500">
                    HOT BET
                  </Badge>
                </div>

                {renderEventCard(filteredEvents[0], true)}
              </div>
            )}

            {/* Events Grid */}
            <div className="bg-slate-900 border border-slate-800 rounded-sm flex-1 overflow-hidden flex flex-col">
              <div className="p-2 border-b border-slate-800 flex justify-between items-center">
                <h3 className="text-sm font-medium text-white">
                  {activeTab === 'all-sports' && 'All Sports'}
                  {activeTab === 'live-now' && 'Live Games'}
                  {activeTab === 'upcoming' && 'Upcoming Events'}
                  {activeTab === 'popular' && 'Popular Leagues'}
                  {activeTab === 'favorites' && 'My Favorites'}
                  {activeTab === 'exotics' && 'Exotic Bets'}
                </h3>
                <div className="flex items-center space-x-1">
                  <Badge className="bg-slate-800 text-slate-300 text-xs">
                    {filteredEvents.length} events
                  </Badge>
                  <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                    <Filter className="h-3.5 w-3.5" />
                  </Button>
                </div>
              </div>

              <div className="flex-1 overflow-auto p-2">
                {filteredEvents.length === 0 ? (
                  <div className="h-full flex flex-col items-center justify-center text-slate-400">
                    <AlertCircle className="h-8 w-8 mb-2" />
                    <p className="text-sm mb-1">No events found</p>
                    <p className="text-xs">Try changing your filters or search query</p>
                  </div>
                ) : (
                  <div className="space-y-2">
                    {filteredEvents.map((event, index) => {
                      // Skip the first item if it's featured and we're on the "all" tab
                      if (index === 0 && event.trending && activeTab === 'all-sports') {
                        return null;
                      }
                      return renderEventCard(event);
                    })}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Right Sidebar - Dynamic Widgets */}
          <div className={`${isMobile ? 'hidden' : 'col-span-3'} flex flex-col gap-2 h-full overflow-hidden`}>
            {/* My Bets */}
            <div className="bg-slate-900 border border-slate-800 rounded-sm p-2">
              <div className="flex justify-between items-center mb-2">
                <h3 className="text-sm font-medium text-white">My Bets</h3>
                <Button
                  variant="outline"
                  size="sm"
                  className="h-6 text-xs"
                >
                  <Plus className="h-3.5 w-3.5 mr-1" />
                  New Bet
                </Button>
              </div>

              <div className="flex border-b border-slate-800 mb-2">
                <Button
                  variant={activeBetsTab === 'active' ? 'default' : 'ghost'}
                  size="sm"
                  className="h-7 text-xs rounded-none"
                  onClick={() => setActiveBetsTab('active')}
                >
                  Active
                </Button>
                <Button
                  variant={activeBetsTab === 'pending' ? 'default' : 'ghost'}
                  size="sm"
                  className="h-7 text-xs rounded-none"
                  onClick={() => setActiveBetsTab('pending')}
                >
                  Pending
                </Button>
                <Button
                  variant={activeBetsTab === 'won' ? 'default' : 'ghost'}
                  size="sm"
                  className="h-7 text-xs rounded-none"
                  onClick={() => setActiveBetsTab('won')}
                >
                  <Check className="h-3.5 w-3.5 mr-1 text-green-500" />
                  Won
                </Button>
                <Button
                  variant={activeBetsTab === 'lost' ? 'default' : 'ghost'}
                  size="sm"
                  className="h-7 text-xs rounded-none"
                  onClick={() => setActiveBetsTab('lost')}
                >
                  <X className="h-3.5 w-3.5 mr-1 text-red-500" />
                  Lost
                </Button>
              </div>

              <div className="h-40 overflow-auto">
                {renderUserBets()}
              </div>
            </div>

            {/* Trending Leagues */}
            <div className="bg-slate-900 border border-slate-800 rounded-sm p-2">
              <div className="flex justify-between items-center mb-2">
                <h3 className="text-sm font-medium text-white">Trending Leagues</h3>
                <Badge className="bg-gradient-to-r from-green-500 to-emerald-600">
                  <TrendingUp className="h-3 w-3 mr-1" /> LIVE
                </Badge>
              </div>

              <div className="h-32 overflow-auto">
                {renderTrendingLeagues()}
              </div>
            </div>

            {/* Watchlist / Favorites */}
            <div className="bg-slate-900 border border-slate-800 rounded-sm p-2 flex-1 overflow-hidden flex flex-col">
              <div className="flex justify-between items-center mb-2">
                <h3 className="text-sm font-medium text-white">My Watchlist</h3>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0"
                >
                  <Bookmark className="h-3.5 w-3.5" />
                </Button>
              </div>

              <div className="flex-1 overflow-auto">
                {renderWatchlist()}
              </div>
            </div>

            {/* Live Commentary */}
            <div className="bg-slate-900 border border-slate-800 rounded-sm p-2">
              <div className="flex justify-between items-center mb-2">
                <h3 className="text-sm font-medium text-white">Live Commentary</h3>
                <Badge className="bg-red-500 text-xs">LIVE</Badge>
              </div>

              <div className="h-32 overflow-auto space-y-2 text-xs">
                <div className="flex space-x-2">
                  <div className="flex-shrink-0">
                    <Avatar className="h-6 w-6 bg-slate-700">
                      <span className="text-[10px]">JD</span>
                    </Avatar>
                  </div>
                  <div className="flex-1 p-2 bg-slate-800 rounded-md">
                    <div className="font-medium text-white">John_Doe</div>
                    <div className="text-slate-300">Chiefs defense looking strong in the 4th quarter!</div>
                    <div className="text-slate-500 mt-1">2 minutes ago</div>
                  </div>
                </div>

                <div className="flex space-x-2">
                  <div className="flex-shrink-0">
                    <Avatar className="h-6 w-6 bg-slate-700">
                      <span className="text-[10px]">BS</span>
                    </Avatar>
                  </div>
                  <div className="flex-1 p-2 bg-slate-800 rounded-md">
                    <div className="font-medium text-white">BetSlayer22</div>
                    <div className="text-slate-300">Ravens need to convert this 3rd down or it's game over</div>
                    <div className="text-slate-500 mt-1">5 minutes ago</div>
                  </div>
                </div>

                <Button
                  variant="ghost"
                  size="sm"
                  className="w-full h-7 text-xs"
                >
                  <MessageSquare className="h-3.5 w-3.5 mr-1" />
                  Join Chat
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Bottom Navigation */}
      {isMobile && (
        <div className="bg-slate-900 border-t border-slate-800 p-1 grid grid-cols-5 gap-1">
          <Button
            variant="ghost"
            size="sm"
            className="h-12 flex flex-col items-center justify-center space-y-1 rounded-md"
          >
            <Home className="h-4 w-4" />
            <span className="text-[10px]">Home</span>
          </Button>
          <Button
            variant="ghost"
            size="sm"
            className="h-12 flex flex-col items-center justify-center space-y-1 rounded-md"
          >
            <Globe className="h-4 w-4" />
            <span className="text-[10px]">Sports</span>
          </Button>
          <Button
            variant="default"
            size="sm"
            className="h-12 flex flex-col items-center justify-center space-y-1 rounded-md bg-gradient-to-r from-purple-500 to-pink-500"
          >
            <Plus className="h-4 w-4" />
            <span className="text-[10px]">Bet</span>
          </Button>
          <Button
            variant="ghost"
            size="sm"
            className="h-12 flex flex-col items-center justify-center space-y-1 rounded-md"
          >
            <Wallet className="h-4 w-4" />
            <span className="text-[10px]">Wallet</span>
          </Button>
          <Button
            variant="ghost"
            size="sm"
            className="h-12 flex flex-col items-center justify-center space-y-1 rounded-md"
          >
            <User className="h-4 w-4" />
            <span className="text-[10px]">Profile</span>
          </Button>
        </div>
      )}

      {/* Event Detail Modal */}
      {showDetailModal && renderEventDetailModal()}
    </div>
  );
};

export default SportsPlatform;