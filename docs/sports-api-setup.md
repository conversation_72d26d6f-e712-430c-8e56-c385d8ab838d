# Sports API Setup Guide

This guide explains how to set up and test the Sports Game Odds API integration in the BetBet platform.

## 1. Configure Your API Key

### Update the .env file
Edit the `.env` file in your project root and add your actual API key:

```env
# External Sports API (add your actual keys)
SPORTS_API_KEY=your_actual_api_key_here  # Replace with your real API key
SPORTS_API_URL=https://api.sportsgameodds.com/v1
```

## 2. Test Endpoints

We've created several endpoints to interact with the Sports API:

### Test Connection
```
GET /api/sports/api-test
```
This endpoint tests your API connection and returns diagnostic information.

### Live Sports Data (requires API key)
- `GET /api/sports/sports-live` - Get all available sports
- `GET /api/sports/leagues-live/{sport_id}` - Get leagues for a sport
- `GET /api/sports/games-live/upcoming` - Get upcoming games
- `GET /api/sports/games-live/{game_id}/odds` - Get odds for a game
- `GET /api/sports/games-live/live` - Get live games

### Mock Data (no API key required)
- `GET /api/sports/sports` - Mock sports data
- `GET /api/sports/events` - Mock events data
- `GET /api/sports/leagues` - Mock leagues data

## 3. Testing the API

### Option 1: Using the FastAPI Docs
1. Start the server: `uvicorn main:app --reload`
2. Navigate to: `http://localhost:8000/docs`
3. Find the `/api/sports/api-test` endpoint
4. Click "Try it out" and "Execute"

### Option 2: Using the test script
```bash
# Run the automated test
python test_sports_api.py
```

### Option 3: Manual test with your API key
1. Edit `manual_test_sports.py` and add your API key
2. Run: `python manual_test_sports.py`

### Option 4: Using curl
```bash
# Test the API connection
curl -X GET "http://localhost:8000/api/sports/api-test" \
  -H "accept: application/json"

# Get live sports data (requires authentication)
curl -X GET "http://localhost:8000/api/sports/sports-live" \
  -H "accept: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## 4. API Headers

When making direct requests to the Sports API, use these headers:

```http
X-API-Key: your_actual_api_key_here
Content-Type: application/json
```

## 5. Response Format

The API typically returns JSON responses. Example:

```json
{
  "status": "success",
  "data": [
    {
      "id": "football",
      "name": "Football",
      "leagues": ["NFL", "NCAAF"]
    }
  ]
}
```

## 6. Error Handling

The integration includes error handling:
- Returns mock data if API is unavailable
- Provides diagnostic information on connection failures
- Rate limiting to prevent excessive API calls

## 7. Rate Limits

Our endpoints have rate limits:
- Test endpoint: 10 requests per minute
- Sports data: 30 requests per minute
- Odds data: 60 requests per minute

## 8. Troubleshooting

### API Key Not Working
1. Verify the key is correctly set in `.env`
2. Restart the server after changing `.env`
3. Check the API key has proper permissions

### Connection Errors
1. Verify the API URL is correct
2. Check your internet connection
3. Test with the manual script first

### No Data Returned
1. Check if the API requires specific parameters
2. Verify the endpoint path is correct
3. Check API documentation for required fields

## 9. Security Notes

- Never commit your API key to version control
- Use environment variables for all sensitive data
- Implement proper authentication for production use
- Monitor API usage to stay within limits

## 10. Next Steps

1. Replace mock data with real API data
2. Implement caching for frequently accessed data
3. Add more comprehensive error handling
4. Create background jobs for data updates
5. Implement webhook support for live updates