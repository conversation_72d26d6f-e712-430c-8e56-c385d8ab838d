"""<PERSON><PERSON><PERSON> to create Alembic migration for enhanced models."""
import os
import sys
from alembic import command
from alembic.config import Config

# Add the app directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Create Alembic configuration
alembic_cfg = Config("alembic.ini")

# Create migration with a descriptive message
migration_message = "Add enhanced models for user stats, achievements, wallet, games, and bets"

# Run the migration
command.revision(alembic_cfg, message=migration_message, autogenerate=True)

print(f"Migration created: {migration_message}")
print("Run 'alembic upgrade head' to apply the migration")