import requests
import json
from datetime import datetime

# Base URL
BASE_URL = "http://localhost:8000/api"

# Create unique username
timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
username = f"gametest_{timestamp}"
email = f"gametest_{timestamp}@example.com"
password = "Test1234!"

# Register user
register_data = {
    "username": username,
    "email": email,
    "password": password
}

print("Registering user...")
response = requests.post(f"{BASE_URL}/auth/register", json=register_data)
print(f"Register response: {response.status_code}")
print(response.json())

if response.status_code != 200:
    print("Registration failed!")
    exit(1)

# Login to get token
login_data = {
    "username": username,
    "password": password
}

print("\nLogging in...")
response = requests.post(f"{BASE_URL}/auth/login", data=login_data)
print(f"Login response: {response.status_code}")
print(response.json())

if response.status_code != 200:
    print("Login failed!")
    exit(1)

token = response.json()["access_token"]
headers = {"Authorization": f"Bearer {token}"}

# Create a game
game_data = {
    "game_type": "chess",
    "wager_amount": 10.0
}

print("\nCreating game...")
response = requests.post(f"{BASE_URL}/game-instances/create", json=game_data, headers=headers)
print(f"Create game response: {response.status_code}")
print(response.json())

if response.status_code == 200:
    game_id = response.json()["id"]
    print(f"\nGame created successfully! ID: {game_id}")
    
    # Get game details
    print("\nGetting game details...")
    response = requests.get(f"{BASE_URL}/game-instances/{game_id}", headers=headers)
    print(f"Get game response: {response.status_code}")
    print(json.dumps(response.json(), indent=2))