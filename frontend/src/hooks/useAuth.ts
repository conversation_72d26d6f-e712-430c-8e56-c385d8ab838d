import { useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import authService from '../services/auth';
import useUserStore from '../stores/userStore';

interface LoginCredentials {
  email: string;
  password: string;
}

interface RegisterData {
  username: string;
  email: string;
  password: string;
  terms?: boolean;
}

const useAuth = () => {
  const navigate = useNavigate();
  const { user, isAuthenticated } = useUserStore();

  const login = useCallback(
    async (credentials: LoginCredentials) => {
      try {
        await authService.login(credentials);
        navigate('/dashboard');
        return true;
      } catch (error) {
        // console.error('Login error:', error);
        return false;
      }
    },
    [navigate]
  );

  const register = useCallback(
    async (userData: RegisterData) => {
      try {
        await authService.register(userData);
        navigate('/dashboard');
        return true;
      } catch (error) {
        // console.error('Registration error:', error);
        return false;
      }
    },
    [navigate]
  );

  const logout = useCallback(async () => {
    await authService.logout();
    navigate('/login');
  }, [navigate]);

  return {
    user,
    isAuthenticated,
    login,
    register,
    logout,
  };
};

export default useAuth;
