import { useState, useCallback } from 'react';
import axios, { AxiosError } from 'axios';

interface ApiState<T> {
  data: T | null;
  isLoading: boolean;
  error: string | null;
}

interface ApiHook<T, P> {
  data: T | null;
  isLoading: boolean;
  error: string | null;
  execute: (params?: P) => Promise<T | null>;
  reset: () => void;
}

function useApi<T = any, P = any>(
  apiFunction: (params?: P) => Promise<any>
): ApiHook<T, P> {
  const [state, setState] = useState<ApiState<T>>({
    data: null,
    isLoading: false,
    error: null,
  });

  const execute = useCallback(
    async (params?: P) => {
      setState({ data: null, isLoading: true, error: null });
      try {
        const response = await apiFunction(params);
        const data = response.data;
        setState({ data, isLoading: false, error: null });
        return data;
      } catch (error) {
        let errorMessage = 'An unexpected error occurred';
        
        if (axios.isAxiosError(error)) {
          const axiosError = error as AxiosError;
          errorMessage = axiosError.response?.data && typeof axiosError.response.data === 'object' && 'message' in axiosError.response.data 
            ? String(axiosError.response.data.message) 
            : axiosError.message;
        } else if (error instanceof Error) {
          errorMessage = error.message;
        }
        
        setState({ data: null, isLoading: false, error: errorMessage });
        return null;
      }
    },
    [apiFunction]
  );

  const reset = useCallback(() => {
    setState({ data: null, isLoading: false, error: null });
  }, []);

  return {
    ...state,
    execute,
    reset,
  };
}

export default useApi;
