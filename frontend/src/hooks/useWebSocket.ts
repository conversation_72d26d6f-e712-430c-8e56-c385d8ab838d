import { useEffect, useCallback } from 'react';
import websocketService from '../services/websocket';
import useUserStore from '../stores/userStore';

const useWebSocket = () => {
  const { isAuthenticated } = useUserStore();

  // Connect to WebSocket when authenticated
  useEffect(() => {
    if (isAuthenticated) {
      websocketService.connect();
    } else {
      websocketService.disconnect();
    }

    // Cleanup on unmount
    return () => {
      websocketService.disconnect();
    };
  }, [isAuthenticated]);

  // Subscribe to an event
  const subscribe = useCallback((event: string, callback: (data: any) => void) => {
    return websocketService.on(event, callback);
  }, []);

  // Emit an event
  const emit = useCallback((event: string, data: any) => {
    websocketService.emit(event, data);
  }, []);

  // Join a game lobby
  const joinGameLobby = useCallback((gameId: string) => {
    websocketService.joinGameLobby(gameId);
  }, []);

  // Submit a game answer
  const submitGameAnswer = useCallback(
    (gameId: string, questionId: string, answer: any) => {
      websocketService.submitGameAnswer(gameId, questionId, answer);
    },
    []
  );

  return {
    isConnected: websocketService.isConnected(),
    subscribe,
    emit,
    joinGameLobby,
    submitGameAnswer,
  };
};

export default useWebSocket;
