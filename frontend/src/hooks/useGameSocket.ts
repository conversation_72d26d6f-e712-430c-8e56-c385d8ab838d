import { useEffect, useState, useCallback } from 'react';
import { io, Socket } from 'socket.io-client';

interface UseGameSocketProps {
  gameType: string;
}

interface GameSocketReturn {
  socket: Socket | null;
  connected: boolean;
  joinGame: (gameId: string, playerData: any) => void;
  leaveGame: (gameId: string) => void;
  sendMessage: (data: any) => void;
  sendGameAction: (data: any) => void;
  error: string | null;
}

export function useGameSocket(gameType: string): GameSocketReturn {
  const [socket, setSocket] = useState<Socket | null>(null);
  const [connected, setConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Create socket connection
    const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:8000';
    const newSocket = io(`${apiUrl}/games/${gameType}`, {
      auth: {
        token: localStorage.getItem('token')
      }
    });

    newSocket.on('connect', () => {
      setConnected(true);
      setError(null);
    });

    newSocket.on('disconnect', () => {
      setConnected(false);
    });

    newSocket.on('error', (err: any) => {
      setError(err.message || 'Connection error');
    });

    setSocket(newSocket);

    return () => {
      newSocket.disconnect();
    };
  }, [gameType]);

  const joinGame = useCallback((gameId: string, playerData: any) => {
    if (socket && connected) {
      socket.emit('joinGame', { gameId, ...playerData });
    }
  }, [socket, connected]);

  const leaveGame = useCallback((gameId: string) => {
    if (socket && connected) {
      socket.emit('leaveGame', { gameId });
    }
  }, [socket, connected]);

  const sendMessage = useCallback((data: any) => {
    if (socket && connected) {
      socket.emit('sendMessage', data);
    }
  }, [socket, connected]);

  const sendGameAction = useCallback((data: any) => {
    if (socket && connected) {
      socket.emit('gameAction', data);
    }
  }, [socket, connected]);

  return {
    socket,
    connected,
    joinGame,
    leaveGame,
    sendMessage,
    sendGameAction,
    error
  };
}