import type { RouteObject } from 'react-router-dom';
import { Navigate } from 'react-router-dom';

// Import components
import LandingPage from '@/components/landing/LandingPage';
import LoginForm from '@/components/auth/LoginForm';
import RegisterForm from '@/components/auth/RegisterForm';
import DashboardComponent from '@/components/dashboard/Dashboard';
import { BetMarketplace } from '@/components/marketplace';
import { SportsPage, MobileSportsPage } from '@/components/sports';
import { ProfilePage, MobileProfilePage } from '@/components/profile';
import { ExpertPicks, MobileExpertPicks } from '@/components/expert-picks';
import Leaderboard from '@/components/leaderboard';
import Wallet from '@/components/wallet';

// Game components
import Games from '@/components/games';
import GameDetailsScreen from '@/components/games/GameDetailsScreen';
import CheckersGameWrapper from '@/components/games/CheckersGameWrapper';
import ChessGameWrapper from '@/components/games/ChessGameWrapper';
import RockPaperScissorsGame from '@/components/games/RockPaperScissorsGame';
import MobileRockPaperScissorsGame from '@/components/games/MobileRockPaperScissorsGame';
import HighlightHeroGameWrapper from '@/components/games/HighlightHeroGameWrapper';
import BlurDetectiveGameWrapper from '@/components/games/BlurDetectiveGameWrapper';
import WordJumbleGameWrapper from '@/components/games/WordJumbleGameWrapper';

// Admin components
import AdminLayout from '@/components/admin/AdminLayout';
import AdminDashboard from '@/components/admin/AdminDashboard';
import KYCDashboard from '@/components/admin/KYCDashboard';

// URL structure configuration
export const ROUTES = {
  // Public routes
  HOME: '/',
  LOGIN: '/login',
  REGISTER: '/register',
  LEADERBOARD: '/leaderboard',

  // Main sections
  DASHBOARD: '/dashboard',
  WALLET: '/wallet',
  PROFILE: '/profile',

  // Sports section
  SPORTS: {
    ROOT: '/sports',
    FOOTBALL: '/sports/football',
    BASKETBALL: '/sports/basketball',
    TENNIS: '/sports/tennis',
    CRICKET: '/sports/cricket',
    MATCH: '/sports/:sport/:matchId', // e.g., /sports/football/match-123
    BET: '/sports/:sport/:matchId/bet/:betId', // e.g., /sports/football/match-123/bet/456
  },

  // Marketplace section
  MARKETPLACE: {
    ROOT: '/marketplace',
    CREATE: '/marketplace/create',
    BET: '/marketplace/bet/:betId', // e.g., /marketplace/bet/789
    USER_BETS: '/marketplace/user/:userId', // e.g., /marketplace/user/john-doe
    CATEGORY: '/marketplace/category/:category', // e.g., /marketplace/category/sports
  },

  // Games section - Three-tier hierarchy
  GAMES: {
    ROOT: '/games', // Games Catalog Page
    INSTANCES: '/games/:gameSlug', // Game Instances Page - e.g., /games/highlight-hero
    SESSION: '/games/:gameSlug/session/:sessionId', // Game Session Page - e.g., /games/highlight-hero/session/abc123
    SPECTATE: '/games/:gameSlug/spectate/:sessionId', // Spectate Session - e.g., /games/highlight-hero/spectate/abc123
    TOURNAMENT: '/games/tournament/:tournamentId', // e.g., /games/tournament/summer-2024
    // Legacy routes for backward compatibility
    LOBBY: '/games/lobby/:gameType',
    // Specific game slugs
    CHESS: '/games/chess',
    CHECKERS: '/games/checkers',
    ROCK_PAPER_SCISSORS: '/games/rock-paper-scissors',
    HIGHLIGHT_HERO: '/games/highlight-hero',
    BLUR_DETECTIVE: '/games/blur-detective',
    WORD_JUMBLE: '/games/word-jumble',
    QUIZ_ARENA: '/games/quiz-arena',
  },

  // Expert picks section
  EXPERT_PICKS: {
    ROOT: '/expert-picks',
    EXPERT: '/expert-picks/expert/:expertId', // e.g., /expert-picks/expert/john-analyst
    CATEGORY: '/expert-picks/category/:category', // e.g., /expert-picks/category/football
    PICK: '/expert-picks/pick/:pickId', // e.g., /expert-picks/pick/123
  },

  // Admin section
  ADMIN: {
    ROOT: '/admin',
    DASHBOARD: '/admin/dashboard',
    KYC: '/admin/kyc',
    KYC_USER: '/admin/kyc/user/:userId', // e.g., /admin/kyc/user/456
    USERS: '/admin/users',
    USER_DETAIL: '/admin/users/:userId', // e.g., /admin/users/123
    SETTINGS: '/admin/settings',
    GAMES: '/admin/games',
    GAME_SESSIONS: '/admin/games/sessions',
    REPORTS: '/admin/reports',
  },
};

// Helper function to generate game session URL
export const generateGameSessionUrl = (gameType: string, sessionId: string, spectate = false) => {
  const baseUrl = spectate ? '/games/:gameType/spectate/:sessionId' : '/games/:gameType/session/:sessionId';
  return baseUrl.replace(':gameType', gameType).replace(':sessionId', sessionId);
};

// Helper function to generate marketplace bet URL
export const generateMarketplaceBetUrl = (betId: string) => {
  return ROUTES.MARKETPLACE.BET.replace(':betId', betId);
};

// Helper function to generate sports match URL
export const generateSportsMatchUrl = (sport: string, matchId: string) => {
  return ROUTES.SPORTS.MATCH.replace(':sport', sport).replace(':matchId', matchId);
};

// Helper function to generate expert pick URL
export const generateExpertPickUrl = (pickId: string) => {
  return ROUTES.EXPERT_PICKS.PICK.replace(':pickId', pickId);
};

// Helper to check if user is on mobile
const isMobile = () => window.innerWidth < 768;

// Route configuration with components
export const getRouteConfig = (isAuthenticated: boolean, user: any): RouteObject[] => {
  return [
    // Public routes
    {
      path: ROUTES.HOME,
      element: isAuthenticated ? <Navigate to={ROUTES.DASHBOARD} /> : <LandingPage />,
    },
    {
      path: ROUTES.LOGIN,
      element: isAuthenticated ? <Navigate to={ROUTES.DASHBOARD} /> : <LoginForm />,
    },
    {
      path: ROUTES.REGISTER,
      element: isAuthenticated ? <Navigate to={ROUTES.DASHBOARD} /> : <RegisterForm />,
    },
    {
      path: ROUTES.LEADERBOARD,
      element: <Leaderboard />,
    },

    // Protected routes
    {
      path: ROUTES.DASHBOARD,
      element: isAuthenticated ? <DashboardComponent /> : <Navigate to={ROUTES.LOGIN} />,
    },
    {
      path: ROUTES.WALLET,
      element: isAuthenticated ? <Wallet /> : <Navigate to={ROUTES.LOGIN} />,
    },
    {
      path: ROUTES.PROFILE,
      element: isAuthenticated ? (
        isMobile() ? <MobileProfilePage onBack={() => window.history.back()} /> : <ProfilePage />
      ) : <Navigate to={ROUTES.LOGIN} />,
    },

    // Sports routes
    {
      path: ROUTES.SPORTS.ROOT,
      element: isAuthenticated ? (
        isMobile() ? <MobileSportsPage /> : <SportsPage />
      ) : <Navigate to={ROUTES.LOGIN} />,
    },
    {
      path: ROUTES.SPORTS.FOOTBALL,
      element: isAuthenticated ? (
        isMobile() ? <MobileSportsPage sport="football" /> : <SportsPage sport="football" />
      ) : <Navigate to={ROUTES.LOGIN} />,
    },
    {
      path: ROUTES.SPORTS.BASKETBALL,
      element: isAuthenticated ? (
        isMobile() ? <MobileSportsPage sport="basketball" /> : <SportsPage sport="basketball" />
      ) : <Navigate to={ROUTES.LOGIN} />,
    },
    {
      path: ROUTES.SPORTS.TENNIS,
      element: isAuthenticated ? (
        isMobile() ? <MobileSportsPage sport="tennis" /> : <SportsPage sport="tennis" />
      ) : <Navigate to={ROUTES.LOGIN} />,
    },
    {
      path: ROUTES.SPORTS.CRICKET,
      element: isAuthenticated ? (
        isMobile() ? <MobileSportsPage sport="cricket" /> : <SportsPage sport="cricket" />
      ) : <Navigate to={ROUTES.LOGIN} />,
    },

    // Marketplace routes
    {
      path: ROUTES.MARKETPLACE.ROOT,
      element: isAuthenticated ? <BetMarketplace /> : <Navigate to={ROUTES.LOGIN} />,
    },

    // Games routes
    {
      path: ROUTES.GAMES.ROOT,
      element: isAuthenticated ? <Games /> : <Navigate to={ROUTES.LOGIN} />,
    },
    {
      path: ROUTES.GAMES.CHESS,
      element: isAuthenticated ? <ChessGameWrapper /> : <Navigate to={ROUTES.LOGIN} />,
    },
    {
      path: ROUTES.GAMES.CHECKERS,
      element: isAuthenticated ? <CheckersGameWrapper /> : <Navigate to={ROUTES.LOGIN} />,
    },
    {
      path: ROUTES.GAMES.ROCK_PAPER_SCISSORS,
      element: isAuthenticated ? (
        isMobile() ? <MobileRockPaperScissorsGame onBack={() => window.history.back()} gameId="" wagerAmount={50} /> : <RockPaperScissorsGame />
      ) : <Navigate to={ROUTES.LOGIN} />,
    },
    {
      path: ROUTES.GAMES.HIGHLIGHT_HERO,
      element: isAuthenticated ? <HighlightHeroGameWrapper isMobile={isMobile()} /> : <Navigate to={ROUTES.LOGIN} />,
    },
    {
      path: ROUTES.GAMES.BLUR_DETECTIVE,
      element: isAuthenticated ? <BlurDetectiveGameWrapper /> : <Navigate to={ROUTES.LOGIN} />,
    },
    {
      path: ROUTES.GAMES.WORD_JUMBLE,
      element: isAuthenticated ? <WordJumbleGameWrapper /> : <Navigate to={ROUTES.LOGIN} />,
    },

    // Expert picks routes
    {
      path: ROUTES.EXPERT_PICKS.ROOT,
      element: isAuthenticated ? (
        isMobile() ? <MobileExpertPicks /> : <ExpertPicks />
      ) : <Navigate to={ROUTES.LOGIN} />,
    },

    // Admin routes
    {
      path: ROUTES.ADMIN.ROOT,
      element: isAuthenticated && user?.is_superuser ? <AdminLayout /> : <Navigate to={ROUTES.DASHBOARD} />,
      children: [
        {
          index: true,
          element: <Navigate to={ROUTES.ADMIN.DASHBOARD} />,
        },
        {
          path: 'dashboard',
          element: <AdminDashboard />,
        },
        {
          path: 'kyc',
          element: <KYCDashboard />,
        },
        {
          path: 'users',
          element: <div>User Management - Coming Soon</div>,
        },
        {
          path: 'settings',
          element: <div>Settings - Coming Soon</div>,
        },
      ],
    },

    // Fallback
    {
      path: '*',
      element: <Navigate to={ROUTES.HOME} replace />,
    },
  ];
};