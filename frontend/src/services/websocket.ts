import { io, Socket } from 'socket.io-client';
import useUserStore from '../stores/userStore';

const WS_URL = import.meta.env.VITE_WS_URL || 'ws://localhost:8000/ws';

class WebSocketService {
  private socket: Socket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private eventHandlers: Record<string, Array<(data: any) => void>> = {};

  connect() {
    if (this.socket) {
      return;
    }

    const token = useUserStore.getState().token;

    this.socket = io(WS_URL, {
      auth: {
        token,
      },
      reconnection: true,
      reconnectionAttempts: this.maxReconnectAttempts,
      reconnectionDelay: this.reconnectDelay,
    });

    this.setupEventListeners();
  }

  private setupEventListeners() {
    if (!this.socket) return;

    this.socket.on('connect', () => {
      // console.log('WebSocket connected');
      this.reconnectAttempts = 0;
    });

    this.socket.on('disconnect', (reason) => {
      // console.log(`WebSocket disconnected: ${reason}`);
    });

    this.socket.on('connect_error', (error) => {
      // console.error('WebSocket connection error:', error);
      this.reconnectAttempts++;

      if (this.reconnectAttempts > this.maxReconnectAttempts) {
        // console.error('Max reconnect attempts reached, giving up');
      }
    });

    // Register all event handlers
    Object.keys(this.eventHandlers).forEach((event) => {
      this.eventHandlers[event].forEach((handler) => {
        this.socket?.on(event, handler);
      });
    });
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
  }

  emit(event: string, data: any) {
    if (!this.socket) {
      // console.error('Cannot emit event: WebSocket not connected');
      return;
    }

    this.socket.emit(event, data);
  }

  on(event: string, callback: (data: any) => void) {
    // Store the handler for reconnection purposes
    if (!this.eventHandlers[event]) {
      this.eventHandlers[event] = [];
    }
    this.eventHandlers[event].push(callback);

    // Register the handler if socket exists
    if (this.socket) {
      this.socket.on(event, callback);
    }

    // Return a function to remove the listener
    return () => {
      this.off(event, callback);
    };
  }

  off(event: string, callback: (data: any) => void) {
    // Remove from stored handlers
    if (this.eventHandlers[event]) {
      this.eventHandlers[event] = this.eventHandlers[event].filter(
        (handler) => handler !== callback
      );
    }

    // Remove from socket if it exists
    if (this.socket) {
      this.socket.off(event, callback);
    }
  }

  // Game-specific methods
  joinGameLobby(gameId: string) {
    this.emit('game:lobby:join', { gameId });
  }

  submitGameAnswer(gameId: string, questionId: string, answer: any) {
    this.emit('game:answer:submit', { gameId, questionId, answer });
  }

  // Helper method to check connection status
  isConnected(): boolean {
    return this.socket?.connected || false;
  }
}

// Create a singleton instance
const websocketService = new WebSocketService();

export default websocketService;
