import { authAPI, userAP<PERSON> } from './api';
import useUserStore from '../stores/userStore';
import { walletService } from './wallet';

interface LoginCredentials {
  email: string;
  password: string;
}

interface RegisterData {
  username: string;
  email: string;
  password: string;
  terms?: boolean;
}

// Response from authentication endpoints
export interface AuthResponse {
  user: {
    id: string;
    username: string;
    email: string;
    balance: number;
  };
  access_token: string;
  refresh_token: string;
}

const authService = {
  login: async (credentials: LoginCredentials) => {
    try {
      // console.log('Login - Starting login process');
      const response = await authAPI.login(credentials);
      // console.log('Login - Response received:', response.data);
      
      const { access_token, refresh_token } = response.data;
      // console.log('Login - Tokens extracted:', { access_token: !!access_token, refresh_token: !!refresh_token });

      // Get user data using the token
      useUserStore.getState().setTokens(access_token, refresh_token);
      // console.log('Login - Tokens stored in user store');
      
      // Verify token storage
      const storedToken = useUserStore.getState().token;
      // console.log('Login - Verify stored token:', storedToken);

      // Fetch user profile
      const userResponse = await userAPI.getProfile();
      // console.log('Login - User profile fetched:', userResponse.data);
      const userData = userResponse.data;

      // Store user data
      useUserStore.getState().setUser(userData);
      // console.log('Login - User data stored');

      // Sync wallet data
      try {
        await walletService.getBalance();
        await walletService.getTransactions();
        // console.log('Login - Wallet synced successfully');
      } catch (walletError) {
        // console.error('Failed to sync wallet:', walletError);
      }

      return userData;
    } catch (error) {
      // console.error('Login error:', error);
      throw error;
    }
  },

  register: async (userData: RegisterData) => {
    try {
      // Register user
      const regResponse = await authAPI.register(userData);
      const newUser = regResponse.data;

      // Login the user after registration
      const loginResponse = await authAPI.login({
        email: userData.email,
        password: userData.password
      });

      const { access_token, refresh_token } = loginResponse.data;

      // Store tokens and user data
      useUserStore.getState().setTokens(access_token, refresh_token);
      useUserStore.getState().setUser(newUser);

      // Sync wallet data
      try {
        await walletService.getBalance();
        await walletService.getTransactions();
      } catch (walletError) {
        // console.error('Failed to sync wallet:', walletError);
      }

      return newUser;
    } catch (error) {
      // console.error('Registration error:', error);
      throw error;
    }
  },

  logout: async () => {
    try {
      // Call logout API to invalidate token on server
      // This might not be necessary on a JWT-based system, but it's good practice
      // for token blacklisting if implemented on the server
      try {
        await authAPI.logout();
      } catch (e) {
        // Even if server logout fails, clear locally
        // console.warn('Server logout failed, but proceeding with local logout');
      }
    } finally {
      // Clear user data from store regardless of API success
      useUserStore.getState().logout();
    }
  },

  refreshToken: async () => {
    const refreshToken = useUserStore.getState().refreshToken;

    if (!refreshToken) {
      throw new Error('No refresh token available');
    }

    try {
      const response = await authAPI.refreshToken(refreshToken);
      const data = response.data;

      // Update tokens in store
      useUserStore.getState().setTokens(data.access_token, data.refresh_token);

      return data;
    } catch (error) {
      // console.error('Token refresh error:', error);
      // If refresh fails, logout user
      useUserStore.getState().logout();
      throw error;
    }
  },

  isAuthenticated: () => {
    return useUserStore.getState().isAuthenticated;
  },
};

export default authService;