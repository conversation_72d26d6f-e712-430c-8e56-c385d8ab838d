import api from './api';
import type { GameType, GameStatus } from '@/types/game';

export interface GameCreateData {
  game_type: GameType;
  wager_amount: number;
  settings?: any;
}

export interface GameResponse {
  id: string;
  game_type: GameType;
  status: GameStatus;
  player1_id: string;
  player2_id?: string;
  current_player_id?: string;
  winner_id?: string;
  wager_amount: number;
  total_pot: number;
  settings: any;
  state: any;
  created_at: string;
  started_at?: string;
  completed_at?: string;
  last_move_at?: string;
}

export interface GameListResponse extends GameResponse {
  player1_username?: string;
  player2_username?: string;
}

export const gameService = {
  // Create a new game
  async createGame(data: GameCreateData): Promise<GameResponse> {
    const response = await api.post('/api/games/create', data);
    return response.data;
  },

  // Join an existing game
  async joinGame(gameId: string): Promise<GameResponse> {
    const response = await api.post(`/api/games/${gameId}/join`);
    return response.data;
  },

  // Get available games to join
  async getAvailableGames(gameType?: GameType): Promise<GameListResponse[]> {
    const params = gameType ? { game_type: gameType } : {};
    const response = await api.get('/api/games/available', { params });
    return response.data;
  },

  // Get user's games
  async getMyGames(status?: GameStatus): Promise<GameListResponse[]> {
    const params = status ? { status } : {};
    const response = await api.get('/api/games/my-games', { params });
    return response.data;
  },

  // Get game details
  async getGame(gameId: string): Promise<GameResponse> {
    const response = await api.get(`/api/games/${gameId}`);
    return response.data;
  },

  // Spectate a game
  async spectateGame(gameId: string): Promise<any> {
    const response = await api.post(`/api/games/${gameId}/spectate`);
    return response.data;
  },

  // Complete a game
  async completeGame(gameId: string, winnerId: string): Promise<any> {
    const response = await api.post(`/api/games/${gameId}/complete`, { winner_id: winnerId });
    return response.data;
  }
};