import api from './api';

export interface ExpertProfile {
  id: number;
  user_id: number;
  is_expert: boolean;
  total_picks: number;
  win_rate: number;
  monthly_revenue: number;
  total_revenue: number;
  followers_count: number;
  avg_odds: number;
  roi: number;
  win_streak: number;
  rating: number;
}

export interface ExpertPick {
  id: number;
  expert_id: number;
  sport: string;
  league: string;
  match: string;
  event_date: string;
  pick_type: string;
  pick_description: string;
  odds: number;
  confidence: number;
  price: number;
  vip_price?: number;
  status: 'draft' | 'active' | 'completed' | 'cancelled';
  booking_code: string;
  universal_code: string;
  analysis?: string;
  media_urls: string[];
  sales_count: number;
  result?: string;
  created_at: string;
  updated_at: string;
}

export interface PickPack {
  id: number;
  expert_id: number;
  name: string;
  description?: string;
  price: number;
  confidence: number;
  expected_roi?: number;
  valid_until: string;
  image_url?: string;
  sales_count: number;
  rating: number;
  picks: ExpertPick[];
}

export interface ExpertStats {
  total_picks: number;
  win_rate: number;
  monthly_revenue: number;
  followers: number;
  avg_odds: number;
  roi: number;
  streak: number;
  rating: number;
}

class ExpertPicksService {
  // Profile methods
  async createOrUpdateProfile(is_expert: boolean) {
    const response = await api.post('/api/expert-picks/profile', { is_expert });
    return response.data;
  }

  async getProfile(userId: string): Promise<ExpertProfile> {
    const response = await api.get(`/api/expert-picks/profile/${userId}`);
    return response.data;
  }

  async getStats(): Promise<ExpertStats> {
    const response = await api.get('/api/expert-picks/stats');
    return response.data;
  }

  // Pick methods
  async createPick(pickData: {
    sport: string;
    league: string;
    match: string;
    event_date: string;
    pick_type: string;
    pick_description: string;
    odds: number;
    confidence: number;
    price: number;
    vip_price?: number;
    analysis?: string;
    media_urls?: string[];
  }): Promise<ExpertPick> {
    const response = await api.post('/api/expert-picks/picks', pickData);
    return response.data;
  }

  async updatePick(pickId: number, updateData: Partial<ExpertPick>): Promise<ExpertPick> {
    const response = await api.put(`/api/expert-picks/picks/${pickId}`, updateData);
    return response.data;
  }

  async getPicks(params?: {
    expert_id?: number;
    sport?: string;
    status?: string;
    limit?: number;
    offset?: number;
  }): Promise<ExpertPick[]> {
    const response = await api.get('/api/expert-picks/picks', { params });
    return response.data;
  }

  async getPick(pickId: number): Promise<ExpertPick> {
    const response = await api.get(`/api/expert-picks/picks/${pickId}`);
    return response.data;
  }

  async getMyPicks(status?: string): Promise<ExpertPick[]> {
    const response = await api.get('/api/expert-picks/my-picks', { params: { status } });
    return response.data;
  }

  // Purchase methods
  async purchasePick(pickId: number, isVip: boolean = false) {
    const response = await api.post(`/api/expert-picks/picks/${pickId}/purchase`, { 
      pick_id: pickId,
      is_vip: isVip 
    });
    return response.data;
  }

  async getPurchasedPicks() {
    const response = await api.get('/api/expert-picks/purchased');
    return response.data;
  }

  // Pack methods
  async createPack(packData: {
    name: string;
    description?: string;
    price: number;
    confidence: number;
    expected_roi?: number;
    valid_until: string;
    image_url?: string;
    pick_ids: number[];
  }): Promise<PickPack> {
    const response = await api.post('/api/expert-picks/packs', packData);
    return response.data;
  }

  async getPacks(expertId?: number, limit: number = 20, offset: number = 0): Promise<PickPack[]> {
    const response = await api.get('/api/expert-picks/packs', { 
      params: { expert_id: expertId, limit, offset } 
    });
    return response.data;
  }

  // Follow methods
  async followExpert(expertId: number) {
    const response = await api.post('/api/expert-picks/follow', { expert_id: expertId });
    return response.data;
  }

  async unfollowExpert(expertId: number) {
    const response = await api.delete(`/api/expert-picks/follow/${expertId}`);
    return response.data;
  }
}

export default new ExpertPicksService();