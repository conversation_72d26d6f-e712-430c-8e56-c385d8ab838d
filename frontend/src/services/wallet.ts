import api from './api';
import useUserStore from '@/stores/userStore';
import useWalletStore from '@/stores/walletStore';

// Use the same api instance as other services
const apiClient = api;

export const walletService = {
  // Get current balance
  async getBalance() {
    try {
      const response = await apiClient.get('/api/wallet/balance');
      const { balance, username, user_id } = response.data;
      
      // Update wallet store
      const walletStore = useWalletStore.getState();
      walletStore.setBalance(balance);
      walletStore.setUser(username, user_id);
      
      return response.data;
    } catch (error) {
      // console.error('Failed to get balance:', error);
      throw error;
    }
  },

  // Deposit funds
  async deposit(amount: number, paymentMethod: string = 'mock') {
    try {
      const response = await apiClient.post('/api/wallet/deposit', {
        amount,
        payment_method: paymentMethod,
      });
      
      const { balance } = response.data;
      useWalletStore.getState().setBalance(balance);
      
      return response.data;
    } catch (error) {
      // console.error('Failed to deposit:', error);
      throw error;
    }
  },

  // Withdraw funds
  async withdraw(amount: number, paymentMethod: string = 'mock') {
    try {
      const response = await apiClient.post('/api/wallet/withdraw', {
        amount,
        payment_method: paymentMethod,
      });
      
      const { balance } = response.data;
      useWalletStore.getState().setBalance(balance);
      
      return response.data;
    } catch (error) {
      // console.error('Failed to withdraw:', error);
      throw error;
    }
  },

  // Place bet
  async placeBet(amount: number, gameId: string, description: string) {
    try {
      // console.log('WalletService - Placing bet:', { amount, gameId, description });
      const token = useUserStore.getState().token;
      // console.log('WalletService - Current token exists:', !!token);
      // console.log('WalletService - Token first 20 chars:', token?.substring(0, 20) + '...');
      // console.log('WalletService - User is authenticated:', useUserStore.getState().isAuthenticated);
      
      const response = await apiClient.post('/api/wallet/bet', {
        amount,
        game_id: gameId,
        description,
      });
      
      // console.log('WalletService - Bet response:', response.data);
      const { balance } = response.data;
      useWalletStore.getState().setBalance(balance);
      
      return response.data;
    } catch (error: any) {
      // console.error('Failed to place bet - Full error:', error);
      // console.error('Failed to place bet - Error response data:', error.response?.data);
      // console.error('Failed to place bet - Error response status:', error.response?.status);
      // console.error('Failed to place bet - Error response headers:', error.response?.headers);
      
      if (error.response?.status === 401) {
        // console.error('Authentication error - Token may be invalid or missing');
        const currentToken = useUserStore.getState().token;
        // console.error('Current token at error time:', currentToken ? 'exists' : 'missing');
      }
      
      throw error;
    }
  },

  // Process win
  async processWin(amount: number, gameId: string, description: string) {
    try {
      const response = await apiClient.post('/api/wallet/win', {
        amount,
        game_id: gameId,
        description,
      });
      
      const { balance } = response.data;
      useWalletStore.getState().setBalance(balance);
      
      return response.data;
    } catch (error) {
      // console.error('Failed to process win:', error);
      throw error;
    }
  },

  // Get transaction history
  async getTransactions(limit: number = 50, offset: number = 0) {
    try {
      const response = await apiClient.get('/api/wallet/transactions', {
        params: { limit, offset },
      });
      
      const { transactions } = response.data;
      useWalletStore.getState().setTransactions(transactions);
      
      return response.data;
    } catch (error) {
      // console.error('Failed to get transactions:', error);
      throw error;
    }
  },
};

// Auto-sync wallet on auth changes
useUserStore.subscribe((state) => {
  if (state.isAuthenticated) {
    // Sync wallet when user logs in
    walletService.getBalance().catch((error) => {
      // console.error('Failed to get balance:', error);
    });
    walletService.getTransactions().catch((error) => {
      // console.error('Failed to get transactions:', error);
    });
  } else {
    // Clear wallet when user logs out
    useWalletStore.getState().reset();
  }
});