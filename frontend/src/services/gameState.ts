import api from './api';
import type { GameState, GameMove } from '../types/game';

export interface GameInstance {
  id: string;
  game_type: string;
  status: 'waiting' | 'active' | 'completed';
  current_turn: string | null;
  winner_id: string | null;
  state: any; // Game-specific state data
  created_at: string;
  updated_at: string;
  players: GamePlayer[];
  moves: GameMove[];
  spectators: number;
}

export interface GamePlayer {
  user_id: string;
  username: string;
  position: number;
  joined_at: string;
}

export interface CreateGameData {
  game_type: string;
  wager_amount: number;
  settings?: any;
}

export interface JoinGameData {
  stake_amount: number;
}

export interface MakeMoveData {
  move_data: any;
}

class GameStateService {
  async createGame(data: CreateGameData): Promise<GameInstance> {
    const response = await api.post('/api/game-instances/create', data);
    return response.data;
  }

  async getGame(gameId: string): Promise<GameInstance> {
    const response = await api.get(`/api/game-instances/${gameId}`);
    return response.data;
  }

  async joinGame(gameId: string, data: JoinGameData): Promise<GameInstance> {
    const response = await api.post(`/api/game-instances/${gameId}/join`, data);
    return response.data;
  }

  async makeMove(gameId: string, data: MakeMoveData): Promise<GameInstance> {
    const response = await api.post(`/api/game-instances/${gameId}/move`, data);
    return response.data;
  }

  async getActiveGames(gameType?: string): Promise<GameInstance[]> {
    const params = gameType ? { game_type: gameType } : {};
    const response = await api.get('/api/game-instances/active', { params });
    return response.data;
  }

  // Helper method to get game thumbnail state
  getGameThumbnail(game: GameInstance): string {
    // This will return a representation of the game state for thumbnail display
    switch (game.game_type) {
      case 'chess':
        return this.getChessThumbnail(game.state);
      case 'checkers':
        return this.getCheckersThumbnail(game.state);
      case 'highlight_hero':
        return this.getHighlightHeroThumbnail(game.state);
      case 'rock_paper_scissors':
        return this.getRPSThumbnail(game.state);
      default:
        return '';
    }
  }

  private getChessThumbnail(state: GameState): string {
    // Generate a simple text representation of chess board
    if (!state.board) return '';
    return state.board.slice(0, 2).join('\n'); // Show first 2 rows
  }

  private getCheckersThumbnail(state: GameState): string {
    // Generate a simple text representation of checkers board
    if (!state.board) return '';
    return state.board.slice(0, 2).join('\n'); // Show first 2 rows
  }

  private getHighlightHeroThumbnail(state: GameState): string {
    // Show current round and scores
    return `Round ${state.current_round || 1} - Scores: ${state.scores?.join(' vs ') || '0 vs 0'}`;
  }

  private getRPSThumbnail(state: GameState): string {
    // Show current round and scores
    return `Round ${state.current_round || 1} - ${state.player1_score || 0} vs ${state.player2_score || 0}`;
  }
}

export const gameStateService = new GameStateService();

// Re-export types for easier importing
export type { GameInstance, GamePlayer, CreateGameData, JoinGameData, MakeMoveData };