import useUserStore from '@/stores/userStore';

interface GameSocketOptions {
  gameId: string;
  onMessage: (message: any) => void;
  onError?: (error: any) => void;
  onClose?: () => void;
  onOpen?: () => void;
}

export class GameSocket {
  private socket: WebSocket | null = null;
  private options: GameSocketOptions;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;

  constructor(options: GameSocketOptions) {
    this.options = options;
  }

  connect() {
    const token = useUserStore.getState().token;
    if (!token) {
      // console.error('No authentication token available');
      return;
    }

    const wsUrl = `${import.meta.env.VITE_WS_BASE_URL || 'ws://localhost:8000'}/ws/game/${this.options.gameId}?token=${token}`;
    
    try {
      this.socket = new WebSocket(wsUrl);
      
      this.socket.onopen = () => {
        // console.log('WebSocket connected');
        this.reconnectAttempts = 0;
        this.options.onOpen?.();
      };

      this.socket.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          this.options.onMessage(data);
        } catch (error) {
          // console.error('Error parsing message:', error);
        }
      };

      this.socket.onerror = (error) => {
        // console.error('WebSocket error:', error);
        this.options.onError?.(error);
      };

      this.socket.onclose = () => {
        // console.log('WebSocket closed');
        this.options.onClose?.();
        this.attemptReconnect();
      };
    } catch (error) {
      // console.error('Error creating WebSocket:', error);
      this.options.onError?.(error);
    }
  }

  private attemptReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      // console.error('Max reconnection attempts reached');
      return;
    }

    this.reconnectAttempts++;
    // console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);
    
    setTimeout(() => {
      this.connect();
    }, this.reconnectDelay * this.reconnectAttempts);
  }

  send(message: any) {
    if (this.socket?.readyState === WebSocket.OPEN) {
      this.socket.send(JSON.stringify(message));
    } else {
      // console.error('WebSocket is not connected');
    }
  }

  // Send specific game actions
  makeMove(move: any, newState: any, gameOver?: boolean, winnerId?: string) {
    this.send({
      type: 'make_move',
      move,
      new_state: newState,
      game_over: gameOver,
      winner_id: winnerId
    });
  }

  sendChat(message: string) {
    this.send({
      type: 'chat',
      message
    });
  }

  joinGame() {
    this.send({
      type: 'join_game'
    });
  }

  leaveGame() {
    this.send({
      type: 'leave_game'
    });
  }

  disconnect() {
    if (this.socket) {
      this.socket.close();
      this.socket = null;
    }
  }

  isConnected(): boolean {
    return this.socket?.readyState === WebSocket.OPEN;
  }
}

// Game socket hook
import { useEffect, useRef, useState } from 'react';

export function useGameSocket(gameId: string, onMessage: (message: any) => void) {
  const socketRef = useRef<GameSocket | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<any>(null);

  useEffect(() => {
    if (!gameId) return;

    const socket = new GameSocket({
      gameId,
      onMessage,
      onOpen: () => {
        setIsConnected(true);
        setError(null);
      },
      onClose: () => {
        setIsConnected(false);
      },
      onError: (error) => {
        setError(error);
        setIsConnected(false);
      }
    });

    socket.connect();
    socketRef.current = socket;

    return () => {
      socket.disconnect();
    };
  }, [gameId]);

  return {
    socket: socketRef.current,
    isConnected,
    error,
    sendMove: (move: any, newState: any, gameOver?: boolean, winnerId?: string) => {
      socketRef.current?.makeMove(move, newState, gameOver, winnerId);
    },
    sendChat: (message: string) => {
      socketRef.current?.sendChat(message);
    },
    joinGame: () => {
      socketRef.current?.joinGame();
    },
    leaveGame: () => {
      socketRef.current?.leaveGame();
    }
  };
}