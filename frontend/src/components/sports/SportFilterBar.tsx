import { useState } from 'react';
import { Globe, Radio, Clock, Star, Filter } from 'lucide-react';

const regions = [
  { code: 'all', name: 'All Regions', icon: Globe },
  { code: 'GB', name: 'UK', flagUrl: 'https://flagcdn.com/16x12/gb.png' },
  { code: 'ES', name: 'Spain', flagUrl: 'https://flagcdn.com/16x12/es.png' },
  { code: 'DE', name: 'Germany', flagUrl: 'https://flagcdn.com/16x12/de.png' },
  { code: 'US', name: 'USA', flagUrl: 'https://flagcdn.com/16x12/us.png' },
];

const sports = [
  { id: 'all', name: 'All Sports', icon: '🏆' },
  { id: 'soccer', name: 'Soccer', icon: '⚽' },
  { id: 'basketball', name: 'Basketball', icon: '🏀' },
  { id: 'tennis', name: 'Tennis', icon: '🎾' },
  { id: 'football', name: 'NFL', icon: '🏈' },
  { id: 'cricket', name: 'Cricket', icon: '🏏' },
  { id: 'esports', name: 'Esports', icon: '🎮' },
];

interface SportFilterBarProps {
  onFilterChange: (filters: any) => void;
}

const SportFilterBar = ({ onFilterChange }: SportFilterBarProps) => {
  const [filters, setFilters] = useState({
    region: 'all',
    sport: 'all',
    status: 'all',
    favorite: false
  });

  const updateFilter = (key: string, value: any) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    onFilterChange(newFilters);
  };

  return (
    <div className="flex flex-col space-y-1 p-1 bg-slate-900 border-b border-slate-800">
      {/* Region filter with flags */}
      <div className="flex items-center space-x-1 overflow-x-auto pb-1 no-scrollbar">
        {regions.map(region => (
          <button 
            key={region.code}
            className={`px-1.5 py-0.5 text-xs rounded-sm whitespace-nowrap flex items-center transition-colors ${
              filters.region === region.code 
                ? 'bg-purple-600 text-white' 
                : 'bg-slate-800 text-slate-300 hover:text-white'
            }`}
            onClick={() => updateFilter('region', region.code)}
          >
            {region.icon ? (
              <region.icon className="h-3 w-3 mr-1" />
            ) : (
              <img src={region.flagUrl} alt={region.name} className="h-2.5 w-3 mr-1" />
            )}
            {region.name}
          </button>
        ))}
      </div>
      
      {/* Sport filter with icons */}
      <div className="flex items-center space-x-1 overflow-x-auto pb-1 no-scrollbar">
        {sports.map(sport => (
          <button 
            key={sport.id}
            className={`px-1.5 py-0.5 text-xs rounded-sm flex items-center transition-colors ${
              filters.sport === sport.id 
                ? 'bg-purple-600 text-white' 
                : 'bg-slate-800 text-slate-300 hover:text-white'
            }`}
            onClick={() => updateFilter('sport', sport.id)}
          >
            <span className="mr-1">{sport.icon}</span>
            {sport.name}
          </button>
        ))}
      </div>
      
      {/* Status filters */}
      <div className="flex items-center space-x-1">
        <button 
          className={`px-1.5 py-0.5 text-xs rounded-sm flex items-center transition-colors ${
            filters.status === 'live' 
              ? 'bg-red-600 text-white' 
              : 'bg-slate-800 text-slate-300 hover:text-white'
          }`}
          onClick={() => updateFilter('status', 'live')}
        >
          <Radio className="h-2.5 w-2.5 mr-1" />
          Live
        </button>
        <button 
          className={`px-1.5 py-0.5 text-xs rounded-sm flex items-center transition-colors ${
            filters.status === 'upcoming' 
              ? 'bg-blue-600 text-white' 
              : 'bg-slate-800 text-slate-300 hover:text-white'
          }`}
          onClick={() => updateFilter('status', 'upcoming')}
        >
          <Clock className="h-2.5 w-2.5 mr-1" />
          Upcoming
        </button>
        <button 
          className={`px-1.5 py-0.5 text-xs rounded-sm flex items-center transition-colors ${
            filters.status === 'today' 
              ? 'bg-green-600 text-white' 
              : 'bg-slate-800 text-slate-300 hover:text-white'
          }`}
          onClick={() => updateFilter('status', 'today')}
        >
          Today
        </button>
        <button 
          className={`px-1.5 py-0.5 text-xs rounded-sm flex items-center transition-colors ${
            filters.favorite 
              ? 'bg-yellow-600 text-white' 
              : 'bg-slate-800 text-slate-300 hover:text-white'
          }`}
          onClick={() => updateFilter('favorite', !filters.favorite)}
        >
          <Star className={`h-2.5 w-2.5 mr-1 ${filters.favorite ? 'fill-yellow-500' : ''}`} />
          Favorites
        </button>
        <button className="px-1.5 py-0.5 text-xs rounded-sm bg-slate-800 text-slate-300 hover:text-white ml-auto">
          <Filter className="h-2.5 w-2.5" />
        </button>
      </div>
    </div>
  );
};

export default SportFilterBar;