import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Search,
  Filter,
  Star,
  Clock,
  TrendingUp,
  Calendar,
  Flame,
  Target,
  Activity,
  X,
  DollarSign,
  MoreHorizontal,
  ChevronRight
} from 'lucide-react';

// Mock data for sports
const SPORTS = [
  { id: 'soccer', name: 'Soccer', icon: '⚽', active: true },
  { id: 'basketball', name: 'Basketball', icon: '🏀', active: true },
  { id: 'tennis', name: 'Tennis', icon: '🎾', active: true },
  { id: 'hockey', name: 'Hockey', icon: '🏒', active: false },
  { id: 'esports', name: 'Esports', icon: '🎮', active: true }
];

const MOCK_MATCHES = [
  {
    id: 1,
    sport: 'soccer',
    league: 'Champions League',
    home: 'Real Madrid',
    away: 'Barcelona',
    time: '20:00',
    date: 'Today',
    odds: { home: 2.10, draw: 3.40, away: 3.50 },
    live: true,
    score: { home: 2, away: 1 },
    minute: 67
  },
  {
    id: 2,
    sport: 'basketball',
    league: 'NBA',
    home: 'Lakers',
    away: 'Warriors',
    time: '22:30',
    date: 'Today',
    odds: { home: 1.85, away: 1.95 },
    live: false
  },
  {
    id: 3,
    sport: 'tennis',
    league: 'Wimbledon',
    home: 'Djokovic',
    away: 'Federer',
    time: '14:00',
    date: 'Tomorrow',
    odds: { home: 1.65, away: 2.20 },
    live: false
  }
];

const MobileSportsPage = () => {
  const [selectedSport, setSelectedSport] = useState('all');
  const [selectedTab, setSelectedTab] = useState('live');
  const [showFilters, setShowFilters] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [favorites, setFavorites] = useState<number[]>([]);

  const toggleFavorite = (matchId: number) => {
    setFavorites(prev => 
      prev.includes(matchId) 
        ? prev.filter(id => id !== matchId)
        : [...prev, matchId]
    );
  };

  const filteredMatches = MOCK_MATCHES.filter(match => {
    if (selectedSport !== 'all' && match.sport !== selectedSport) return false;
    if (selectedTab === 'live' && !match.live) return false;
    if (selectedTab === 'upcoming' && match.live) return false;
    if (searchQuery && !match.home.toLowerCase().includes(searchQuery.toLowerCase()) && 
        !match.away.toLowerCase().includes(searchQuery.toLowerCase())) return false;
    return true;
  });

  return (
    <div className="min-h-screen bg-slate-950 text-white pt-16">
      {/* Header */}
      <div className="sticky top-16 z-20 bg-slate-900 border-b border-slate-800">
        <div className="p-3">
          <div className="flex items-center justify-between mb-3">
            <h1 className="text-lg font-semibold">Sports Betting</h1>
            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowFilters(true)}
                className="h-8 w-8 p-0"
              >
                <Filter className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Search Bar */}
          <div className="relative mb-3">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-slate-400" />
            <Input
              placeholder="Search teams or leagues..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-9 h-9 bg-slate-800 border-slate-700 text-sm"
            />
          </div>

          {/* Sport Pills */}
          <div className="flex space-x-2 overflow-x-auto pb-3 scrollbar-hide">
            <Button
              variant={selectedSport === 'all' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setSelectedSport('all')}
              className={`h-8 px-3 text-xs whitespace-nowrap ${
                selectedSport === 'all' ? 'bg-purple-600' : ''
              }`}
            >
              All Sports
            </Button>
            {SPORTS.filter(s => s.active).map(sport => (
              <Button
                key={sport.id}
                variant={selectedSport === sport.id ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setSelectedSport(sport.id)}
                className={`h-8 px-3 text-xs whitespace-nowrap ${
                  selectedSport === sport.id ? 'bg-purple-600' : ''
                }`}
              >
                <span className="mr-1">{sport.icon}</span>
                {sport.name}
              </Button>
            ))}
          </div>
        </div>

        {/* Tabs */}
        <Tabs value={selectedTab} onValueChange={setSelectedTab}>
          <TabsList className="w-full grid grid-cols-3 bg-transparent border-t border-slate-800 rounded-none h-10">
            <TabsTrigger value="live" className="text-xs">
              <Activity className="h-3 w-3 mr-1" />
              Live
            </TabsTrigger>
            <TabsTrigger value="upcoming" className="text-xs">
              <Clock className="h-3 w-3 mr-1" />
              Upcoming
            </TabsTrigger>
            <TabsTrigger value="popular" className="text-xs">
              <Flame className="h-3 w-3 mr-1" />
              Popular
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {/* Main Content */}
      <div className="p-3 pb-20 mt-2">
        {/* Featured Match */}
        {selectedTab === 'live' && filteredMatches.some(m => m.live) && (
          <Card className="bg-gradient-to-r from-purple-900/30 to-pink-900/30 border-purple-700 mb-4 p-4">
            <div className="flex items-center justify-between mb-2">
              <Badge className="bg-red-500 text-white animate-pulse text-xs">
                <Activity className="h-3 w-3 mr-1" />
                LIVE
              </Badge>
              <Badge className="bg-slate-800 text-xs">
                Champions League
              </Badge>
            </div>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <p className="font-medium">Real Madrid</p>
                  <p className="text-2xl font-bold">2</p>
                </div>
                <div className="px-4">
                  <p className="text-sm text-slate-400">67'</p>
                </div>
                <div className="flex-1 text-right">
                  <p className="font-medium">Barcelona</p>
                  <p className="text-2xl font-bold">1</p>
                </div>
              </div>
              <div className="grid grid-cols-3 gap-2">
                <Button variant="outline" className="h-9 text-xs border-slate-700">
                  <span className="text-slate-400 mr-1">1</span>
                  <span className="font-bold">2.10</span>
                </Button>
                <Button variant="outline" className="h-9 text-xs border-slate-700">
                  <span className="text-slate-400 mr-1">X</span>
                  <span className="font-bold">3.40</span>
                </Button>
                <Button variant="outline" className="h-9 text-xs border-slate-700">
                  <span className="text-slate-400 mr-1">2</span>
                  <span className="font-bold">3.50</span>
                </Button>
              </div>
            </div>
          </Card>
        )}

        {/* Match List */}
        <div className="space-y-3">
          {filteredMatches.map(match => (
            <Card key={match.id} className="bg-slate-900 border-slate-800 p-3">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-2">
                  {match.live ? (
                    <Badge className="bg-red-500 text-white animate-pulse text-[10px] h-5">
                      LIVE {match.minute}'
                    </Badge>
                  ) : (
                    <Badge className="bg-slate-700 text-[10px] h-5">
                      {match.date} {match.time}
                    </Badge>
                  )}
                  <span className="text-xs text-slate-400">{match.league}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => toggleFavorite(match.id)}
                    className="h-7 w-7 p-0"
                  >
                    <Star 
                      className={`h-4 w-4 ${
                        favorites.includes(match.id) 
                          ? 'fill-yellow-500 text-yellow-500' 
                          : 'text-slate-400'
                      }`} 
                    />
                  </Button>
                  <Button variant="ghost" size="sm" className="h-7 w-7 p-0">
                    <MoreHorizontal className="h-4 w-4 text-slate-400" />
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">{match.home}</span>
                  {match.live && (
                    <span className="text-lg font-bold">{match.score?.home}</span>
                  )}
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">{match.away}</span>
                  {match.live && (
                    <span className="text-lg font-bold">{match.score?.away}</span>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-3 gap-2 mt-3">
                {match.odds.draw ? (
                  <>
                    <Button variant="outline" className="h-8 text-xs border-slate-700">
                      <span className="text-slate-400 mr-1">1</span>
                      <span className="font-bold">{match.odds.home}</span>
                    </Button>
                    <Button variant="outline" className="h-8 text-xs border-slate-700">
                      <span className="text-slate-400 mr-1">X</span>
                      <span className="font-bold">{match.odds.draw}</span>
                    </Button>
                    <Button variant="outline" className="h-8 text-xs border-slate-700">
                      <span className="text-slate-400 mr-1">2</span>
                      <span className="font-bold">{match.odds.away}</span>
                    </Button>
                  </>
                ) : (
                  <>
                    <Button variant="outline" className="h-8 text-xs border-slate-700 col-span-1">
                      <span className="text-slate-400 mr-1">1</span>
                      <span className="font-bold">{match.odds.home}</span>
                    </Button>
                    <div className="col-span-1 flex items-center justify-center">
                      <span className="text-xs text-slate-500">vs</span>
                    </div>
                    <Button variant="outline" className="h-8 text-xs border-slate-700 col-span-1">
                      <span className="text-slate-400 mr-1">2</span>
                      <span className="font-bold">{match.odds.away}</span>
                    </Button>
                  </>
                )}
              </div>

              <Button 
                variant="ghost" 
                className="w-full h-7 text-xs text-slate-400 mt-2"
              >
                View all markets
                <ChevronRight className="h-3 w-3 ml-1" />
              </Button>
            </Card>
          ))}
        </div>

        {filteredMatches.length === 0 && (
          <div className="text-center py-12">
            <Activity className="h-12 w-12 text-slate-600 mx-auto mb-3" />
            <p className="text-slate-400">No matches found</p>
            <p className="text-sm text-slate-500 mt-1">Try adjusting your filters</p>
          </div>
        )}
      </div>

      {/* Filters Overlay */}
      {showFilters && (
        <div className="fixed inset-0 z-50 bg-slate-900 flex flex-col">
          <div className="flex items-center justify-between p-4 border-b border-slate-800">
            <h2 className="text-lg font-semibold">Filters</h2>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowFilters(false)}
              className="h-8 w-8 p-0"
            >
              <X className="h-5 w-5" />
            </Button>
          </div>

          <div className="flex-1 p-4 space-y-6">
            <div>
              <h3 className="text-sm font-medium mb-3">Sports</h3>
              <div className="space-y-2">
                {SPORTS.map(sport => (
                  <Button
                    key={sport.id}
                    variant={selectedSport === sport.id ? 'default' : 'outline'}
                    className="w-full justify-start h-10 text-sm"
                    onClick={() => setSelectedSport(sport.id)}
                  >
                    <span className="mr-2">{sport.icon}</span>
                    {sport.name}
                  </Button>
                ))}
              </div>
            </div>

            <div>
              <h3 className="text-sm font-medium mb-3">Time Range</h3>
              <div className="grid grid-cols-2 gap-2">
                <Button variant="outline" className="h-10 text-sm">Today</Button>
                <Button variant="outline" className="h-10 text-sm">Tomorrow</Button>
                <Button variant="outline" className="h-10 text-sm">This Week</Button>
                <Button variant="outline" className="h-10 text-sm">All</Button>
              </div>
            </div>
          </div>

          <div className="p-4 border-t border-slate-800">
            <Button 
              className="w-full bg-gradient-to-r from-purple-500 to-pink-500"
              onClick={() => setShowFilters(false)}
            >
              Apply Filters
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default MobileSportsPage;