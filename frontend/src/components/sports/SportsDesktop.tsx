import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  Trophy,
  Users,
  DollarSign,
  Clock,
  Search,
  Radio,
  Star,
  Filter,
  ChevronDown,
  ChevronRight,
  Globe,
  TrendingUp,
  Activity,
  Calendar,
  Plus,
  Heart,
  MessageSquare,
  BarChart3,
  Play,
  Timer,
  Eye,
  Target,
  Zap,
  Shield,
  Flame,
  X
} from 'lucide-react';

interface SportsDesktopProps {
  onEventSelect?: (event: any) => void;
}

// Mock data for sports
const sportsData = [
  { id: 'soccer', name: 'Soccer', icon: '⚽', activeEvents: 142, color: 'green' },
  { id: 'basketball', name: 'Basketball', icon: '🏀', activeEvents: 87, color: 'orange' },
  { id: 'tennis', name: 'Tennis', icon: '🎾', activeEvents: 54, color: 'yellow' },
  { id: 'football', name: 'Football', icon: '🏈', activeEvents: 23, color: 'blue' },
  { id: 'baseball', name: 'Baseball', icon: '⚾', activeEvents: 15, color: 'red' },
  { id: 'cricket', name: 'Cricket', icon: '🏏', activeEvents: 18, color: 'purple' },
];

const topEvents = [
  {
    id: '1',
    sport: 'soccer',
    league: 'Champions League',
    homeTeam: 'Real Madrid',
    awayTeam: 'Barcelona',
    startTime: '20:00',
    odds: { home: 2.10, draw: 3.40, away: 3.50 },
    markets: 145,
    isLive: true,
    score: { home: 2, away: 1 },
    minute: "78'"
  },
  {
    id: '2',
    sport: 'basketball',
    league: 'NBA',
    homeTeam: 'Lakers',
    awayTeam: 'Warriors',
    startTime: '21:30',
    odds: { home: 1.75, away: 2.05 },
    markets: 89,
    isLive: false,
    score: null,
    minute: null
  },
  {
    id: '3',
    sport: 'tennis',
    league: 'Wimbledon',
    player1: 'R. Federer',
    player2: 'N. Djokovic',
    startTime: '14:00',
    odds: { player1: 1.95, player2: 1.85 },
    markets: 45,
    isLive: true,
    score: { set1: '6-4', set2: '5-3', currentGame: '40-30' },
    sets: 2
  }
];

const SportsDesktop: React.FC<SportsDesktopProps> = ({ onEventSelect }) => {
  const [selectedSport, setSelectedSport] = useState('all');
  const [selectedLeague, setSelectedLeague] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [favoriteEvents, setFavoriteEvents] = useState<string[]>([]);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  const toggleFavorite = (eventId: string) => {
    setFavoriteEvents(prev => 
      prev.includes(eventId) 
        ? prev.filter(id => id !== eventId)
        : [...prev, eventId]
    );
  };

  return (
    <div className="h-full bg-slate-950 text-white overflow-hidden">
      {/* Main Grid Layout */}
      <div className="grid grid-cols-12 gap-1 p-1 h-full overflow-hidden">
        
        {/* Left Sidebar - Sports & Leagues */}
        <div className="col-span-2 flex flex-col gap-1 overflow-hidden">
          
          {/* Sports List */}
          <div className="bg-slate-900 border border-slate-800 rounded-sm p-1">
            <h3 className="text-xs font-medium text-white mb-1.5">Sports</h3>
            <div className="space-y-1">
              <SportButton
                id="all"
                label="All Sports"
                icon={<Globe className="h-3 w-3" />}
                count={416}
                active={selectedSport === 'all'}
                onClick={() => setSelectedSport('all')}
              />
              {sportsData.map(sport => (
                <SportButton
                  key={sport.id}
                  id={sport.id}
                  label={sport.name}
                  icon={<span className="text-sm">{sport.icon}</span>}
                  count={sport.activeEvents}
                  active={selectedSport === sport.id}
                  onClick={() => setSelectedSport(sport.id)}
                />
              ))}
            </div>
          </div>

          {/* Popular Leagues */}
          <div className="bg-slate-900 border border-slate-800 rounded-sm p-1 flex-1 flex flex-col overflow-hidden">
            <h3 className="text-xs font-medium text-white mb-1.5">Popular Leagues</h3>
            <div className="space-y-1 flex-1 overflow-auto">
              <LeagueItem
                logo="🏆"
                name="Champions League"
                sport="Soccer"
                events={34}
                active={selectedLeague === 'champions-league'}
                onClick={() => setSelectedLeague('champions-league')}
              />
              <LeagueItem
                logo="🏆"
                name="Premier League"
                sport="Soccer"
                events={28}
                active={selectedLeague === 'premier-league'}
                onClick={() => setSelectedLeague('premier-league')}
              />
              <LeagueItem
                logo="🏀"
                name="NBA"
                sport="Basketball"
                events={15}
                active={selectedLeague === 'nba'}
                onClick={() => setSelectedLeague('nba')}
              />
              <LeagueItem
                logo="🎾"
                name="Wimbledon"
                sport="Tennis"
                events={45}
                active={selectedLeague === 'wimbledon'}
                onClick={() => setSelectedLeague('wimbledon')}
              />
            </div>
          </div>

          {/* Quick Stats */}
          <div className="bg-slate-900 border border-slate-800 rounded-sm p-2">
            <div className="grid grid-cols-2 gap-2">
              <div className="bg-slate-800 p-1.5 rounded-sm text-center">
                <p className="text-[10px] text-slate-400">Live Now</p>
                <p className="text-white font-medium text-sm">86</p>
              </div>
              <div className="bg-slate-800 p-1.5 rounded-sm text-center">
                <p className="text-[10px] text-slate-400">Today</p>
                <p className="text-white font-medium text-sm">234</p>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content - Events Grid */}
        <div className="col-span-7 flex flex-col overflow-hidden">
          
          {/* Header Bar */}
          <div className="h-7 bg-slate-900 border border-slate-800 rounded-sm px-2 mb-1 flex items-center">
            <div className="flex items-center space-x-3">
              <Badge className="bg-red-500 text-white text-[10px] h-4 px-1.5 animate-pulse">
                <Radio className="h-2 w-2 mr-0.5" />
                86 LIVE
              </Badge>
              <div className="flex items-center">
                <Clock className="h-3 w-3 text-slate-400 mr-1" />
                <span className="text-xs text-white">Starting Soon: 15</span>
              </div>
            </div>

            <div className="ml-auto flex items-center space-x-2">
              <div className="relative">
                <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-3 w-3 text-slate-400" />
                <Input
                  placeholder="Find events..."
                  className="h-5 pl-7 pr-2 text-[10px] bg-slate-800 border-slate-700 rounded-sm w-32"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              <Button
                variant="ghost"
                size="sm"
                className="h-5 px-2 rounded-sm text-[10px] hover:bg-slate-800"
                onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
              >
                <Filter className="h-3 w-3 mr-1 text-slate-400" />
                {viewMode === 'grid' ? 'Grid View' : 'List View'}
              </Button>
            </div>
          </div>

          {/* Featured Event Banner */}
          <div className="h-32 bg-slate-900 border border-slate-800 rounded-sm mb-1 relative overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-r from-slate-900/90 via-slate-900/50 to-slate-900/90 z-10"></div>
            <div className="relative z-20 h-full flex flex-col justify-center px-4">
              <Badge className="mb-2 w-fit bg-red-500 text-white text-[10px] h-4 px-1.5">
                LIVE NOW
              </Badge>
              <h2 className="text-white font-bold text-lg mb-1">Real Madrid vs Barcelona</h2>
              <p className="text-slate-300 text-xs mb-2">Champions League Semi-Final • Camp Nou</p>
              <div className="flex items-center space-x-3">
                <div className="flex items-center space-x-2">
                  <Badge className="bg-slate-800 text-white text-[10px] h-4 px-1.5">
                    RM 2.10
                  </Badge>
                  <Badge className="bg-slate-800 text-white text-[10px] h-4 px-1.5">
                    Draw 3.40
                  </Badge>
                  <Badge className="bg-slate-800 text-white text-[10px] h-4 px-1.5">
                    FCB 3.50
                  </Badge>
                </div>
                <Button
                  size="sm"
                  className="h-6 px-3 text-xs bg-gradient-to-r from-green-500 to-cyan-500 hover:opacity-90"
                >
                  <Play className="h-3 w-3 mr-1" />
                  Bet Now
                </Button>
              </div>
            </div>
          </div>

          {/* Events Grid */}
          <div className="bg-slate-900 border border-slate-800 rounded-sm flex-1 overflow-hidden">
            <div className="grid grid-cols-3 gap-px bg-slate-800 p-px overflow-auto h-full">
              {topEvents.map((event) => (
                <EventCard
                  key={event.id}
                  event={event}
                  isFavorite={favoriteEvents.includes(event.id)}
                  onToggleFavorite={() => toggleFavorite(event.id)}
                  onSelect={() => onEventSelect?.(event)}
                />
              ))}
            </div>
          </div>
        </div>

        {/* Right Sidebar - Betslip & Top Bets */}
        <div className="col-span-3 flex flex-col gap-1 overflow-hidden">
          
          {/* Betslip */}
          <div className="bg-slate-900 border border-slate-800 rounded-sm overflow-hidden flex-1">
            <div className="h-6 px-2 flex items-center justify-between border-b border-slate-800">
              <h3 className="text-xs font-medium text-white">Betslip</h3>
              <Badge className="bg-purple-500 text-white text-[10px] h-4 px-1.5">
                3
              </Badge>
            </div>
            
            <div className="p-2 space-y-2">
              <BetslipItem
                team="Real Madrid"
                odds={2.10}
                stake={50}
                event="vs Barcelona"
              />
              <BetslipItem
                team="Lakers"
                odds={1.75}
                stake={100}
                event="vs Warriors"
              />
              <BetslipItem
                team="Federer"
                odds={1.95}
                stake={25}
                event="vs Djokovic"
              />
            </div>
            
            <div className="p-2 border-t border-slate-800">
              <div className="flex justify-between items-center mb-2">
                <span className="text-xs text-slate-400">Total Stake:</span>
                <span className="text-xs font-medium text-white">$175</span>
              </div>
              <div className="flex justify-between items-center mb-2">
                <span className="text-xs text-slate-400">Potential Win:</span>
                <span className="text-xs font-medium text-green-500">$358.75</span>
              </div>
              <Button className="w-full h-6 text-xs bg-gradient-to-r from-green-500 to-cyan-500 hover:opacity-90">
                Place Bet
              </Button>
            </div>
          </div>

          {/* Popular Bets */}
          <div className="bg-slate-900 border border-slate-800 rounded-sm overflow-hidden">
            <div className="h-6 px-2 flex items-center justify-between border-b border-slate-800">
              <h3 className="text-xs font-medium text-white">Popular Bets</h3>
              <Flame className="h-3 w-3 text-orange-500" />
            </div>
            
            <div className="p-2 space-y-2">
              <PopularBetItem
                event="Real Madrid to Win"
                amount="$15.2K"
                percentage={78}
                trend="up"
              />
              <PopularBetItem
                event="Over 2.5 Goals"
                amount="$8.7K"
                percentage={65}
                trend="up"
              />
              <PopularBetItem
                event="Lakers -5.5"
                amount="$12.1K"
                percentage={52}
                trend="down"
              />
            </div>
          </div>

          {/* Quick Stats */}
          <div className="bg-slate-900 border border-slate-800 rounded-sm p-2">
            <h3 className="text-xs font-medium text-white mb-2">Your Stats Today</h3>
            <div className="grid grid-cols-2 gap-2">
              <div className="bg-slate-800 p-1.5 rounded-sm text-center">
                <p className="text-[10px] text-slate-400">Bets</p>
                <p className="text-white font-medium text-sm">12</p>
              </div>
              <div className="bg-slate-800 p-1.5 rounded-sm text-center">
                <p className="text-[10px] text-slate-400">Win Rate</p>
                <p className="text-green-500 font-medium text-sm">67%</p>
              </div>
              <div className="bg-slate-800 p-1.5 rounded-sm text-center">
                <p className="text-[10px] text-slate-400">Profit</p>
                <p className="text-green-500 font-medium text-sm">+$420</p>
              </div>
              <div className="bg-slate-800 p-1.5 rounded-sm text-center">
                <p className="text-[10px] text-slate-400">Best Odds</p>
                <p className="text-white font-medium text-sm">3.45</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Component definitions
interface SportButtonProps {
  id: string;
  label: string;
  icon: React.ReactNode;
  count: number;
  active: boolean;
  onClick: () => void;
}

const SportButton: React.FC<SportButtonProps> = ({ label, icon, count, active, onClick }) => (
  <Button
    variant="ghost"
    className={`w-full justify-between h-6 px-2 text-xs rounded-sm ${
      active
        ? 'bg-slate-800 text-white'
        : 'text-slate-400 hover:text-white hover:bg-slate-800/50'
    }`}
    onClick={onClick}
  >
    <div className="flex items-center">
      {icon}
      <span className="ml-1.5">{label}</span>
    </div>
    <Badge className="h-4 px-1 text-[10px] bg-slate-800 text-slate-400">
      {count}
    </Badge>
  </Button>
);

interface LeagueItemProps {
  logo: string;
  name: string;
  sport: string;
  events: number;
  active: boolean;
  onClick: () => void;
}

const LeagueItem: React.FC<LeagueItemProps> = ({ logo, name, sport, events, active, onClick }) => (
  <div
    className={`flex items-center justify-between p-1.5 rounded-sm cursor-pointer ${
      active ? 'bg-slate-800' : 'hover:bg-slate-800/50'
    }`}
    onClick={onClick}
  >
    <div className="flex items-center">
      <span className="text-sm mr-1.5">{logo}</span>
      <div>
        <p className="text-xs text-white">{name}</p>
        <p className="text-[10px] text-slate-400">{sport}</p>
      </div>
    </div>
    <span className="text-[10px] text-slate-400">{events}</span>
  </div>
);

interface EventCardProps {
  event: any;
  isFavorite: boolean;
  onToggleFavorite: () => void;
  onSelect: () => void;
}

const EventCard: React.FC<EventCardProps> = ({ event, isFavorite, onToggleFavorite, onSelect }) => {
  const isTeamSport = 'homeTeam' in event;
  
  return (
    <div className="bg-slate-900 p-2 hover:bg-slate-800/70 cursor-pointer" onClick={onSelect}>
      {/* Header */}
      <div className="flex items-start justify-between mb-1.5">
        <div className="flex items-center space-x-1.5">
          <span className="text-sm">{event.sport === 'soccer' ? '⚽' : event.sport === 'basketball' ? '🏀' : '🎾'}</span>
          <div>
            <p className="text-[10px] text-slate-400">{event.league}</p>
            {event.isLive ? (
              <Badge className="bg-red-500 text-white text-[10px] h-3 px-1 animate-pulse">
                <Radio className="h-2 w-2 mr-0.5" />
                LIVE {event.minute}
              </Badge>
            ) : (
              <p className="text-[10px] text-white">{event.startTime}</p>
            )}
          </div>
        </div>
        <Button
          variant="ghost"
          size="sm"
          className="h-5 w-5 p-0"
          onClick={(e) => {
            e.stopPropagation();
            onToggleFavorite();
          }}
        >
          <Heart className={`h-3 w-3 ${isFavorite ? 'text-red-500 fill-red-500' : 'text-slate-400'}`} />
        </Button>
      </div>

      {/* Match/Event Display */}
      {isTeamSport ? (
        <div className="mb-2">
          <div className="flex justify-between items-center mb-1">
            <span className="text-xs text-white">{event.homeTeam}</span>
            <span className="text-xs font-bold text-white">{event.score ? `${event.score.home}` : '-'}</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-xs text-white">{event.awayTeam}</span>
            <span className="text-xs font-bold text-white">{event.score ? `${event.score.away}` : '-'}</span>
          </div>
        </div>
      ) : (
        <div className="mb-2">
          <div className="flex justify-between items-center mb-1">
            <span className="text-xs text-white">{event.player1}</span>
            <span className="text-[10px] text-slate-400">{event.score?.set1}</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-xs text-white">{event.player2}</span>
            <span className="text-[10px] text-slate-400">{event.score?.set2}</span>
          </div>
        </div>
      )}

      {/* Odds */}
      <div className="grid grid-cols-3 gap-1 mb-2">
        {isTeamSport && event.odds.draw && (
          <>
            <Button
              variant="outline"
              className="h-5 px-1 text-[10px] border-slate-700 hover:bg-slate-700"
            >
              {event.homeTeam?.split(' ')[0]}<br />{event.odds.home}
            </Button>
            <Button
              variant="outline"
              className="h-5 px-1 text-[10px] border-slate-700 hover:bg-slate-700"
            >
              Draw<br />{event.odds.draw}
            </Button>
            <Button
              variant="outline"
              className="h-5 px-1 text-[10px] border-slate-700 hover:bg-slate-700"
            >
              {event.awayTeam?.split(' ')[0]}<br />{event.odds.away}
            </Button>
          </>
        )}
        {!isTeamSport && (
          <>
            <Button
              variant="outline"
              className="h-5 px-1 text-[10px] border-slate-700 hover:bg-slate-700 col-span-1"
            >
              {event.player1?.split(' ')[1]}<br />{event.odds.player1}
            </Button>
            <div className="col-span-1"></div>
            <Button
              variant="outline"
              className="h-5 px-1 text-[10px] border-slate-700 hover:bg-slate-700 col-span-1"
            >
              {event.player2?.split(' ')[1]}<br />{event.odds.player2}
            </Button>
          </>
        )}
      </div>

      {/* Footer */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <div className="flex items-center text-[10px] text-slate-400">
            <BarChart3 className="h-2.5 w-2.5 mr-0.5" />
            {event.markets}
          </div>
          {event.isLive && (
            <div className="flex items-center text-[10px] text-slate-400">
              <Eye className="h-2.5 w-2.5 mr-0.5" />
              {Math.floor(Math.random() * 1000)}
            </div>
          )}
        </div>
        <ChevronRight className="h-3 w-3 text-slate-400" />
      </div>
    </div>
  );
};

interface BetslipItemProps {
  team: string;
  odds: number;
  stake: number;
  event: string;
}

const BetslipItem: React.FC<BetslipItemProps> = ({ team, odds, stake, event }) => (
  <div className="bg-slate-800 p-1.5 rounded-sm">
    <div className="flex justify-between items-start mb-1">
      <div>
        <p className="text-xs font-medium text-white">{team}</p>
        <p className="text-[10px] text-slate-400">{event}</p>
      </div>
      <Button
        variant="ghost"
        size="sm"
        className="h-4 w-4 p-0"
      >
        <X className="h-3 w-3 text-slate-400" />
      </Button>
    </div>
    <div className="flex justify-between items-center">
      <span className="text-[10px] text-slate-400">@ {odds}</span>
      <div className="flex items-center">
        <span className="text-[10px] text-white mr-1">$</span>
        <Input
          type="number"
          value={stake}
          className="h-4 w-12 text-[10px] bg-slate-700 border-slate-600 rounded-sm px-1"
        />
      </div>
    </div>
  </div>
);

interface PopularBetItemProps {
  event: string;
  amount: string;
  percentage: number;
  trend: 'up' | 'down';
}

const PopularBetItem: React.FC<PopularBetItemProps> = ({ event, amount, percentage, trend }) => (
  <div className="bg-slate-800 p-1.5 rounded-sm">
    <div className="flex justify-between items-center mb-1">
      <p className="text-xs text-white">{event}</p>
      <span className="text-[10px] text-green-500">{amount}</span>
    </div>
    <div className="flex items-center space-x-2">
      <Progress value={percentage} className="h-1 flex-1" />
      <div className="flex items-center text-[10px]">
        {trend === 'up' ? (
          <TrendingUp className="h-2.5 w-2.5 text-green-500 mr-0.5" />
        ) : (
          <ChevronDown className="h-2.5 w-2.5 text-red-500 mr-0.5" />
        )}
        <span className={trend === 'up' ? 'text-green-500' : 'text-red-500'}>
          {percentage}%
        </span>
      </div>
    </div>
  </div>
);

export default SportsDesktop;