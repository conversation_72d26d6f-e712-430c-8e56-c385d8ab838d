import { useState } from 'react';
import { <PERSON>, Bell, CircleDot } from 'lucide-react';

interface Player {
  id: string;
  number: number;
  team: 'home' | 'away';
  position: { x: number; y: number };
}

interface MatchEvent {
  id: string;
  minute: string;
  type: 'goal' | 'yellowCard' | 'redCard' | 'substitution';
  player: string;
  description: string;
  team: 'home' | 'away';
}

interface MatchStat {
  name: string;
  home: number;
  away: number;
}

interface MatchData {
  id: string;
  league: string;
  homeTeam: string;
  awayTeam: string;
  homeTeamLogo?: string;
  awayTeamLogo?: string;
  time: string;
  score: { home: number; away: number };
  ballPosition: { x: number; y: number };
  players: Player[];
  events: MatchEvent[];
  stats: MatchStat[];
  recentEvent?: { type: string; player: string };
}

interface MatchVisualizationProps {
  match: MatchData;
}

const MatchVisualization = ({ match }: MatchVisualizationProps) => {
  const [viewMode, setViewMode] = useState<'pitch' | 'stats' | 'timeline'>('pitch');
  
  return (
    <div className="w-full h-full bg-slate-900 border border-slate-800 rounded-sm overflow-hidden">
      {/* Match header */}
      <div className="flex items-center justify-between p-2 border-b border-slate-800">
        <div className="flex items-center">
          <CircleDot className="h-3 w-3 text-red-500 animate-pulse mr-2" />
          <span className="text-xs text-slate-400">{match.league}</span>
          <span className="mx-2 text-slate-700">|</span>
          <span className="text-xs text-white">{match.time}</span>
        </div>
        <div className="flex space-x-1">
          <button 
            className={`px-2 py-0.5 text-xs rounded-sm transition-colors ${
              viewMode === 'pitch' 
                ? 'bg-slate-700 text-white' 
                : 'text-slate-400 hover:text-white'
            }`}
            onClick={() => setViewMode('pitch')}
          >
            Pitch
          </button>
          <button 
            className={`px-2 py-0.5 text-xs rounded-sm transition-colors ${
              viewMode === 'stats' 
                ? 'bg-slate-700 text-white' 
                : 'text-slate-400 hover:text-white'
            }`}
            onClick={() => setViewMode('stats')}
          >
            Stats
          </button>
          <button 
            className={`px-2 py-0.5 text-xs rounded-sm transition-colors ${
              viewMode === 'timeline' 
                ? 'bg-slate-700 text-white' 
                : 'text-slate-400 hover:text-white'
            }`}
            onClick={() => setViewMode('timeline')}
          >
            Timeline
          </button>
        </div>
      </div>
      
      {/* Teams and score */}
      <div className="flex items-center justify-between p-2 bg-slate-800">
        <div className="flex items-center flex-1 justify-end">
          <span className="text-sm font-medium text-white mr-2">{match.homeTeam}</span>
          {match.homeTeamLogo && (
            <img src={match.homeTeamLogo} alt={match.homeTeam} className="h-5 w-5" />
          )}
        </div>
        <div className="flex items-center px-3">
          <span className="text-lg font-bold text-white">{match.score.home}</span>
          <span className="mx-1 text-slate-500">-</span>
          <span className="text-lg font-bold text-white">{match.score.away}</span>
        </div>
        <div className="flex items-center flex-1">
          {match.awayTeamLogo && (
            <img src={match.awayTeamLogo} alt={match.awayTeam} className="h-5 w-5" />
          )}
          <span className="text-sm font-medium text-white ml-2">{match.awayTeam}</span>
        </div>
      </div>
      
      {/* Visualization content */}
      <div className="p-2">
        {viewMode === 'pitch' && (
          <div className="relative w-full h-48 bg-green-900 rounded-sm overflow-hidden">
            {/* Soccer field markings */}
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-24 h-24 border-2 border-white/30 rounded-full"></div>
              <div className="absolute w-full h-px bg-white/30"></div>
              <div className="absolute w-px h-full bg-white/30"></div>
            </div>
            
            {/* Goal areas */}
            <div className="absolute top-1/2 left-0 transform -translate-y-1/2 w-8 h-16 border-2 border-white/30 border-l-0"></div>
            <div className="absolute top-1/2 right-0 transform -translate-y-1/2 w-8 h-16 border-2 border-white/30 border-r-0"></div>
            
            {/* Penalty areas */}
            <div className="absolute top-1/2 left-0 transform -translate-y-1/2 w-12 h-24 border-2 border-white/30 border-l-0"></div>
            <div className="absolute top-1/2 right-0 transform -translate-y-1/2 w-12 h-24 border-2 border-white/30 border-r-0"></div>
            
            {/* Ball position */}
            <div 
              className="absolute" 
              style={{ 
                left: `${match.ballPosition.x}%`, 
                top: `${match.ballPosition.y}%`,
                transform: 'translate(-50%, -50%)'
              }}
            >
              <div className="h-2 w-2 bg-white rounded-full shadow-lg"></div>
            </div>
            
            {/* Players */}
            {match.players.map(player => (
              <div 
                key={player.id}
                className={`absolute ${
                  player.team === 'home' ? 'bg-blue-500' : 'bg-red-500'
                } h-3 w-3 rounded-full shadow-lg transition-all duration-300`}
                style={{ 
                  left: `${player.position.x}%`, 
                  top: `${player.position.y}%`,
                  transform: 'translate(-50%, -50%)'
                }}
              >
                <span className="absolute -top-4 left-1/2 transform -translate-x-1/2 text-[8px] text-white font-bold">
                  {player.number}
                </span>
              </div>
            ))}
            
            {/* Recent event animation */}
            {match.recentEvent && (
              <div className="absolute inset-0 flex items-center justify-center">
                {match.recentEvent.type === 'goal' && (
                  <div className="text-2xl text-white animate-bounce">⚽ GOAL!</div>
                )}
              </div>
            )}
          </div>
        )}
        
        {viewMode === 'stats' && (
          <div className="space-y-2">
            {match.stats.map(stat => (
              <div key={stat.name} className="w-full">
                <div className="flex justify-between mb-1">
                  <span className="text-xs text-white">{stat.home}</span>
                  <span className="text-xs text-slate-400">{stat.name}</span>
                  <span className="text-xs text-white">{stat.away}</span>
                </div>
                <div className="flex h-1.5 bg-slate-800 rounded-full overflow-hidden">
                  <div 
                    className="bg-blue-500 h-full transition-all duration-300"
                    style={{ width: `${(stat.home / (stat.home + stat.away)) * 100}%` }}
                  ></div>
                  <div 
                    className="bg-red-500 h-full transition-all duration-300"
                    style={{ width: `${(stat.away / (stat.home + stat.away)) * 100}%` }}
                  ></div>
                </div>
              </div>
            ))}
          </div>
        )}
        
        {viewMode === 'timeline' && (
          <div className="h-48 overflow-auto">
            {match.events.map(event => (
              <div key={event.id} className="flex items-start py-1 border-b border-slate-800/50">
                <div className="flex flex-col items-center mr-2">
                  <span className={`text-xs font-medium ${
                    event.team === 'home' ? 'text-blue-500' : 'text-red-500'
                  }`}>
                    {event.minute}'
                  </span>
                  <div className={`h-2 w-2 rounded-full mt-1 ${
                    event.team === 'home' ? 'bg-blue-500' : 'bg-red-500'
                  }`}></div>
                </div>
                <div>
                  <div className="flex items-center">
                    {event.type === 'goal' && <span className="mr-1">⚽</span>}
                    {event.type === 'yellowCard' && <span className="mr-1">🟨</span>}
                    {event.type === 'redCard' && <span className="mr-1">🟥</span>}
                    {event.type === 'substitution' && <span className="mr-1">🔄</span>}
                    <span className="text-xs text-white">{event.player}</span>
                  </div>
                  <p className="text-xs text-slate-400">{event.description}</p>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
      
      {/* Match actions */}
      <div className="flex items-center justify-between p-2 border-t border-slate-800">
        <div className="flex items-center">
          <button className="p-1 text-slate-400 hover:text-white">
            <Star className="h-3 w-3" />
          </button>
          <button className="p-1 text-slate-400 hover:text-white">
            <Bell className="h-3 w-3" />
          </button>
        </div>
        <button className="px-2 py-0.5 text-xs bg-slate-700 text-white rounded-sm hover:bg-slate-600 transition-colors">
          View All Markets
        </button>
      </div>
    </div>
  );
};

export default MatchVisualization;