import { useState } from 'react';
import { 
  X, 
  Radio, 
  Star, 
  Search,
  ChevronRight
} from 'lucide-react';

const countries = [
  { code: 'GB', name: 'United Kingdom', flagUrl: 'https://flagcdn.com/16x12/gb.png', matchCount: 45 },
  { code: 'ES', name: 'Spain', flagUrl: 'https://flagcdn.com/16x12/es.png', matchCount: 38 },
  { code: 'DE', name: 'Germany', flagUrl: 'https://flagcdn.com/16x12/de.png', matchCount: 32 },
  { code: 'FR', name: 'France', flagUrl: 'https://flagcdn.com/16x12/fr.png', matchCount: 29 },
  { code: 'IT', name: 'Italy', flagUrl: 'https://flagcdn.com/16x12/it.png', matchCount: 25 },
  { code: 'US', name: 'United States', flagUrl: 'https://flagcdn.com/16x12/us.png', matchCount: 41 },
];

const sports = [
  { id: 'soccer', name: 'Soccer', icon: '⚽', matchCount: 142 },
  { id: 'basketball', name: 'Basketball', icon: '🏀', matchCount: 87 },
  { id: 'tennis', name: 'Tennis', icon: '🎾', matchCount: 54 },
  { id: 'football', name: 'NFL', icon: '🏈', matchCount: 23 },
  { id: 'cricket', name: 'Cricket', icon: '🏏', matchCount: 18 },
  { id: 'esports', name: 'Esports', icon: '🎮', matchCount: 92 },
];

const leagues = [
  { id: 'epl', sportId: 'soccer', name: 'Premier League', logoUrl: '', countryCode: 'GB' },
  { id: 'liga', sportId: 'soccer', name: 'La Liga', logoUrl: '', countryCode: 'ES' },
  { id: 'bundesliga', sportId: 'soccer', name: 'Bundesliga', logoUrl: '', countryCode: 'DE' },
  { id: 'nba', sportId: 'basketball', name: 'NBA', logoUrl: '', countryCode: 'US' },
  { id: 'euroleague', sportId: 'basketball', name: 'EuroLeague', logoUrl: '', countryCode: null },
  { id: 'wimbledon', sportId: 'tennis', name: 'Wimbledon', logoUrl: '', countryCode: 'GB' },
];

interface SportsNavigationProps {
  onSelectFilter: (filters: any) => void;
}

const SportsNavigation = ({ onSelectFilter }: SportsNavigationProps) => {
  const [activeView, setActiveView] = useState<'country' | 'sport' | 'league'>('sport');
  const [activeCountry, setActiveCountry] = useState<string | null>(null);
  const [activeSport, setActiveSport] = useState<string | null>('soccer');
  const [activeLeague, setActiveLeague] = useState<string | null>(null);
  const [showFavorites, setShowFavorites] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  const clearFilters = () => {
    setActiveCountry(null);
    setActiveSport(null);
    setActiveLeague(null);
    setShowFavorites(false);
    onSelectFilter({});
  };

  const handleCountrySelect = (countryCode: string) => {
    setActiveCountry(countryCode);
    setActiveView('sport');
    onSelectFilter({ country: countryCode });
  };

  const handleSportSelect = (sportId: string) => {
    setActiveSport(sportId);
    setActiveView('league');
    onSelectFilter({ country: activeCountry, sport: sportId });
  };

  const handleLeagueSelect = (leagueId: string) => {
    setActiveLeague(leagueId);
    onSelectFilter({ country: activeCountry, sport: activeSport, league: leagueId });
  };

  const filteredLeagues = leagues.filter(league => 
    (!activeSport || league.sportId === activeSport) &&
    (!activeCountry || league.countryCode === activeCountry)
  );

  return (
    <div className="flex flex-col h-full bg-slate-900 border-r border-slate-800 w-64">
      {/* View selection tabs */}
      <div className="flex h-7 border-b border-slate-800">
        <button 
          className={`flex-1 text-xs font-medium transition-colors ${
            activeView === 'country' 
              ? 'text-white border-b-2 border-purple-500' 
              : 'text-slate-400 hover:text-white'
          }`}
          onClick={() => setActiveView('country')}
        >
          By Country
        </button>
        <button 
          className={`flex-1 text-xs font-medium transition-colors ${
            activeView === 'sport' 
              ? 'text-white border-b-2 border-purple-500' 
              : 'text-slate-400 hover:text-white'
          }`}
          onClick={() => setActiveView('sport')}
        >
          By Sport
        </button>
        <button 
          className={`flex-1 text-xs font-medium transition-colors ${
            activeView === 'league' 
              ? 'text-white border-b-2 border-purple-500' 
              : 'text-slate-400 hover:text-white'
          }`}
          onClick={() => setActiveView('league')}
        >
          By League
        </button>
      </div>

      {/* Search bar */}
      <div className="p-2 border-b border-slate-800">
        <div className="relative">
          <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-3 w-3 text-slate-400" />
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Search..."
            className="w-full pl-7 pr-2 py-1 bg-slate-800 border border-slate-700 rounded-sm text-xs text-white placeholder-slate-400 focus:outline-none focus:border-slate-600"
          />
        </div>
      </div>

      {/* Quick filters */}
      <div className="flex items-center space-x-1 p-2 border-b border-slate-800">
        <button
          className={`flex items-center px-2 py-1 rounded-sm text-xs transition-colors ${
            showFavorites
              ? 'bg-yellow-600 text-white'
              : 'bg-slate-800 text-slate-400 hover:text-white'
          }`}
          onClick={() => setShowFavorites(!showFavorites)}
        >
          <Star className={`h-3 w-3 mr-1 ${showFavorites ? 'fill-yellow-400' : ''}`} />
          Favorites
        </button>
        <button
          className="flex items-center px-2 py-1 rounded-sm text-xs bg-slate-800 text-slate-400 hover:text-white transition-colors"
        >
          <Radio className="h-3 w-3 mr-1 text-red-500" />
          Live Only
        </button>
      </div>
      
      {/* Content based on active view */}
      <div className="flex-1 overflow-auto">
        {activeView === 'country' && (
          <div className="p-0">
            {countries.map(country => (
              <button 
                key={country.code} 
                className={`flex items-center w-full px-3 py-1.5 hover:bg-slate-800 text-left transition-colors ${
                  activeCountry === country.code ? 'bg-slate-800' : ''
                }`}
                onClick={() => handleCountrySelect(country.code)}
              >
                <img 
                  src={country.flagUrl} 
                  alt={country.name} 
                  className="h-3 w-4 mr-2" 
                />
                <span className="text-xs text-white">{country.name}</span>
                <span className="ml-auto text-xs text-slate-400">{country.matchCount}</span>
                <ChevronRight className="h-3 w-3 text-slate-400 ml-1" />
              </button>
            ))}
          </div>
        )}
        
        {activeView === 'sport' && (
          <div className="p-0">
            {sports.map(sport => (
              <button 
                key={sport.id} 
                className={`flex items-center w-full px-3 py-1.5 hover:bg-slate-800 text-left transition-colors ${
                  activeSport === sport.id ? 'bg-slate-800' : ''
                }`}
                onClick={() => handleSportSelect(sport.id)}
              >
                <span className="text-base mr-2">{sport.icon}</span>
                <span className="text-xs text-white">{sport.name}</span>
                <span className="ml-auto text-xs text-slate-400">{sport.matchCount}</span>
                <ChevronRight className="h-3 w-3 text-slate-400 ml-1" />
              </button>
            ))}
          </div>
        )}
        
        {activeView === 'league' && (
          <div className="p-0">
            {filteredLeagues.map(league => (
              <button 
                key={league.id} 
                className={`flex items-center w-full px-3 py-1.5 hover:bg-slate-800 text-left transition-colors ${
                  activeLeague === league.id ? 'bg-slate-800' : ''
                }`}
                onClick={() => handleLeagueSelect(league.id)}
              >
                <span className="text-xs text-white">{league.name}</span>
                {league.countryCode && (
                  <img 
                    src={`https://flagcdn.com/16x12/${league.countryCode.toLowerCase()}.png`} 
                    alt={league.countryCode} 
                    className="h-3 w-4 ml-2" 
                  />
                )}
                <span className="ml-auto text-xs text-slate-400">12</span>
                <ChevronRight className="h-3 w-3 text-slate-400 ml-1" />
              </button>
            ))}
          </div>
        )}
      </div>
      
      {/* Active filters */}
      {(activeCountry || activeSport || activeLeague) && (
        <div className="p-2 border-t border-slate-800 bg-slate-900/50">
          <div className="flex items-center justify-between mb-1">
            <span className="text-xs text-slate-400">Active Filters:</span>
            <button
              onClick={clearFilters}
              className="text-xs text-slate-400 hover:text-white"
            >
              Clear all
            </button>
          </div>
          <div className="flex flex-wrap gap-1">
            {activeCountry && (
              <span className="inline-flex items-center px-1.5 py-0.5 rounded-sm text-xs bg-slate-800 text-white">
                <img 
                  src={`https://flagcdn.com/16x12/${activeCountry.toLowerCase()}.png`} 
                  alt={activeCountry} 
                  className="h-2.5 w-3 mr-1" 
                />
                {countries.find(c => c.code === activeCountry)?.name}
                <X 
                  className="h-3 w-3 ml-1 text-slate-400 hover:text-white cursor-pointer" 
                  onClick={(e) => {
                    e.stopPropagation();
                    setActiveCountry(null);
                  }} 
                />
              </span>
            )}
            {activeSport && (
              <span className="inline-flex items-center px-1.5 py-0.5 rounded-sm text-xs bg-slate-800 text-white">
                {sports.find(s => s.id === activeSport)?.icon}
                <span className="ml-1">{sports.find(s => s.id === activeSport)?.name}</span>
                <X 
                  className="h-3 w-3 ml-1 text-slate-400 hover:text-white cursor-pointer" 
                  onClick={(e) => {
                    e.stopPropagation();
                    setActiveSport(null);
                  }} 
                />
              </span>
            )}
            {activeLeague && (
              <span className="inline-flex items-center px-1.5 py-0.5 rounded-sm text-xs bg-slate-800 text-white">
                {leagues.find(l => l.id === activeLeague)?.name}
                <X 
                  className="h-3 w-3 ml-1 text-slate-400 hover:text-white cursor-pointer" 
                  onClick={(e) => {
                    e.stopPropagation();
                    setActiveLeague(null);
                  }} 
                />
              </span>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default SportsNavigation;