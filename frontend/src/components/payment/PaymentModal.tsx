import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, DialogTitle } from '../ui/dialog';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Tabs, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '../ui/tabs';
import { Card } from '../ui/card';
import api from '../../services/api';
import useWalletStore from '../../stores/walletStore';
import { CreditCard, Building2, Bitcoin, Loader2 } from 'lucide-react';

interface PaymentModalProps {
  isOpen: boolean;
  onClose: () => void;
  mode: 'deposit' | 'withdraw';
  onSuccess?: () => void;
}

interface PaymentMethod {
  id: string;
  type: string;
  last_four?: string;
  card_brand?: string;
  bank_name?: string;
  crypto_address?: string;
  is_default: boolean;
}

const PaymentModal: React.FC<PaymentModalProps> = ({ isOpen, onClose, mode, onSuccess }) => {
  const { balance, setBalance } = useWalletStore();
  const [amount, setAmount] = useState('');
  const [selectedMethod, setSelectedMethod] = useState<PaymentMethod | null>(null);
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [activeTab, setActiveTab] = useState('saved');

  React.useEffect(() => {
    if (isOpen) {
      fetchPaymentMethods();
    }
  }, [isOpen]);

  const fetchPaymentMethods = async () => {
    try {
      const response = await api.get('/payment/payment-methods');
      setPaymentMethods(response.data.payment_methods);
      const defaultMethod = response.data.payment_methods.find((m: PaymentMethod) => m.is_default);
      if (defaultMethod) {
        setSelectedMethod(defaultMethod);
      }
    } catch (error) {
      // console.error('Failed to fetch payment methods:', error);
    }
  };

  const handleSubmit = async () => {
    if (!amount || parseFloat(amount) <= 0) {
      setError('Please enter a valid amount');
      return;
    }

    if (!selectedMethod) {
      setError('Please select a payment method');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const endpoint = mode === 'deposit' ? '/payment/deposit' : '/payment/withdraw';
      const response = await api.post(endpoint, {
        amount: parseFloat(amount),
        payment_method: {
          type: selectedMethod.type,
          last_four: selectedMethod.last_four,
          card_brand: selectedMethod.card_brand,
          bank_name: selectedMethod.bank_name,
          crypto_address: selectedMethod.crypto_address,
        },
        description: `${mode === 'deposit' ? 'Deposit' : 'Withdrawal'} via ${selectedMethod.type}`,
      });

      if (response.data) {
        // Refresh wallet balance
      const response = await api.get('/api/v1/wallet/balance');
      setBalance(response.data.balance);
        if (onSuccess) {
          onSuccess();
        }
        onClose();
      }
    } catch (error: any) {
      setError(error.response?.data?.detail || `${mode} failed. Please try again.`);
    } finally {
      setLoading(false);
    }
  };

  const getMethodIcon = (type: string) => {
    switch (type) {
      case 'card':
        return <CreditCard className="w-5 h-5" />;
      case 'bank_transfer':
        return <Building2 className="w-5 h-5" />;
      case 'crypto':
        return <Bitcoin className="w-5 h-5" />;
      default:
        return null;
    }
  };

  const getMethodDisplay = (method: PaymentMethod) => {
    switch (method.type) {
      case 'card':
        return `${method.card_brand} •••• ${method.last_four}`;
      case 'bank_transfer':
        return `${method.bank_name} •••• ${method.last_four}`;
      case 'crypto':
        return `Bitcoin ${method.crypto_address?.substring(0, 8)}...`;
      default:
        return method.type;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>
            {mode === 'deposit' ? 'Deposit Funds' : 'Withdraw Funds'}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <div>
            <Label>Amount</Label>
            <div className="relative">
              <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500">$</span>
              <Input
                type="number"
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
                placeholder="0.00"
                className="pl-8"
                min="0"
                step="0.01"
              />
            </div>
            {mode === 'withdraw' && (
              <p className="text-sm text-gray-600 mt-1">
                Available balance: ${balance.toFixed(2)}
              </p>
            )}
          </div>

          <div>
            <Label>Payment Method</Label>
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="saved">Saved Methods</TabsTrigger>
                <TabsTrigger value="new">New Method</TabsTrigger>
              </TabsList>

              <TabsContent value="saved" className="space-y-2 mt-4">
                {paymentMethods.map((method) => (
                  <Card
                    key={method.id}
                    className={`p-3 cursor-pointer transition-colors ${
                      selectedMethod?.id === method.id
                        ? 'border-purple-500 bg-purple-50'
                        : 'hover:border-gray-300'
                    }`}
                    onClick={() => setSelectedMethod(method)}
                  >
                    <div className="flex items-center space-x-3">
                      {getMethodIcon(method.type)}
                      <div className="flex-1">
                        <div className="font-medium">{getMethodDisplay(method)}</div>
                        {method.is_default && (
                          <span className="text-xs text-gray-500">Default</span>
                        )}
                      </div>
                      <div className="w-4 h-4 rounded-full border-2 border-purple-500">
                        {selectedMethod?.id === method.id && (
                          <div className="w-2 h-2 rounded-full bg-purple-500 m-0.5" />
                        )}
                      </div>
                    </div>
                  </Card>
                ))}
              </TabsContent>

              <TabsContent value="new" className="mt-4">
                <div className="text-center py-8 text-gray-500">
                  <CreditCard className="w-12 h-12 mx-auto mb-2 text-gray-400" />
                  <p>Adding new payment methods coming soon!</p>
                </div>
              </TabsContent>
            </Tabs>
          </div>

          {error && (
            <div className="bg-red-50 text-red-600 p-3 rounded-lg text-sm">
              {error}
            </div>
          )}

          <div className="space-y-3">
            <div className="bg-gray-50 p-3 rounded-lg">
              <div className="flex justify-between text-sm">
                <span>Amount</span>
                <span>${amount || '0.00'}</span>
              </div>
              <div className="flex justify-between text-sm mt-1">
                <span>Processing Fee</span>
                <span>
                  {mode === 'deposit' 
                    ? selectedMethod?.type === 'card' ? '2.5%' : selectedMethod?.type === 'bank_transfer' ? '1%' : '0.5%'
                    : '$2.00'
                  }
                </span>
              </div>
              <div className="flex justify-between font-semibold mt-2 pt-2 border-t">
                <span>Total</span>
                <span>
                  $
                  {mode === 'deposit'
                    ? (
                        parseFloat(amount || '0') +
                        parseFloat(amount || '0') *
                          (selectedMethod?.type === 'card' ? 0.025 : selectedMethod?.type === 'bank_transfer' ? 0.01 : 0.005)
                      ).toFixed(2)
                    : (parseFloat(amount || '0') + 2).toFixed(2)}
                </span>
              </div>
            </div>

            <Button
              className="w-full"
              onClick={handleSubmit}
              disabled={loading || !amount || !selectedMethod}
            >
              {loading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Processing...
                </>
              ) : (
                `${mode === 'deposit' ? 'Deposit' : 'Withdraw'} Funds`
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default PaymentModal;