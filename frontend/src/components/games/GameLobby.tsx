import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { useState, useEffect } from 'react';
import { Button } from '../ui/button';
import { Card } from '../ui/card';
import { Badge } from '../ui/badge';
import { 
  Users, Clock, Trophy, DollarSign, Play, Eye, ArrowLeft, 
  RefreshCw, Plus, Shield, Zap 
} from 'lucide-react';
import LoadingSpinner from '../shared/LoadingSpinner';
import { useGameStateStore } from '@/stores/gameStateStore';
import { gameStateService } from '@/services/gameState';

interface GameSession {
  id: string;
  hostName: string;
  players: number;
  maxPlayers: number;
  wager: number;
  status: 'waiting' | 'starting' | 'playing';
  timeLimit: number;
  createdAt: Date;
}

const GameLobby = () => {
  const { gameType } = useParams();
  const navigate = useNavigate();
  const { activeGames, loadActiveGames } = useGameStateStore();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Load active games from API
  useEffect(() => {
    const loadGames = async () => {
      setLoading(true);
      await loadActiveGames(gameType);
      setLoading(false);
    };
    
    loadGames();
  }, [gameType, loadActiveGames]);

  const handleJoinSession = (sessionId: string) => {
    navigate(`/games/${gameType}/session/${sessionId}`);
  };

  const handleCreateSession = () => {
    navigate(`/games/${gameType}`);
  };

  const handleSpectate = (sessionId: string) => {
    navigate(`/games/${gameType}/spectate/${sessionId}`);
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadActiveGames(gameType);
    setRefreshing(false);
  };

  const getGameName = () => {
    const gameNames: { [key: string]: string } = {
      'chess': 'Chess',
      'checkers': 'Checkers',
      'rock-paper-scissors': 'Rock Paper Scissors',
      'highlight-hero': 'Highlight Hero',
      'blur-detective': 'Blur Detective'
    };
    return gameNames[gameType || ''] || gameType;
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="large" text={`Loading ${getGameName()} sessions...`} />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-slate-950 p-4">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="mb-6">
          <div className="flex items-center gap-4 mb-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate('/games')}
              className="text-slate-400 hover:text-white"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Games
            </Button>
          </div>
          
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold text-white mb-2">{getGameName()} Lobby</h1>
              <p className="text-slate-400">Join an existing game or create your own</p>
            </div>
            
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={handleRefresh}
                disabled={refreshing}
                className="text-slate-400 hover:text-white"
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
              
              <Button
                onClick={handleCreateSession}
                className="bg-gradient-to-r from-purple-500 to-pink-500"
              >
                <Plus className="h-4 w-4 mr-2" />
                Create Game
              </Button>
            </div>
          </div>
        </div>

        {/* Active Sessions */}
        <div className="mb-6">
          <h2 className="text-xl font-semibold text-white mb-4">Open Sessions</h2>
          
          {activeGames.length === 0 ? (
            <Card className="bg-slate-900 border-slate-800 p-8 text-center">
              <Trophy className="h-12 w-12 text-slate-600 mx-auto mb-4" />
              <p className="text-slate-400 mb-4">No active sessions at the moment</p>
              <Button onClick={handleCreateSession}>Be the first to create one!</Button>
            </Card>
          ) : (
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {activeGames.map((game) => (
                <Card key={game.id} className="bg-slate-900 border-slate-800 p-4">
                  <div className="flex justify-between items-start mb-3">
                    <div>
                      <h3 className="font-semibold text-white">
                        {game.players[0]?.username || 'Anonymous'}'s Game
                      </h3>
                      <p className="text-sm text-slate-400">
                        Created {Math.floor((Date.now() - new Date(game.created_at).getTime()) / 60000)}m ago
                      </p>
                    </div>
                    <Badge className={
                      game.status === 'waiting' ? 'bg-green-500' :
                      game.status === 'active' ? 'bg-yellow-500' :
                      'bg-red-500'
                    }>
                      {game.status.toUpperCase()}
                    </Badge>
                  </div>
                  
                  {/* Game State Thumbnail */}
                  <div className="bg-slate-800 rounded p-2 mb-3 text-xs font-mono text-slate-300">
                    {gameStateService.getGameThumbnail(game)}
                  </div>
                  
                  <div className="space-y-2 mb-4">
                    <div className="flex items-center text-sm text-slate-400">
                      <Users className="h-4 w-4 mr-2" />
                      {game.players.length}/2 Players
                    </div>
                    <div className="flex items-center text-sm text-slate-400">
                      <DollarSign className="h-4 w-4 mr-2" />
                      ${game.state.stake_amount || 50} Wager
                    </div>
                    <div className="flex items-center text-sm text-slate-400">
                      <Eye className="h-4 w-4 mr-2" />
                      {game.spectators} Spectators
                    </div>
                  </div>
                  
                  <div className="flex gap-2">
                    {game.status === 'waiting' && game.players.length < 2 && (
                      <Button
                        className="flex-1"
                        onClick={() => handleJoinSession(game.id)}
                      >
                        <Play className="h-4 w-4 mr-2" />
                        Join Game
                      </Button>
                    )}
                    {(game.status === 'active' || game.players.length >= 2) && (
                      <Button
                        variant="outline"
                        className="flex-1"
                        onClick={() => handleSpectate(game.id)}
                      >
                        <Eye className="h-4 w-4 mr-2" />
                        Spectate
                      </Button>
                    )}
                  </div>
                </Card>
              ))}
            </div>
          )}
        </div>

        {/* Quick Play Options */}
        <div>
          <h2 className="text-xl font-semibold text-white mb-4">Quick Play</h2>
          <div className="grid gap-4 md:grid-cols-3">
            <Card className="bg-slate-900 border-slate-800 p-4 cursor-pointer hover:border-slate-700"
                  onClick={handleCreateSession}>
              <Shield className="h-8 w-8 text-blue-500 mb-2" />
              <h3 className="font-semibold text-white mb-1">Practice Mode</h3>
              <p className="text-sm text-slate-400">Play against AI to improve your skills</p>
            </Card>
            
            <Card className="bg-slate-900 border-slate-800 p-4 cursor-pointer hover:border-slate-700"
                  onClick={handleCreateSession}>
              <Zap className="h-8 w-8 text-yellow-500 mb-2" />
              <h3 className="font-semibold text-white mb-1">Quick Match</h3>
              <p className="text-sm text-slate-400">Get matched with a random opponent</p>
            </Card>
            
            <Card className="bg-slate-900 border-slate-800 p-4 cursor-pointer hover:border-slate-700"
                  onClick={() => navigate('/games')}>
              <Trophy className="h-8 w-8 text-purple-500 mb-2" />
              <h3 className="font-semibold text-white mb-1">Tournament</h3>
              <p className="text-sm text-slate-400">Join scheduled tournaments for bigger prizes</p>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GameLobby;