import React from 'react';
import { Button } from '@/components/ui/button';

interface CheckersGameSimpleProps {
  onBack?: () => void;
}

const CheckersGameSimple: React.FC<CheckersGameSimpleProps> = ({ onBack }) => {
  // console.log('CheckersGameSimple rendering');
  
  return (
    <div className="w-full h-screen bg-gray-900 p-4">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-white text-2xl mb-4">Checkers Game</h1>
        
        {/* Board container */}
        <div className="flex justify-center mb-4">
          <div className="w-96 h-96 bg-gray-800 border-2 border-gray-600 rounded-lg p-2">
            <div className="grid grid-cols-8 gap-0 w-full h-full">
              {Array.from({ length: 64 }).map((_, index) => {
                const row = Math.floor(index / 8);
                const col = index % 8;
                const isLight = (row + col) % 2 === 0;
                
                return (
                  <div
                    key={index}
                    className={`${
                      isLight ? 'bg-gray-600' : 'bg-gray-800'
                    } border border-gray-700`}
                  />
                );
              })}
            </div>
          </div>
        </div>
        
        {onBack && (
          <Button onClick={onBack} className="bg-blue-600 hover:bg-blue-700">
            Back to Games
          </Button>
        )}
      </div>
    </div>
  );
};

export default CheckersGameSimple;