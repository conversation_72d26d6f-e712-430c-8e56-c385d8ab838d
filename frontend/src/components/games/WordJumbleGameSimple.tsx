import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Brain, 
  Check, 
  X, 
  Shuffle, 
  Timer,
  Target,
  Trophy
} from 'lucide-react';

// Sample word sets
const WORD_SETS = {
  easy: [
    { letters: 'TCA', solutions: ['CAT', 'ACT', 'TAC'], target: 'CAT' },
    { letters: 'GOD', solutions: ['DOG', 'GOD'], target: 'DOG' },
    { letters: 'UNS', solutions: ['SUN', 'NUS'], target: 'SUN' }
  ],
  medium: [
    { letters: 'OWLF', solutions: ['FLOW', 'FOWL', 'WOLF'], target: 'FLOW' },
    { letters: 'KROW', solutions: ['WORK'], target: 'WORK' },
    { letters: 'MITE', solutions: ['TIME', 'ITEM', 'EMIT', 'MITE'], target: 'TIME' }
  ],
  hard: [
    { letters: 'POTMCER', solutions: ['COMPUTER', 'COMPETE', 'COME', 'PORT', 'MORE'], target: 'COMPUTER' },
    { letters: 'RINGDEA', solutions: ['READING', 'GARDEN', 'GRADE', 'DEAR', 'GEAR'], target: 'READING' }
  ]
};

interface WordJumbleGameSimpleProps {
  difficulty?: 'easy' | 'medium' | 'hard';
  onComplete?: (score: number) => void;
}

const WordJumbleGameSimple: React.FC<WordJumbleGameSimpleProps> = ({ 
  difficulty = 'medium',
  onComplete 
}) => {
  const [currentWordSet, setCurrentWordSet] = useState<any>(null);
  const [scrambledLetters, setScrambledLetters] = useState<string[]>([]);
  const [currentWord, setCurrentWord] = useState('');
  const [score, setScore] = useState(0);
  const [timeLeft, setTimeLeft] = useState(60);
  const [isPlaying, setIsPlaying] = useState(false);
  const [showResult, setShowResult] = useState(false);
  const [foundWords, setFoundWords] = useState<string[]>([]);

  useEffect(() => {
    if (isPlaying && timeLeft > 0) {
      const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000);
      return () => clearTimeout(timer);
    } else if (timeLeft === 0 && isPlaying) {
      endGame();
    }
  }, [timeLeft, isPlaying]);

  const shuffleArray = (array: any[]) => {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  };

  const startGame = () => {
    const wordSets = WORD_SETS[difficulty];
    const randomSet = wordSets[Math.floor(Math.random() * wordSets.length)];
    setCurrentWordSet(randomSet);
    setScrambledLetters(shuffleArray(randomSet.letters.split('')));
    setCurrentWord('');
    setScore(0);
    setTimeLeft(60);
    setIsPlaying(true);
    setShowResult(false);
    setFoundWords([]);
  };

  const handleLetterClick = (letter: string, index: number) => {
    if (!isPlaying) return;
    
    const newScrambled = [...scrambledLetters];
    newScrambled[index] = '';
    setScrambledLetters(newScrambled);
    setCurrentWord(currentWord + letter);
  };

  const handleClear = () => {
    if (!currentWordSet) return;
    setScrambledLetters(currentWordSet.letters.split(''));
    setCurrentWord('');
  };

  const handleShuffle = () => {
    const availableLetters = scrambledLetters.filter(l => l !== '');
    const usedLetters = currentWord.split('');
    const allLetters = [...availableLetters, ...usedLetters];
    setScrambledLetters(shuffleArray(allLetters));
    setCurrentWord('');
  };

  const calculateWordScore = (word: string) => {
    const baseScore = word.length * 10;
    let multiplier = 1;

    if (currentWordSet.solutions.includes(word)) {
      multiplier = 1.5;
    }
    if (word === currentWordSet.target) {
      multiplier = 2;
    }
    if (word.length >= 6) multiplier += 0.5;

    return Math.round(baseScore * multiplier);
  };

  const submitWord = () => {
    if (!currentWord || currentWord.length < 3) return;
    
    if (foundWords.includes(currentWord)) {
      // Already found this word
      return;
    }

    const wordScore = calculateWordScore(currentWord);
    if (currentWordSet.solutions.includes(currentWord)) {
      setScore(score + wordScore);
      setFoundWords([...foundWords, currentWord]);
    }
    
    handleClear();
  };

  const endGame = () => {
    setIsPlaying(false);
    setShowResult(true);
    if (onComplete) {
      onComplete(score);
    }
  };

  return (
    <div className="bg-slate-900 rounded-lg p-6 max-w-md mx-auto">
      <div className="text-center mb-6">
        <div className="flex items-center justify-center mb-2">
          <Brain className="h-8 w-8 text-purple-500 mr-2" />
          <h2 className="text-2xl font-bold text-white">Word Jumble</h2>
        </div>
        
        {!isPlaying && !showResult && (
          <p className="text-slate-400">Unscramble letters to form words!</p>
        )}
      </div>

      {!isPlaying && !showResult && (
        <div className="text-center">
          <Button 
            onClick={startGame}
            className="bg-gradient-to-r from-purple-500 to-pink-500"
          >
            Start Game
          </Button>
        </div>
      )}

      {isPlaying && currentWordSet && (
        <>
          {/* Game Stats */}
          <div className="flex justify-between mb-4">
            <div className="text-sm">
              <span className="text-slate-400">Score: </span>
              <span className="text-white font-bold">{score}</span>
            </div>
            <div className="text-sm">
              <span className="text-slate-400">Time: </span>
              <span className="text-white font-bold">{timeLeft}s</span>
            </div>
          </div>

          <Progress value={(timeLeft / 60) * 100} className="mb-4 h-2" />

          {/* Target hint */}
          <div className="bg-purple-900/30 border border-purple-700 p-2 rounded mb-4 text-center">
            <p className="text-xs text-purple-400">
              Target word: {currentWordSet.target.length} letters (2x bonus!)
            </p>
          </div>

          {/* Scrambled Letters */}
          <div className="mb-4">
            <div className="flex justify-between items-center mb-2">
              <h3 className="text-sm font-medium text-white">Letters</h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleShuffle}
                className="h-6 w-6 p-0"
              >
                <Shuffle className="h-3 w-3" />
              </Button>
            </div>
            <div className="flex gap-2 justify-center flex-wrap">
              {scrambledLetters.map((letter, index) => (
                <button
                  key={index}
                  onClick={() => letter && handleLetterClick(letter, index)}
                  disabled={!letter}
                  className={`w-10 h-10 rounded border-2 font-bold text-lg transition-all
                    ${letter 
                      ? 'bg-slate-800 border-slate-600 text-white hover:bg-slate-700 cursor-pointer' 
                      : 'bg-transparent border-transparent'
                    }`}
                >
                  {letter}
                </button>
              ))}
            </div>
          </div>

          {/* Current Word */}
          <div className="mb-4">
            <h3 className="text-sm font-medium text-white mb-2">Your Word</h3>
            <div className="bg-slate-800 p-3 rounded text-center">
              <span className="text-xl font-bold text-white tracking-wider">
                {currentWord || '---'}
              </span>
              {currentWord && (
                <span className="text-xs text-slate-400 ml-2">
                  ({calculateWordScore(currentWord)} pts)
                </span>
              )}
            </div>
          </div>

          {/* Actions */}
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleClear}
              disabled={!currentWord}
              className="flex-1"
            >
              <X className="h-3 w-3 mr-1" />
              Clear
            </Button>
            <Button
              size="sm"
              onClick={submitWord}
              disabled={!currentWord || currentWord.length < 3}
              className="flex-1 bg-green-600 hover:bg-green-700"
            >
              <Check className="h-3 w-3 mr-1" />
              Submit
            </Button>
          </div>

          {/* Found Words */}
          {foundWords.length > 0 && (
            <div className="mt-4">
              <h3 className="text-sm font-medium text-white mb-2">Found Words</h3>
              <div className="flex flex-wrap gap-2">
                {foundWords.map((word, index) => (
                  <Badge key={index} variant="secondary" className="text-xs">
                    {word}
                  </Badge>
                ))}
              </div>
            </div>
          )}
        </>
      )}

      {showResult && (
        <div className="text-center">
          <Trophy className="h-16 w-16 text-yellow-500 mx-auto mb-4" />
          <h3 className="text-xl font-bold text-white mb-2">Game Over!</h3>
          <p className="text-2xl font-bold text-green-500 mb-2">{score} points</p>
          
          {currentWordSet && (
            <div className="bg-slate-800 p-3 rounded mb-4">
              <p className="text-sm text-slate-400 mb-2">
                Target word was: <span className="text-purple-400 font-bold">{currentWordSet.target}</span>
              </p>
              <p className="text-xs text-slate-400">
                Found {foundWords.length} / {currentWordSet.solutions.length} words
              </p>
            </div>
          )}
          
          <Button onClick={startGame} className="bg-gradient-to-r from-purple-500 to-pink-500">
            Play Again
          </Button>
        </div>
      )}
    </div>
  );
};

export default WordJumbleGameSimple;