import { useState } from 'react';
import {
  Users,
  Activity,
  Send,
  ChevronLeft,
  Volume2,
  VolumeX,
  Sparkles,
  DollarSign,
  Menu,
  X,
  Eye
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import type { GameData } from './gameTypes';

interface MobileLiveSpectatorSectionProps {
  game?: GameData;
  onBack?: () => void;
  className?: string;
}

const MobileLiveSpectatorSection = ({ game, onBack, className = '' }: MobileLiveSpectatorSectionProps) => {
  const [selectedBet, setSelectedBet] = useState('player-a');
  const [betAmount, setBetAmount] = useState('10');
  const [chatMessage, setChatMessage] = useState('');
  const [soundEnabled, setSoundEnabled] = useState(true);
  const [activeTab, setActiveTab] = useState('game');
  const [showMenu, setShowMenu] = useState(false);

  return (
    <div className={`h-screen bg-slate-950 text-white overflow-hidden flex flex-col ${className}`}>
      {/* Fixed Header */}
      <div className="flex-none bg-slate-900 border-b border-slate-800">
        <div className="flex items-center justify-between p-2">
          <div className="flex items-center gap-2">
            {onBack ? (
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0"
                onClick={onBack}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
            ) : (
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0"
                onClick={() => setShowMenu(!showMenu)}
              >
                <Menu className="h-4 w-4" />
              </Button>
            )}
            <h1 className="text-sm font-bold">{game?.name || 'Speed Quiz'}</h1>
          </div>
          <div className="flex items-center gap-2">
            <Badge className="bg-purple-500">
              <DollarSign className="h-3 w-3 mr-0.5" />
              ${game?.prize || 2450}
            </Badge>
            <Badge className="bg-green-500">
              <Eye className="h-3 w-3 mr-0.5" />
              {game?.viewers || 1200}
            </Badge>
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0"
              onClick={() => setSoundEnabled(!soundEnabled)}
            >
              {soundEnabled ? <Volume2 className="h-4 w-4" /> : <VolumeX className="h-4 w-4" />}
            </Button>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="px-2 pb-2">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-4 h-8">
              <TabsTrigger value="game" className="text-xs">Game</TabsTrigger>
              <TabsTrigger value="bets" className="text-xs">Bets</TabsTrigger>
              <TabsTrigger value="chat" className="text-xs">Chat</TabsTrigger>
              <TabsTrigger value="leaders" className="text-xs">Leaders</TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      </div>

      {/* Slide-out Menu */}
      {showMenu && (
        <div className="fixed inset-0 z-40 flex">
          <div className="fixed inset-0 bg-black bg-opacity-50" onClick={() => setShowMenu(false)} />
          <div className="relative flex flex-col w-64 bg-slate-900 border-r border-slate-800">
            <div className="flex items-center justify-between p-3 border-b border-slate-800">
              <h2 className="text-sm font-bold">Active Games</h2>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0"
                onClick={() => setShowMenu(false)}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
            <div className="flex-1 overflow-auto p-2">
              {Array.from({ length: 8 }).map((_, idx) => (
                <div key={idx} className="mb-2 p-2 bg-slate-800 rounded-sm border border-slate-700">
                  <div className="flex justify-between items-start mb-1">
                    <h4 className="text-xs font-medium text-white">
                      {['Speed Quiz', 'Tap Race', 'Puzzle Duel', 'Blur Guess'][idx % 4]}
                    </h4>
                    <Badge className="bg-red-500 text-[10px] h-4 px-1">LIVE</Badge>
                  </div>
                  <div className="flex items-center justify-between text-[10px] text-slate-400">
                    <div className="flex items-center">
                      <Users className="h-2.5 w-2.5 mr-1" />
                      <span>{234 + idx * 23} watching</span>
                    </div>
                    <div className="flex items-center">
                      <Sparkles className="h-2.5 w-2.5 mr-0.5" />
                      <span>${(1250 + idx * 150).toLocaleString()}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className="flex-1 overflow-y-auto pb-16">
        {/* Game Tab */}
        {activeTab === 'game' && (
          <div className="p-2">
            {/* Game Visualization */}
            <div className="bg-slate-900 rounded-lg border border-slate-800 p-4 mb-2">
              <div className="flex justify-between items-center mb-4">
                <Badge className="bg-red-500">
                  <Activity className="h-3 w-3 mr-1" />
                  LIVE
                </Badge>
                <span className="text-sm text-slate-400">Round {game?.currentRound || 2}/{game?.totalRounds || 3}</span>
              </div>

              {/* Player vs Player Display */}
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div className="text-center">
                  <div className="h-16 w-16 rounded-full bg-blue-500 mx-auto mb-2"></div>
                  <h3 className="text-sm font-bold text-white">{game?.players.player1.name || 'Player A'}</h3>
                  <div className="mt-1">
                    <div className="text-xl font-bold text-white">12</div>
                    <div className="text-xs text-slate-400">Points</div>
                  </div>
                  <Progress value={65} className="mt-2" />
                </div>

                <div className="text-center">
                  <div className="h-16 w-16 rounded-full bg-red-500 mx-auto mb-2"></div>
                  <h3 className="text-sm font-bold text-white">{game?.players.player2.name || 'Player B'}</h3>
                  <div className="mt-1">
                    <div className="text-xl font-bold text-white">10</div>
                    <div className="text-xs text-slate-400">Points</div>
                  </div>
                  <Progress value={55} className="mt-2" />
                </div>
              </div>

              {/* Current Question */}
              <div className="bg-slate-800 rounded-sm p-3 text-center">
                <h4 className="text-xs font-medium text-slate-400 mb-1">Current Question:</h4>
                <p className="text-sm text-white">What is the capital of Australia?</p>
                <div className="mt-2 text-xs text-slate-400">Next question in: 0:08</div>
              </div>
            </div>

            {/* Game Stats */}
            <div className="bg-slate-900 rounded-lg border border-slate-800 p-3">
              <div className="grid grid-cols-3 gap-2 text-center">
                <div>
                  <div className="text-xs text-slate-400">Total Pool</div>
                  <div className="text-sm font-bold text-yellow-500">$2,450</div>
                </div>
                <div>
                  <div className="text-xs text-slate-400">Player A Odds</div>
                  <div className="text-sm font-bold text-blue-500">1.45x</div>
                </div>
                <div>
                  <div className="text-xs text-slate-400">Player B Odds</div>
                  <div className="text-sm font-bold text-red-500">2.75x</div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Bets Tab */}
        {activeTab === 'bets' && (
          <div className="p-2">
            <div className="bg-slate-900 rounded-lg border border-slate-800 p-3 mb-2">
              <h3 className="text-sm font-medium text-white mb-3">Place Your Bet</h3>

              <RadioGroup value={selectedBet} onValueChange={setSelectedBet}>
                <div className="grid grid-cols-2 gap-2 mb-3">
                  <div>
                    <Label htmlFor="player-a" className="cursor-pointer">
                      <div className={`p-3 rounded-sm border ${selectedBet === 'player-a' ? 'border-blue-500 bg-blue-500/10' : 'border-slate-700'}`}>
                        <RadioGroupItem value="player-a" id="player-a" className="sr-only" />
                        <div className="text-center">
                          <div className="text-sm font-medium text-white">Player A</div>
                          <div className="text-xl font-bold text-blue-500">1.45x</div>
                          <div className="text-xs text-slate-400">65% win chance</div>
                        </div>
                      </div>
                    </Label>
                  </div>
                  <div>
                    <Label htmlFor="player-b" className="cursor-pointer">
                      <div className={`p-3 rounded-sm border ${selectedBet === 'player-b' ? 'border-red-500 bg-red-500/10' : 'border-slate-700'}`}>
                        <RadioGroupItem value="player-b" id="player-b" className="sr-only" />
                        <div className="text-center">
                          <div className="text-sm font-medium text-white">Player B</div>
                          <div className="text-xl font-bold text-red-500">2.75x</div>
                          <div className="text-xs text-slate-400">35% win chance</div>
                        </div>
                      </div>
                    </Label>
                  </div>
                </div>
              </RadioGroup>

              <div className="mb-3">
                <Label className="text-sm">Bet Amount</Label>
                <Input
                  type="number"
                  value={betAmount}
                  onChange={(e) => setBetAmount(e.target.value)}
                  className="h-10"
                  placeholder="Enter amount"
                />
                <div className="flex justify-between mt-1">
                  <span className="text-xs text-slate-400">Min: $5</span>
                  <span className="text-xs text-slate-400">Max: $500</span>
                </div>
              </div>

              <div className="bg-slate-800 rounded-sm p-3 mb-3">
                <div className="flex justify-between text-sm">
                  <span className="text-slate-400">Potential Win:</span>
                  <span className="text-green-500 font-bold">
                    ${(parseFloat(betAmount || '0') * (selectedBet === 'player-a' ? 1.45 : 2.75)).toFixed(2)}
                  </span>
                </div>
              </div>

              <Button
                className="w-full h-10 bg-gradient-to-r from-purple-500 to-pink-500"
                disabled={!betAmount || parseFloat(betAmount) < 5}
              >
                Place Bet
              </Button>
            </div>

            {/* Recent Bets */}
            <div className="bg-slate-900 rounded-lg border border-slate-800 p-3">
              <h3 className="text-sm font-medium text-white mb-2">Recent Bets</h3>
              <div className="space-y-1 max-h-64 overflow-auto">
                {Array.from({ length: 20 }).map((_, idx) => (
                  <div key={idx} className="flex items-center justify-between p-2 bg-slate-800 rounded-sm">
                    <div className="flex items-center">
                      <div className="h-6 w-6 rounded-full bg-slate-700 mr-2"></div>
                      <span className="text-xs text-white">User{idx + 1}</span>
                    </div>
                    <div className="text-xs">
                      <span className={idx % 2 === 0 ? 'text-blue-500' : 'text-red-500'}>
                        Player {idx % 2 === 0 ? 'A' : 'B'}
                      </span>
                      <span className="text-slate-400 ml-2">${(10 + idx * 5)}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Chat Tab */}
        {activeTab === 'chat' && (
          <div className="h-[calc(100vh-12rem)] flex flex-col p-2">
            <div className="flex-1 bg-slate-900 rounded-lg border border-slate-800 mb-2 overflow-hidden">
              <div className="h-full overflow-auto p-3">
                {Array.from({ length: 30 }).map((_, idx) => (
                  <div key={idx} className="mb-2">
                    <div className="flex items-start">
                      <div className="h-6 w-6 rounded-full bg-slate-700 mr-2 flex-shrink-0"></div>
                      <div className="flex-1">
                        <span className="text-xs font-medium text-white">User{idx + 1}: </span>
                        <span className="text-xs text-slate-300">
                          {['Go Player A!', 'That was close!', 'Easy win', 'Great game!', 'Player B is on fire!'][idx % 5]}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            <div className="flex gap-2">
              <Input
                value={chatMessage}
                onChange={(e) => setChatMessage(e.target.value)}
                placeholder="Type a message..."
                className="flex-1 h-10"
              />
              <Button size="sm" className="h-10 px-3">
                <Send className="h-4 w-4" />
              </Button>
            </div>
          </div>
        )}

        {/* Leaders Tab */}
        {activeTab === 'leaders' && (
          <div className="p-2">
            <div className="bg-slate-900 rounded-lg border border-slate-800 p-3">
              <h3 className="text-sm font-medium text-white mb-3">Top Predictors</h3>
              <div className="space-y-2">
                {Array.from({ length: 10 }).map((_, idx) => (
                  <div key={idx} className="flex items-center justify-between p-2 bg-slate-800 rounded-sm">
                    <div className="flex items-center">
                      <Badge className={`h-6 w-6 rounded-full mr-2 ${
                        idx === 0 ? 'bg-yellow-500' :
                        idx === 1 ? 'bg-slate-400' :
                        idx === 2 ? 'bg-orange-600' :
                        'bg-slate-600'
                      }`}>
                        {idx + 1}
                      </Badge>
                      <span className="text-sm text-white">TopBettor{idx + 1}</span>
                    </div>
                    <span className="text-sm text-green-500 font-bold">
                      +${(1250 - idx * 125)}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Floating Action Button */}
      {activeTab === 'game' && (
        <Button
          className="fixed bottom-4 right-4 h-14 w-14 rounded-full bg-gradient-to-r from-purple-500 to-pink-500"
          onClick={() => setActiveTab('bets')}
        >
          <DollarSign className="h-6 w-6" />
        </Button>
      )}
    </div>
  );
};

export default MobileLiveSpectatorSection;