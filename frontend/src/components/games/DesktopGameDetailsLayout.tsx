import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import DesktopGameInstanceCard from './DesktopGameInstanceCard';
import {
  ArrowLeft,
  Users,
  Eye,
  Star,
  Trophy,
  Plus,
  Crown,
  Grid3x3,
  Hand,
  Film,
  Brain,
  Layers,
  Zap
} from 'lucide-react';

interface DesktopGameDetailsLayoutProps {
  gameId: string;
  gameName: string;
  gameType: string;
  onBack: () => void;
  onJoinGame: (instanceId: string) => void;
  gameInstances: any[];
  renderGameThumbnail: (instance: any) => React.ReactNode;
}

const DesktopGameDetailsLayout: React.FC<DesktopGameDetailsLayoutProps> = ({
  gameId,
  gameName,
  gameType,
  onBack,
  onJoinGame,
  gameInstances,
  renderGameThumbnail
}) => {
  const [filter, setFilter] = useState<'all' | 'waiting' | 'live'>('all');
  const [isFavorited, setIsFavorited] = useState(false);

  const filteredInstances = gameInstances.filter(instance => {
    if (filter === 'all') return true;
    return instance.status === filter;
  });

  const getGameIcon = () => {
    switch(gameType) {
      case 'chess':
      case 'chess-blitz':
        return <Crown className="h-5 w-5" />;
      case 'checkers':
        return <Grid3x3 className="h-5 w-5" />;
      case 'rock-paper-scissors':
        return <Hand className="h-5 w-5" />;
      case 'highlight-hero':
        return <Film className="h-5 w-5" />;
      case 'blur-detective':
        return <Eye className="h-5 w-5" />;
      case 'trivia':
      case 'speed-trivia':
        return <Brain className="h-5 w-5" />;
      case 'strategy':
        return <Layers className="h-5 w-5" />;
      default:
        return <Zap className="h-5 w-5" />;
    }
  };

  return (
    <div className="min-h-screen bg-slate-950 text-white pt-16">
      {/* Header */}
      <div className="h-20 bg-slate-900 border-b border-slate-800 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Button
              variant="ghost"
              size="sm"
              onClick={onBack}
              className="h-8 w-8 p-0 mr-3"
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <div className="flex items-center">
              <div className="h-10 w-10 rounded bg-gradient-to-br from-slate-700 to-slate-600 flex items-center justify-center mr-3">
                {getGameIcon()}
              </div>
              <div>
                <h1 className="text-xl font-semibold">{gameName}</h1>
                <div className="flex items-center space-x-3 mt-1">
                  <Badge className="bg-slate-800 text-xs px-2 py-1">
                    <Users className="h-3 w-3 mr-1" />
                    {filteredInstances.length} active
                  </Badge>
                  <Badge className="bg-slate-800 text-xs px-2 py-1">
                    <Eye className="h-3 w-3 mr-1" />
                    {filteredInstances.reduce((sum, inst) => sum + inst.viewers, 0)} watching
                  </Badge>
                </div>
              </div>
            </div>
          </div>
          
          <div className="flex items-center space-x-3">
            {/* Filter Tabs */}
            <div className="flex space-x-1">
              <Button
                variant={filter === 'all' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setFilter('all')}
                className={`h-8 text-xs px-3 ${filter === 'all' ? 'bg-purple-600' : 'text-slate-400 hover:text-white'}`}
              >
                All ({gameInstances.length})
              </Button>
              <Button
                variant={filter === 'waiting' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setFilter('waiting')}
                className={`h-8 text-xs px-3 ${filter === 'waiting' ? 'bg-purple-600' : 'text-slate-400 hover:text-white'}`}
              >
                Waiting ({gameInstances.filter(g => g.status === 'waiting').length})
              </Button>
              <Button
                variant={filter === 'live' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setFilter('live')}
                className={`h-8 text-xs px-3 ${filter === 'live' ? 'bg-purple-600' : 'text-slate-400 hover:text-white'}`}
              >
                Live ({gameInstances.filter(g => g.status === 'live').length})
              </Button>
            </div>
            
            <Button
              variant={isFavorited ? "default" : "outline"}
              size="sm"
              onClick={() => setIsFavorited(!isFavorited)}
              className={`h-8 text-xs ${isFavorited ? "bg-yellow-600 hover:bg-yellow-700" : "border-slate-700"}`}
            >
              <Star className={`h-3 w-3 mr-1 ${isFavorited ? "fill-current" : ""}`} />
              {isFavorited ? "Favorited" : "Favorite"}
            </Button>
          </div>
        </div>
      </div>

      {/* Main Layout */}
      <div className="flex h-[calc(100vh-64px-80px)]">
        {/* Left Sidebar */}
        <div className="w-64 bg-slate-950 border-r border-slate-800 p-4 space-y-4">
          {/* Game Stats */}
          <div className="bg-slate-900 border border-slate-800 rounded-lg p-4">
            <h3 className="text-sm font-semibold text-white mb-3 flex items-center">
              <Trophy className="h-4 w-4 mr-2 text-yellow-500" />
              Game Stats
            </h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-xs text-slate-400">Active Games</span>
                <span className="text-sm font-bold text-white">{filteredInstances.length}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-xs text-slate-400">Avg. Stake</span>
                <span className="text-sm font-bold text-white">$62.50</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-xs text-slate-400">Your Wins</span>
                <span className="text-sm font-bold text-green-500">23</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-xs text-slate-400">Win Rate</span>
                <span className="text-sm font-bold text-white">68%</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-xs text-slate-400">Total Earned</span>
                <span className="text-sm font-bold text-yellow-500">$1,245</span>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="bg-slate-900 border border-slate-800 rounded-lg p-4">
            <h3 className="text-sm font-semibold text-white mb-3">Quick Actions</h3>
            <div className="space-y-2">
              <Button className="w-full bg-gradient-to-r from-green-500 to-cyan-500 hover:opacity-90 text-sm">
                <Plus className="h-4 w-4 mr-2" />
                Create Game
              </Button>
              <Button variant="outline" className="w-full border-slate-700 text-slate-300 hover:bg-slate-800">
                <Star className="h-4 w-4 mr-2" />
                Favorites
              </Button>
            </div>
          </div>

          {/* Recent Activity */}
          <div className="bg-slate-900 border border-slate-800 rounded-lg p-4 flex-1">
            <h3 className="text-sm font-semibold text-white mb-3">Recent Activity</h3>
            <div className="space-y-2 text-xs">
              <div className="flex items-center justify-between py-1">
                <span className="text-slate-400">Won vs Player X</span>
                <span className="text-green-500">+$50</span>
              </div>
              <div className="flex items-center justify-between py-1">
                <span className="text-slate-400">Lost vs Player Y</span>
                <span className="text-red-500">-$25</span>
              </div>
              <div className="flex items-center justify-between py-1">
                <span className="text-slate-400">Won vs Player Z</span>
                <span className="text-green-500">+$75</span>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content - 4 Column Grid */}
        <div className="flex-1 bg-slate-900 overflow-hidden">
          <div className="h-full overflow-auto p-6">
            <div className="grid grid-cols-4 gap-6">
              {filteredInstances.map((instance) => (
                <DesktopGameInstanceCard
                  key={instance.id}
                  instance={instance}
                  gameType={gameType}
                  onJoinGame={onJoinGame}
                  renderThumbnail={renderGameThumbnail}
                />
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DesktopGameDetailsLayout;
