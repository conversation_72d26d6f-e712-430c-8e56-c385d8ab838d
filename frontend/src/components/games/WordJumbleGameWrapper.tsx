import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import WordJumbleGame from './WordJumbleGame';
import WordJumbleGameSetup from './WordJumbleGameSetup';
import { useGameStateStore } from '@/stores/gameStateStore';
import type { GameData } from './gameTypes';
import type { WordJumbleGameConfig } from './WordJumbleGameSetup';

interface WordJumbleGameWrapperProps {
  game?: GameData;
  onBack?: () => void;
}

const WordJumbleGameWrapper: React.FC<WordJumbleGameWrapperProps> = ({ game, onBack }) => {
  const params = useParams();
  const navigate = useNavigate();
  const sessionId = params.sessionId;
  
  const [showSetup, setShowSetup] = useState(!game && !sessionId);
  const [gameId, setGameId] = useState<string | null>(game?.id || sessionId || null);
  const [isCreatingGame, setIsCreatingGame] = useState(false);
  
  const { createGame } = useGameStateStore();
  
  const handleBack = () => {
    if (onBack) {
      onBack();
    } else {
      navigate('/games');
    }
  };

  const handleStartGame = async (config: WordJumbleGameConfig) => {
    setIsCreatingGame(true);
    
    try {
      // Create a new game instance
      const newGame = await createGame('word_jumble', config.betAmount);
      
      if (newGame) {
        // Navigate to the game session URL
        navigate(`/games/word-jumble/session/${newGame.id}`);
      }
    } catch (error) {
      console.error('Failed to create game:', error);
    } finally {
      setIsCreatingGame(false);
    }
  };

  const handleGameComplete = () => {
    handleBack();
  };

  if (showSetup) {
    return (
      <>
        <WordJumbleGameSetup
          isOpen={true}
          onClose={handleBack}
          onStartGame={handleStartGame}
        />
        {isCreatingGame && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <div className="bg-slate-900 rounded-lg p-6">
              <div className="text-white">Creating game...</div>
            </div>
          </div>
        )}
      </>
    );
  }
  
  if (!gameId) {
    return <div className="text-white">Error: No game ID available</div>;
  }
  
  return (
    <WordJumbleGame
      gameId={gameId}
      onGameEnd={handleGameComplete}
      onBack={handleBack}
    />
  );
};

export default WordJumbleGameWrapper;