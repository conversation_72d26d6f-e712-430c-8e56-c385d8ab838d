import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Input } from '@/components/ui/input';
import {
  Trophy,
  Clock,
  MessageSquare,
  Users,
  Settings,
  Star,
  Target,
  Zap,
  RotateCcw,
  Check,
  X,
  AlertCircle,
  DollarSign,
  ChevronDown,
  Shuffle,
  Type,
  MousePointer,
  Award,
  Timer,
  Keyboard,
  Hand,
  Crown,
  Flame,
  Send
} from 'lucide-react';

// Game status
const GAME_STATUS = {
  SETUP: 'setup',
  WAITING: 'waiting',
  PLAYING: 'playing',
  ROUND_END: 'round_end',
  GAME_END: 'game_end'
};

// Input modes
const INPUT_MODE = {
  DRAG: 'drag',
  TYPE: 'type'
};

// Sample word sets for different difficulties
const WORD_SETS = {
  easy: [
    { letters: 'TCA', solutions: ['CAT', 'ACT', 'TAC'], target: 'CAT' },
    { letters: 'GOD', solutions: ['DOG', 'GOD'], target: 'DOG' },
    { letters: 'UNS', solutions: ['SUN', 'NUS'], target: 'SUN' },
    { letters: 'RAT', solutions: ['TAR', 'RAT', 'ART'], target: 'TAR' },
    { letters: 'TEN', solutions: ['NET', 'TEN'], target: 'NET' }
  ],
  medium: [
    { letters: 'OWLF', solutions: ['FLOW', 'FOWL', 'WOLF'], target: 'FLOW' },
    { letters: 'KROW', solutions: ['WORK', 'KROW'], target: 'WORK' },
    { letters: 'MITE', solutions: ['TIME', 'ITEM', 'EMIT', 'MITE'], target: 'TIME' },
    { letters: 'POTS', solutions: ['STOP', 'POST', 'TOPS', 'POTS', 'SPOT', 'OPTS'], target: 'STOP' },
    { letters: 'ATRE', solutions: ['TEAR', 'RATE', 'TARE', 'LATE'], target: 'TEAR' }
  ],
  hard: [
    { letters: 'LESTT', solutions: ['SETTLE', 'LETTERS', 'LEST', 'LETS', 'TEST'], target: 'LETTERS' },
    { letters: 'NGAECH', solutions: ['CHANGE', 'CHANCE', 'HANG', 'CAGE', 'EACH'], target: 'CHANGE' },
    { letters: 'POTMCER', solutions: ['COMPUTER', 'COMPETE', 'COME', 'PORT', 'MORE'], target: 'COMPUTER' },
    { letters: 'RINGDEA', solutions: ['READING', 'GARDEN', 'GRADE', 'DEAR', 'GEAR'], target: 'READING' },
    { letters: 'MALISAN', solutions: ['ANIMALS', 'ANIMAL', 'SNAIL', 'NAIL', 'MAIL'], target: 'ANIMALS' }
  ]
};

interface WordJumbleGameProps {
  gameId?: string;
  onGameEnd?: () => void;
  onBack?: () => void;
}

const WordJumbleGame: React.FC<WordJumbleGameProps> = ({ gameId, onGameEnd, onBack }) => {
  // Game configuration
  const [config, setConfig] = useState({
    gameMode: '1v1', // '1v1' or '1vN'
    maxPlayers: 4,
    difficulty: 'medium',
    rounds: 5,
    timePerRound: 60, // seconds
    wagerAmount: 50,
    inputMode: INPUT_MODE.DRAG,
    allowPartialWords: true,
    minWordLength: 3
  });

  // Game state
  const [gameStatus, setGameStatus] = useState(GAME_STATUS.SETUP);
  const [currentRound, setCurrentRound] = useState(1);
  const [timeLeft, setTimeLeft] = useState(0);
  const [currentLetters, setCurrentLetters] = useState<string[]>([]);
  const [availableLetters, setAvailableLetters] = useState<{letter: string, id: number, used: boolean}[]>([]);
  const [answerBoxes, setAnswerBoxes] = useState<string[]>([]);
  const [currentWord, setCurrentWord] = useState('');
  const [typedInput, setTypedInput] = useState('');
  const [showSettings, setShowSettings] = useState(false);
  const [currentWordSet, setCurrentWordSet] = useState<any>(null);
  const [draggedLetter, setDraggedLetter] = useState<{letter: string, index: number} | null>(null);
  const [dragOverIndex, setDragOverIndex] = useState<number | null>(null);
  const [selectedLetterIndex, setSelectedLetterIndex] = useState<number | null>(null);

  // Players and scores
  const [players, setPlayers] = useState([
    { id: 1, name: 'You', score: 0, currentWord: '', isReady: false, lastWordPoints: 0 },
    { id: 2, name: 'Player 2', score: 0, currentWord: '', isReady: false, lastWordPoints: 0 },
    { id: 3, name: 'Player 3', score: 0, currentWord: '', isReady: false, lastWordPoints: 0 },
    { id: 4, name: 'Player 4', score: 0, currentWord: '', isReady: false, lastWordPoints: 0 }
  ]);

  // Round tracking
  const [roundHistory, setRoundHistory] = useState<any[]>([]);
  const [gameResult, setGameResult] = useState<any>(null);
  const [totalGameTime, setTotalGameTime] = useState(0);
  const [roundWinner, setRoundWinner] = useState<any>(null);

  // Refs
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const gameTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Chat state
  const [messages, setMessages] = useState([
    { text: "Welcome to Word Jumble! Unscramble letters to form words and compete for the highest score.", type: "system" }
  ]);
  const [messageInput, setMessageInput] = useState('');
  const [showChat, setShowChat] = useState(true);

  // Clean up timers
  useEffect(() => {
    return () => {
      if (timerRef.current) clearInterval(timerRef.current);
      if (gameTimerRef.current) clearInterval(gameTimerRef.current);
    };
  }, []);

  // Update typed input when current word changes
  useEffect(() => {
    if (config.inputMode === INPUT_MODE.TYPE) {
      setTypedInput(currentWord);
    }
  }, [currentWord, config.inputMode]);

  // Add message to chat
  const addMessage = (text: string, type = "player") => {
    setMessages(prev => [...prev, { text, type }]);
  };

  // Send chat message
  const sendMessage = () => {
    if (messageInput.trim()) {
      addMessage(`You: ${messageInput}`);
      setMessageInput('');
    }
  };

  // Shuffle array
  const shuffleArray = (array: any[]) => {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  };

  // Get word sets for current difficulty
  const getWordSets = (difficulty: string) => {
    return WORD_SETS[difficulty as keyof typeof WORD_SETS] || WORD_SETS.medium;
  };

  // Start the game
  const startGame = () => {
    // Reset game state
    setCurrentRound(1);
    setRoundHistory([]);
    setGameResult(null);
    setTotalGameTime(0);

    // Reset player scores
    setPlayers(prev => prev.slice(0, config.gameMode === '1v1' ? 2 : config.maxPlayers).map(player => ({
      ...player,
      score: 0,
      currentWord: '',
      isReady: false,
      lastWordPoints: 0
    })));

    // Start game timer
    gameTimerRef.current = setInterval(() => {
      setTotalGameTime(prev => prev + 1);
    }, 1000);

    // Start first round
    startRound(1);

    setGameStatus(GAME_STATUS.PLAYING);
    addMessage(`Game started! ${config.gameMode === '1v1' ? '1v1' : `1v${config.maxPlayers}`} mode, ${config.rounds} rounds.`, "system");
  };

  // Start a new round
  const startRound = (roundNumber: number) => {
    const wordSets = getWordSets(config.difficulty);
    const wordSet = wordSets[(roundNumber - 1) % wordSets.length];

    setCurrentWordSet(wordSet);

    // Shuffle the letters
    const shuffledLetters = shuffleArray(wordSet.letters.split(''));
    setCurrentLetters(shuffledLetters);
    setAvailableLetters(shuffledLetters.map((letter, index) => ({ letter, id: index, used: false })));

    // Initialize answer boxes
    const maxLength = Math.max(...wordSet.solutions.map((word: string) => word.length));
    setAnswerBoxes(new Array(maxLength).fill(''));
    setCurrentWord('');
    setTypedInput('');

    // Reset player states
    setPlayers(prev => prev.map(player => ({
      ...player,
      currentWord: '',
      isReady: false,
      lastWordPoints: 0
    })));

    // Start round timer
    setTimeLeft(config.timePerRound);
    timerRef.current = setInterval(() => {
      setTimeLeft(prev => {
        if (prev <= 1) {
          clearInterval(timerRef.current!);
          endRound();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    addMessage(`Round ${roundNumber} started! Letters: ${wordSet.letters}`, "system");

    // Simulate other players thinking/typing
    simulateOtherPlayers();
  };

  // Simulate other players making words
  const simulateOtherPlayers = () => {
    const activePlayerCount = config.gameMode === '1v1' ? 2 : config.maxPlayers;

    // Simulate other players with random delays
    for (let i = 1; i < activePlayerCount; i++) {
      const delay = Math.random() * (config.timePerRound * 0.8) * 1000;

      setTimeout(() => {
        if (gameStatus === GAME_STATUS.PLAYING && currentWordSet) {
          const possibleWords = currentWordSet.solutions.filter((word: string) => word.length >= config.minWordLength);
          const randomWord = possibleWords[Math.floor(Math.random() * possibleWords.length)];

          setPlayers(prev => prev.map(player =>
            player.id === i + 1 ? {
              ...player,
              currentWord: randomWord,
              isReady: true,
              lastWordPoints: calculateWordScore(randomWord)
            } : player
          ));

          addMessage(`${players[i]?.name || `Player ${i + 1}`} formed: "${randomWord}"`, "system");

          // Check if this ends the round
          const updatedPlayers = players.map(player =>
            player.id === i + 1 ? { ...player, isReady: true } : player
          );

          if (updatedPlayers.slice(0, activePlayerCount).every(player => player.isReady)) {
            setTimeout(() => endRound(), 1000);
          }
        }
      }, delay);
    }
  };

  // Handle drag start
  const handleDragStart = (e: React.DragEvent, letter: string, index: number) => {
    if (config.inputMode !== INPUT_MODE.DRAG) return;

    setDraggedLetter({ letter, index });
    e.dataTransfer.effectAllowed = 'move';
  };

  // Handle drag over
  const handleDragOver = (e: React.DragEvent, boxIndex: number) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
    setDragOverIndex(boxIndex);
  };

  // Handle drag leave
  const handleDragLeave = () => {
    setDragOverIndex(null);
  };

  // Handle drop
  const handleDrop = (e: React.DragEvent, boxIndex: number) => {
    e.preventDefault();

    if (!draggedLetter) return;

    // Remove letter from available letters
    setAvailableLetters(prev => prev.map(item =>
      item.id === draggedLetter.index ? { ...item, used: true } : item
    ));

    // Add letter to answer box
    const newAnswerBoxes = [...answerBoxes];
    newAnswerBoxes[boxIndex] = draggedLetter.letter;
    setAnswerBoxes(newAnswerBoxes);

    // Update current word
    const newWord = newAnswerBoxes.join('');
    setCurrentWord(newWord);

    setDraggedLetter(null);
    setDragOverIndex(null);
  };

  // Handle letter click (for mobile/touch)
  const handleLetterClick = (letter: string, index: number) => {
    if (config.inputMode !== INPUT_MODE.DRAG) return;

    // Find first empty box
    const emptyBoxIndex = answerBoxes.findIndex(box => box === '');
    if (emptyBoxIndex !== -1) {
      // Remove letter from available letters
      setAvailableLetters(prev => prev.map(item =>
        item.id === index ? { ...item, used: true } : item
      ));

      // Add letter to answer box
      const newAnswerBoxes = [...answerBoxes];
      newAnswerBoxes[emptyBoxIndex] = letter;
      setAnswerBoxes(newAnswerBoxes);

      // Update current word
      const newWord = newAnswerBoxes.join('');
      setCurrentWord(newWord);
    }
  };

  // Handle answer box click (to remove letter)
  const handleAnswerBoxClick = (boxIndex: number) => {
    if (answerBoxes[boxIndex] && config.inputMode === INPUT_MODE.DRAG) {
      const letter = answerBoxes[boxIndex];

      // Find the original letter index and mark as available
      const originalLetterIndex = currentLetters.findIndex((l, i) =>
        l === letter && availableLetters[i]?.used
      );

      if (originalLetterIndex !== -1) {
        setAvailableLetters(prev => prev.map(item =>
          item.id === originalLetterIndex ? { ...item, used: false } : item
        ));
      }

      // Remove letter from answer box
      const newAnswerBoxes = [...answerBoxes];
      newAnswerBoxes[boxIndex] = '';
      setAnswerBoxes(newAnswerBoxes);

      // Update current word
      const newWord = newAnswerBoxes.join('');
      setCurrentWord(newWord);
    }
  };

  // Handle typed input
  const handleTypedInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (config.inputMode !== INPUT_MODE.TYPE) return;

    const value = e.target.value.toUpperCase();
    const validLetters = currentLetters.join('');

    // Check if all letters in the input are available
    const letterCount: {[key: string]: number} = {};
    for (const letter of validLetters) {
      letterCount[letter] = (letterCount[letter] || 0) + 1;
    }

    let isValid = true;
    const inputLetterCount: {[key: string]: number} = {};
    for (const letter of value) {
      inputLetterCount[letter] = (inputLetterCount[letter] || 0) + 1;
      if (inputLetterCount[letter] > (letterCount[letter] || 0)) {
        isValid = false;
        break;
      }
    }

    if (isValid) {
      setTypedInput(value);
      setCurrentWord(value);

      // Update answer boxes to show typed letters
      const newAnswerBoxes = new Array(answerBoxes.length).fill('');
      for (let i = 0; i < value.length && i < newAnswerBoxes.length; i++) {
        newAnswerBoxes[i] = value[i];
      }
      setAnswerBoxes(newAnswerBoxes);
    }
  };

  // Calculate word score
  const calculateWordScore = (word: string) => {
    if (!currentWordSet || !word) return 0;

    const baseScore = word.length * 10;
    let multiplier = 1;

    // Bonus for valid words
    if (currentWordSet.solutions.includes(word)) {
      multiplier = 1.5;
    }

    // Bonus for target word
    if (word === currentWordSet.target) {
      multiplier = 2;
    }

    // Length bonus
    if (word.length >= 6) multiplier += 0.5;
    if (word.length >= 8) multiplier += 0.5;

    return Math.round(baseScore * multiplier);
  };

  // Submit current word
  const submitWord = () => {
    if (!currentWord || currentWord.length < config.minWordLength) {
      addMessage(`Word must be at least ${config.minWordLength} letters long!`, "system");
      return;
    }

    const score = calculateWordScore(currentWord);

    // Update player score and mark as ready
    setPlayers(prev => prev.map(player =>
      player.id === 1 ? {
        ...player,
        currentWord,
        isReady: true,
        lastWordPoints: score,
        score: player.score + score
      } : player
    ));

    addMessage(`You submitted: "${currentWord}" for ${score} points`, "system");

    // Check if all players are ready
    const activePlayerCount = config.gameMode === '1v1' ? 2 : config.maxPlayers;
    const readyPlayers = players.filter(p => p.id <= activePlayerCount && (p.id === 1 || p.isReady));

    if (readyPlayers.length >= activePlayerCount - 1) {
      setTimeout(() => endRound(), 2000);
    }
  };

  // Clear current word
  const clearWord = () => {
    setCurrentWord('');
    setTypedInput('');
    setAnswerBoxes(new Array(answerBoxes.length).fill(''));
    setAvailableLetters(prev => prev.map(item => ({ ...item, used: false })));
  };

  // Shuffle available letters
  const shuffleLetters = () => {
    const availableOnly = availableLetters.filter(item => !item.used);
    const shuffled = shuffleArray(availableOnly.map(item => item.letter));

    let shuffledIndex = 0;
    setAvailableLetters(prev => prev.map(item =>
      item.used ? item : { ...item, letter: shuffled[shuffledIndex++] }
    ));

    // Also update currentLetters for consistency
    const newCurrentLetters = [...currentLetters];
    shuffledIndex = 0;
    for (let i = 0; i < newCurrentLetters.length; i++) {
      if (!availableLetters[i]?.used) {
        newCurrentLetters[i] = shuffled[shuffledIndex++];
      }
    }
    setCurrentLetters(newCurrentLetters);
  };

  // End current round
  const endRound = () => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }

    setGameStatus(GAME_STATUS.ROUND_END);

    // Calculate round results
    const activePlayerCount = config.gameMode === '1v1' ? 2 : config.maxPlayers;
    const roundPlayers = players.slice(0, activePlayerCount);

    // Find round winner
    const winner = roundPlayers.reduce((prev, current) =>
      current.lastWordPoints > prev.lastWordPoints ? current : prev
    );

    setRoundWinner(winner);

    // Add to round history
    setRoundHistory(prev => [...prev, {
      round: currentRound,
      letters: currentLetters.join(''),
      players: roundPlayers.map(p => ({
        name: p.name,
        word: p.currentWord,
        points: p.lastWordPoints
      })),
      winner: winner.name,
      targetWord: currentWordSet?.target
    }]);

    addMessage(`Round ${currentRound} ended! Winner: ${winner.name} with "${winner.currentWord}" (${winner.lastWordPoints} points)`, "system");

    // Check if game is over
    if (currentRound >= config.rounds) {
      setTimeout(() => endGame(), 3000);
    } else {
      setTimeout(() => proceedToNextRound(), 3000);
    }
  };

  // Proceed to next round
  const proceedToNextRound = () => {
    const nextRound = currentRound + 1;
    setCurrentRound(nextRound);
    setRoundWinner(null);
    setGameStatus(GAME_STATUS.PLAYING);
    startRound(nextRound);
  };

  // End the game
  const endGame = () => {
    setGameStatus(GAME_STATUS.GAME_END);

    // Clear timers
    if (timerRef.current) clearInterval(timerRef.current);
    if (gameTimerRef.current) clearInterval(gameTimerRef.current);

    // Calculate final results
    const activePlayerCount = config.gameMode === '1v1' ? 2 : config.maxPlayers;
    const finalPlayers = players.slice(0, activePlayerCount);
    const gameWinner = finalPlayers.reduce((prev, current) =>
      current.score > prev.score ? current : prev
    );

    let result = {
      winner: gameWinner,
      players: finalPlayers,
      totalRounds: config.rounds,
      payout: 0
    };

    // Calculate payout (only for player 1)
    if (gameWinner.id === 1) {
      const winMargin = gameWinner.score - Math.max(...finalPlayers.filter(p => p.id !== 1).map(p => p.score));
      if (winMargin > 100) {
        result.payout = config.wagerAmount * 3;
      } else if (winMargin > 50) {
        result.payout = config.wagerAmount * 2;
      } else {
        result.payout = config.wagerAmount * 1.5;
      }
      addMessage(`Congratulations! You won! Payout: $${result.payout.toFixed(2)}`, "system");
    } else {
      addMessage(`Game over! ${gameWinner.name} wins with ${gameWinner.score} points.`, "system");
    }

    setGameResult(result);
    if (onGameEnd) onGameEnd();
  };

  // Reset the game
  const resetGame = () => {
    // Clear timers
    if (timerRef.current) clearInterval(timerRef.current);
    if (gameTimerRef.current) clearInterval(gameTimerRef.current);

    // Reset state
    setGameStatus(GAME_STATUS.SETUP);
    setCurrentRound(1);
    setRoundHistory([]);
    setGameResult(null);
    setCurrentWordSet(null);
    setCurrentWord('');
    setTypedInput('');
    setPlayers(prev => prev.map(player => ({ ...player, score: 0, currentWord: '', isReady: false, lastWordPoints: 0 })));

    addMessage("Game reset! Configure your settings and start a new word challenge.", "system");
  };

  // Format time display
  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds < 10 ? '0' : ''}${remainingSeconds}`;
  };

  // Get difficulty color
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'text-green-500';
      case 'medium': return 'text-yellow-500';
      case 'hard': return 'text-red-500';
      default: return 'text-slate-400';
    }
  };

  return (
    <div className="w-full min-h-screen bg-slate-950 flex flex-col pt-16">
      <style>{`
        /* Custom scrollbar styles */
        .custom-scrollbar::-webkit-scrollbar {
          width: 6px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
          background: rgba(51, 65, 85, 0.3);
          border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
          background: rgba(100, 116, 139, 0.5);
          border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
          background: rgba(100, 116, 139, 0.7);
        }
      `}</style>
      
      {/* Main content area */}
      <div className="flex-1 p-2 pb-16 overflow-hidden">
        <div className="grid grid-cols-12 gap-2 h-full">
          {/* Left Panel - Game Info */}
          <div className="col-span-3 flex flex-col gap-2 h-full overflow-hidden">
            <div className="flex-1 overflow-y-auto custom-scrollbar pr-1 space-y-2">
              {/* Game Status */}
              <div className="bg-slate-900 border border-slate-800 rounded-sm p-3">
                <div className="flex justify-between items-center mb-3">
                  <h3 className="text-sm font-semibold text-white">Word Jumble</h3>
                  {gameStatus === GAME_STATUS.PLAYING && (
                    <Badge className="bg-green-500 text-white text-xs">LIVE</Badge>
                  )}
                </div>
                
                <div className="space-y-2">
                  <div className="bg-slate-800 p-2 rounded">
                    <div className="text-xs text-slate-400">Game Status</div>
                    <div className="text-sm font-bold text-white capitalize">{gameStatus}</div>
                  </div>
                  
                  {gameStatus === GAME_STATUS.PLAYING && (
                    <>
                      <div className="grid grid-cols-2 gap-2">
                        <div className="bg-slate-800 p-2 rounded">
                          <div className="text-xs text-slate-400">Round</div>
                          <div className="text-sm font-bold text-white">{currentRound}/{config.rounds}</div>
                        </div>
                        <div className="bg-slate-800 p-2 rounded">
                          <div className="text-xs text-slate-400">Time Left</div>
                          <div className="text-sm font-bold text-white">{formatTime(timeLeft)}</div>
                        </div>
                      </div>
                      
                      {currentWordSet && (
                        <div className="bg-purple-900/30 border border-purple-700 p-2 rounded">
                          <div className="text-xs text-purple-400">Target Word</div>
                          <div className="text-sm font-bold text-purple-300">{currentWordSet.target}</div>
                          <div className="text-xs text-purple-400 mt-1">2x points bonus!</div>
                        </div>
                      )}
                    </>
                  )}
                </div>
              </div>

              {/* Game Configuration */}
              {gameStatus === GAME_STATUS.SETUP && (
                <div className="bg-slate-900 border border-slate-800 rounded-sm p-3">
                  <h3 className="text-sm font-semibold text-white mb-2">Game Settings</h3>
                  
                  <div className="space-y-3">
                    <div>
                      <label className="text-xs text-slate-400 block mb-1">Difficulty</label>
                      <div className="flex space-x-1">
                        {['easy', 'medium', 'hard'].map(diff => (
                          <Button
                            key={diff}
                            variant={config.difficulty === diff ? "default" : "outline"}
                            size="sm"
                            className="flex-1 h-7 text-xs capitalize"
                            onClick={() => setConfig({ ...config, difficulty: diff })}
                          >
                            {diff}
                          </Button>
                        ))}
                      </div>
                    </div>

                    <div>
                      <label className="text-xs text-slate-400 block mb-1">Rounds</label>
                      <div className="flex space-x-1">
                        {[3, 5, 7, 10].map(rounds => (
                          <Button
                            key={rounds}
                            variant={config.rounds === rounds ? "default" : "outline"}
                            size="sm"
                            className="flex-1 h-7 text-xs"
                            onClick={() => setConfig({ ...config, rounds })}
                          >
                            {rounds}
                          </Button>
                        ))}
                      </div>
                    </div>

                    <div>
                      <label className="text-xs text-slate-400 block mb-1">Time per Round</label>
                      <div className="flex space-x-1">
                        {[30, 60, 90, 120].map(time => (
                          <Button
                            key={time}
                            variant={config.timePerRound === time ? "default" : "outline"}
                            size="sm"
                            className="flex-1 h-7 text-xs"
                            onClick={() => setConfig({ ...config, timePerRound: time })}
                          >
                            {time}s
                          </Button>
                        ))}
                      </div>
                    </div>

                    <div>
                      <label className="text-xs text-slate-400 block mb-1">Wager Amount</label>
                      <div className="flex space-x-1">
                        {[25, 50, 100, 200].map(amount => (
                          <Button
                            key={amount}
                            variant={config.wagerAmount === amount ? "default" : "outline"}
                            size="sm"
                            className="flex-1 h-7 text-xs"
                            onClick={() => setConfig({ ...config, wagerAmount: amount })}
                          >
                            ${amount}
                          </Button>
                        ))}
                      </div>
                    </div>

                    <div>
                      <label className="text-xs text-slate-400 block mb-1">Input Mode</label>
                      <div className="flex space-x-1">
                        <Button
                          variant={config.inputMode === INPUT_MODE.DRAG ? "default" : "outline"}
                          size="sm"
                          className="flex-1 h-7 text-xs"
                          onClick={() => setConfig({ ...config, inputMode: INPUT_MODE.DRAG })}
                        >
                          <Hand className="h-3 w-3 mr-1" />
                          Drag
                        </Button>
                        <Button
                          variant={config.inputMode === INPUT_MODE.TYPE ? "default" : "outline"}
                          size="sm"
                          className="flex-1 h-7 text-xs"
                          onClick={() => setConfig({ ...config, inputMode: INPUT_MODE.TYPE })}
                        >
                          <Keyboard className="h-3 w-3 mr-1" />
                          Type
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Player Scores */}
              {gameStatus !== GAME_STATUS.SETUP && (
                <div className="bg-slate-900 border border-slate-800 rounded-sm p-3">
                  <h3 className="text-sm font-semibold text-white mb-2">Player Scores</h3>
                  
                  <div className="space-y-2">
                    {players.slice(0, config.gameMode === '1v1' ? 2 : config.maxPlayers).map((player) => (
                      <div
                        key={player.id}
                        className={`flex items-center justify-between p-2 rounded-sm ${
                          player.id === 1 ? 'bg-blue-900/30 border border-blue-700' : 'bg-slate-800'
                        }`}
                      >
                        <div className="flex items-center">
                          <div className={`h-6 w-6 rounded-full mr-2 flex items-center justify-center text-xs font-bold ${
                            player.id === 1 ? 'bg-blue-500' : 'bg-slate-600'
                          }`}>
                            {player.id}
                          </div>
                          <span className="text-sm text-white">{player.name}</span>
                          {player.isReady && gameStatus === GAME_STATUS.PLAYING && (
                            <Badge className="ml-2 h-4 bg-green-500">Ready</Badge>
                          )}
                        </div>

                        <div className="text-right">
                          <div className="text-sm font-bold text-white">{player.score} pts</div>
                          {player.currentWord && (
                            <div className="text-xs text-slate-400">"{player.currentWord}" (+{player.lastWordPoints})</div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Round History */}
              {roundHistory.length > 0 && (
                <div className="bg-slate-900 border border-slate-800 rounded-sm p-3">
                  <h3 className="text-sm font-semibold text-white mb-2">Round History</h3>
                  
                  <div className="space-y-2 max-h-40 overflow-y-auto custom-scrollbar pr-1">
                    {roundHistory.map((round, index) => (
                      <div key={index} className="bg-slate-800 p-2 rounded text-xs">
                        <div className="flex justify-between mb-1">
                          <span className="text-white font-medium">Round {round.round}</span>
                          <span className="text-purple-400">Target: {round.targetWord}</span>
                        </div>
                        <div className="text-slate-400">
                          Winner: {round.winner} ({round.players.find((p: any) => p.name === round.winner)?.points} pts)
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Center - Game Board */}
          <div className="col-span-6 flex flex-col items-center justify-center p-4 overflow-y-auto custom-scrollbar">
            {gameStatus === GAME_STATUS.SETUP && (
              <div className="w-full max-w-md text-center">
                <div className="bg-slate-900 border border-slate-800 rounded-lg p-8">
                  <Trophy className="h-16 w-16 text-yellow-500 mx-auto mb-4" />
                  <h2 className="text-2xl font-bold text-white mb-2">Word Jumble Challenge</h2>
                  <p className="text-slate-400 mb-6">
                    Unscramble letters to form words and compete against other players!
                  </p>
                  
                  <div className="bg-slate-800 p-4 rounded mb-6">
                    <div className="grid grid-cols-2 gap-3 text-sm">
                      <div>
                        <span className="text-slate-400">Difficulty:</span>
                        <span className={`font-bold ml-2 ${getDifficultyColor(config.difficulty)}`}>
                          {config.difficulty.toUpperCase()}
                        </span>
                      </div>
                      <div>
                        <span className="text-slate-400">Rounds:</span>
                        <span className="font-bold text-white ml-2">{config.rounds}</span>
                      </div>
                      <div>
                        <span className="text-slate-400">Time/Round:</span>
                        <span className="font-bold text-white ml-2">{config.timePerRound}s</span>
                      </div>
                      <div>
                        <span className="text-slate-400">Wager:</span>
                        <span className="font-bold text-green-500 ml-2">${config.wagerAmount}</span>
                      </div>
                    </div>
                  </div>
                  
                  <Button 
                    className="bg-gradient-to-r from-purple-500 to-pink-500 text-white"
                    onClick={startGame}
                  >
                    <Zap className="h-4 w-4 mr-2" />
                    Start Game
                  </Button>
                </div>
              </div>
            )}

            {gameStatus === GAME_STATUS.PLAYING && (
              <div className="w-full max-w-xl">
                {/* Progress bar */}
                <div className="mb-4">
                  <Progress value={(timeLeft / config.timePerRound) * 100} className="h-2" />
                </div>

                {/* Scrambled Letters */}
                <div className="mb-4">
                  <h3 className="text-sm font-medium text-white mb-2 flex items-center">
                    <Shuffle className="h-4 w-4 mr-2" />
                    Scrambled Letters
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 w-6 p-0 ml-2"
                      onClick={shuffleLetters}
                      disabled={gameStatus !== GAME_STATUS.PLAYING}
                    >
                      <Shuffle className="h-3 w-3" />
                    </Button>
                  </h3>

                  <div className="flex flex-wrap gap-2 justify-center">
                    {availableLetters.map((item, index) => (
                      <div
                        key={index}
                        className={`
                          w-12 h-12 bg-slate-800 border-2 border-slate-600 rounded-sm
                          flex items-center justify-center text-xl font-bold text-white
                          cursor-pointer transition-all hover:border-slate-500
                          ${item.used ? 'opacity-30 cursor-not-allowed' : 'hover:bg-slate-700'}
                          ${draggedLetter?.index === index ? 'opacity-50' : ''}
                        `}
                        draggable={!item.used && config.inputMode === INPUT_MODE.DRAG}
                        onDragStart={(e) => handleDragStart(e, item.letter, index)}
                        onClick={() => !item.used && handleLetterClick(item.letter, index)}
                      >
                        {item.letter}
                      </div>
                    ))}
                  </div>
                </div>

                {/* Answer Boxes */}
                <div className="mb-4">
                  <h3 className="text-sm font-medium text-white mb-2 flex items-center">
                    <Target className="h-4 w-4 mr-2" />
                    Your Word
                    <span className="ml-2 text-xs text-slate-400">
                      ({currentWord.length} letters, {calculateWordScore(currentWord)} points)
                    </span>
                  </h3>

                  <div className="flex flex-wrap gap-2 justify-center mb-3">
                    {answerBoxes.map((letter, index) => (
                      <div
                        key={index}
                        className={`
                          w-12 h-12 bg-slate-900 border-2 border-slate-700 rounded-sm
                          flex items-center justify-center text-xl font-bold text-white
                          transition-all cursor-pointer
                          ${letter ? 'border-purple-500 bg-purple-900/30' : 'border-dashed'}
                          ${dragOverIndex === index ? 'border-yellow-500 bg-yellow-900/30' : ''}
                          ${letter ? 'hover:border-red-500' : ''}
                        `}
                        onDragOver={(e) => handleDragOver(e, index)}
                        onDragLeave={handleDragLeave}
                        onDrop={(e) => handleDrop(e, index)}
                        onClick={() => handleAnswerBoxClick(index)}
                      >
                        {letter}
                      </div>
                    ))}
                  </div>

                  {config.inputMode === INPUT_MODE.TYPE && (
                    <div className="flex justify-center mb-3">
                      <Input
                        type="text"
                        placeholder="Type your word here..."
                        className="h-12 text-center text-xl font-bold bg-slate-800 border-slate-700 max-w-md"
                        value={typedInput}
                        onChange={handleTypedInputChange}
                        disabled={gameStatus !== GAME_STATUS.PLAYING}
                      />
                    </div>
                  )}

                  <div className="flex justify-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      className="h-8 text-xs"
                      onClick={clearWord}
                      disabled={!currentWord || gameStatus !== GAME_STATUS.PLAYING}
                    >
                      <X className="h-3 w-3 mr-1" />
                      Clear
                    </Button>

                    <Button
                      className="h-8 text-xs bg-gradient-to-r from-green-500 to-green-600"
                      onClick={submitWord}
                      disabled={!currentWord || currentWord.length < config.minWordLength || gameStatus !== GAME_STATUS.PLAYING || players[0]?.isReady}
                    >
                      <Check className="h-3 w-3 mr-1" />
                      Submit Word
                    </Button>
                  </div>
                </div>

                {/* Word hints */}
                {currentWordSet && (
                  <div className="bg-slate-900 border border-slate-800 rounded-sm p-3 text-center">
                    <p className="text-xs text-slate-400">
                      {currentWordSet.solutions.length} possible words • 
                      Target word: {currentWordSet.target.length} letters
                    </p>
                  </div>
                )}
              </div>
            )}

            {gameStatus === GAME_STATUS.ROUND_END && roundWinner && (
              <div className="w-full max-w-md text-center">
                <div className="bg-slate-900 border border-slate-800 rounded-lg p-8">
                  <Crown className="h-16 w-16 text-yellow-500 mx-auto mb-4" />
                  <h2 className="text-2xl font-bold text-white mb-2">Round {currentRound} Complete!</h2>
                  <p className="text-lg text-white mb-4">
                    Winner: <span className="text-yellow-500 font-bold">{roundWinner.name}</span>
                  </p>
                  <p className="text-slate-400 mb-2">
                    Winning word: <span className="text-white font-bold">"{roundWinner.currentWord}"</span>
                  </p>
                  <p className="text-slate-400">
                    Points: <span className="text-green-500 font-bold">{roundWinner.lastWordPoints}</span>
                  </p>
                  {currentWordSet && (
                    <p className="text-purple-400 mt-4">
                      Target word was: <span className="font-bold">{currentWordSet.target}</span>
                    </p>
                  )}
                </div>
              </div>
            )}

            {gameStatus === GAME_STATUS.GAME_END && gameResult && (
              <div className="w-full max-w-md text-center">
                <div className="bg-slate-900 border border-slate-800 rounded-lg p-8">
                  <Trophy className="h-20 w-20 text-yellow-500 mx-auto mb-4" />
                  <h2 className="text-3xl font-bold text-white mb-4">Game Over!</h2>
                  
                  <div className="bg-slate-800 p-4 rounded mb-6">
                    <p className="text-lg text-white mb-2">
                      Champion: <span className="text-yellow-500 font-bold">{gameResult.winner.name}</span>
                    </p>
                    <p className="text-2xl font-bold text-green-500">{gameResult.winner.score} points</p>
                    
                    {gameResult.payout > 0 && (
                      <div className="mt-4 p-3 bg-green-900/30 border border-green-700 rounded">
                        <p className="text-green-400">Congratulations! You won!</p>
                        <p className="text-2xl font-bold text-green-500">${gameResult.payout.toFixed(2)}</p>
                      </div>
                    )}
                  </div>
                  
                  <div className="space-y-2 mb-6">
                    <h3 className="text-sm font-medium text-white mb-2">Final Standings</h3>
                    {gameResult.players
                      .sort((a: any, b: any) => b.score - a.score)
                      .map((player: any, index: number) => (
                        <div key={player.id} className="flex justify-between text-sm">
                          <span className="text-slate-400">
                            #{index + 1} {player.name}
                          </span>
                          <span className="text-white font-bold">{player.score} pts</span>
                        </div>
                      ))}
                  </div>
                  
                  <div className="flex gap-2">
                    <Button onClick={resetGame} className="flex-1">
                      <RotateCcw className="h-4 w-4 mr-2" />
                      Play Again
                    </Button>
                    {onBack && (
                      <Button onClick={onBack} variant="outline" className="flex-1">
                        <ChevronDown className="h-4 w-4 mr-2 rotate-90" />
                        Back to Lobby
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Game controls */}
            {gameStatus === GAME_STATUS.PLAYING && (
              <div className="flex gap-3 mt-4 justify-center">
                <Button onClick={resetGame} size="sm" variant="outline" className="text-xs">
                  <RotateCcw className="h-3 w-3 mr-1" />
                  Reset
                </Button>
                {onBack && (
                  <Button onClick={onBack} size="sm" variant="outline" className="text-xs">
                    <ChevronDown className="h-3 w-3 mr-1 rotate-90" />
                    Back
                  </Button>
                )}
              </div>
            )}
          </div>

          {/* Right Panel - Chat */}
          <div className="col-span-3 flex flex-col gap-2 h-full overflow-hidden">
            <div className="bg-slate-900 border border-slate-800 rounded-sm flex-1 flex flex-col overflow-hidden min-h-0 relative">
              <div className="p-3 border-b border-slate-800 flex justify-between items-center shrink-0">
                <h3 className="text-sm font-semibold text-white">Game Chat</h3>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => setShowChat(!showChat)}
                  className="h-6 w-6 p-0"
                >
                  {showChat ? <ChevronDown className="h-3 w-3" /> : <MessageSquare className="h-3 w-3" />}
                </Button>
              </div>
              
              {showChat && (
                <>
                  <div className="flex-1 overflow-y-auto p-3 space-y-1 custom-scrollbar min-h-0 pb-16">
                    {messages.map((msg, i) => (
                      <div key={i} className={`text-xs ${msg.type === 'system' ? 'text-slate-400 italic' : 'text-white'}`}>
                        {msg.text}
                      </div>
                    ))}
                  </div>
                  
                  <div className="absolute bottom-0 left-0 right-0 p-2 bg-slate-900 border-t border-slate-800">
                    <div className="flex gap-1">
                      <input
                        type="text"
                        value={messageInput}
                        onChange={(e) => setMessageInput(e.target.value)}
                        onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
                        placeholder="Type a message..."
                        className="flex-1 bg-slate-800 text-white text-xs rounded px-2 py-1 focus:outline-none focus:ring-1 focus:ring-purple-500"
                      />
                      <Button
                        onClick={sendMessage}
                        size="sm"
                        className="bg-purple-600 hover:bg-purple-700 h-7 px-2"
                      >
                        <Send className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WordJumbleGame;