import React, { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Input } from '@/components/ui/input';
import useWalletStore from '@/stores/walletStore';
import { walletService } from '@/services/wallet';
import { testAuth } from '@/services/api';
import {
  Trophy,
  Clock,
  MessageSquare,
  Users,
  ArrowRight,
  RotateCcw,
  ChevronDown,
  X,
  Settings,
  Zap,
  AlertCircle,
  DollarSign,
  Star,
  Shield,
  Hand,
  Scissors
} from 'lucide-react';

// Game choices
const CHOICES = {
  ROCK: 'rock',
  PAPER: 'paper',
  SCISSORS: 'scissors'
};

// Game status
const GAME_STATUS = {
  SETUP: 'setup',
  PLAYING: 'playing',
  ROUND_END: 'round_end',
  GAME_END: 'game_end'
};

// Rock Paper Scissors Game Component
const RockPaperScissorsGame = () => {
  // Wallet store
  const { balance } = useWalletStore();
  
  // Game configuration
  const [config, setConfig] = useState({
    rounds: 3,
    tieBreakerEnabled: true,
    countdownTime: 3,
    wagerAmount: 50,
    showChat: true,
    roundTimeLimit: 10
  });

  // Game state
  const [gameStatus, setGameStatus] = useState(GAME_STATUS.SETUP);
  const [currentRound, setCurrentRound] = useState(1);
  const [scores, setScores] = useState({ player1: 0, player2: 0 });
  const [choices, setChoices] = useState({ player1: null, player2: null });
  const [timer, setTimer] = useState(0);
  const [timerInterval, setTimerInterval] = useState(null);
  const [roundCountdown, setRoundCountdown] = useState(0);
  const [countdown, setCountdown] = useState(0);
  const [roundHistory, setRoundHistory] = useState([]);
  const [roundResult, setRoundResult] = useState(null);
  const [gameResult, setGameResult] = useState(null);
  const [isPlayer1Turn, setIsPlayer1Turn] = useState(true);
  const [showSettings, setShowSettings] = useState(false);
  const [aiThinking, setAiThinking] = useState(false);
  const [lastRoundTime, setLastRoundTime] = useState(0);
  const [totalGameTime, setTotalGameTime] = useState(0);
  const [showResults, setShowResults] = useState(false);
  const [animating, setAnimating] = useState(false);

  // Chat state
  const [messages, setMessages] = useState([
    { text: "Welcome to Rock Paper Scissors! Configure your game and press 'Start Game' when ready.", type: "system" }
  ]);
  const [messageInput, setMessageInput] = useState('');
  const messageEndRef = useRef(null);
  
  // Game ID for tracking bets
  const [gameId, setGameId] = useState('');

  // Effects

  // Scroll chat to bottom when messages change
  useEffect(() => {
    if (messageEndRef.current) {
      messageEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  // Clean up intervals on unmount
  useEffect(() => {
    return () => {
      if (timerInterval) {
        clearInterval(timerInterval);
      }
    };
  }, [timerInterval]);

  // Autoscroll round history
  const roundHistoryRef = useRef(null);
  useEffect(() => {
    if (roundHistoryRef.current) {
      roundHistoryRef.current.scrollTop = roundHistoryRef.current.scrollHeight;
    }
  }, [roundHistory]);

  // Add message to chat
  const addMessage = (text, type = "player") => {
    setMessages(prev => [...prev, { text, type }]);
  };

  // Send chat message
  const sendMessage = () => {
    if (messageInput.trim()) {
      addMessage(`Player: ${messageInput}`);
      setMessageInput('');
    }
  };

  // Start the game
  const startGame = async () => {
    // Check if user has sufficient balance
    if (balance < config.wagerAmount) {
      addMessage("Insufficient balance! Please deposit more funds.", "system");
      return;
    }

    try {
      // Place bet via API
      const gameId = `rps-${Date.now()}`;
      await walletService.placeBet(
        config.wagerAmount,
        gameId,
        `Rock Paper Scissors - ${config.rounds} rounds`
      );
      
      // Store game ID for later use
      setGameId(gameId);

      // Reset game state
      setScores({ player1: 0, player2: 0 });
      setChoices({ player1: null, player2: null });
      setCurrentRound(1);
      setRoundHistory([]);
      setRoundResult(null);
      setGameResult(null);
      setTotalGameTime(0);

      // Start the timer
      const interval = setInterval(() => {
        setTotalGameTime(prev => prev + 1);
      }, 1000);
      setTimerInterval(interval);

      // Set game status to playing
      setGameStatus(GAME_STATUS.PLAYING);

      // Add message
      addMessage("Game started! Choose Rock, Paper, or Scissors.", "system");
      addMessage(`Wager of $${config.wagerAmount} placed.`, "system");

      // Start the round countdown
      startRoundCountdown();
    } catch (error) {
      // console.error('Failed to start game:', error);
      addMessage("Failed to place bet. Please try again.", "system");
    }
  };

  // Start round countdown
  const startRoundCountdown = () => {
    setRoundCountdown(config.roundTimeLimit);
    const interval = setInterval(() => {
      setRoundCountdown(prev => {
        if (prev <= 1) {
          clearInterval(interval);
          // If timer reaches 0, make a random choice for any player who hasn't chosen
          handleTimeUp();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return interval;
  };

  // Handle time up
  const handleTimeUp = () => {
    // If player hasn't made a choice, make a random one
    if (!choices.player1) {
      const randomChoice = getRandomChoice();
      makeChoice(randomChoice, 'player1');
      addMessage("Time's up! Made a random choice for Player 1.", "system");
    }

    // AI always makes a choice before time's up, but we'll handle this case just in case
    if (!choices.player2) {
      const aiChoice = getRandomChoice();
      makeChoice(aiChoice, 'player2');
      addMessage("Time's up! Made a random choice for Player 2.", "system");
    }
  };

  // Get random choice
  const getRandomChoice = () => {
    const options = [CHOICES.ROCK, CHOICES.PAPER, CHOICES.SCISSORS];
    return options[Math.floor(Math.random() * options.length)];
  };

  // Make a choice for a player
  const makeChoice = (choice, player) => {
    if (gameStatus !== GAME_STATUS.PLAYING || animating) return;

    // If player 2's turn (AI), we'll simulate thinking
    if (player === 'player2' && !choices.player2) {
      setAiThinking(true);

      // Simulate AI "thinking"
      setTimeout(() => {
        setChoices(prev => ({ ...prev, [player]: choice }));
        setAiThinking(false);

        // If both players have made a choice, determine the round winner
        if (choices.player1) {
          startRevealAnimation();
        }
      }, 500 + Math.random() * 1000);
      return;
    }

    // Update choices
    setChoices(prev => ({ ...prev, [player]: choice }));

    // If this is player 1 and they just chose, have AI make a choice
    if (player === 'player1' && !choices.player1) {
      // Add message
      addMessage(`You chose ${choice}.`, "system");

      // Choose for AI
      const aiChoice = getSmartAiChoice();
      makeChoice(aiChoice, 'player2');
    }

    // If both players have made a choice, determine the round winner
    if ((player === 'player1' && choices.player2) ||
        (player === 'player2' && choices.player1)) {
      startRevealAnimation();
    }
  };

  // Smart AI choice based on player's history
  const getSmartAiChoice = () => {
    // If less than 3 rounds, just choose randomly
    if (roundHistory.length < 3) {
      return getRandomChoice();
    }

    // Analyze player's pattern
    const recentChoices = roundHistory.slice(-3).map(round => round.player1Choice);

    // Detect patterns (e.g., if player has chosen the same thing twice in a row)
    if (recentChoices[1] === recentChoices[2]) {
      // Player might choose the same thing again, so choose what beats that
      const playerLikelyChoice = recentChoices[2];

      // Choose what beats player's likely choice
      if (playerLikelyChoice === CHOICES.ROCK) return CHOICES.PAPER;
      if (playerLikelyChoice === CHOICES.PAPER) return CHOICES.SCISSORS;
      if (playerLikelyChoice === CHOICES.SCISSORS) return CHOICES.ROCK;
    }

    // Check if player alternates
    if (recentChoices[0] === recentChoices[2] && recentChoices[0] !== recentChoices[1]) {
      // Player might be alternating, predict next
      const playerLikelyChoice = recentChoices[1];

      // Choose what beats player's likely choice
      if (playerLikelyChoice === CHOICES.ROCK) return CHOICES.PAPER;
      if (playerLikelyChoice === CHOICES.PAPER) return CHOICES.SCISSORS;
      if (playerLikelyChoice === CHOICES.SCISSORS) return CHOICES.ROCK;
    }

    // If no pattern detected, choose randomly
    return getRandomChoice();
  };

  // Start the animation for revealing choices
  const startRevealAnimation = () => {
    setAnimating(true);

    // Start countdown
    setCountdown(config.countdownTime);
    const countdownInterval = setInterval(() => {
      setCountdown(prev => {
        if (prev <= 1) {
          clearInterval(countdownInterval);
          determineRoundWinner();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  // Determine the winner of the current round
  const determineRoundWinner = () => {
    const player1Choice = choices.player1;
    const player2Choice = choices.player2;

    // Calculate round time
    const roundTime = config.roundTimeLimit - roundCountdown;
    setLastRoundTime(roundTime);

    let result = "";

    // Determine winner
    if (player1Choice === player2Choice) {
      result = "tie";
      addMessage("It's a tie!", "system");
    } else if (
      (player1Choice === CHOICES.ROCK && player2Choice === CHOICES.SCISSORS) ||
      (player1Choice === CHOICES.PAPER && player2Choice === CHOICES.ROCK) ||
      (player1Choice === CHOICES.SCISSORS && player2Choice === CHOICES.PAPER)
    ) {
      result = "player1";
      setScores(prev => ({ ...prev, player1: prev.player1 + 1 }));
      addMessage("You win this round!", "system");
    } else {
      result = "player2";
      setScores(prev => ({ ...prev, player2: prev.player2 + 1 }));
      addMessage("Player 2 wins this round!", "system");
    }

    // Update round history
    setRoundHistory(prev => [
      ...prev,
      {
        round: currentRound,
        player1Choice,
        player2Choice,
        result,
        time: roundTime
      }
    ]);

    // Update round result
    setRoundResult(result);

    // Show results
    setShowResults(true);

    // Check if game is over
    const isGameOver = checkGameOver(result);

    if (!isGameOver) {
      // Proceed to next round after delay
      setTimeout(() => {
        proceedToNextRound();
      }, 3000);
    }

    setAnimating(false);
  };

  // Check if the game is over
  const checkGameOver = (result) => {
    const newScores = {
      player1: result === "player1" ? scores.player1 + 1 : scores.player1,
      player2: result === "player2" ? scores.player2 + 1 : scores.player2
    };

    // Check if either player has won enough rounds
    const maxRounds = config.rounds;
    const player1Wins = newScores.player1;
    const player2Wins = newScores.player2;

    // Check if a player has mathematically won
    const remainingRounds = maxRounds - currentRound;

    if (player1Wins > player2Wins + remainingRounds) {
      // Player 1 has won enough rounds to secure victory
      endGame("player1");
      return true;
    }

    if (player2Wins > player1Wins + remainingRounds) {
      // Player 2 has won enough rounds to secure victory
      endGame("player2");
      return true;
    }

    // Check if we've played all rounds
    if (currentRound >= maxRounds) {
      // Check for tie and if tie breaker is enabled
      if (player1Wins === player2Wins && config.tieBreakerEnabled) {
        // Add message
        addMessage("It's a tie! Playing tie-breaker round.", "system");
        return false; // Continue to tie-breaker
      } else {
        // End game
        if (player1Wins > player2Wins) {
          endGame("player1");
        } else if (player2Wins > player1Wins) {
          endGame("player2");
        } else {
          endGame("tie");
        }
        return true;
      }
    }

    return false;
  };

  // Proceed to the next round
  const proceedToNextRound = () => {
    // Reset round state
    setChoices({ player1: null, player2: null });
    setRoundResult(null);
    setShowResults(false);
    setCurrentRound(prev => prev + 1);

    // Add message
    addMessage(`Round ${currentRound + 1} starting!`, "system");

    // Start countdown for next round
    startRoundCountdown();
  };

  // End the game
  const endGame = async (winner) => {
    // Update game status
    setGameStatus(GAME_STATUS.GAME_END);

    // Clear timer
    if (timerInterval) {
      clearInterval(timerInterval);
      setTimerInterval(null);
    }

    // Set game result
    setGameResult(winner);

    // Handle payouts
    try {
      if (winner === "player1") {
        const winAmount = config.wagerAmount * 2;
        
        // Process win via API
        await walletService.processWin(
          winAmount,
          gameId,
          `Rock Paper Scissors - Won ${config.rounds} rounds`
        );

        addMessage("Congratulations! You win the game!", "system");
        addMessage(`Payout: $${winAmount.toFixed(2)}`, "system");
      } else if (winner === "player2") {
        addMessage("Player 2 wins the game!", "system");
        addMessage("Better luck next time.", "system");
      } else {
        // Tie - return wager
        await walletService.processWin(
          config.wagerAmount,
          gameId,
          `Rock Paper Scissors - Game tied, wager returned`
        );
        
        addMessage("The game ended in a tie!", "system");
        addMessage(`Wager returned: $${config.wagerAmount.toFixed(2)}`, "system");
      }
    } catch (error) {
      // console.error('Failed to process payout:', error);
      addMessage("Error processing payout. Please contact support.", "system");
    }
  };

  // Reset the game
  const resetGame = () => {
    // Clear timer
    if (timerInterval) {
      clearInterval(timerInterval);
      setTimerInterval(null);
    }

    // Reset game state
    setGameStatus(GAME_STATUS.SETUP);
    setScores({ player1: 0, player2: 0 });
    setChoices({ player1: null, player2: null });
    setCurrentRound(1);
    setRoundHistory([]);
    setRoundResult(null);
    setGameResult(null);
    setShowResults(false);

    // Add message
    addMessage("Game reset. Configure your game and press 'Start Game' when ready.", "system");
  };

  // Format time display
  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds < 10 ? '0' : ''}${remainingSeconds}`;
  };

  // Get choice icon based on choice
  const getChoiceIcon = (choice) => {
    switch (choice) {
      case CHOICES.ROCK:
        return <div className="text-2xl">🪨</div>;
      case CHOICES.PAPER:
        return <div className="text-2xl">📜</div>;
      case CHOICES.SCISSORS:
        return <div className="text-2xl">✂️</div>;
      default:
        return <div className="text-2xl">❓</div>;
    }
  };

  // Get choice name
  const getChoiceName = (choice) => {
    switch (choice) {
      case CHOICES.ROCK:
        return "Rock";
      case CHOICES.PAPER:
        return "Paper";
      case CHOICES.SCISSORS:
        return "Scissors";
      default:
        return "Unknown";
    }
  };

  // Get result description
  const getResultDescription = (result) => {
    switch (result) {
      case "player1":
        return "You Win!";
      case "player2":
        return "Player 2 Wins!";
      case "tie":
        return "It's a Tie!";
      default:
        return "";
    }
  };

  // Get result color
  const getResultColor = (result) => {
    switch (result) {
      case "player1":
        return "text-green-500";
      case "player2":
        return "text-red-500";
      case "tie":
        return "text-yellow-500";
      default:
        return "";
    }
  };

  // Render settings panel
  const renderSettings = () => {
    return (
      <div className="bg-slate-900 border border-slate-800 rounded-sm p-3 absolute top-0 left-0 right-0 z-10">
        <div className="flex justify-between items-center mb-3">
          <h3 className="text-sm font-medium text-white">Game Settings</h3>
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0"
            onClick={() => setShowSettings(false)}
          >
            <X className="h-3 w-3" />
          </Button>
        </div>

        <div className="space-y-3">
          <div>
            <label className="text-xs text-slate-400 block mb-1">Number of Rounds</label>
            <div className="flex space-x-2">
              {[1, 3, 5, 7].map(num => (
                <Button
                  key={num}
                  variant={config.rounds === num ? "default" : "outline"}
                  size="sm"
                  className="flex-1 h-7 text-xs"
                  onClick={() => setConfig({ ...config, rounds: num })}
                >
                  {num}
                </Button>
              ))}
            </div>
          </div>

          <div>
            <label className="text-xs text-slate-400 block mb-1">Wager Amount</label>
            <div className="flex space-x-2">
              {[10, 25, 50, 100].map(amount => (
                <Button
                  key={amount}
                  variant={config.wagerAmount === amount ? "default" : "outline"}
                  size="sm"
                  className="flex-1 h-7 text-xs"
                  disabled={amount > balance}
                  onClick={() => setConfig({ ...config, wagerAmount: amount })}
                >
                  ${amount}
                </Button>
              ))}
            </div>
            {config.wagerAmount > balance && (
              <p className="text-xs text-red-500 mt-1">Insufficient balance</p>
            )}
          </div>

          <div>
            <label className="text-xs text-slate-400 block mb-1">Round Time Limit</label>
            <div className="flex space-x-2">
              {[5, 10, 15, 30].map(time => (
                <Button
                  key={time}
                  variant={config.roundTimeLimit === time ? "default" : "outline"}
                  size="sm"
                  className="flex-1 h-7 text-xs"
                  onClick={() => setConfig({ ...config, roundTimeLimit: time })}
                >
                  {time}s
                </Button>
              ))}
            </div>
          </div>

          <div className="flex items-center justify-between">
            <label className="text-xs text-slate-400">Play Tie-Breaker</label>
            <Button
              variant={config.tieBreakerEnabled ? "default" : "outline"}
              size="sm"
              className="h-7 w-14 text-xs"
              onClick={() => setConfig({ ...config, tieBreakerEnabled: !config.tieBreakerEnabled })}
            >
              {config.tieBreakerEnabled ? "On" : "Off"}
            </Button>
          </div>

          <Button
            className="w-full h-8 text-xs rounded-sm mt-2 bg-gradient-to-r from-purple-500 to-pink-500"
            onClick={() => setShowSettings(false)}
          >
            Save Settings
          </Button>
        </div>
      </div>
    );
  };

  // Render the choice buttons
  const renderChoiceButtons = () => {
    const buttons = [
      { choice: CHOICES.ROCK, icon: "🪨", label: "Rock", color: "from-gray-600 to-gray-800" },
      { choice: CHOICES.PAPER, icon: "📜", label: "Paper", color: "from-blue-600 to-blue-800" },
      { choice: CHOICES.SCISSORS, icon: "✂️", label: "Scissors", color: "from-red-600 to-red-800" }
    ];

    return (
      <div className="grid grid-cols-3 gap-3">
        {buttons.map(button => (
          <Button
            key={button.choice}
            className={`h-24 text-xs rounded-sm flex flex-col items-center justify-center bg-gradient-to-b ${button.color} hover:brightness-110 transition-all ${
              choices.player1 === button.choice ? 'ring-2 ring-yellow-400' : ''
            }`}
            disabled={!!choices.player1 || gameStatus !== GAME_STATUS.PLAYING || animating}
            onClick={() => makeChoice(button.choice, 'player1')}
          >
            <div className="text-3xl mb-2">{button.icon}</div>
            <span>{button.label}</span>
          </Button>
        ))}
      </div>
    );
  };

  // Render game info
  const renderGameInfo = () => {
    return (
      <div className="bg-slate-900 border border-slate-800 rounded-sm p-2">
        <div className="flex justify-between items-center mb-2">
          <h3 className="text-sm font-medium text-white">
            {gameStatus === GAME_STATUS.SETUP ? "Game Setup" :
             gameStatus === GAME_STATUS.PLAYING ? `Round ${currentRound} of ${config.rounds}` :
             "Game Over"}
          </h3>
          <Badge className={`
            ${gameStatus === GAME_STATUS.PLAYING ? 'bg-green-500' :
              gameStatus === GAME_STATUS.SETUP ? 'bg-yellow-500' :
              'bg-blue-500'}
          `}>
            {gameStatus === GAME_STATUS.PLAYING ? 'LIVE' :
             gameStatus === GAME_STATUS.SETUP ? 'SETUP' :
             'FINISHED'}
          </Badge>
        </div>

        <div className="grid grid-cols-2 gap-2">
          <div className="bg-slate-800 p-2 rounded-sm">
            <div className="text-xs text-slate-400">Wager</div>
            <div className="text-sm font-bold text-white">${config.wagerAmount.toFixed(2)}</div>
          </div>

          <div className="bg-slate-800 p-2 rounded-sm">
            <div className="text-xs text-slate-400">Game Time</div>
            <div className="text-sm font-bold text-white">{formatTime(totalGameTime)}</div>
          </div>
        </div>
      </div>
    );
  };

  // Render player info
  const renderPlayerInfo = () => {
    return (
      <div className="bg-slate-900 border border-slate-800 rounded-sm p-2">
        <div className="mb-2">
          <div className="flex justify-between items-center">
            <div className="flex items-center">
              <div className="h-6 w-6 rounded-full bg-blue-500 mr-2 flex items-center justify-center text-xs font-bold">P1</div>
              <h3 className="text-sm font-medium text-white">You</h3>
            </div>
            <Badge className="bg-green-500">{scores.player1}</Badge>
          </div>
        </div>

        <div className="mb-3">
          <div className="flex justify-between items-center">
            <div className="flex items-center">
              <div className="h-6 w-6 rounded-full bg-red-500 mr-2 flex items-center justify-center text-xs font-bold">P2</div>
              <h3 className="text-sm font-medium text-white">Player 2</h3>
            </div>
            <Badge className="bg-green-500">{scores.player2}</Badge>
          </div>
        </div>

        {/* Game Result (shown only when game is over) */}
        {gameStatus === GAME_STATUS.GAME_END && (
          <div className="mt-2 p-2 bg-slate-800 rounded-sm">
            <h3 className="text-sm font-medium text-white mb-1">Game Result</h3>
            <div className="text-center py-2">
              {gameResult === "player1" ? (
                <div className="text-green-400 font-bold">You Win!</div>
              ) : gameResult === "player2" ? (
                <div className="text-red-400 font-bold">Player 2 Wins!</div>
              ) : (
                <div className="text-yellow-400 font-bold">Game Ended in a Tie</div>
              )}

              <div className="text-xs text-green-500 mt-1">
                {gameResult === "player1" ? (
                  <>Payout: ${(config.wagerAmount * 2).toFixed(2)}</>
                ) : gameResult === "tie" ? (
                  <>Wager returned: ${config.wagerAmount.toFixed(2)}</>
                ) : (
                  <>Better luck next time!</>
                )}
              </div>
            </div>

            <Button
              onClick={resetGame}
              className="w-full h-7 text-xs rounded-sm mt-2 bg-gradient-to-r from-purple-500 to-pink-500"
            >
              <RotateCcw className="h-3 w-3 mr-1" />
              Play Again
            </Button>
          </div>
        )}
      </div>
    );
  };

  // Render round history
  const renderRoundHistory = () => {
    if (roundHistory.length === 0) {
      return <div className="text-xs text-slate-400 p-2">No rounds played yet</div>;
    }

    return (
      <div className="text-xs p-2 overflow-auto max-h-full" ref={roundHistoryRef}>
        {roundHistory.map((round, index) => (
          <div key={index} className="mb-2 p-2 bg-slate-800 rounded-sm">
            <div className="flex justify-between items-center mb-1">
              <span className="font-medium text-white">Round {round.round}</span>
              <span className={getResultColor(round.result)}>
                {getResultDescription(round.result)}
              </span>
            </div>

            <div className="flex justify-between mt-1">
              <div className="flex items-center">
                <div className="h-4 w-4 rounded-full bg-blue-500 mr-1 flex items-center justify-center text-[8px]">P1</div>
                <span className="text-slate-300">{getChoiceName(round.player1Choice)}</span>
              </div>
              <div className="flex items-center">
                <span className="text-slate-300">{getChoiceName(round.player2Choice)}</span>
                <div className="h-4 w-4 rounded-full bg-red-500 ml-1 flex items-center justify-center text-[8px]">P2</div>
              </div>
            </div>

            <div className="mt-1 text-slate-400">
              Time: {round.time}s
            </div>
          </div>
        ))}
      </div>
    );
  };

  // Render the countdown when choices are being revealed
  const renderCountdown = () => {
    if (countdown === 0 || !animating) return null;

    return (
      <div className="absolute inset-0 bg-black/70 flex items-center justify-center z-10">
        <div className="text-center">
          <div className="text-6xl font-bold text-white mb-2">{countdown}</div>
          <div className="text-sm text-slate-300">Revealing choices...</div>
        </div>
      </div>
    );
  };

  // Render the results after a round
  const renderResults = () => {
    if (!showResults || !choices.player1 || !choices.player2) return null;

    return (
      <div className="mt-4 bg-slate-800 rounded-sm p-3">
        <h3 className="text-sm font-medium text-white mb-3 text-center">Round Result</h3>

        <div className="flex justify-between items-center mb-4">
          <div className="text-center flex-1">
            <div className="mb-1 flex justify-center">
              {getChoiceIcon(choices.player1)}
            </div>
            <div className="text-xs text-blue-400">You chose {getChoiceName(choices.player1)}</div>
          </div>

          <div className="px-4">
            <div className={`text-lg font-bold ${getResultColor(roundResult)}`}>
              {getResultDescription(roundResult)}
            </div>
          </div>

          <div className="text-center flex-1">
            <div className="mb-1 flex justify-center">
              {getChoiceIcon(choices.player2)}
            </div>
            <div className="text-xs text-red-400">Player 2 chose {getChoiceName(choices.player2)}</div>
          </div>
        </div>

        <div className="text-center text-xs text-slate-400">
          {roundResult === "player1" ? (
            <>{getChoiceName(choices.player1)} beats {getChoiceName(choices.player2)}</>
          ) : roundResult === "player2" ? (
            <>{getChoiceName(choices.player2)} beats {getChoiceName(choices.player1)}</>
          ) : (
            <>Both chose {getChoiceName(choices.player1)}</>
          )}
        </div>
      </div>
    );
  };

  // Render setup screen
  const renderSetupScreen = () => {
    return (
      <div className="flex flex-col items-center justify-center h-full bg-slate-900 border border-slate-800 rounded-sm p-4">
        <div className="text-5xl mb-4">🎮</div>
        <h1 className="text-2xl font-bold text-white mb-1">Rock Paper Scissors</h1>
        <p className="text-sm text-slate-400 mb-4 text-center">
          Configure your game settings and press Start to begin!
        </p>

        <div className="w-full max-w-md mb-6">
          <div className="grid grid-cols-2 gap-3 mb-4">
            <div className="bg-slate-800 p-3 rounded-sm">
              <div className="flex items-center mb-2">
                <Trophy className="h-4 w-4 text-yellow-500 mr-1" />
                <span className="text-sm font-medium text-white">Rounds</span>
              </div>
              <div className="flex space-x-1">
                {[1, 3, 5, 7].map(num => (
                  <Button
                    key={num}
                    variant={config.rounds === num ? "default" : "outline"}
                    size="sm"
                    className="flex-1 h-7 text-xs"
                    onClick={() => setConfig({ ...config, rounds: num })}
                  >
                    {num}
                  </Button>
                ))}
              </div>
            </div>

            <div className="bg-slate-800 p-3 rounded-sm">
              <div className="flex items-center mb-2">
                <DollarSign className="h-4 w-4 text-green-500 mr-1" />
                <span className="text-sm font-medium text-white">Wager</span>
              </div>
              <div className="flex space-x-1">
                {[10, 25, 50, 100].map(amount => (
                  <Button
                    key={amount}
                    variant={config.wagerAmount === amount ? "default" : "outline"}
                    size="sm"
                    className="flex-1 h-7 text-xs"
                    disabled={amount > balance}
                    onClick={() => setConfig({ ...config, wagerAmount: amount })}
                  >
                    ${amount}
                  </Button>
                ))}
              </div>
              <div className="text-xs text-center mt-2">
                <span className="text-slate-400">Balance: </span>
                <span className="text-green-500 font-bold">${balance.toFixed(2)}</span>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-3">
            <div className="bg-slate-800 p-3 rounded-sm">
              <div className="flex items-center mb-2">
                <Clock className="h-4 w-4 text-blue-500 mr-1" />
                <span className="text-sm font-medium text-white">Time Limit</span>
              </div>
              <div className="flex space-x-1">
                {[5, 10, 15, 30].map(time => (
                  <Button
                    key={time}
                    variant={config.roundTimeLimit === time ? "default" : "outline"}
                    size="sm"
                    className="flex-1 h-7 text-xs"
                    onClick={() => setConfig({ ...config, roundTimeLimit: time })}
                  >
                    {time}s
                  </Button>
                ))}
              </div>
            </div>

            <div className="bg-slate-800 p-3 rounded-sm">
              <div className="flex items-center mb-2">
                <Star className="h-4 w-4 text-yellow-500 mr-1" />
                <span className="text-sm font-medium text-white">Game Options</span>
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <label className="text-xs text-slate-400">Tie-Breaker</label>
                  <Button
                    variant={config.tieBreakerEnabled ? "default" : "outline"}
                    size="sm"
                    className="h-7 w-14 text-xs"
                    onClick={() => setConfig({ ...config, tieBreakerEnabled: !config.tieBreakerEnabled })}
                  >
                    {config.tieBreakerEnabled ? "On" : "Off"}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <Button
          className="w-64 h-10 text-sm rounded-sm bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600"
          disabled={config.wagerAmount > balance}
          onClick={startGame}
        >
          <Zap className="h-4 w-4 mr-2" />
          {config.wagerAmount > balance ? "Insufficient Balance" : "Start Game"}
        </Button>
      </div>
    );
  };

  // Main game layout
  return (
    <div className="min-h-screen bg-slate-950 text-white pt-16">
      <div className="grid grid-cols-12 gap-2 p-2 h-[calc(100vh-64px)]">
      {/* Game Information - Left column */}
      <div className="col-span-3 flex flex-col gap-2 h-full">
        {renderGameInfo()}
        {renderPlayerInfo()}

        {/* Round History */}
        <div className="bg-slate-900 border border-slate-800 rounded-sm flex-1 flex flex-col overflow-hidden">
          <div className="p-2 border-b border-slate-800 flex justify-between items-center">
            <h3 className="text-sm font-medium text-white">Round History</h3>
            <span className="text-xs text-slate-400">{roundHistory.length} rounds</span>
          </div>

          <div className="flex-1 overflow-auto">
            {renderRoundHistory()}
          </div>
        </div>
      </div>

      {/* Game Board - Middle column */}
      <div className="col-span-6 flex flex-col h-full">
        {/* Game board */}
        <div className="flex-1 bg-slate-900 border border-slate-800 rounded-sm p-3 flex flex-col relative">
          {gameStatus === GAME_STATUS.SETUP ? (
            renderSetupScreen()
          ) : (
            <div className="flex-1 flex flex-col">
              {/* Countdown/Timer */}
              {gameStatus === GAME_STATUS.PLAYING && (
                <div className="mb-4">
                  <div className="flex justify-between items-center mb-1">
                    <span className="text-xs text-slate-400">Time Remaining</span>
                    <span className="text-xs text-white">{roundCountdown}s</span>
                  </div>
                  <Progress
                    value={(roundCountdown / config.roundTimeLimit) * 100}
                    className="h-2"
                  />
                </div>
              )}

              {/* Player 2's choice area */}
              <div className="mb-8 flex items-center justify-center">
                <div className="bg-slate-800 rounded-sm w-32 h-32 flex items-center justify-center relative">
                  {showResults ? (
                    <div className="text-5xl">{getChoiceIcon(choices.player2)}</div>
                  ) : aiThinking ? (
                    <div className="text-center">
                      <div className="text-xl text-slate-400 animate-pulse mb-1">🤔</div>
                      <div className="text-xs text-slate-500">Thinking...</div>
                    </div>
                  ) : (
                    <>
                      <div className="text-5xl text-slate-700">❓</div>
                      {choices.player2 && (
                        <div className="absolute top-2 right-2">
                          <Badge className="bg-green-500">Ready</Badge>
                        </div>
                      )}
                    </>
                  )}
                </div>
              </div>

              {/* VS display */}
              <div className="flex items-center justify-center mb-8">
                <div className="bg-slate-800 rounded-full w-16 h-16 flex items-center justify-center text-xl font-bold text-white">
                  VS
                </div>
              </div>

              {/* Results display */}
              {renderResults()}

              {/* Player 1's choice section */}
              <div className="mt-auto">
                <h3 className="text-sm font-medium text-white mb-2">
                  {showResults ? "Your choice:" : "Choose your weapon:"}
                </h3>

                {showResults ? (
                  <div className="bg-slate-800 rounded-sm h-16 flex items-center justify-center">
                    <div className="text-3xl">{getChoiceIcon(choices.player1)}</div>
                  </div>
                ) : (
                  renderChoiceButtons()
                )}
              </div>
            </div>
          )}

          {/* Countdown overlay */}
          {renderCountdown()}

          {/* Settings overlay */}
          {showSettings && renderSettings()}
        </div>

        {/* Game controls */}
        <div className="flex justify-between items-center mt-2">
          <Button
            variant="outline"
            size="sm"
            className="h-8 text-xs"
            onClick={resetGame}
          >
            <RotateCcw className="h-3 w-3 mr-1" />
            Reset Game
          </Button>

          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              className={`h-8 text-xs ${showSettings ? 'bg-slate-800' : ''}`}
              onClick={() => setShowSettings(!showSettings)}
            >
              <Settings className="h-3 w-3 mr-1" />
              Settings
            </Button>

            <Button
              variant={gameStatus === GAME_STATUS.PLAYING ? 'default' : 'outline'}
              size="sm"
              className="h-8 text-xs"
              disabled={gameStatus !== GAME_STATUS.SETUP || config.wagerAmount > balance}
              onClick={startGame}
            >
              {gameStatus === GAME_STATUS.SETUP ? (
                config.wagerAmount > balance ? (
                  <>
                    <AlertCircle className="h-3 w-3 mr-1" />
                    Insufficient Balance
                  </>
                ) : (
                  <>
                    <ArrowRight className="h-3 w-3 mr-1" />
                    Start Game
                  </>
                )
              ) : gameStatus === GAME_STATUS.PLAYING ? (
                <>
                  <Clock className="h-3 w-3 mr-1" />
                  Game In Progress
                </>
              ) : (
                <>
                  <Trophy className="h-3 w-3 mr-1" />
                  Game Over
                </>
              )}
            </Button>
          </div>
        </div>
      </div>

      {/* Chat and Stats - Right column */}
      <div className="col-span-3 flex flex-col gap-2 h-full">
        {/* Game Stats */}
        <div className="bg-slate-900 border border-slate-800 rounded-sm p-2">
          <div className="flex justify-between items-center mb-2">
            <h3 className="text-sm font-medium text-white">Game Stats</h3>
            <Badge className="bg-blue-500">Live</Badge>
          </div>

          <div className="mb-2 text-center">
            <div className="text-xs text-slate-400">Your Balance</div>
            <div className="text-lg font-bold text-green-500">${balance.toFixed(2)}</div>
          </div>

          <div className="grid grid-cols-2 gap-2 mb-2">
            <div className="bg-slate-800 p-2 rounded-sm text-center">
              <div className="text-xs text-slate-400">Rounds Played</div>
              <div className="text-lg font-bold text-white">{roundHistory.length}</div>
            </div>

            <div className="bg-slate-800 p-2 rounded-sm text-center">
              <div className="text-xs text-slate-400">Remaining</div>
              <div className="text-lg font-bold text-white">
                {Math.max(0, config.rounds - currentRound + (roundResult === null ? 0 : 1))}
              </div>
            </div>
          </div>

          <div className="mb-2">
            <div className="flex justify-between items-center mb-1">
              <div className="flex items-center">
                <div className="h-4 w-4 rounded-full bg-blue-500 mr-1"></div>
                <span className="text-xs text-white">You</span>
              </div>
              <span className="text-xs text-white">{scores.player1} wins</span>
            </div>
            <Progress value={(scores.player1 / config.rounds) * 100} className="h-1.5 bg-slate-700" />
          </div>

          <div>
            <div className="flex justify-between items-center mb-1">
              <div className="flex items-center">
                <div className="h-4 w-4 rounded-full bg-red-500 mr-1"></div>
                <span className="text-xs text-white">Player 2</span>
              </div>
              <span className="text-xs text-white">{scores.player2} wins</span>
            </div>
            <Progress value={(scores.player2 / config.rounds) * 100} className="h-1.5 bg-slate-700" />
          </div>
        </div>

        {/* Live Betting */}
        <div className="bg-slate-900 border border-slate-800 rounded-sm p-2">
          <div className="flex justify-between items-center mb-2">
            <h3 className="text-sm font-medium text-white">Live Betting</h3>
            <Badge className={gameStatus === GAME_STATUS.PLAYING ? "bg-green-500" : "bg-red-500"}>
              {gameStatus === GAME_STATUS.PLAYING ? "OPEN" : "CLOSED"}
            </Badge>
          </div>

          <div className="grid grid-cols-3 gap-2">
            <div className="bg-slate-800 p-2 rounded-sm flex flex-col items-center">
              <div className="text-2xl mb-1">🪨</div>
              <div className="text-xs text-slate-400">Rock</div>
              <div className="text-sm font-bold text-white">2.1x</div>
            </div>

            <div className="bg-slate-800 p-2 rounded-sm flex flex-col items-center">
              <div className="text-2xl mb-1">📜</div>
              <div className="text-xs text-slate-400">Paper</div>
              <div className="text-sm font-bold text-white">2.0x</div>
            </div>

            <div className="bg-slate-800 p-2 rounded-sm flex flex-col items-center">
              <div className="text-2xl mb-1">✂️</div>
              <div className="text-xs text-slate-400">Scissors</div>
              <div className="text-sm font-bold text-white">2.2x</div>
            </div>
          </div>

          <div className="mt-2">
            <Button
              className="w-full h-7 text-xs rounded-sm bg-gradient-to-r from-purple-500 to-pink-500"
              disabled={gameStatus !== GAME_STATUS.PLAYING}
            >
              <DollarSign className="h-3 w-3 mr-1" />
              Place Side Bet
            </Button>
          </div>
        </div>

        {/* Chat */}
        <div className="flex-1 bg-slate-900 border border-slate-800 rounded-sm flex flex-col overflow-hidden">
          <div className="p-2 border-b border-slate-800 flex justify-between items-center">
            <div className="flex items-center">
              <MessageSquare className="h-4 w-4 text-slate-400 mr-1" />
              <h3 className="text-sm font-medium text-white">Game Chat</h3>
            </div>
            <div className="flex items-center">
              <Badge className="h-5 bg-green-500 mr-1 text-[10px]">
                <Users className="h-3 w-3 mr-1" />
                5
              </Badge>
              <Button
                variant="ghost"
                size="sm"
                className="h-5 w-5 p-0"
                onClick={() => setConfig({ ...config, showChat: !config.showChat })}
              >
                <ChevronDown className={`h-3 w-3 text-slate-400 ${!config.showChat ? 'rotate-180' : ''}`} />
              </Button>
            </div>
          </div>

          {config.showChat && (
            <>
              {/* Chat messages - scrollable */}
              <div className="flex-1 overflow-auto p-2 space-y-1">
                {messages.map((msg, idx) => (
                  <div
                    key={idx}
                    className={`text-xs p-1 rounded-sm ${
                      msg.type === 'system' ? 'text-slate-400 italic' : 'text-white'
                    }`}
                  >
                    {msg.text}
                  </div>
                ))}
                <div ref={messageEndRef} />
              </div>

              {/* Chat input */}
              <div className="p-2 border-t border-slate-800">
                <div className="flex space-x-2">
                  <Input
                    placeholder="Type a message..."
                    className="h-8 text-xs bg-slate-800 border-slate-700"
                    value={messageInput}
                    onChange={(e) => setMessageInput(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        sendMessage();
                      }
                    }}
                  />
                  <Button
                    onClick={sendMessage}
                    className="h-8 px-3 bg-gradient-to-r from-purple-500 to-pink-500"
                  >
                    Send
                  </Button>
                </div>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
    </div>
  );
};

export default RockPaperScissorsGame;