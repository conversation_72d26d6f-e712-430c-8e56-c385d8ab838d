import React, { useState } from 'react';
import { type GameData } from './gameTypes';
import CrazyEightsGame from './CrazyEightsGame';

interface CrazyEightsGameWrapperProps {
  game: GameData;
  onBack: () => void;
}

const CrazyEightsGameWrapper: React.FC<CrazyEightsGameWrapperProps> = ({ game, onBack }) => {
  const [gameResult, setGameResult] = useState(null);

  const handleGameEnd = (result: any) => {
    setGameResult(result);
    // Handle game end logic here
    console.log('Crazy Eights game ended:', result);
  };

  return (
    <CrazyEightsGame
      onBack={onBack}
      onGameEnd={handleGameEnd}
    />
  );
};

export default CrazyEightsGameWrapper;
