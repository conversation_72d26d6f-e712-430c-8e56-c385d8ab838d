import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import ChessGameWrapper from './ChessGameWrapper';
import CheckersGameWrapper from './CheckersGameWrapper';
import RockPaperScissorsGame from './RockPaperScissorsGame';
import MobileRockPaperScissorsGame from './MobileRockPaperScissorsGame';
import HighlightHeroGameWrapper from './HighlightHeroGameWrapper';
import BlurDetectiveGameWrapper from './BlurDetectiveGameWrapper';
import WordJumbleGameWrapper from './WordJumbleGameWrapper';
import QuizArenaGameWrapper from './QuizArenaGameWrapper';
import CrazyEightsGameWrapper from './CrazyEightsGameWrapper';
import Breadcrumb, { generateGameBreadcrumbs } from '@/components/shared/Breadcrumb';

interface GameSessionRouterProps {
  className?: string;
}

const GameSessionRouter: React.FC<GameSessionRouterProps> = ({ className = '' }) => {
  const { gameSlug, sessionId } = useParams<{ gameSlug: string; sessionId: string }>();
  const navigate = useNavigate();
  const [isMobile, setIsMobile] = React.useState(false);

  React.useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const handleBack = () => {
    navigate(`/games/${gameSlug}`);
  };

  if (!gameSlug || !sessionId) {
    return (
      <div className="min-h-screen bg-slate-950 pt-16">
        <div className="container mx-auto px-4 py-6">
          <div className="text-center text-red-400">
            Error: Missing game slug or session ID
          </div>
        </div>
      </div>
    );
  }

  // Generate breadcrumbs for the session page
  const breadcrumbs = generateGameBreadcrumbs(gameSlug, sessionId);

  // Route to appropriate game component based on game slug
  const renderGameComponent = () => {
    switch (gameSlug) {
      case 'chess':
        return <ChessGameWrapper onBack={handleBack} />;

      case 'checkers':
        // For checkers, we need to create mock game data
        const checkersGame = {
          id: sessionId,
          name: 'Checkers Game',
          type: 'checkers',
          players: {
            player1: { id: '1', name: 'Player 1' },
            player2: { id: '2', name: 'Player 2' }
          },
          currentRound: 1,
          totalRounds: 1,
          prize: 100,
          viewers: 0,
          status: 'live' as const
        };
        return <CheckersGameWrapper game={checkersGame} onBack={handleBack} />;

      case 'rock-paper-scissors':
        return isMobile ? (
          <MobileRockPaperScissorsGame
            onBack={handleBack}
            gameId={sessionId}
            wagerAmount={50}
          />
        ) : (
          <RockPaperScissorsGame />
        );

      case 'highlight-hero':
        return <HighlightHeroGameWrapper isMobile={isMobile} onBack={handleBack} />;

      case 'blur-detective':
        return <BlurDetectiveGameWrapper onBack={handleBack} />;

      case 'word-jumble':
        return <WordJumbleGameWrapper onBack={handleBack} />;

      case 'quiz-arena':
        // For quiz arena, we need to create mock game data
        const quizArenaGame = {
          id: sessionId,
          name: 'Quiz Arena Game',
          type: 'quiz-arena',
          players: {
            player1: { id: '1', name: 'Player 1' },
            player2: { id: '2', name: 'Player 2' }
          },
          currentRound: 1,
          totalRounds: 15,
          prize: 1000000,
          viewers: 0,
          status: 'live' as const
        };
        return <QuizArenaGameWrapper game={quizArenaGame} onBack={handleBack} />;

      case 'crazy-eights':
        // For crazy eights, we need to create mock game data
        const crazyEightsGame = {
          id: sessionId,
          name: 'Crazy Eights Game',
          type: 'crazy-eights',
          players: {
            player1: { id: '1', name: 'Player 1' },
            player2: { id: '2', name: 'Player 2' },
            player3: { id: '3', name: 'Player 3' },
            player4: { id: '4', name: 'Player 4' }
          },
          currentRound: 1,
          totalRounds: 1,
          prize: 100,
          viewers: 0,
          status: 'live' as const
        };
        return <CrazyEightsGameWrapper game={crazyEightsGame} onBack={handleBack} />;

      default:
        return (
          <div className="min-h-screen bg-slate-950 pt-16">
            <div className="container mx-auto px-4 py-6">
              <Breadcrumb items={breadcrumbs} className="mb-6" />
              <div className="text-center">
                <div className="text-red-400 mb-4">
                  Game type "{gameSlug}" not supported yet
                </div>
                <button
                  onClick={handleBack}
                  className="text-blue-400 hover:text-blue-300"
                >
                  ← Back to {gameSlug} instances
                </button>
              </div>
            </div>
          </div>
        );
    }
  };

  return (
    <div className={`${className}`}>
      {renderGameComponent()}
    </div>
  );
};

export default GameSessionRouter;
