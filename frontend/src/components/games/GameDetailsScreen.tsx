import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Card } from '@/components/ui/card';
import {
  ArrowLeft,
  Users,
  DollarSign,
  Clock,
  Eye,
  Star,
  PlayCircle,
  Radio,
  Trophy,
  Timer,
  Zap,
  Shield,
  Crown,
  Layers,
  Grid3x3,
  Hand,
  Brain,
  Plus,
  Film
} from 'lucide-react';

interface GameDetailsScreenProps {
  gameId: string;
  gameName: string;
  gameType: string;
  onBack: () => void;
  onJoinGame: (instanceId: string) => void;
}

interface GameInstance {
  id: string;
  players: {
    player1: { id: string; name: string; avatar?: string };
    player2?: { id: string; name: string; avatar?: string };
  };
  stake: number;
  status: 'waiting' | 'live' | 'finished';
  currentTurn?: string;
  timeRemaining?: string;
  viewers: number;
  boardState?: any; // This would be the actual game state for thumbnail preview
  playerCount?: number; // For games like blur-detective that support more than 2 players
  maxPlayers?: number;
}

const GameDetailsScreen: React.FC<GameDetailsScreenProps> = ({
  gameId,
  gameName,
  gameType,
  onBack,
  onJoinGame
}) => {
  const [filter, setFilter] = useState<'all' | 'waiting' | 'live'>('all');
  const [isFavorited, setIsFavorited] = useState(false);

  // Generate appropriate game instances based on game type
  const getGameInstances = (): GameInstance[] => {
    // For blur-detective, show multiplayer games
    if (gameType === 'blur-detective') {
      return [
        {
          id: '1',
          players: {
            player1: { id: '1', name: 'QuizMaster' },
          },
          stake: 100,
          status: 'waiting',
          viewers: 45,
          playerCount: 3,
          maxPlayers: 8
        },
        {
          id: '2',
          players: {
            player1: { id: '2', name: 'DetectivePro' },
            player2: { id: '3', name: 'CelebExpert' }
          },
          stake: 50,
          status: 'live',
          timeRemaining: '3:20',
          viewers: 234,
          playerCount: 5,
          maxPlayers: 6
        },
        {
          id: '3',
          players: {
            player1: { id: '4', name: 'BlurHunter' },
          },
          stake: 200,
          status: 'waiting',
          viewers: 78,
          playerCount: 1,
          maxPlayers: 8
        },
        {
          id: '4',
          players: {
            player1: { id: '5', name: 'ImageSleuth' },
            player2: { id: '6', name: 'PixelDetective' }
          },
          stake: 150,
          status: 'live',
          timeRemaining: '1:45',
          viewers: 567,
          playerCount: 8,
          maxPlayers: 8
        },
        {
          id: '5',
          players: {
            player1: { id: '7', name: 'CelebSpotter' },
          },
          stake: 25,
          status: 'waiting',
          viewers: 12,
          playerCount: 2,
          maxPlayers: 4
        }
      ];
    }
    
    // Default instances for other games
    return [
    {
      id: '1',
      players: {
        player1: { id: '1', name: 'AlexTheGreat' },
        player2: { id: '2', name: 'ChessMaster99' }
      },
      stake: 50,
      status: 'live',
      currentTurn: '1',
      timeRemaining: '2:45',
      viewers: 234
    },
    {
      id: '2',
      players: {
        player1: { id: '3', name: 'ProGamer' },
      },
      stake: 25,
      status: 'waiting',
      viewers: 12
    },
    {
      id: '3',
      players: {
        player1: { id: '4', name: 'QuickDraw' },
        player2: { id: '5', name: 'SpeedDemon' }
      },
      stake: 100,
      status: 'live',
      currentTurn: '2',
      timeRemaining: '1:15',
      viewers: 567
    },
    {
      id: '4',
      players: {
        player1: { id: '6', name: 'Strategist' },
      },
      stake: 75,
      status: 'waiting',
      viewers: 45
    },
    {
      id: '5',
      players: {
        player1: { id: '7', name: 'NightHawk' },
        player2: { id: '8', name: 'Phoenix' }
      },
      stake: 200,
      status: 'live',
      currentTurn: '1',
      timeRemaining: '4:20',
      viewers: 892
    },
    {
      id: '6',
      players: {
        player1: { id: '9', name: 'Warrior' },
      },
      stake: 30,
      status: 'waiting',
      viewers: 8
    },
    {
      id: '7',
      players: {
        player1: { id: '10', name: 'TacticalGenius' },
        player2: { id: '11', name: 'MindReader' }
      },
      stake: 150,
      status: 'live',
      currentTurn: '2',
      timeRemaining: '0:45',
      viewers: 445
    },
    {
      id: '8',
      players: {
        player1: { id: '12', name: 'Rookie123' },
      },
      stake: 10,
      status: 'waiting',
      viewers: 3
    },
    {
      id: '9',
      players: {
        player1: { id: '13', name: 'ElitePlayer' },
        player2: { id: '14', name: 'Champion' }
      },
      stake: 500,
      status: 'live',
      currentTurn: '1',
      timeRemaining: '3:30',
      viewers: 1234
    }
    ];
  };
  
  const gameInstances = getGameInstances();

  const filteredInstances = gameInstances.filter(instance => {
    if (filter === 'all') return true;
    return instance.status === filter;
  });

  const getGameIcon = () => {
    switch(gameType) {
      case 'chess':
      case 'chess-blitz':
        return <Crown className="h-5 w-5" />;
      case 'checkers':
        return <Grid3x3 className="h-5 w-5" />;
      case 'rock-paper-scissors':
        return <Hand className="h-5 w-5" />;
      case 'highlight-hero':
        return <Film className="h-5 w-5" />;
      case 'blur-detective':
        return <Eye className="h-5 w-5" />;
      case 'trivia':
      case 'speed-trivia':
        return <Brain className="h-5 w-5" />;
      case 'strategy':
        return <Layers className="h-5 w-5" />;
      default:
        return <Zap className="h-5 w-5" />;
    }
  };

  // Sample chess positions for thumbnails
  const getChessPieces = (instanceId: string) => {
    // Different positions for different games to show variety
    const positions: { [key: string]: { square: number; piece: string; color: 'white' | 'black' }[] } = {
      '1': [
        { square: 0, piece: 'rook', color: 'black' },
        { square: 4, piece: 'king', color: 'black' },
        { square: 7, piece: 'rook', color: 'black' },
        { square: 12, piece: 'pawn', color: 'black' },
        { square: 35, piece: 'knight', color: 'white' },
        { square: 52, piece: 'pawn', color: 'white' },
        { square: 60, piece: 'king', color: 'white' },
      ],
      '3': [
        { square: 3, piece: 'queen', color: 'black' },
        { square: 6, piece: 'king', color: 'black' },
        { square: 27, piece: 'bishop', color: 'black' },
        { square: 36, piece: 'pawn', color: 'white' },
        { square: 42, piece: 'knight', color: 'white' },
        { square: 61, piece: 'king', color: 'white' },
      ],
      '5': [
        { square: 2, piece: 'king', color: 'black' },
        { square: 18, piece: 'queen', color: 'black' },
        { square: 45, piece: 'queen', color: 'white' },
        { square: 58, piece: 'king', color: 'white' },
      ],
      '7': [
        { square: 11, piece: 'king', color: 'black' },
        { square: 19, piece: 'rook', color: 'black' },
        { square: 43, piece: 'rook', color: 'white' },
        { square: 51, piece: 'king', color: 'white' },
      ],
      '9': [
        { square: 4, piece: 'king', color: 'black' },
        { square: 13, piece: 'pawn', color: 'black' },
        { square: 14, piece: 'pawn', color: 'black' },
        { square: 20, piece: 'pawn', color: 'black' },
        { square: 27, piece: 'queen', color: 'white' },
        { square: 43, piece: 'pawn', color: 'white' },
        { square: 44, piece: 'pawn', color: 'white' },
        { square: 60, piece: 'king', color: 'white' },
      ]
    };
    return positions[instanceId] || [];
  };

  const getPieceImage = (piece: string, color: 'white' | 'black') => {
    return `/images/chess/${color}-${piece}.png`;
  };

  const renderGameThumbnail = (instance: GameInstance) => {
    const isChess = gameType === 'chess' || gameType === 'chess-blitz';
    const isCheckers = gameType === 'checkers';
    const isHighlightHero = gameType === 'highlight-hero';
    const isBlurDetective = gameType === 'blur-detective';
    
    if (isBlurDetective) {
      return (
        <div className="relative h-full w-full bg-gradient-to-br from-purple-900/30 to-blue-900/30 rounded overflow-hidden flex items-center justify-center">
          <div className="absolute inset-0 bg-black/20 backdrop-blur-sm" />
          <Eye className="h-12 sm:h-8 w-12 sm:w-8 text-blue-400 z-10" />
          {instance.status === 'live' && (
            <div className="absolute top-1 right-1 z-20">
              <Badge className="bg-green-500 text-white text-[9px] h-3.5 px-1 animate-pulse">
                <Radio className="h-2 w-2 mr-0.5" />
                LIVE
              </Badge>
            </div>
          )}
          <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-2">
            <p className="text-[10px] text-white/80 text-center">Celebrity Detective</p>
          </div>
        </div>
      );
    }
    
    if (isHighlightHero) {
      return (
        <div className="relative h-full w-full bg-gradient-to-br from-purple-900/30 to-pink-900/30 rounded overflow-hidden flex items-center justify-center">
          <div className="absolute inset-0 bg-black/20" />
          <Film className="h-12 sm:h-8 w-12 sm:w-8 text-purple-400 z-10" />
          {instance.status === 'live' && (
            <div className="absolute top-1 right-1 z-20">
              <Badge className="bg-green-500 text-white text-[9px] h-3.5 px-1 animate-pulse">
                <Radio className="h-2 w-2 mr-0.5" />
                LIVE
              </Badge>
            </div>
          )}
          <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-2">
            <p className="text-[10px] text-white/80 text-center">Video Quiz</p>
          </div>
        </div>
      );
    }
    
    if (isChess) {
      const pieces = getChessPieces(instance.id);
      return (
        <div className="relative h-full w-full bg-slate-800 rounded overflow-hidden">
          <div className="absolute inset-0 grid grid-cols-8 grid-rows-8">
            {Array.from({ length: 64 }).map((_, i) => {
              const row = Math.floor(i / 8);
              const col = i % 8;
              const isLight = (row + col) % 2 === 0;
              const piece = pieces.find(p => p.square === i);
              
              return (
                <div
                  key={i}
                  className={`relative ${isLight ? 'bg-slate-700' : 'bg-slate-600'}`}
                >
                  {piece && (
                    <img
                      src={getPieceImage(piece.piece, piece.color)}
                      alt={`${piece.color} ${piece.piece}`}
                      className="absolute inset-0 w-full h-full p-0.5"
                    />
                  )}
                </div>
              );
            })}
          </div>
          {instance.status === 'live' && (
            <div className="absolute top-1 right-1">
              <Badge className="bg-green-500 text-white text-[9px] h-3.5 px-1 animate-pulse">
                <Radio className="h-2 w-2 mr-0.5" />
                LIVE
              </Badge>
            </div>
          )}
        </div>
      );
    }
    
    if (isCheckers) {
      return (
        <div className="relative h-full w-full bg-slate-800 rounded overflow-hidden">
          <div className="absolute inset-0 grid grid-cols-8 grid-rows-8">
            {Array.from({ length: 64 }).map((_, i) => {
              const row = Math.floor(i / 8);
              const col = i % 8;
              const isPlayable = (row + col) % 2 === 1;
              
              return (
                <div
                  key={i}
                  className={`relative ${isPlayable ? 'bg-slate-600' : 'bg-slate-700'}`}
                >
                  {/* Sample checker pieces */}
                  {isPlayable && i < 24 && i % 4 !== 3 && (
                    <div className="absolute inset-1 rounded-full bg-red-600 border border-red-700" />
                  )}
                  {isPlayable && i > 39 && i % 4 !== 0 && (
                    <div className="absolute inset-1 rounded-full bg-slate-900 border border-slate-800" />
                  )}
                </div>
              );
            })}
          </div>
          {instance.status === 'live' && (
            <div className="absolute top-1 right-1">
              <Badge className="bg-green-500 text-white text-[9px] h-3.5 px-1 animate-pulse">
                <Radio className="h-2 w-2 mr-0.5" />
                LIVE
              </Badge>
            </div>
          )}
        </div>
      );
    }

    return (
      <div className="relative h-full w-full bg-gradient-to-br from-slate-800 to-slate-700 rounded flex items-center justify-center">
        <div className="text-slate-600">
          {getGameIcon()}
        </div>
        {instance.status === 'live' && (
          <div className="absolute top-1 right-1">
            <Badge className="bg-green-500 text-white text-[9px] h-3.5 px-1 animate-pulse">
              <Radio className="h-2 w-2 mr-0.5" />
              LIVE
            </Badge>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-slate-950 text-white pt-16">
      {/* Header */}
      <div className="h-auto sm:h-[88px] bg-slate-900 border-b border-slate-800 p-4 sm:p-3">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center">
            <Button
              variant="ghost"
              size="sm"
              onClick={onBack}
              className="h-7 w-7 p-0 mr-2"
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <div className="flex items-center">
              <div className="h-10 w-10 sm:h-8 sm:w-8 rounded bg-gradient-to-br from-slate-700 to-slate-600 flex items-center justify-center mr-3 sm:mr-2 flex-shrink-0">
                {getGameIcon()}
              </div>
              <div className="flex-1">
                <h1 className="text-lg sm:text-base font-semibold">{gameName}</h1>
                <div className="flex items-center space-x-2 mt-1 sm:mt-0">
                  <Badge className="bg-slate-800 text-[10px] h-4 px-1.5">
                    <Users className="h-2.5 w-2.5 mr-0.5" />
                    {filteredInstances.length} active
                  </Badge>
                  <Badge className="bg-slate-800 text-[10px] h-4 px-1.5">
                    <Eye className="h-2.5 w-2.5 mr-0.5" />
                    {filteredInstances.reduce((sum, inst) => sum + inst.viewers, 0)} watching
                  </Badge>
                </div>
              </div>
            </div>
          </div>
          <Button
            variant={isFavorited ? "default" : "outline"}
            size="sm"
            onClick={() => setIsFavorited(!isFavorited)}
            className={`h-7 text-xs ${isFavorited ? "bg-yellow-600 hover:bg-yellow-700" : "border-slate-700"}`}
          >
            <Star className={`h-3 w-3 ${isFavorited ? "fill-current" : ""}`} />
            <span className="ml-1">{isFavorited ? "Favorited" : "Favorite"}</span>
          </Button>
        </div>

        {/* Filter Tabs */}
        <div className="flex space-x-1 overflow-x-auto mt-3 sm:mt-0">
          <Button
            variant={filter === 'all' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setFilter('all')}
            className={`h-6 text-xs px-3 ${filter === 'all' ? 'bg-purple-600' : 'text-slate-400 hover:text-white'}`}
          >
            All ({gameInstances.length})
          </Button>
          <Button
            variant={filter === 'waiting' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setFilter('waiting')}
            className={`h-6 text-xs px-3 ${filter === 'waiting' ? 'bg-purple-600' : 'text-slate-400 hover:text-white'}`}
          >
            Waiting ({gameInstances.filter(g => g.status === 'waiting').length})
          </Button>
          <Button
            variant={filter === 'live' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setFilter('live')}
            className={`h-6 text-xs px-3 ${filter === 'live' ? 'bg-purple-600' : 'text-slate-400 hover:text-white'}`}
          >
            Live ({gameInstances.filter(g => g.status === 'live').length})
          </Button>
        </div>
      </div>

      {/* Main Content Grid */}
      <div className="block sm:grid sm:grid-cols-12 gap-2 p-3 sm:p-2 h-auto sm:h-[calc(100vh-64px-88px)]">
        {/* Left Sidebar - Quick Stats - Hidden on mobile */}
        <div className="hidden sm:flex col-span-2 flex-col gap-2">
          <div className="bg-slate-900 border border-slate-800 rounded-sm p-2">
            <h3 className="text-xs font-medium text-white mb-2">Game Stats</h3>
            <div className="space-y-2">
              <div className="bg-slate-800 p-2 rounded-sm">
                <p className="text-[10px] text-slate-400">Avg. Stake</p>
                <p className="text-sm font-bold text-white">$62.50</p>
              </div>
              <div className="bg-slate-800 p-2 rounded-sm">
                <p className="text-[10px] text-slate-400">Your Wins</p>
                <p className="text-sm font-bold text-green-500">23</p>
              </div>
              <div className="bg-slate-800 p-2 rounded-sm">
                <p className="text-[10px] text-slate-400">Win Rate</p>
                <p className="text-sm font-bold text-white">68%</p>
              </div>
              <div className="bg-slate-800 p-2 rounded-sm">
                <p className="text-[10px] text-slate-400">Total Prize</p>
                <p className="text-sm font-bold text-yellow-500">$1,245</p>
              </div>
            </div>
          </div>

          {/* Create New Game Button */}
          <Button className="h-10 bg-gradient-to-r from-green-500 to-cyan-500 hover:opacity-90 text-sm">
            <Plus className="h-4 w-4 mr-1" />
            Create Game
          </Button>
        </div>

        {/* Main Content - Game Instances Grid */}
        <div className="col-span-12 sm:col-span-10 bg-slate-900 border border-slate-800 rounded-lg sm:rounded-sm overflow-hidden">
          <div className="grid grid-cols-2 sm:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-2 sm:gap-1.5 p-3 sm:p-2 overflow-auto h-full" style={{maxHeight: 'calc(100vh - 64px - 120px)'}}>
          {/* COMPACT DESKTOP CARDS - MORE COLUMNS */}
          {filteredInstances.map((instance) => (
            <div key={instance.id} className="bg-slate-900 border border-slate-800 rounded-lg sm:rounded-sm p-2 sm:p-1.5 hover:bg-slate-800/70 cursor-pointer transition-colors h-fit">
              {/* Game Thumbnail */}
              <div className="aspect-square mb-2 sm:mb-1">
                {renderGameThumbnail(instance)}
              </div>

              {/* Game Info */}
              <div>
                <div className="flex items-center justify-between mb-1.5 sm:mb-1">
                  <div className="flex items-center space-x-0.5">
                    <Badge className="bg-green-600 text-white text-[9px] sm:text-[8px] h-4 sm:h-3 px-1">
                      <DollarSign className="h-2 w-2 mr-0.5" />
                      ${instance.stake}
                    </Badge>
                    <Badge className="bg-slate-800 text-white text-[9px] sm:text-[8px] h-4 sm:h-3 px-1">
                      <Eye className="h-2 w-2 mr-0.5" />
                      {instance.viewers}
                    </Badge>
                  </div>
                  {instance.status === 'live' && instance.timeRemaining && (
                    <Badge className="bg-orange-600 text-white text-[9px] sm:text-[8px] h-4 sm:h-3 px-1">
                      <Clock className="h-2 w-2 mr-0.5" />
                      {instance.timeRemaining}
                    </Badge>
                  )}
                </div>

                {/* Players */}
                {gameType === 'blur-detective' && instance.playerCount !== undefined ? (
                  // Multiplayer display for blur-detective
                  <div className="mb-2 sm:mb-1">
                    <div className="flex items-center justify-between text-[9px] sm:text-[8px] text-white">
                      <div className="flex items-center">
                        <Users className="h-2.5 w-2.5 mr-0.5 text-slate-400" />
                        <span>{instance.playerCount}/{instance.maxPlayers || 8}</span>
                      </div>
                      {instance.status === 'waiting' && (
                        <Badge className="bg-blue-600 text-white text-[7px] h-2.5 px-0.5">
                          Open
                        </Badge>
                      )}
                    </div>
                    <div className="mt-0.5 text-[8px] sm:text-[7px] text-slate-400 truncate">
                      {instance.players.player1.name}
                    </div>
                  </div>
                ) : (
                  // Regular 2-player display
                  <div className="space-y-0.5 mb-2 sm:mb-1">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="h-3 w-3 rounded-full bg-slate-700 mr-0.5" />
                        <span className="text-[9px] sm:text-[8px] text-white truncate max-w-[60px]">{instance.players.player1.name}</span>
                      </div>
                      {instance.currentTurn === '1' && instance.status === 'live' && (
                        <Badge className="bg-green-500 text-white text-[6px] h-2 px-0.5 animate-pulse">
                          •
                        </Badge>
                      )}
                    </div>
                  {instance.players.player2 ? (
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="h-3 w-3 rounded-full bg-slate-700 mr-0.5" />
                        <span className="text-[9px] sm:text-[8px] text-white truncate max-w-[60px]">{instance.players.player2.name}</span>
                      </div>
                      {instance.currentTurn === '2' && instance.status === 'live' && (
                        <Badge className="bg-green-500 text-white text-[6px] h-2 px-0.5 animate-pulse">
                          •
                        </Badge>
                      )}
                    </div>
                  ) : (
                    <div className="flex items-center">
                      <div className="h-3 w-3 rounded-full border border-dashed border-slate-600 mr-0.5" />
                      <span className="text-[9px] sm:text-[8px] text-slate-500">Waiting...</span>
                    </div>
                  )}
                  </div>
                )}

                {/* Action Buttons */}
                {instance.status === 'waiting' ? (
                  <Button
                    className="w-full h-6 sm:h-5 text-[9px] sm:text-[8px] bg-gradient-to-r from-green-500 to-cyan-500 hover:opacity-90 rounded-sm font-medium"
                    onClick={(e) => {
                      e.stopPropagation();
                      onJoinGame(instance.id);
                    }}
                  >
                    <PlayCircle className="h-2.5 w-2.5 mr-0.5" />
                    Join
                  </Button>
                ) : (
                  <Button
                    variant="outline"
                    className="w-full h-6 sm:h-5 text-[9px] sm:text-[8px] border-slate-700 bg-slate-800 hover:bg-slate-700 rounded-sm font-medium"
                    onClick={(e) => {
                      e.stopPropagation();
                      onJoinGame(instance.id);
                    }}
                  >
                    <Eye className="h-2.5 w-2.5 mr-0.5" />
                    Watch
                  </Button>
                )}
              </div>
            </div>
          ))}
          </div>
        </div>
      </div>
      
      {/* Mobile Create Game Button */}
      <div className="sm:hidden fixed bottom-0 left-0 right-0 p-4 bg-slate-950 border-t border-slate-800">
        <Button className="w-full h-12 bg-gradient-to-r from-green-500 to-cyan-500 hover:opacity-90 text-sm font-medium">
          <Plus className="h-5 w-5 mr-2" />
          Create New Game
        </Button>
      </div>
    </div>
  );
};

export default GameDetailsScreen;