import React, { useState, useEffect, useRef } from 'react';
import { <PERSON>Lef<PERSON>, Clock } from 'lucide-react';
import { Button } from '@/components/ui/button';
import useAuth from '@/hooks/useAuth';
import useGameStore from '@/stores/gameStore';
import useWebSocket from '@/hooks/useWebSocket';

interface MobileChessGameProps {
  gameId: string;
  isTimedGame?: boolean;
  timeLimit?: number;
  wagerAmount?: number;
  onBack: () => void;
  onGameEnd: () => void;
}

// Initial board state
const INITIAL_BOARD: string[][] = [
  ['r', 'n', 'b', 'q', 'k', 'b', 'n', 'r'],
  ['p', 'p', 'p', 'p', 'p', 'p', 'p', 'p'],
  ['.', '.', '.', '.', '.', '.', '.', '.'],
  ['.', '.', '.', '.', '.', '.', '.', '.'],
  ['.', '.', '.', '.', '.', '.', '.', '.'],
  ['.', '.', '.', '.', '.', '.', '.', '.'],
  ['P', 'P', 'P', 'P', 'P', 'P', 'P', 'P'],
  ['R', 'N', 'B', 'Q', 'K', 'B', 'N', 'R']
];

// Piece images mapping
const PIECE_IMAGES: { [key: string]: string } = {
  'K': '/images/chess/white-king.png',
  'Q': '/images/chess/white-queen.png',
  'R': '/images/chess/white-rook.png',
  'B': '/images/chess/white-bishop.png',
  'N': '/images/chess/white-knight.png',
  'P': '/images/chess/white-pawn.png',
  'k': '/images/chess/black-king.png',
  'q': '/images/chess/black-queen.png',
  'r': '/images/chess/black-rook.png',
  'b': '/images/chess/black-bishop.png',
  'n': '/images/chess/black-knight.png',
  'p': '/images/chess/black-pawn.png'
};

interface ChessMove {
  from: number;
  to: number;
  piece: string;
  captured: string | null;
  promotion?: string;
}

const MobileChessGame: React.FC<MobileChessGameProps> = ({
  gameId,
  isTimedGame = false,
  timeLimit = 600,
  wagerAmount = 100,
  onBack,
  onGameEnd
}) => {
  const { user } = useAuth();
  const { currentGame } = useGameStore();
  const game = currentGame;

  // State management
  const [selectedSquare, setSelectedSquare] = useState<number | null>(null);
  const [validMoves, setValidMoves] = useState<number[]>([]);
  const [boardState, setBoardState] = useState(INITIAL_BOARD);
  const [moveHistory, setMoveHistory] = useState<ChessMove[]>([]);
  const [currentTurn, setCurrentTurn] = useState<'white' | 'black'>('white');
  const [isWhitePlayer, setIsWhitePlayer] = useState(true);
  const [capturedPieces, setCapturedPieces] = useState<{ white: string[], black: string[] }>({ white: [], black: [] });
  const [gameStatus, setGameStatus] = useState<'playing' | 'check' | 'checkmate' | 'stalemate' | 'draw'>('playing');

  // Timer states
  const [whiteTime, setWhiteTime] = useState(timeLimit);
  const [blackTime, setBlackTime] = useState(timeLimit);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // WebSocket
  const { connected, sendMessage } = useWebSocket();

  // Utility functions
  const getPiece = (board: string[][], index: number): string => {
    const rank = Math.floor(index / 8);
    const file = index % 8;
    return board[rank][file];
  };

  const setPiece = (board: string[][], index: number, piece: string): string[][] => {
    const newBoard = board.map(row => [...row]);
    const rank = Math.floor(index / 8);
    const file = index % 8;
    newBoard[rank][file] = piece;
    return newBoard;
  };

  const isWhitePiece = (piece: string): boolean => {
    return piece.toUpperCase() === piece && piece !== '.';
  };

  const isValidSquare = (rank: number, file: number): boolean => {
    return rank >= 0 && rank < 8 && file >= 0 && file < 8;
  };

  // Calculate valid moves for a piece
  const calculateValidMoves = (rank: number, file: number): number[] => {
    const piece = boardState[rank][file];
    if (piece === '.') return [];

    const isWhite = isWhitePiece(piece);
    const pieceType = piece.toUpperCase();

    let moves: number[] = [];

    switch (pieceType) {
      case 'P':
        moves = getPawnMoves(rank, file, isWhite);
        break;
      case 'R':
        moves = getRookMoves(rank, file, isWhite);
        break;
      case 'N':
        moves = getKnightMoves(rank, file, isWhite);
        break;
      case 'B':
        moves = getBishopMoves(rank, file, isWhite);
        break;
      case 'Q':
        moves = getQueenMoves(rank, file, isWhite);
        break;
      case 'K':
        moves = getKingMoves(rank, file, isWhite);
        break;
    }

    return moves.filter(move => isValidMove(rank * 8 + file, move));
  };

  // Get pawn moves
  const getPawnMoves = (rank: number, file: number, isWhite: boolean): number[] => {
    const moves: number[] = [];
    const direction = isWhite ? -1 : 1;
    const startRank = isWhite ? 6 : 1;

    // Move forward
    const newRank = rank + direction;
    if (isValidSquare(newRank, file) && getPiece(boardState, newRank * 8 + file) === '.') {
      moves.push(newRank * 8 + file);

      // Double move from start
      if (rank === startRank) {
        const doubleRank = rank + (2 * direction);
        if (getPiece(boardState, doubleRank * 8 + file) === '.') {
          moves.push(doubleRank * 8 + file);
        }
      }
    }

    // Capture diagonally
    for (const newFile of [file - 1, file + 1]) {
      if (isValidSquare(newRank, newFile)) {
        const targetPiece = getPiece(boardState, newRank * 8 + newFile);
        if (targetPiece !== '.' && isWhitePiece(targetPiece) !== isWhite) {
          moves.push(newRank * 8 + newFile);
        }
      }
    }

    return moves;
  };

  // Get rook moves
  const getRookMoves = (rank: number, file: number, isWhite: boolean): number[] => {
    const moves: number[] = [];
    const directions = [[0, 1], [0, -1], [1, 0], [-1, 0]];

    for (const [dr, df] of directions) {
      for (let i = 1; i < 8; i++) {
        const newRank = rank + (dr * i);
        const newFile = file + (df * i);
        
        if (!isValidSquare(newRank, newFile)) break;
        
        const targetPiece = getPiece(boardState, newRank * 8 + newFile);
        if (targetPiece === '.') {
          moves.push(newRank * 8 + newFile);
        } else {
          if (isWhitePiece(targetPiece) !== isWhite) {
            moves.push(newRank * 8 + newFile);
          }
          break;
        }
      }
    }

    return moves;
  };

  // Get knight moves
  const getKnightMoves = (rank: number, file: number, isWhite: boolean): number[] => {
    const moves: number[] = [];
    const jumps = [
      [-2, -1], [-2, 1], [-1, -2], [-1, 2],
      [1, -2], [1, 2], [2, -1], [2, 1]
    ];

    for (const [dr, df] of jumps) {
      const newRank = rank + dr;
      const newFile = file + df;
      
      if (isValidSquare(newRank, newFile)) {
        const targetPiece = getPiece(boardState, newRank * 8 + newFile);
        if (targetPiece === '.' || isWhitePiece(targetPiece) !== isWhite) {
          moves.push(newRank * 8 + newFile);
        }
      }
    }

    return moves;
  };

  // Get bishop moves
  const getBishopMoves = (rank: number, file: number, isWhite: boolean): number[] => {
    const moves: number[] = [];
    const directions = [[1, 1], [1, -1], [-1, 1], [-1, -1]];

    for (const [dr, df] of directions) {
      for (let i = 1; i < 8; i++) {
        const newRank = rank + (dr * i);
        const newFile = file + (df * i);
        
        if (!isValidSquare(newRank, newFile)) break;
        
        const targetPiece = getPiece(boardState, newRank * 8 + newFile);
        if (targetPiece === '.') {
          moves.push(newRank * 8 + newFile);
        } else {
          if (isWhitePiece(targetPiece) !== isWhite) {
            moves.push(newRank * 8 + newFile);
          }
          break;
        }
      }
    }

    return moves;
  };

  // Get queen moves
  const getQueenMoves = (rank: number, file: number, isWhite: boolean): number[] => {
    return [...getRookMoves(rank, file, isWhite), ...getBishopMoves(rank, file, isWhite)];
  };

  // Get king moves (simplified - no castling)
  const getKingMoves = (rank: number, file: number, isWhite: boolean): number[] => {
    const moves: number[] = [];
    const directions = [
      [-1, -1], [-1, 0], [-1, 1],
      [0, -1], [0, 1],
      [1, -1], [1, 0], [1, 1]
    ];

    for (const [dr, df] of directions) {
      const newRank = rank + dr;
      const newFile = file + df;
      
      if (isValidSquare(newRank, newFile)) {
        const targetPiece = getPiece(boardState, newRank * 8 + newFile);
        if (targetPiece === '.' || isWhitePiece(targetPiece) !== isWhite) {
          moves.push(newRank * 8 + newFile);
        }
      }
    }

    return moves;
  };

  // Check if move is valid (simplified - no check validation)
  const isValidMove = (from: number, to: number): boolean => {
    return true; // Simplified for mobile version
  };

  // Handle square click
  const handleSquareClick = (index: number) => {
    const piece = getPiece(boardState, index);
    const rank = Math.floor(index / 8);
    const file = index % 8;

    if (selectedSquare === null) {
      // Select a piece
      if (piece !== '.' && 
          ((currentTurn === 'white' && isWhitePiece(piece)) || 
           (currentTurn === 'black' && !isWhitePiece(piece)))) {
        setSelectedSquare(index);
        const moves = calculateValidMoves(rank, file);
        setValidMoves(moves);
      }
    } else {
      // Move selected piece
      if (validMoves.includes(index)) {
        makeMove(selectedSquare, index);
      } else if (index === selectedSquare) {
        // Deselect
        setSelectedSquare(null);
        setValidMoves([]);
      } else if (piece !== '.' && 
                 ((currentTurn === 'white' && isWhitePiece(piece)) || 
                  (currentTurn === 'black' && !isWhitePiece(piece)))) {
        // Select new piece
        setSelectedSquare(index);
        const moves = calculateValidMoves(rank, file);
        setValidMoves(moves);
      } else {
        // Invalid selection
        setSelectedSquare(null);
        setValidMoves([]);
      }
    }
  };

  // Make a move
  const makeMove = (from: number, to: number) => {
    const newBoard = [...boardState];
    const piece = getPiece(boardState, from);
    const capturedPiece = getPiece(boardState, to);

    // Update board
    const updatedBoard = setPiece(boardState, to, piece);
    const finalBoard = setPiece(updatedBoard, from, '.');
    setBoardState(finalBoard);

    // Update captured pieces
    if (capturedPiece !== '.') {
      const color = isWhitePiece(capturedPiece) ? 'white' : 'black';
      setCapturedPieces(prev => ({
        ...prev,
        [color]: [...prev[color], capturedPiece]
      }));
    }

    // Update move history
    setMoveHistory([...moveHistory, {
      from,
      to,
      piece,
      captured: capturedPiece !== '.' ? capturedPiece : null
    }]);

    // Clear selection
    setSelectedSquare(null);
    setValidMoves([]);

    // Switch turn
    setCurrentTurn(currentTurn === 'white' ? 'black' : 'white');

    // Send move via WebSocket
    if (connected) {
      sendMessage({
        type: 'move',
        gameId,
        from,
        to
      });
    }
  };

  // Timer effect
  useEffect(() => {
    if (isTimedGame && gameStatus === 'playing') {
      intervalRef.current = setInterval(() => {
        if (currentTurn === 'white') {
          setWhiteTime(prev => {
            if (prev <= 1) {
              setGameStatus('checkmate');
              return 0;
            }
            return prev - 1;
          });
        } else {
          setBlackTime(prev => {
            if (prev <= 1) {
              setGameStatus('checkmate');
              return 0;
            }
            return prev - 1;
          });
        }
      }, 1000);

      return () => {
        if (intervalRef.current) clearInterval(intervalRef.current);
      };
    }
  }, [currentTurn, isTimedGame, gameStatus]);

  // Render piece
  const renderPiece = (piece: string) => {
    if (piece === '.') return null;
    return (
      <img
        src={PIECE_IMAGES[piece]}
        alt={piece}
        className="w-full h-full object-contain"
      />
    );
  };

  // Render square
  const renderSquare = (index: number) => {
    const rank = Math.floor(index / 8);
    const file = index % 8;
    const piece = boardState[rank][file];
    const isLight = (rank + file) % 2 === 0;
    const isSelected = selectedSquare === index;
    const isValidMove = validMoves.includes(index);

    return (
      <div
        key={index}
        onClick={() => handleSquareClick(index)}
        className={`
          aspect-square flex items-center justify-center relative
          ${isLight ? 'bg-amber-100' : 'bg-amber-900'}
          ${isSelected ? 'ring-4 ring-yellow-400' : ''}
          ${isValidMove ? 'after:absolute after:w-3 after:h-3 after:bg-green-500 after:rounded-full after:opacity-70' : ''}
        `}
      >
        {renderPiece(piece)}
      </div>
    );
  };

  // Format time
  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className="h-[100dvh] bg-slate-950 flex flex-col">
      {/* Header */}
      <div className="bg-slate-900 border-b border-slate-800 p-3">
        <div className="flex items-center justify-between mb-2">
          <button
            onClick={onBack}
            className="text-slate-400 p-1"
          >
            <ArrowLeft className="h-5 w-5" />
          </button>
          <h1 className="text-white font-medium">Chess Game</h1>
          <div className="w-6" />
        </div>

        {/* Timer Display */}
        {isTimedGame && (
          <div className="flex justify-between items-center text-sm">
            <div className={`flex items-center gap-1 ${currentTurn === 'black' ? 'text-yellow-400' : 'text-slate-400'}`}>
              <Clock className="h-3 w-3" />
              <span>{formatTime(blackTime)}</span>
            </div>
            <span className="text-slate-500 text-xs">Turn: {currentTurn}</span>
            <div className={`flex items-center gap-1 ${currentTurn === 'white' ? 'text-yellow-400' : 'text-slate-400'}`}>
              <Clock className="h-3 w-3" />
              <span>{formatTime(whiteTime)}</span>
            </div>
          </div>
        )}
      </div>

      {/* Board */}
      <div className="flex-1 flex items-center justify-center p-4">
        <div className="w-full max-w-sm aspect-square">
          <div className="grid grid-cols-8 grid-rows-8 w-full h-full border-2 border-amber-700">
            {Array.from({ length: 64 }).map((_, index) => renderSquare(index))}
          </div>
        </div>
      </div>

      {/* Game Info */}
      <div className="bg-slate-900 border-t border-slate-800 p-3">
        <div className="flex justify-between items-center mb-2">
          <div>
            <div className="text-sm text-slate-400">Wager</div>
            <div className="text-white font-medium">${wagerAmount}</div>
          </div>
          <div className="text-right">
            <div className="text-sm text-slate-400">Status</div>
            <div className="text-white font-medium capitalize">
              {gameStatus === 'playing' ? currentTurn + "'s turn" : gameStatus}
            </div>
          </div>
        </div>

        {/* Captured Pieces */}
        <div className="grid grid-cols-2 gap-2 mt-3">
          <div className="bg-slate-800 rounded p-2">
            <div className="text-xs text-slate-400 mb-1">White captured</div>
            <div className="flex gap-1">
              {capturedPieces.white.map((piece, i) => (
                <div key={i} className="w-5 h-5">
                  {renderPiece(piece)}
                </div>
              ))}
            </div>
          </div>
          <div className="bg-slate-800 rounded p-2">
            <div className="text-xs text-slate-400 mb-1">Black captured</div>
            <div className="flex gap-1">
              {capturedPieces.black.map((piece, i) => (
                <div key={i} className="w-5 h-5">
                  {renderPiece(piece)}
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Game Actions */}
        {gameStatus !== 'playing' && (
          <Button 
            onClick={onGameEnd}
            className="w-full mt-3 bg-yellow-500 hover:bg-yellow-600 text-slate-900"
          >
            Return to Lobby
          </Button>
        )}
      </div>
    </div>
  );
};

export default MobileChessGame;