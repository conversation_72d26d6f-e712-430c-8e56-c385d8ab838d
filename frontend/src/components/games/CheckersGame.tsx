import React, { useState, useEffect, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  MessageSquare,
  Users,
  Crown,
  AlertCircle,
  RotateCcw,
  ChevronDown,
  X,
  Send
} from 'lucide-react';

const BOARD_SIZE = 8;

const PIECE_TYPE = {
  NONE: 0,
  RED: 1,
  RED_KING: 2,
  BLACK: 3,
  BLACK_KING: 4
};

const PLAYER = {
  RED: 'red',
  BLACK: 'black'
};

const GAME_STATUS = {
  WAITING: 'waiting',
  PLAYING: 'playing',
  RED_WON: 'red_won',
  BLACK_WON: 'black_won',
  DRAW: 'draw'
};

interface CheckersGameProps {
  isTimedGame?: boolean;
  timeLimit?: number; // in seconds
  wagerAmount?: number;
  onGameComplete?: (winner: string, payout: number) => void;
  onBack?: () => void;
}

// Checkers Game Component
const CheckersGame: React.FC<CheckersGameProps> = ({ 
  isTimedGame = false, 
  timeLimit = 300, // 5 minutes default
  wagerAmount = 100,
  onGameComplete: _onGameComplete,
  onBack
}) => {
  // Initialize board immediately
  const initBoard = () => {
    const newBoard = Array(BOARD_SIZE).fill(null).map(() => Array(BOARD_SIZE).fill(PIECE_TYPE.NONE));
    
    // Place initial pieces
    for (let row = 0; row < BOARD_SIZE; row++) {
      for (let col = 0; col < BOARD_SIZE; col++) {
        if ((row + col) % 2 === 1) {
          if (row < 3) {
            newBoard[row][col] = PIECE_TYPE.BLACK;
          } else if (row > 4) {
            newBoard[row][col] = PIECE_TYPE.RED;
          }
        }
      }
    }
    
    return newBoard;
  };

  const [board, setBoard] = useState<number[][]>(initBoard());
  const [currentPlayer, setCurrentPlayer] = useState(PLAYER.RED);
  const [selectedPiece, setSelectedPiece] = useState<{ row: number; col: number } | null>(null);
  const [validMoves, setValidMoves] = useState<any[]>([]);
  const [capturedPieces, setCapturedPieces] = useState({ red: 0, black: 0 });
  const [, setGameStatus] = useState(GAME_STATUS.PLAYING);
  const [gameTime, setGameTime] = useState(0);
  const [, setTimer] = useState<NodeJS.Timeout | null>(null);
  const [showChat, setShowChat] = useState(true);
  const [showRules, setShowRules] = useState(false);
  
  // Timer state for timed games
  const [redTimeLeft, setRedTimeLeft] = useState(timeLimit);
  const [blackTimeLeft, setBlackTimeLeft] = useState(timeLimit);

  // Chat state
  const [messages, setMessages] = useState([
    { text: "Game started! Red player goes first.", type: "system" },
    { text: "Welcome to Checkers Arena!", type: "system" }
  ]);
  const [messageInput, setMessageInput] = useState('');

  // Mock spectator data
  const [spectators] = useState([
    { id: 1, name: 'JohnDoe', avatar: '👤' },
    { id: 2, name: 'SarahM', avatar: '👧' },
    { id: 3, name: 'BetMaster', avatar: '🎲' }
  ]);

  // Mock side bets
  const [sideBets] = useState([
    { user: 'CryptoKing', amount: 25, side: 'red' },
    { user: 'GamingPro', amount: 50, side: 'black' },
    { user: 'RiskTaker', amount: 30, side: 'red' }
  ]);

  // Start game timer
  useEffect(() => {
    const interval = setInterval(() => {
      setGameTime(prev => prev + 1);
    }, 1000);
    setTimer(interval);

    return () => {
      if (interval) clearInterval(interval);
    };
  }, []);

  // Get valid moves for a piece
  const getValidMoves = useCallback((row: number, col: number, boardState = board) => {
    const moves: any[] = [];
    const piece = boardState[row][col];

    if (piece === PIECE_TYPE.NONE) {
      return moves;
    }

    const isRed = piece === PIECE_TYPE.RED || piece === PIECE_TYPE.RED_KING;
    const isKing = piece === PIECE_TYPE.RED_KING || piece === PIECE_TYPE.BLACK_KING;

    // Directions for movement
    const directions = [];
    if (isRed || isKing) {
      directions.push({ rowDelta: -1, colDelta: -1 }); // Up-left
      directions.push({ rowDelta: -1, colDelta: 1 });  // Up-right
    }
    if (!isRed || isKing) {
      directions.push({ rowDelta: 1, colDelta: -1 });  // Down-left
      directions.push({ rowDelta: 1, colDelta: 1 });   // Down-right
    }

    // Check for jumps first
    const jumps: any[] = [];
    directions.forEach(dir => {
      const jumpRow = row + 2 * dir.rowDelta;
      const jumpCol = col + 2 * dir.colDelta;
      const adjRow = row + dir.rowDelta;
      const adjCol = col + dir.colDelta;

      if (jumpRow >= 0 && jumpRow < BOARD_SIZE && jumpCol >= 0 && jumpCol < BOARD_SIZE) {
        const adjPiece = boardState[adjRow]?.[adjCol];
        const jumpPos = boardState[jumpRow]?.[jumpCol];

        // Check if we can jump over opponent's piece
        if (jumpPos === PIECE_TYPE.NONE &&
            ((isRed && (adjPiece === PIECE_TYPE.BLACK || adjPiece === PIECE_TYPE.BLACK_KING)) ||
             (!isRed && (adjPiece === PIECE_TYPE.RED || adjPiece === PIECE_TYPE.RED_KING)))) {
          jumps.push({
            row: jumpRow,
            col: jumpCol,
            isJump: true,
            captureRow: adjRow,
            captureCol: adjCol
          });
        }
      }
    });

    // If jumps are available, only return jumps (forced captures)
    if (jumps.length > 0) {
      return jumps;
    }

    // Otherwise, check for regular moves
    directions.forEach(dir => {
      const newRow = row + dir.rowDelta;
      const newCol = col + dir.colDelta;

      if (newRow >= 0 && newRow < BOARD_SIZE && newCol >= 0 && newCol < BOARD_SIZE &&
          boardState[newRow]?.[newCol] === PIECE_TYPE.NONE) {
        moves.push({
          row: newRow,
          col: newCol,
          isJump: false
        });
      }
    });

    return moves;
  }, [board]);

  // Handle square click
  const handleSquareClick = (row: number, col: number) => {
    const piece = board[row][col];
    
    // If a piece is already selected
    if (selectedPiece) {
      // Check if clicking on a valid move square
      const isValidMove = validMoves.some(move => move.row === row && move.col === col);
      
      if (isValidMove) {
        // Make the move
        makeMove(selectedPiece.row, selectedPiece.col, row, col);
      } else if (piece !== PIECE_TYPE.NONE) {
        // Clicking on another piece - select it if it's the current player's piece
        selectPiece(row, col);
      } else {
        // Clicking on empty square that's not a valid move - deselect
        setSelectedPiece(null);
        setValidMoves([]);
      }
    } else {
      // No piece selected - try to select this one
      if (piece !== PIECE_TYPE.NONE) {
        selectPiece(row, col);
      }
    }
  };

  // Select a piece
  const selectPiece = (row: number, col: number) => {
    const piece = board[row][col];
    const isRedPiece = piece === PIECE_TYPE.RED || piece === PIECE_TYPE.RED_KING;
    const isBlackPiece = piece === PIECE_TYPE.BLACK || piece === PIECE_TYPE.BLACK_KING;

    // Check if it's the current player's piece
    if ((currentPlayer === PLAYER.RED && isRedPiece) ||
        (currentPlayer === PLAYER.BLACK && isBlackPiece)) {
      const moves = getValidMoves(row, col);
      setSelectedPiece({ row, col });
      setValidMoves(moves);
    }
  };

  // Make a move
  const makeMove = (fromRow: number, fromCol: number, toRow: number, toCol: number) => {
    const move = validMoves.find(m => m.row === toRow && m.col === toCol);
    if (!move) return;

    const newBoard = board.map(row => [...row]);
    const piece = newBoard[fromRow][fromCol];

    // Move the piece
    newBoard[fromRow][fromCol] = PIECE_TYPE.NONE;
    newBoard[toRow][toCol] = piece;

    // Handle capture
    if (move.isJump) {
      const capturedPiece = newBoard[move.captureRow][move.captureCol];
      newBoard[move.captureRow][move.captureCol] = PIECE_TYPE.NONE;

      // Update captured pieces count
      const newCapturedPieces = { ...capturedPieces };
      if (capturedPiece === PIECE_TYPE.RED || capturedPiece === PIECE_TYPE.RED_KING) {
        newCapturedPieces.red += 1;
      } else {
        newCapturedPieces.black += 1;
      }
      setCapturedPieces(newCapturedPieces);
      
      // Add capture message
      addMessage(`${currentPlayer === PLAYER.RED ? 'Red' : 'Black'} captured a piece!`, 'system');
    }

    // Check for king promotion
    if ((currentPlayer === PLAYER.RED && toRow === 0 && piece === PIECE_TYPE.RED) ||
        (currentPlayer === PLAYER.BLACK && toRow === BOARD_SIZE - 1 && piece === PIECE_TYPE.BLACK)) {
      newBoard[toRow][toCol] = currentPlayer === PLAYER.RED ? PIECE_TYPE.RED_KING : PIECE_TYPE.BLACK_KING;
      addMessage(`${currentPlayer === PLAYER.RED ? 'Red' : 'Black'} piece promoted to King!`, 'system');
    }

    setBoard(newBoard);

    // Check for additional jumps
    if (move.isJump) {
      const additionalJumps = getValidMoves(toRow, toCol, newBoard).filter(m => m.isJump);
      if (additionalJumps.length > 0) {
        // Must continue jumping
        setSelectedPiece({ row: toRow, col: toCol });
        setValidMoves(additionalJumps);
        return;
      }
    }

    // End turn
    setSelectedPiece(null);
    setValidMoves([]);
    const nextPlayer = currentPlayer === PLAYER.RED ? PLAYER.BLACK : PLAYER.RED;
    setCurrentPlayer(nextPlayer);
    addMessage(`${nextPlayer === PLAYER.RED ? 'Red' : 'Black'} player's turn`, 'system');
  };

  // Add message to chat
  const addMessage = (text: string, type = 'player') => {
    setMessages(prev => [...prev, { text, type }]);
  };

  // Send chat message
  const sendMessage = () => {
    if (messageInput.trim()) {
      addMessage(`Player: ${messageInput}`, 'player');
      setMessageInput('');
    }
  };

  // Reset the game
  const resetGame = () => {
    setBoard(initBoard());
    setCurrentPlayer(PLAYER.RED);
    setSelectedPiece(null);
    setValidMoves([]);
    setCapturedPieces({ red: 0, black: 0 });
    setGameStatus(GAME_STATUS.PLAYING);
    setGameTime(0);
    setRedTimeLeft(timeLimit);
    setBlackTimeLeft(timeLimit);
    addMessage('Game has been reset!', 'system');
  };

  // Format time display
  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds < 10 ? '0' : ''}${remainingSeconds}`;
  };

  // Render the game board
  const renderBoard = () => {
    return (
      <div className="aspect-square bg-gray-800 border-2 border-gray-600 rounded-lg">
        <div className="grid grid-cols-8 gap-0 w-full h-full">
          {Array.from({ length: 64 }).map((_, index) => {
            const row = Math.floor(index / 8);
            const col = index % 8;
            const isLight = (row + col) % 2 === 0;
            const cell = board[row]?.[col] || PIECE_TYPE.NONE;
            const isSelected = selectedPiece && selectedPiece.row === row && selectedPiece.col === col;
            const isValidMove = validMoves.some(move => move.row === row && move.col === col);
            
            return (
              <div
                key={index}
                className={`relative flex items-center justify-center cursor-pointer
                  ${isLight ? 'bg-amber-100' : 'bg-amber-900'}
                  ${isSelected ? 'ring-4 ring-yellow-400' : ''}
                  ${isValidMove ? 'after:absolute after:w-4 after:h-4 after:bg-green-500 after:rounded-full after:opacity-60' : ''}
                  hover:brightness-110`}
                onClick={() => handleSquareClick(row, col)}
              >
                {/* Render piece */}
                {cell !== PIECE_TYPE.NONE && (
                  <div
                    className={`w-5/6 h-5/6 rounded-full border-2 shadow-lg
                      ${cell === PIECE_TYPE.RED ? 'bg-red-600 border-red-800' :
                        cell === PIECE_TYPE.BLACK ? 'bg-gray-800 border-gray-900' :
                        cell === PIECE_TYPE.RED_KING ? 'bg-red-600 border-red-800' :
                        'bg-gray-800 border-gray-900'}
                      ${isSelected ? 'transform scale-110' : ''}`}
                  >
                    {(cell === PIECE_TYPE.RED_KING || cell === PIECE_TYPE.BLACK_KING) && (
                      <div className="flex items-center justify-center h-full">
                        <Crown className="h-4 w-4 text-yellow-400" />
                      </div>
                    )}
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  // Main game layout
  return (
    <div className="w-full min-h-screen bg-slate-950 flex flex-col pt-16">
      <style>{`
        /* Custom scrollbar styles */
        .custom-scrollbar::-webkit-scrollbar {
          width: 6px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
          background: rgba(51, 65, 85, 0.3);
          border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
          background: rgba(100, 116, 139, 0.5);
          border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
          background: rgba(100, 116, 139, 0.7);
        }
      `}</style>
      
      {/* Main content area with padding to ensure visibility above footer */}
      <div className="flex-1 p-2 pb-16 overflow-hidden">
        <div className="grid grid-cols-12 gap-2 h-full">
        {/* Left Panel - Game Info */}
        <div className="col-span-3 flex flex-col gap-2 h-full overflow-hidden">
          <div className="flex-1 overflow-y-auto custom-scrollbar pr-1 space-y-2">
            {/* Game Status */}
            <div className="bg-slate-900 border border-slate-800 rounded-sm p-3">
              <div className="flex justify-between items-center mb-3">
                <h3 className="text-sm font-semibold text-white">Checkers Match</h3>
                <Badge className="bg-green-500 text-white text-xs">LIVE</Badge>
              </div>
              
              <div className="space-y-2">
                <div className="bg-slate-800 p-2 rounded">
                  <div className="text-xs text-slate-400">Current Turn</div>
                  <div className={`text-sm font-bold ${currentPlayer === PLAYER.RED ? 'text-red-500' : 'text-gray-300'}`}>
                    {currentPlayer === PLAYER.RED ? 'Red' : 'Black'} Player
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-2">
                  <div className="bg-slate-800 p-2 rounded">
                    <div className="text-xs text-slate-400">Wager</div>
                    <div className="text-sm font-bold text-white">${wagerAmount}</div>
                  </div>
                  <div className="bg-slate-800 p-2 rounded">
                    <div className="text-xs text-slate-400">Time</div>
                    <div className="text-sm font-bold text-white">{formatTime(gameTime)}</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Player Info */}
            <div className="bg-slate-900 border border-slate-800 rounded-sm p-3">
              <h3 className="text-sm font-semibold text-white mb-2">Players</h3>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                    <span className="text-sm text-white">RedMaster</span>
                  </div>
                  <span className="text-xs text-green-500">$50</span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-gray-800 rounded-full"></div>
                    <span className="text-sm text-white">BlackKnight</span>
                  </div>
                  <span className="text-xs text-green-500">$50</span>
                </div>
              </div>
            </div>

            {/* Captured Pieces */}
            <div className="bg-slate-900 border border-slate-800 rounded-sm p-3">
              <h3 className="text-sm font-semibold text-white mb-2">Captured</h3>
              <div className="space-y-1">
                <div className="flex justify-between">
                  <span className="text-xs text-red-500">Red Pieces:</span>
                  <span className="text-sm font-bold text-white">{capturedPieces.red}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-xs text-gray-400">Black Pieces:</span>
                  <span className="text-sm font-bold text-white">{capturedPieces.black}</span>
                </div>
              </div>
            </div>

            {/* Side Bets */}
            <div className="bg-slate-900 border border-slate-800 rounded-sm p-3">
              <h3 className="text-sm font-semibold text-white mb-2">Side Bets</h3>
              <div className="space-y-1 overflow-y-auto max-h-32 custom-scrollbar pr-1">
                {sideBets.map((bet, i) => (
                  <div key={i} className="flex justify-between text-xs">
                    <span className="text-slate-300">{bet.user}</span>
                    <span className={`font-bold ${bet.side === 'red' ? 'text-red-500' : 'text-gray-400'}`}>
                      ${bet.amount} on {bet.side}
                    </span>
                  </div>
                ))}
              </div>
              <div className="mt-2 pt-2 border-t border-slate-700">
                <div className="flex justify-between text-xs">
                  <span className="text-slate-400">Total Pool:</span>
                  <span className="text-green-500 font-bold">$205</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Center - Game Board */}
        <div className="col-span-6 flex flex-col items-center justify-center p-4 overflow-y-auto custom-scrollbar">
          <div className="w-full max-w-xl">
            {renderBoard()}
          </div>
          
          {/* Game Controls */}
          <div className="flex gap-3 mt-4">
            {onBack && (
              <Button onClick={onBack} size="sm" className="bg-blue-600 hover:bg-blue-700 text-xs">
                <ChevronDown className="h-3 w-3 mr-1 rotate-90" />
                Back
              </Button>
            )}
            <Button onClick={resetGame} size="sm" className="bg-green-600 hover:bg-green-700 text-xs">
              <RotateCcw className="h-3 w-3 mr-1" />
              Reset
            </Button>
            <Button 
              onClick={() => setShowRules(!showRules)}
              size="sm" 
              variant="outline"
              className="border-slate-700 text-xs"
            >
              <AlertCircle className="h-3 w-3 mr-1" />
              Rules
            </Button>
          </div>

          {/* Rules Panel */}
          {showRules && (
            <div className="mt-3 bg-slate-900 border border-slate-800 rounded-sm p-3 max-w-md">
              <div className="flex justify-between items-center mb-2">
                <h3 className="text-sm font-semibold text-white">Quick Rules</h3>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => setShowRules(false)}
                  className="h-6 w-6 p-0"
                >
                  <X className="h-3 w-3" />
                </Button>
              </div>
              <div className="text-xs text-slate-300 space-y-1">
                <p>• Click a piece to select, click square to move</p>
                <p>• Capture by jumping over opponent pieces</p>
                <p>• Kings (crowned) can move in all directions</p>
                <p>• Must capture if possible</p>
              </div>
            </div>
          )}
        </div>

        {/* Right Panel - Chat & Spectators */}
        <div className="col-span-3 flex flex-col gap-2 h-full overflow-hidden">
          {/* Fixed spectators and timer section */}
          <div className="flex flex-col gap-2">
            {/* Spectators */}
            <div className="bg-slate-900 border border-slate-800 rounded-sm p-3">
              <div className="flex justify-between items-center mb-2">
                <h3 className="text-sm font-semibold text-white">Spectators</h3>
                <div className="flex items-center gap-1">
                  <Users className="h-3 w-3 text-green-500" />
                  <span className="text-xs text-green-500">{spectators.length}</span>
                </div>
              </div>
              <div className="flex -space-x-2">
                {spectators.map((spectator) => (
                  <div
                    key={spectator.id}
                    className="w-8 h-8 rounded-full bg-slate-700 border-2 border-slate-900 flex items-center justify-center text-xs"
                    title={spectator.name}
                  >
                    {spectator.avatar}
                  </div>
                ))}
                <div className="w-8 h-8 rounded-full bg-slate-800 border-2 border-slate-900 flex items-center justify-center text-xs text-slate-400">
                  +{Math.max(0, spectators.length - 3)}
                </div>
              </div>
            </div>

            {/* Timer (if timed game) */}
            {isTimedGame && (
              <div className="bg-slate-900 border border-slate-800 rounded-sm p-3">
                <h3 className="text-sm font-semibold text-white mb-2">Time Remaining</h3>
                <div className="space-y-2">
                  <div>
                    <div className="flex justify-between text-xs mb-1">
                      <span className="text-red-500">Red</span>
                      <span className="text-white">{formatTime(redTimeLeft)}</span>
                    </div>
                    <Progress value={(redTimeLeft / timeLimit) * 100} className="h-1.5" />
                  </div>
                  <div>
                    <div className="flex justify-between text-xs mb-1">
                      <span className="text-gray-400">Black</span>
                      <span className="text-white">{formatTime(blackTimeLeft)}</span>
                    </div>
                    <Progress value={(blackTimeLeft / timeLimit) * 100} className="h-1.5" />
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Chat - takes remaining space */}
          <div className="bg-slate-900 border border-slate-800 rounded-sm flex-1 flex flex-col overflow-hidden min-h-0 relative">
            <div className="p-3 border-b border-slate-800 flex justify-between items-center shrink-0">
              <h3 className="text-sm font-semibold text-white">Chat</h3>
              <Button
                size="sm"
                variant="ghost"
                onClick={() => setShowChat(!showChat)}
                className="h-6 w-6 p-0"
              >
                {showChat ? <ChevronDown className="h-3 w-3" /> : <MessageSquare className="h-3 w-3" />}
              </Button>
            </div>
            
            {showChat && (
              <>
                <div className="flex-1 overflow-y-auto p-3 space-y-1 custom-scrollbar min-h-0 pb-16">
                  {messages.map((msg, i) => (
                    <div key={i} className={`text-xs ${msg.type === 'system' ? 'text-slate-400 italic' : 'text-white'}`}>
                      {msg.text}
                    </div>
                  ))}
                </div>
                
                <div className="absolute bottom-0 left-0 right-0 p-2 bg-slate-900 border-t border-slate-800">
                  <div className="flex gap-1">
                    <input
                      type="text"
                      value={messageInput}
                      onChange={(e) => setMessageInput(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
                      placeholder="Type a message..."
                      className="flex-1 bg-slate-800 text-white text-xs rounded px-2 py-1 focus:outline-none focus:ring-1 focus:ring-purple-500"
                    />
                    <Button
                      onClick={sendMessage}
                      size="sm"
                      className="bg-purple-600 hover:bg-purple-700 h-7 px-2"
                    >
                      <Send className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              </>
            )}
          </div>
        </div>
        </div>
      </div>
    </div>
  );
};

export default CheckersGame;