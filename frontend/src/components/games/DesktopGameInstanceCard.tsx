import React from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Users, 
  DollarSign, 
  Clock, 
  Eye, 
  PlayCircle,
  Radio
} from 'lucide-react';

interface DesktopGameInstanceCardProps {
  instance: {
    id: string;
    players: {
      player1: { id: string; name: string; avatar?: string };
      player2?: { id: string; name: string; avatar?: string };
    };
    stake: number;
    status: 'waiting' | 'live' | 'finished';
    currentTurn?: string;
    timeRemaining?: string;
    viewers: number;
    playerCount?: number;
    maxPlayers?: number;
  };
  gameType: string;
  onJoinGame: (instanceId: string) => void;
  renderThumbnail: (instance: any) => React.ReactNode;
}

const DesktopGameInstanceCard: React.FC<DesktopGameInstanceCardProps> = ({
  instance,
  gameType,
  onJoinGame,
  renderThumbnail
}) => {
  return (
    <div 
      className="bg-slate-800 border border-slate-700 rounded-lg p-3 hover:border-slate-600 transition-all cursor-pointer w-full max-w-[280px]"
      onClick={() => onJoinGame(instance.id)}
    >
      {/* Thumbnail - Fixed aspect ratio */}
      <div className="w-full h-32 mb-2 relative bg-slate-700 rounded overflow-hidden">
        {renderThumbnail(instance)}
      </div>

      {/* Stake and Status */}
      <div className="flex items-center justify-between mb-2">
        <Badge className="bg-green-600 text-white text-xs px-1.5 py-0.5">
          <DollarSign className="h-3 w-3 mr-1" />
          ${instance.stake}
        </Badge>
        {instance.status === 'live' && instance.timeRemaining && (
          <Badge className="bg-orange-600 text-white text-xs px-1.5 py-0.5">
            <Clock className="h-3 w-3 mr-1" />
            {instance.timeRemaining}
          </Badge>
        )}
      </div>

      {/* Players */}
      {gameType === 'blur-detective' && instance.playerCount !== undefined ? (
        <div className="mb-2">
          <div className="flex items-center justify-between mb-1">
            <div className="flex items-center text-xs text-white">
              <Users className="h-3 w-3 mr-1 text-slate-400" />
              <span>{instance.playerCount}/{instance.maxPlayers || 8}</span>
            </div>
            {instance.status === 'waiting' && (
              <Badge className="bg-blue-600 text-white text-xs px-1 py-0.5">
                Open
              </Badge>
            )}
          </div>
          <p className="text-xs text-slate-400 truncate">
            Host: {instance.players.player1.name}
          </p>
        </div>
      ) : (
        <div className="space-y-1 mb-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center min-w-0 flex-1">
              <div className="h-2.5 w-2.5 rounded-full bg-slate-600 mr-1.5 flex-shrink-0" />
              <span className="text-xs text-white truncate">{instance.players.player1.name}</span>
            </div>
            {instance.currentTurn === '1' && instance.status === 'live' && (
              <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse ml-2" />
            )}
          </div>
          {instance.players.player2 ? (
            <div className="flex items-center justify-between">
              <div className="flex items-center min-w-0 flex-1">
                <div className="h-2.5 w-2.5 rounded-full bg-slate-600 mr-1.5 flex-shrink-0" />
                <span className="text-xs text-white truncate">{instance.players.player2.name}</span>
              </div>
              {instance.currentTurn === '2' && instance.status === 'live' && (
                <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse ml-2" />
              )}
            </div>
          ) : (
            <div className="flex items-center">
              <div className="h-2.5 w-2.5 rounded-full border border-dashed border-slate-600 mr-1.5" />
              <span className="text-xs text-slate-500">Waiting...</span>
            </div>
          )}
        </div>
      )}

      {/* Viewers and Live Status */}
      <div className="flex items-center justify-between mb-2 text-xs text-slate-400">
        <div className="flex items-center">
          <Eye className="h-3 w-3 mr-1" />
          <span>{instance.viewers} watching</span>
        </div>
        {instance.status === 'live' && (
          <Badge className="bg-red-500 text-white text-xs px-1 py-0.5 animate-pulse">
            LIVE
          </Badge>
        )}
      </div>

      {/* Action Button */}
      {instance.status === 'waiting' ? (
        <Button
          size="sm"
          className="w-full bg-gradient-to-r from-green-500 to-cyan-500 hover:opacity-90 text-xs font-medium h-7"
          onClick={(e) => {
            e.stopPropagation();
            onJoinGame(instance.id);
          }}
        >
          <PlayCircle className="h-3 w-3 mr-1" />
          Join Game
        </Button>
      ) : (
        <Button
          variant="outline"
          size="sm"
          className="w-full border-slate-600 text-slate-300 hover:bg-slate-700 text-xs font-medium h-7"
          onClick={(e) => {
            e.stopPropagation();
            onJoinGame(instance.id);
          }}
        >
          <Eye className="h-3 w-3 mr-1" />
          Spectate
        </Button>
      )}
    </div>
  );
};

export default DesktopGameInstanceCard;
