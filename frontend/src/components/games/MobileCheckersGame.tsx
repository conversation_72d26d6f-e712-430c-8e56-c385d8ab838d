import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Input } from '@/components/ui/input';
import {
  MessageSquare,
  Crown,
  AlertCircle,
  RotateCcw,
  X,
  Send,
  ArrowLeft,
  Gamepad,
  Info,
  Clock,
  DollarSign
} from 'lucide-react';

const BOARD_SIZE = 8;

const PIECE_TYPE = {
  NONE: 0,
  RED: 1,
  BLACK: 2,
  RED_KING: 3,
  BLACK_KING: 4
};

const PLAYER = {
  RED: 'red',
  BLACK: 'black'
};

interface MobileCheckersGameProps {
  isTimedGame?: boolean;
  timeLimit?: number;
  wagerAmount?: number;
  onGameComplete?: (winner: string, payout: number) => void;
  onBack?: () => void;
}

const MobileCheckersGame: React.FC<MobileCheckersGameProps> = ({ 
  isTimedGame = false, 
  timeLimit = 300, // 5 minutes default
  wagerAmount = 100,
  onGameComplete: _onGameComplete,
  onBack
}) => {
  // Initialize board immediately
  const initBoard = () => {
    const newBoard = Array(BOARD_SIZE).fill(null).map(() => Array(BOARD_SIZE).fill(PIECE_TYPE.NONE));
    
    // Place initial pieces
    for (let row = 0; row < BOARD_SIZE; row++) {
      for (let col = 0; col < BOARD_SIZE; col++) {
        if ((row + col) % 2 === 1) {
          if (row < 3) {
            newBoard[row][col] = PIECE_TYPE.BLACK;
          } else if (row > 4) {
            newBoard[row][col] = PIECE_TYPE.RED;
          }
        }
      }
    }
    
    return newBoard;
  };

  const [board, setBoard] = useState<number[][]>(initBoard());
  const [currentPlayer, setCurrentPlayer] = useState(PLAYER.RED);
  const [selectedPiece, setSelectedPiece] = useState<{ row: number; col: number } | null>(null);
  const [validMoves, setValidMoves] = useState<any[]>([]);
  const [capturedPieces, setCapturedPieces] = useState({ red: 0, black: 0 });
  const [gameTime, setGameTime] = useState(0);
  const [, setTimer] = useState<NodeJS.Timeout | null>(null);
  const [activeTab, setActiveTab] = useState('game');
  const [showChat, setShowChat] = useState(false);
  const [chatMessage, setChatMessage] = useState('');
  
  // Timer state for timed games
  const [redTimeLeft, setRedTimeLeft] = useState(timeLimit);
  const [blackTimeLeft, setBlackTimeLeft] = useState(timeLimit);

  // Effect to handle game timer
  useEffect(() => {
    const interval = setInterval(() => {
      setGameTime(prevTime => prevTime + 1);
      
      if (isTimedGame) {
        if (currentPlayer === PLAYER.RED) {
          setRedTimeLeft(prev => Math.max(0, prev - 1));
        } else {
          setBlackTimeLeft(prev => Math.max(0, prev - 1));
        }
      }
    }, 1000);

    setTimer(interval);
    return () => clearInterval(interval);
  }, [currentPlayer, isTimedGame]);

  // Check valid moves for a piece
  const getValidMoves = (row: number, col: number) => {
    const piece = board[row][col];
    const moves: any[] = [];
    if (piece === PIECE_TYPE.NONE) return moves;

    const isKing = piece === PIECE_TYPE.RED_KING || piece === PIECE_TYPE.BLACK_KING;
    const isRed = piece === PIECE_TYPE.RED || piece === PIECE_TYPE.RED_KING;
    
    const directions = isKing 
      ? [[-1, -1], [-1, 1], [1, -1], [1, 1]]
      : isRed 
        ? [[-1, -1], [-1, 1]]
        : [[1, -1], [1, 1]];

    for (const [dRow, dCol] of directions) {
      const newRow = row + dRow;
      const newCol = col + dCol;
      
      if (newRow >= 0 && newRow < BOARD_SIZE && newCol >= 0 && newCol < BOARD_SIZE) {
        if (board[newRow][newCol] === PIECE_TYPE.NONE) {
          moves.push({ row: newRow, col: newCol, capture: false });
        } else {
          const jumpRow = newRow + dRow;
          const jumpCol = newCol + dCol;
          
          if (jumpRow >= 0 && jumpRow < BOARD_SIZE && jumpCol >= 0 && jumpCol < BOARD_SIZE) {
            if (board[jumpRow][jumpCol] === PIECE_TYPE.NONE) {
              const targetPiece = board[newRow][newCol];
              const isOpponent = isRed 
                ? (targetPiece === PIECE_TYPE.BLACK || targetPiece === PIECE_TYPE.BLACK_KING)
                : (targetPiece === PIECE_TYPE.RED || targetPiece === PIECE_TYPE.RED_KING);
              
              if (isOpponent) {
                moves.push({ 
                  row: jumpRow, 
                  col: jumpCol, 
                  capture: true, 
                  capturedRow: newRow, 
                  capturedCol: newCol 
                });
              }
            }
          }
        }
      }
    }
    
    return moves;
  };

  // Handle piece selection
  const handlePieceClick = (row: number, col: number) => {
    const piece = board[row][col];
    
    if (selectedPiece) {
      // Try to move
      const validMove = validMoves.find(move => move.row === row && move.col === col);
      if (validMove) {
        makeMove(selectedPiece.row, selectedPiece.col, row, col, validMove);
        return;
      }
    }
    
    // Select piece
    if (piece !== PIECE_TYPE.NONE) {
      const isCurrentPlayerPiece = 
        (currentPlayer === PLAYER.RED && (piece === PIECE_TYPE.RED || piece === PIECE_TYPE.RED_KING)) ||
        (currentPlayer === PLAYER.BLACK && (piece === PIECE_TYPE.BLACK || piece === PIECE_TYPE.BLACK_KING));
      
      if (isCurrentPlayerPiece) {
        setSelectedPiece({ row, col });
        setValidMoves(getValidMoves(row, col));
      } else {
        setSelectedPiece(null);
        setValidMoves([]);
      }
    } else {
      setSelectedPiece(null);
      setValidMoves([]);
    }
  };

  // Make a move
  const makeMove = (fromRow: number, fromCol: number, toRow: number, toCol: number, move: any) => {
    const newBoard = [...board.map(row => [...row])];
    const piece = newBoard[fromRow][fromCol];
    
    newBoard[toRow][toCol] = piece;
    newBoard[fromRow][fromCol] = PIECE_TYPE.NONE;
    
    // Handle capture
    if (move.capture) {
      newBoard[move.capturedRow][move.capturedCol] = PIECE_TYPE.NONE;
      if (currentPlayer === PLAYER.RED) {
        setCapturedPieces(prev => ({ ...prev, red: prev.red + 1 }));
      } else {
        setCapturedPieces(prev => ({ ...prev, black: prev.black + 1 }));
      }
    }
    
    // King promotion
    if (piece === PIECE_TYPE.RED && toRow === 0) {
      newBoard[toRow][toCol] = PIECE_TYPE.RED_KING;
    } else if (piece === PIECE_TYPE.BLACK && toRow === BOARD_SIZE - 1) {
      newBoard[toRow][toCol] = PIECE_TYPE.BLACK_KING;
    }
    
    setBoard(newBoard);
    setSelectedPiece(null);
    setValidMoves([]);
    setCurrentPlayer(currentPlayer === PLAYER.RED ? PLAYER.BLACK : PLAYER.RED);
  };

  // Draw board square
  const renderSquare = (row: number, col: number) => {
    const piece = board[row][col];
    const isBlack = (row + col) % 2 === 1;
    const isSelected = selectedPiece?.row === row && selectedPiece?.col === col;
    const isValidMove = validMoves.some(move => move.row === row && move.col === col);
    
    const cellSize = 'w-10 h-10'; // Smaller size for mobile
    
    return (
      <div
        key={`${row}-${col}`}
        className={`
          ${cellSize} relative cursor-pointer
          ${isBlack ? 'bg-amber-800' : 'bg-amber-100'}
          ${isSelected ? 'ring-2 ring-blue-500' : ''}
          ${isValidMove ? 'ring-2 ring-green-500' : ''}
        `}
        onClick={() => handlePieceClick(row, col)}
      >
        {piece !== PIECE_TYPE.NONE && (
          <div className={`
            absolute inset-1 rounded-full border-2
            ${piece === PIECE_TYPE.RED || piece === PIECE_TYPE.RED_KING ? 'bg-red-600 border-red-700' : 'bg-gray-800 border-gray-900'}
            ${isSelected ? 'ring-2 ring-blue-400' : ''}
          `}>
            {(piece === PIECE_TYPE.RED_KING || piece === PIECE_TYPE.BLACK_KING) && (
              <Crown className="h-4 w-4 text-yellow-400 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2" />
            )}
          </div>
        )}
      </div>
    );
  };

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  // Main game layout
  return (
    <div className="h-screen bg-slate-950 flex flex-col pt-16">
      {/* Header */}
      <div className="flex-none bg-slate-900 border-b border-slate-800">
        <div className="flex items-center justify-between p-3">
          <div className="flex items-center gap-2">
            {onBack && (
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0"
                onClick={onBack}
              >
                <ArrowLeft className="h-4 w-4" />
              </Button>
            )}
            <h1 className="text-lg font-semibold text-white">Checkers</h1>
          </div>
          
          <Badge className="bg-green-500 text-white">
            <DollarSign className="h-3 w-3 mr-1" />
            {wagerAmount}
          </Badge>
        </div>
      </div>

      {/* Main content */}
      <div className="flex-1 overflow-y-auto">
        {activeTab === 'game' && (
          <div className="p-4">
            {/* Game Status Bar */}
            <div className="bg-slate-900 border border-slate-800 rounded-lg p-3 mb-4">
              <div className="flex justify-between items-center mb-2">
                <div className="text-sm">
                  <span className="text-slate-400">Current Turn: </span>
                  <span className={`font-semibold ${currentPlayer === PLAYER.RED ? 'text-red-500' : 'text-gray-300'}`}>
                    {currentPlayer === PLAYER.RED ? 'Red' : 'Black'}
                  </span>
                </div>
                <div className="text-sm text-slate-400">
                  <Clock className="h-3 w-3 inline mr-1" />
                  {formatTime(gameTime)}
                </div>
              </div>
              
              {/* Captured pieces display */}
              <div className="flex justify-between">
                <div className="text-sm">
                  <span className="text-red-500">Red: {12 - capturedPieces.black}</span>
                </div>
                <div className="text-sm">
                  <span className="text-gray-300">Black: {12 - capturedPieces.red}</span>
                </div>
              </div>
            </div>

            {/* Timer bars for timed games */}
            {isTimedGame && (
              <div className="mb-4 space-y-2">
                <div className="bg-slate-900 rounded-lg p-2">
                  <div className="flex justify-between items-center mb-1">
                    <span className="text-xs text-red-500">Red Time</span>
                    <span className="text-xs text-slate-400">{formatTime(redTimeLeft)}</span>
                  </div>
                  <Progress 
                    value={(redTimeLeft / timeLimit) * 100} 
                    className="h-1 bg-slate-800"
                  />
                </div>
                <div className="bg-slate-900 rounded-lg p-2">
                  <div className="flex justify-between items-center mb-1">
                    <span className="text-xs text-gray-300">Black Time</span>
                    <span className="text-xs text-slate-400">{formatTime(blackTimeLeft)}</span>
                  </div>
                  <Progress 
                    value={(blackTimeLeft / timeLimit) * 100} 
                    className="h-1 bg-slate-800"
                  />
                </div>
              </div>
            )}

            {/* Game Board */}
            <div className="bg-slate-900 border border-slate-800 rounded-lg p-4 mx-auto inline-block">
              <div className="grid grid-cols-8 gap-0 border-2 border-slate-700">
                {Array.from({ length: BOARD_SIZE }).map((_, row) => (
                  Array.from({ length: BOARD_SIZE }).map((_, col) => (
                    renderSquare(row, col)
                  ))
                ))}
              </div>
            </div>

            {/* Game Controls */}
            <div className="mt-4 flex justify-center gap-3">
              <Button 
                variant="outline" 
                size="sm"
                className="text-slate-400 border-slate-700"
              >
                <RotateCcw className="h-4 w-4 mr-1" />
                Restart
              </Button>
              <Button 
                variant="outline" 
                size="sm"
                className="text-slate-400 border-slate-700"
              >
                <AlertCircle className="h-4 w-4 mr-1" />
                Resign
              </Button>
            </div>
          </div>
        )}

        {activeTab === 'info' && (
          <div className="p-4 space-y-4">
            {/* Game Info */}
            <div className="bg-slate-900 border border-slate-800 rounded-lg p-4">
              <h3 className="text-sm font-semibold text-white mb-3">Game Information</h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-slate-400">Game Type:</span>
                  <span className="text-white">{isTimedGame ? 'Timed' : 'Classic'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-400">Wager Amount:</span>
                  <span className="text-green-500">${wagerAmount}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-400">Time Limit:</span>
                  <span className="text-white">{isTimedGame ? `${timeLimit/60} minutes` : 'Unlimited'}</span>
                </div>
              </div>
            </div>

            {/* Player Stats */}
            <div className="bg-slate-900 border border-slate-800 rounded-lg p-4">
              <h3 className="text-sm font-semibold text-white mb-3">Player Statistics</h3>
              <div className="space-y-3">
                <div>
                  <div className="flex justify-between items-center mb-1">
                    <span className="text-sm text-red-500">Red Player</span>
                    <Badge className="bg-red-500/20 text-red-500">You</Badge>
                  </div>
                  <div className="text-xs text-slate-400">
                    Captured: {capturedPieces.red} pieces
                  </div>
                </div>
                <div>
                  <div className="flex justify-between items-center mb-1">
                    <span className="text-sm text-gray-300">Black Player</span>
                    <Badge className="bg-gray-500/20 text-gray-300">AI</Badge>
                  </div>
                  <div className="text-xs text-slate-400">
                    Captured: {capturedPieces.black} pieces
                  </div>
                </div>
              </div>
            </div>

            {/* Rules Summary */}
            <div className="bg-slate-900 border border-slate-800 rounded-lg p-4">
              <h3 className="text-sm font-semibold text-white mb-3">Quick Rules</h3>
              <ul className="text-xs text-slate-400 space-y-1">
                <li>• Pieces move diagonally forward</li>
                <li>• Capture by jumping over opponent pieces</li>
                <li>• Kings can move backwards</li>
                <li>• Win by capturing all opponent pieces</li>
              </ul>
            </div>
          </div>
        )}

        {/* Chat Panel */}
        {showChat && (
          <div className="fixed inset-0 z-40 bg-black/50" onClick={() => setShowChat(false)}>
            <div 
              className="fixed bottom-16 left-0 right-0 h-96 bg-slate-900 border-t border-slate-800 animate-slide-up"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex items-center justify-between p-3 border-b border-slate-800">
                <h3 className="text-sm font-semibold text-white">Game Chat</h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowChat(false)}
                  className="h-8 w-8 p-0"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
              
              <div className="h-[calc(100%-8rem)] overflow-y-auto p-3">
                <div className="space-y-3">
                  <div className="text-xs">
                    <span className="text-blue-500 font-semibold">RedPlayer: </span>
                    <span className="text-slate-300">Good game!</span>
                  </div>
                  <div className="text-xs">
                    <span className="text-gray-500 font-semibold">BlackPlayer: </span>
                    <span className="text-slate-300">Nice move!</span>
                  </div>
                </div>
              </div>
              
              <div className="absolute bottom-0 left-0 right-0 p-3 border-t border-slate-800">
                <div className="flex gap-2">
                  <Input
                    type="text"
                    placeholder="Type a message..."
                    value={chatMessage}
                    onChange={(e) => setChatMessage(e.target.value)}
                    className="flex-1 h-9 text-sm bg-slate-800 border-slate-700"
                  />
                  <Button
                    size="sm"
                    className="h-9 px-3"
                  >
                    <Send className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Bottom Navigation */}
      <div className="flex-none bg-slate-900 border-t border-slate-800">
        <div className="grid grid-cols-3 h-16">
          <button
            onClick={() => {
              setActiveTab('game');
              setShowChat(false);
            }}
            className={`flex flex-col items-center justify-center gap-1 transition-colors ${
              activeTab === 'game' ? 'text-purple-500' : 'text-slate-400'
            }`}
          >
            <Gamepad className="h-5 w-5" />
            <span className="text-xs">Game</span>
          </button>
          
          <button
            onClick={() => {
              setShowChat(!showChat);
              setActiveTab('game');
            }}
            className={`flex flex-col items-center justify-center gap-1 transition-colors ${
              showChat ? 'text-purple-500' : 'text-slate-400'
            }`}
          >
            <MessageSquare className="h-5 w-5" />
            <span className="text-xs">Chat</span>
          </button>
          
          <button
            onClick={() => {
              setActiveTab('info');
              setShowChat(false);
            }}
            className={`flex flex-col items-center justify-center gap-1 transition-colors ${
              activeTab === 'info' ? 'text-purple-500' : 'text-slate-400'
            }`}
          >
            <Info className="h-5 w-5" />
            <span className="text-xs">Info</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default MobileCheckersGame;