import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  User, 
  Swords, 
  DollarSign, 
  Clock,
  Trophy,
  Star,
  AlertCircle
} from 'lucide-react';

interface IncomingChallengeProps {
  isOpen: boolean;
  onClose: () => void;
  onAccept: () => void;
  onDecline: () => void;
  challenge: ChallengeInfo | null;
}

interface ChallengeInfo {
  id: string;
  challenger: {
    username: string;
    rating: number;
    winRate: number;
    avatar?: string;
  };
  gameType: string;
  wagerAmount: number;
  timeLimit: number;
  isRanked: boolean;
  expiresIn: number; // seconds
}

const IncomingChallengeDialog: React.FC<IncomingChallengeProps> = ({
  isOpen,
  onClose,
  onAccept,
  onDecline,
  challenge
}) => {
  const [timeLeft, setTimeLeft] = useState(challenge?.expiresIn || 30);

  useEffect(() => {
    if (isOpen && challenge) {
      setTimeLeft(challenge.expiresIn);
      
      const timer = setInterval(() => {
        setTimeLeft((prev) => {
          if (prev <= 1) {
            onDecline();
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [isOpen, challenge, onDecline]);

  if (!challenge) return null;

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return mins > 0 ? `${mins}:${secs.toString().padStart(2, '0')}` : `${secs}s`;
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="bg-slate-900 border-slate-800 text-white max-w-md">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold flex items-center gap-2">
            <Swords className="h-5 w-5 text-red-500 animate-pulse" />
            Incoming Challenge!
          </DialogTitle>
          <DialogDescription className="text-sm text-slate-400">
            You've been challenged to a {challenge.gameType} match
          </DialogDescription>
        </DialogHeader>

        {/* Timer Bar */}
        <div className="w-full">
          <div className="flex justify-between items-center mb-2">
            <span className="text-xs text-slate-400">Time to respond</span>
            <span className="text-sm font-bold text-white">{formatTime(timeLeft)}</span>
          </div>
          <Progress 
            value={(timeLeft / challenge.expiresIn) * 100} 
            className="h-2"
          />
        </div>

        <div className="space-y-4 py-4">
          {/* Challenger Info */}
          <div className="bg-slate-800 rounded-lg p-4">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center">
                  <User className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-lg font-bold text-white">{challenge.challenger.username}</p>
                  <div className="flex items-center gap-2 text-sm text-slate-400">
                    <Star className="h-3 w-3" />
                    <span>{challenge.challenger.rating}</span>
                    <span>•</span>
                    <span>{challenge.challenger.winRate}% WR</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Challenge Details */}
          <div className="space-y-3">
            <div className="flex items-center justify-between py-2 border-b border-slate-700">
              <span className="text-sm text-slate-400">Game Type</span>
              <Badge className="bg-purple-600">
                {challenge.gameType.charAt(0).toUpperCase() + challenge.gameType.slice(1)}
              </Badge>
            </div>

            <div className="flex items-center justify-between py-2 border-b border-slate-700">
              <span className="text-sm text-slate-400 flex items-center gap-2">
                <DollarSign className="h-4 w-4" />
                Wager Amount
              </span>
              <span className="text-lg font-bold text-green-500">${challenge.wagerAmount}</span>
            </div>

            <div className="flex items-center justify-between py-2 border-b border-slate-700">
              <span className="text-sm text-slate-400 flex items-center gap-2">
                <Clock className="h-4 w-4" />
                Time Control
              </span>
              <span className="text-sm text-white">
                {challenge.timeLimit > 0 ? `${challenge.timeLimit / 60} minutes` : 'No limit'}
              </span>
            </div>

            <div className="flex items-center justify-between py-2">
              <span className="text-sm text-slate-400 flex items-center gap-2">
                <Trophy className="h-4 w-4" />
                Ranked Game
              </span>
              <span className="text-sm text-white">
                {challenge.isRanked ? 'Yes' : 'No'}
              </span>
            </div>
          </div>

          {/* Warning for high stakes */}
          {challenge.wagerAmount >= 50 && (
            <div className="bg-amber-900 bg-opacity-20 border border-amber-600 rounded-lg p-3">
              <div className="flex items-center gap-2">
                <AlertCircle className="h-4 w-4 text-amber-500" />
                <p className="text-sm text-amber-200">
                  This is a high-stakes match. Make sure you're comfortable with the wager amount.
                </p>
              </div>
            </div>
          )}
        </div>

        <DialogFooter className="flex gap-2">
          <Button
            variant="outline"
            onClick={onDecline}
            className="border-slate-700 text-red-500 hover:bg-red-500 hover:text-white"
          >
            Decline
          </Button>
          <Button
            onClick={onAccept}
            className="bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600"
          >
            Accept Challenge
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default IncomingChallengeDialog;