import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import {
  Trophy,
  Users,
  DollarSign,
  Clock,
  Plus,
  Filter,
  UserX,
  Timer,
  Zap,
  Brain,
  Sword,
  Gamepad,
  Eye,
  Layers,
  Radio,
  PlayCircle,
  X,
  Flame,
  ArrowLeft,
  Grid3x3,
  Hand,
  Crown,
  Film,
  Star
} from 'lucide-react';
import type { GameData } from './gameTypes';

interface MobileChallengeGamesProps {
  onGameSelect: (game: GameData) => void;
}

// Helper function to create game data
const createGameData = (id: string, name: string, type: string, prize: number, viewers: number, status: 'waiting' | 'live' | 'finished' = 'live'): GameData => ({
  id,
  name,
  type,
  players: {
    player1: { id: '1', name: 'Player A' },
    player2: { id: '2', name: 'Player B' }
  },
  currentRound: Math.floor(Math.random() * 3) + 1,
  totalRounds: 3,
  prize,
  viewers,
  status
});

const MobileChallengeGames: React.FC<MobileChallengeGamesProps> = ({ onGameSelect }) => {
  const [activeTab, setActiveTab] = useState('games');
  const [showFilters, setShowFilters] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState('all');

  return (
    <div className="h-screen bg-slate-950 text-white overflow-hidden flex flex-col pt-16">
      {/* Fixed Header */}
      <div className="flex-none bg-slate-900 border-b border-slate-800">
        <div className="flex items-center justify-between p-3">
          <div className="flex items-center">
            <Trophy className="h-5 w-5 text-amber-500 mr-2" />
            <h1 className="text-lg font-semibold text-white">Challenge Arena</h1>
          </div>

          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowFilters(true)}
              className="h-9 w-9 p-0"
            >
              <Filter className="h-5 w-5 text-white" />
            </Button>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="flex px-3 pb-3">
          <Button
            variant={activeTab === 'games' ? 'default' : 'ghost'}
            size="sm"
            className={`flex-1 h-9 text-sm ${
              activeTab === 'games'
                ? 'bg-purple-600 text-white'
                : 'text-slate-400'
            }`}
            onClick={() => setActiveTab('games')}
          >
            <Gamepad className="h-4 w-4 mr-1" />
            Games
          </Button>
          <Button
            variant={activeTab === 'challenges' ? 'default' : 'ghost'}
            size="sm"
            className={`flex-1 h-9 text-sm ${
              activeTab === 'challenges'
                ? 'bg-purple-600 text-white'
                : 'text-slate-400'
            }`}
            onClick={() => setActiveTab('challenges')}
          >
            <Sword className="h-4 w-4 mr-1" />
            Challenges
          </Button>
          <Button
            variant={activeTab === 'leaderboard' ? 'default' : 'ghost'}
            size="sm"
            className={`flex-1 h-9 text-sm ${
              activeTab === 'leaderboard'
                ? 'bg-purple-600 text-white'
                : 'text-slate-400'
            }`}
            onClick={() => setActiveTab('leaderboard')}
          >
            <Trophy className="h-4 w-4 mr-1" />
            Leaders
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-y-auto pb-20">
        {activeTab === 'games' && <GamesSection onGameSelect={onGameSelect} />}
        {activeTab === 'challenges' && <ChallengesSection />}
        {activeTab === 'leaderboard' && <LeaderboardSection />}
      </div>

      {/* Floating Action Button */}
      <Button
        className="fixed bottom-6 right-6 h-14 w-14 rounded-full bg-gradient-to-r from-green-500 to-cyan-500 shadow-lg z-20"
        onClick={() => {}}
      >
        <Plus className="h-6 w-6" />
      </Button>

      {/* Filters Overlay */}
      {showFilters && (
        <div className="fixed inset-0 z-40 bg-slate-900 flex flex-col">
          <div className="flex items-center justify-between p-4 border-b border-slate-800">
            <div className="flex items-center">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowFilters(false)}
                className="h-8 w-8 p-0 mr-2"
              >
                <ArrowLeft className="h-5 w-5 text-white" />
              </Button>
              <h2 className="text-lg font-semibold text-white">Filters</h2>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowFilters(false)}
              className="h-8 w-8 p-0"
            >
              <X className="h-5 w-5 text-white" />
            </Button>
          </div>

          <div className="flex-1 overflow-y-auto p-4 space-y-6">
            {/* Categories */}
            <div>
              <h3 className="text-sm font-medium text-white mb-3">Categories</h3>
              <div className="grid grid-cols-2 gap-2">
                {[
                  { id: 'all', name: 'All Games', icon: <Gamepad className="h-4 w-4" /> },
                  { id: 'pvp', name: 'Player vs Player', icon: <Sword className="h-4 w-4" /> },
                  { id: 'pve', name: 'Player vs Time', icon: <Clock className="h-4 w-4" /> },
                  { id: 'arcade', name: 'Arcade', icon: <Gamepad className="h-4 w-4" /> }
                ].map(category => (
                  <Button
                    key={category.id}
                    variant="outline"
                    className={`h-12 text-sm border-slate-700 ${
                      selectedCategory === category.id
                        ? 'bg-purple-600 border-purple-600 text-white'
                        : 'text-white'
                    }`}
                    onClick={() => setSelectedCategory(category.id)}
                  >
                    {category.icon}
                    <span className="ml-2">{category.name}</span>
                  </Button>
                ))}
              </div>
            </div>

            {/* Stake Range */}
            <div>
              <h3 className="text-sm font-medium text-white mb-3">Stake Range</h3>
              <div className="grid grid-cols-2 gap-2">
                {['Any', '$1-5', '$5-25', '$25+'].map(amount => (
                  <Button
                    key={amount}
                    variant="outline"
                    className="h-10 text-sm border-slate-700 text-white"
                  >
                    {amount}
                  </Button>
                ))}
              </div>
            </div>

            {/* Difficulty */}
            <div>
              <h3 className="text-sm font-medium text-white mb-3">Difficulty</h3>
              <div className="grid grid-cols-3 gap-2">
                {[
                  { name: 'Easy', color: 'bg-green-500' },
                  { name: 'Medium', color: 'bg-yellow-500' },
                  { name: 'Hard', color: 'bg-red-500' }
                ].map(difficulty => (
                  <Button
                    key={difficulty.name}
                    variant="outline"
                    className="h-10 text-sm border-slate-700 text-white"
                  >
                    <span className={`h-2 w-2 rounded-full ${difficulty.color} mr-2`} />
                    {difficulty.name}
                  </Button>
                ))}
              </div>
            </div>

            <Button
              className="w-full h-12 text-sm bg-gradient-to-r from-green-500 to-cyan-500"
              onClick={() => setShowFilters(false)}
            >
              Apply Filters
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

// Games Section
const GamesSection = ({ onGameSelect }: { onGameSelect: (game: GameData) => void }) => (
  <div className="p-3 pt-5 space-y-4">
    {/* Featured Game */}
    <Card className="p-4 bg-gradient-to-r from-purple-900/50 to-pink-900/50 border-purple-700">
      <div className="flex items-start justify-between mb-3">
        <div>
          <Badge className="bg-green-500 text-white text-xs mb-2">NEW GAME</Badge>
          <h3 className="text-lg font-bold text-white">Blur Detective</h3>
          <p className="text-sm text-slate-300">Identify blurred images faster than your opponent!</p>
        </div>
        <Flame className="h-6 w-6 text-orange-500" />
      </div>

      <div className="flex items-center space-x-4">
        <Button
          className="flex-1 h-10 bg-gradient-to-r from-green-500 to-cyan-500"
          onClick={() => onGameSelect(createGameData('blur-detective', 'Blur Detective', 'visual', 50, 567, 'live'))}
        >
          <Eye className="h-4 w-4 mr-2" />
          Browse/Play
        </Button>
        <div className="flex items-center space-x-2">
          <Badge className="bg-slate-800 text-white text-xs">
            <DollarSign className="h-3 w-3 mr-1" />
            $5-50
          </Badge>
          <Badge className="bg-slate-800 text-white text-xs">
            <Clock className="h-3 w-3 mr-1" />
            2 min
          </Badge>
        </div>
      </div>
    </Card>

    {/* Games Grid */}
    <div className="grid grid-cols-1 gap-3">
      <MobileGameCard
        name="Chess Blitz"
        icon={<Layers className="h-4 w-4" />}
        description="Rapid chess with 2-minute time limit"
        playerMode="1v1"
        stakeRange="$5-100"
        duration="3 min"
        difficulty="Hard"
        isLive={true}
        activePlayers={14}
        onPlay={() => onGameSelect(createGameData('chess-blitz', 'Chess Blitz', 'strategy', 100, 234, 'live'))}
      />

      <MobileGameCard
        name="Speed Trivia"
        icon={<Brain className="h-4 w-4" />}
        description="Answer trivia questions faster than opponents"
        playerMode="1vN"
        stakeRange="$1-25"
        duration="2 min"
        difficulty="Medium"
        isLive={true}
        activePlayers={28}
        onPlay={() => onGameSelect(createGameData('speed-trivia', 'Speed Trivia', 'trivia', 25, 1234, 'live'))}
      />

      <MobileGameCard
        name="Tap Dash"
        icon={<Zap className="h-4 w-4" />}
        description="Test your reflexes - tap when green!"
        playerMode="1v1"
        stakeRange="$1-20"
        duration="1 min"
        difficulty="Easy"
        isLive={true}
        activePlayers={19}
        onPlay={() => onGameSelect(createGameData('tap-dash', 'Tap Dash', 'reaction', 20, 89, 'live'))}
      />

      <MobileGameCard
        name="Spot The Fake"
        icon={<Eye className="h-4 w-4" />}
        description="Find the incorrect image or fact"
        playerMode="1vN"
        stakeRange="$2-50"
        duration="3 min"
        difficulty="Medium"
        isLive={false}
        activePlayers={7}
        onPlay={() => onGameSelect(createGameData('spot-fake', 'Spot The Fake', 'visual', 50, 45, 'waiting'))}
      />

      <MobileGameCard
        name="Checkers"
        icon={<Grid3x3 className="h-4 w-4" />}
        description="Classic board game strategy"
        playerMode="1v1"
        stakeRange="$1-100"
        duration="5-15 min"
        difficulty="Medium"
        isLive={true}
        activePlayers={42}
        onPlay={() => onGameSelect(createGameData('checkers', 'Checkers', 'checkers', 50, 234, 'waiting'))}
      />

      <MobileGameCard
        name="Chess"
        icon={<Crown className="h-4 w-4" />}
        description="The ultimate strategy game"
        playerMode="1v1"
        stakeRange="$5-500"
        duration="5-30 min"
        difficulty="Hard"
        isLive={true}
        activePlayers={78}
        onPlay={() => onGameSelect(createGameData('chess', 'Chess', 'chess', 100, 456, 'waiting'))}
      />

      <MobileGameCard
        name="Rock Paper Scissors"
        icon={<Hand className="h-4 w-4" />}
        description="Classic hand game of strategy and luck"
        playerMode="1v1"
        stakeRange="$1-100"
        duration="1-5 min"
        difficulty="Easy"
        isLive={true}
        activePlayers={36}
        onPlay={() => onGameSelect(createGameData('rock-paper-scissors', 'Rock Paper Scissors', 'rock-paper-scissors', 50, 421, 'live'))}
      />

      <MobileGameCard
        name="Highlight Hero"
        icon={<Film className="h-4 w-4" />}
        description="Watch video highlights and test your knowledge"
        playerMode="1v1"
        stakeRange="$10-200"
        duration="5 min"
        difficulty="Medium"
        isLive={true}
        activePlayers={16}
        onPlay={() => onGameSelect(createGameData('highlight-hero', 'Highlight Hero', 'highlight-hero', 50, 523, 'live'))}
      />

      <MobileGameCard
        name="Blur Detective"
        icon={<Eye className="h-4 w-4" />}
        description="Guess the celebrity from blurred images"
        playerMode="2-8"
        stakeRange="$10-1000"
        duration="5-10 min"
        difficulty="Medium"
        isLive={true}
        activePlayers={24}
        onPlay={() => onGameSelect(createGameData('blur-detective', 'Blur Detective', 'blur-detective', 100, 687, 'live'))}
      />

      <MobileGameCard
        name="Word Jumble"
        icon={<Brain className="h-4 w-4" />}
        description="Unscramble letters to form words"
        playerMode="1vN"
        stakeRange="$1-50"
        duration="2 min"
        difficulty="Medium"
        isLive={true}
        activePlayers={22}
        onPlay={() => onGameSelect(createGameData('word-jumble', 'Word Jumble', 'word-jumble', 50, 345, 'live'))}
      />
    </div>
  </div>
);

// Challenges Section
const ChallengesSection = () => (
  <div className="p-3 pt-5 space-y-4">
    {/* Your Active Challenges */}
    <div>
      <h3 className="text-sm font-medium text-white mb-3 flex items-center">
        <Radio className="h-4 w-4 text-green-500 mr-2 animate-pulse" />
        Your Active Challenges
      </h3>

      <div className="space-y-3">
        <MobileChallengeCard
          game="Chess Blitz"
          opponent="Player X"
          stake={25}
          timeLeft="1:32"
          status="your-turn"
        />

        <MobileChallengeCard
          game="Speed Trivia"
          opponent="3 Players"
          stake={10}
          timeLeft="0:45"
          status="waiting"
        />
      </div>
    </div>

    {/* Open Challenges */}
    <div>
      <h3 className="text-sm font-medium text-white mb-3">Open Challenges</h3>

      <div className="space-y-3">
        <MobileOpenChallenge
          game="Blur Detective"
          creator="Player Y"
          stake={50}
          timeLimit="2:00"
          players="1/2"
        />

        <MobileOpenChallenge
          game="Memory Sequence"
          creator="Player Z"
          stake={15}
          timeLimit="2:30"
          players="2/4"
        />
      </div>
    </div>
  </div>
);

// Leaderboard Section
const LeaderboardSection = () => (
  <div className="p-3 pt-5 space-y-4">
    {/* Your Stats */}
    <Card className="p-4 bg-slate-900 border-slate-800">
      <h3 className="text-sm font-medium text-white mb-3">Your Stats</h3>
      <div className="grid grid-cols-2 gap-3">
        <div>
          <p className="text-xs text-slate-400">Ranking</p>
          <p className="text-lg font-bold text-white">#8</p>
        </div>
        <div>
          <p className="text-xs text-slate-400">Win Rate</p>
          <p className="text-lg font-bold text-green-500">65.5%</p>
        </div>
        <div>
          <p className="text-xs text-slate-400">Total Earned</p>
          <p className="text-lg font-bold text-white">$3,240</p>
        </div>
        <div>
          <p className="text-xs text-slate-400">Games Played</p>
          <p className="text-lg font-bold text-white">142</p>
        </div>
      </div>
    </Card>

    {/* Top Players */}
    <Card className="p-4 bg-slate-900 border-slate-800">
      <h3 className="text-sm font-medium text-white mb-3">Top Players</h3>
      <div className="space-y-3">
        {[
          { rank: 1, name: 'Player A', earnings: '$12,540', winRate: '78%' },
          { rank: 2, name: 'Player B', earnings: '$10,220', winRate: '75%' },
          { rank: 3, name: 'Player C', earnings: '$8,190', winRate: '72%' },
          { rank: 8, name: 'You', earnings: '$3,240', winRate: '65.5%', isYou: true },
        ].map(player => (
          <div
            key={player.rank}
            className={`flex items-center justify-between p-2 rounded ${
              player.isYou ? 'bg-slate-800' : ''
            }`}
          >
            <div className="flex items-center">
              <span className={`w-6 text-center text-sm font-bold ${
                player.rank === 1 ? 'text-yellow-500' :
                player.rank === 2 ? 'text-slate-300' :
                player.rank === 3 ? 'text-amber-600' :
                'text-slate-500'
              }`}>
                {player.rank}
              </span>
              <div className="ml-3">
                <p className="text-sm font-medium text-white">
                  {player.name}
                  {player.isYou && <Badge className="ml-2 text-xs bg-purple-600">You</Badge>}
                </p>
                <p className="text-xs text-slate-400">Win Rate: {player.winRate}</p>
              </div>
            </div>
            <span className="text-sm font-bold text-green-500">{player.earnings}</span>
          </div>
        ))}
      </div>
    </Card>
  </div>
);

// Mobile Game Card Component
interface MobileGameCardProps {
  name: string;
  icon: React.ReactNode;
  description: string;
  playerMode: string;
  stakeRange: string;
  duration: string;
  difficulty: string;
  isLive: boolean;
  activePlayers: number;
  onPlay?: () => void;
}

const MobileGameCard: React.FC<MobileGameCardProps> = ({
  name,
  icon,
  description,
  playerMode,
  stakeRange,
  duration,
  difficulty,
  isLive,
  activePlayers,
  onPlay
}) => {
  const getDifficultyColor = (diff: string) => {
    switch(diff) {
      case 'Easy': return 'text-green-500 border-green-500';
      case 'Medium': return 'text-yellow-500 border-yellow-500';
      case 'Hard': return 'text-red-500 border-red-500';
      default: return 'text-slate-400 border-slate-400';
    }
  };

  return (
    <Card className="p-4 bg-slate-900 border-slate-800">
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center">
          <div className="h-10 w-10 rounded-lg bg-gradient-to-br from-slate-700 to-slate-600 flex items-center justify-center mr-3">
            {icon}
          </div>
          <div>
            <h3 className="text-sm font-medium text-white">{name}</h3>
            <Badge className={`text-xs px-2 py-0.5 border bg-transparent ${getDifficultyColor(difficulty)}`}>
              {difficulty}
            </Badge>
          </div>
        </div>
        {isLive && (
          <Badge className="bg-green-500 text-white text-xs animate-pulse">
            <Radio className="h-3 w-3 mr-1" />
            LIVE
          </Badge>
        )}
      </div>

      <p className="text-xs text-slate-300 mb-3">{description}</p>

      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-3">
          <div className="flex items-center text-xs text-slate-400">
            <Users className="h-3 w-3 mr-1" />
            {playerMode}
          </div>
          <div className="flex items-center text-xs text-slate-400">
            <DollarSign className="h-3 w-3 mr-1" />
            {stakeRange}
          </div>
          <div className="flex items-center text-xs text-slate-400">
            <Clock className="h-3 w-3 mr-1" />
            {duration}
          </div>
        </div>
        <div className="text-xs text-green-500">
          {activePlayers} playing
        </div>
      </div>

      <div className="flex space-x-2">
        <Button
          className="flex-1 h-9 text-sm bg-gradient-to-r from-green-500 to-cyan-500"
          onClick={(e) => {
            e.stopPropagation();
            if (onPlay) onPlay();
          }}
        >
          <Eye className="h-4 w-4 mr-1" />
          Browse/Play
        </Button>
        <Button variant="outline" className="flex-1 h-9 text-sm border-slate-700">
          <Star className="h-4 w-4 mr-1" />
          Favorite
        </Button>
      </div>
    </Card>
  );
};

// Mobile Challenge Card
interface MobileChallengeCardProps {
  game: string;
  opponent: string;
  stake: number;
  timeLeft: string;
  status: 'your-turn' | 'waiting';
}

const MobileChallengeCard: React.FC<MobileChallengeCardProps> = ({
  game,
  opponent,
  stake,
  timeLeft,
  status
}) => (
  <Card className="p-4 bg-slate-900 border-slate-800">
    <div className="flex items-center justify-between mb-2">
      <h4 className="text-sm font-medium text-white">{game}</h4>
      <Badge
        className={`text-xs ${
          status === 'your-turn'
            ? 'bg-green-500 text-white animate-pulse'
            : 'bg-slate-600 text-white'
        }`}
      >
        {status === 'your-turn' ? 'Your Turn' : 'Waiting'}
      </Badge>
    </div>

    <div className="flex items-center justify-between">
      <div className="flex items-center">
        <UserX className="h-4 w-4 text-slate-400 mr-2" />
        <span className="text-sm text-slate-300">{opponent}</span>
      </div>
      <div className="flex items-center space-x-3">
        <span className="text-sm font-medium text-green-500">${stake}</span>
        <div className="flex items-center text-sm text-orange-400">
          <Clock className="h-3 w-3 mr-1" />
          {timeLeft}
        </div>
      </div>
    </div>
  </Card>
);

// Mobile Open Challenge
interface MobileOpenChallengeProps {
  game: string;
  creator: string;
  stake: number;
  timeLimit: string;
  players: string;
}

const MobileOpenChallenge: React.FC<MobileOpenChallengeProps> = ({
  game,
  creator,
  stake,
  timeLimit,
  players
}) => (
  <Card className="p-4 bg-slate-900 border-slate-800">
    <div className="flex items-center justify-between mb-2">
      <h4 className="text-sm font-medium text-white">{game}</h4>
      <span className="text-sm font-medium text-green-500">${stake}</span>
    </div>

    <div className="flex items-center justify-between">
      <div className="flex items-center space-x-3">
        <div className="flex items-center text-sm text-slate-300">
          <UserX className="h-4 w-4 text-slate-400 mr-2" />
          {creator}
        </div>
        <div className="flex items-center text-sm text-slate-400">
          <Timer className="h-3 w-3 mr-1" />
          {timeLimit}
        </div>
        <div className="flex items-center text-sm text-slate-400">
          <Users className="h-3 w-3 mr-1" />
          {players}
        </div>
      </div>
      <Button size="sm" className="h-8 px-3 bg-green-600">
        Join
      </Button>
    </div>
  </Card>
);

export default MobileChallengeGames;