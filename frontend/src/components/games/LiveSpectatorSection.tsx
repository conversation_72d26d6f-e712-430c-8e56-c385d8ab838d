import { useState } from 'react';
import {
  Users,
  Activity,
  Send,
  Volume2,
  VolumeX,
  Maximize2,
  Sparkles,
  ArrowLeft,
  Eye
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import type { GameData } from './gameTypes';

interface LiveSpectatorSectionProps {
  game?: GameData;
  onBack?: () => void;
  className?: string;
}

const LiveSpectatorSection = ({ game, onBack, className = '' }: LiveSpectatorSectionProps) => {
  const [selectedBet, setSelectedBet] = useState('player-a');
  const [betAmount, setBetAmount] = useState('10');
  const [chatMessage, setChatMessage] = useState('');
  const [soundEnabled, setSoundEnabled] = useState(true);
  const [selectedGameTab, setSelectedGameTab] = useState('live');

  return (
    <div className={`h-full bg-slate-950 text-white ${className}`}>
      <div className="grid grid-cols-12 gap-1 p-1 h-full">

        {/* Left Sidebar - Game Selection & Info */}
        <div className="col-span-2 flex flex-col gap-1">
          {/* Game Categories */}
          <div className="bg-slate-900 rounded-sm border border-slate-800 p-2">
            <h3 className="text-xs font-medium text-white mb-2">Game Types</h3>
            <Tabs value={selectedGameTab} onValueChange={setSelectedGameTab}>
              <TabsList className="grid w-full grid-cols-2 h-7">
                <TabsTrigger value="live" className="text-xs">Live</TabsTrigger>
                <TabsTrigger value="upcoming" className="text-xs">Upcoming</TabsTrigger>
              </TabsList>
            </Tabs>
          </div>

          {/* Live Games List - SCROLLABLE */}
          <div className="flex-1 bg-slate-900 rounded-sm border border-slate-800 flex flex-col overflow-hidden">
            <div className="p-2 border-b border-slate-800">
              <h3 className="text-xs font-medium text-white">Active Matches</h3>
            </div>
            <div className="flex-1 overflow-auto p-1">
              {Array.from({ length: 12 }).map((_, idx) => (
                <div key={idx} className="mb-1 p-2 bg-slate-800 rounded-sm border border-slate-700 hover:border-purple-500 cursor-pointer">
                  <div className="flex justify-between items-start mb-1">
                    <h4 className="text-xs font-medium text-white">
                      {['Speed Quiz', 'Tap Race', 'Puzzle Duel', 'Blur Guess'][idx % 4]}
                    </h4>
                    <Badge className="bg-red-500 text-[10px] h-4 px-1">
                      <Activity className="h-2.5 w-2.5 mr-0.5" />
                      LIVE
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between text-[10px] text-slate-400">
                    <div className="flex items-center">
                      <Users className="h-2.5 w-2.5 mr-1" />
                      <span>{234 + idx * 23} watching</span>
                    </div>
                    <div className="flex items-center">
                      <Sparkles className="h-2.5 w-2.5 mr-0.5" />
                      <span>${(1250 + idx * 150).toLocaleString()}</span>
                    </div>
                  </div>
                  <div className="mt-1 flex justify-between items-center">
                    <div className="text-[10px]">
                      <span className="text-slate-400">Player A vs Player B</span>
                    </div>
                    <div className="text-[10px] text-yellow-500">
                      Round {(idx % 3) + 1}/3
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Main Game Viewing Area */}
        <div className="col-span-7 flex flex-col gap-1">
          {/* Game Header */}
          <div className="bg-slate-900 rounded-sm border border-slate-800 p-2 flex justify-between items-center">
            <div className="flex items-center gap-3">
              {onBack && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-7 w-7 p-0"
                  onClick={onBack}
                >
                  <ArrowLeft className="h-4 w-4" />
                </Button>
              )}
              <h2 className="text-sm font-bold text-white">{game?.name || 'Speed Quiz Championship'}</h2>
              <Badge className="bg-purple-500">${game?.prize || 2450} Pool</Badge>
              <Badge className="bg-green-500">
                <Eye className="h-3 w-3 mr-1" />
                {game?.viewers || 1234} watching
              </Badge>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                className="h-7 w-7 p-0"
                onClick={() => setSoundEnabled(!soundEnabled)}
              >
                {soundEnabled ? <Volume2 className="h-4 w-4" /> : <VolumeX className="h-4 w-4" />}
              </Button>
              <Button variant="ghost" size="sm" className="h-7 w-7 p-0">
                <Maximize2 className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Game Visualizer */}
          <div className="flex-1 bg-slate-900 rounded-sm border border-slate-800 p-4 relative">
            <div className="absolute top-2 right-2">
              <Badge className="bg-red-500">
                <Activity className="h-3 w-3 mr-1" />
                LIVE
              </Badge>
            </div>

            {/* Game Board/Visualization Area */}
            <div className="h-full flex flex-col items-center justify-center">
              {/* Player vs Player Display */}
              <div className="grid grid-cols-3 gap-8 w-full max-w-2xl">
                <div className="text-center">
                  <div className="h-20 w-20 rounded-full bg-blue-500 mx-auto mb-2"></div>
                  <h3 className="text-lg font-bold text-white">{game?.players.player1.name || 'Player A'}</h3>
                  <div className="mt-2">
                    <div className="text-2xl font-bold text-white">12</div>
                    <div className="text-xs text-slate-400">Points</div>
                  </div>
                  <Progress value={65} className="mt-2" />
                </div>

                <div className="flex flex-col items-center justify-center">
                  <div className="text-3xl font-bold text-yellow-500">VS</div>
                  <div className="mt-2 text-sm text-slate-400">Round {game?.currentRound || 2}/{game?.totalRounds || 3}</div>
                  <div className="mt-2 text-xs text-slate-400">Next Question in</div>
                  <div className="text-lg font-bold text-white">0:08</div>
                </div>

                <div className="text-center">
                  <div className="h-20 w-20 rounded-full bg-red-500 mx-auto mb-2"></div>
                  <h3 className="text-lg font-bold text-white">{game?.players.player2.name || 'Player B'}</h3>
                  <div className="mt-2">
                    <div className="text-2xl font-bold text-white">10</div>
                    <div className="text-xs text-slate-400">Points</div>
                  </div>
                  <Progress value={55} className="mt-2" />
                </div>
              </div>

              {/* Current Question/Challenge Display */}
              <div className="mt-8 text-center">
                <div className="bg-slate-800 rounded-sm p-4 border border-slate-700">
                  <h4 className="text-sm font-medium text-white mb-2">Current Question:</h4>
                  <p className="text-lg text-white">What is the capital of Australia?</p>
                </div>
              </div>
            </div>
          </div>

          {/* Game Stats */}
          <div className="bg-slate-900 rounded-sm border border-slate-800 p-2">
            <div className="grid grid-cols-6 gap-2">
              <div className="text-center">
                <div className="text-xs text-slate-400">Total Pool</div>
                <div className="text-sm font-bold text-yellow-500">$2,450</div>
              </div>
              <div className="text-center">
                <div className="text-xs text-slate-400">Bets Placed</div>
                <div className="text-sm font-bold text-white">1,234</div>
              </div>
              <div className="text-center">
                <div className="text-xs text-slate-400">Avg Stake</div>
                <div className="text-sm font-bold text-white">$15</div>
              </div>
              <div className="text-center">
                <div className="text-xs text-slate-400">Player A Odds</div>
                <div className="text-sm font-bold text-blue-500">1.45x</div>
              </div>
              <div className="text-center">
                <div className="text-xs text-slate-400">Player B Odds</div>
                <div className="text-sm font-bold text-red-500">2.75x</div>
              </div>
              <div className="text-center">
                <div className="text-xs text-slate-400">Time Left</div>
                <div className="text-sm font-bold text-white">3:42</div>
              </div>
            </div>
          </div>
        </div>

        {/* Right Sidebar - Betting & Chat */}
        <div className="col-span-3 flex flex-col gap-1">
          {/* Betting Panel */}
          <div className="bg-slate-900 rounded-sm border border-slate-800 p-2">
            <h3 className="text-xs font-medium text-white mb-2">Place Your Bet</h3>

            <RadioGroup value={selectedBet} onValueChange={setSelectedBet}>
              <div className="grid grid-cols-2 gap-2 mb-2">
                <div>
                  <Label htmlFor="player-a" className="cursor-pointer">
                    <div className={`p-2 rounded-sm border ${selectedBet === 'player-a' ? 'border-blue-500 bg-blue-500/10' : 'border-slate-700'}`}>
                      <RadioGroupItem value="player-a" id="player-a" className="sr-only" />
                      <div className="text-center">
                        <div className="text-xs font-medium text-white">Player A</div>
                        <div className="text-lg font-bold text-blue-500">1.45x</div>
                        <div className="text-[10px] text-slate-400">65% win chance</div>
                      </div>
                    </div>
                  </Label>
                </div>
                <div>
                  <Label htmlFor="player-b" className="cursor-pointer">
                    <div className={`p-2 rounded-sm border ${selectedBet === 'player-b' ? 'border-red-500 bg-red-500/10' : 'border-slate-700'}`}>
                      <RadioGroupItem value="player-b" id="player-b" className="sr-only" />
                      <div className="text-center">
                        <div className="text-xs font-medium text-white">Player B</div>
                        <div className="text-lg font-bold text-red-500">2.75x</div>
                        <div className="text-[10px] text-slate-400">35% win chance</div>
                      </div>
                    </div>
                  </Label>
                </div>
              </div>
            </RadioGroup>

            <div className="mb-2">
              <Label className="text-xs">Bet Amount</Label>
              <Input
                type="number"
                value={betAmount}
                onChange={(e) => setBetAmount(e.target.value)}
                className="h-8 text-sm"
                placeholder="Enter amount"
              />
              <div className="flex justify-between mt-1">
                <span className="text-[10px] text-slate-400">Min: $5</span>
                <span className="text-[10px] text-slate-400">Max: $500</span>
              </div>
            </div>

            <div className="bg-slate-800 rounded-sm p-2 mb-2">
              <div className="flex justify-between text-xs">
                <span className="text-slate-400">Potential Win:</span>
                <span className="text-green-500 font-bold">
                  ${(parseFloat(betAmount || '0') * (selectedBet === 'player-a' ? 1.45 : 2.75)).toFixed(2)}
                </span>
              </div>
            </div>

            <Button
              className="w-full h-8 bg-gradient-to-r from-purple-500 to-pink-500"
              disabled={!betAmount || parseFloat(betAmount) < 5}
            >
              Place Bet
            </Button>
          </div>

          {/* Live Bets Feed - SCROLLABLE */}
          <div className="bg-slate-900 rounded-sm border border-slate-800 flex-1 flex flex-col overflow-hidden">
            <div className="p-2 border-b border-slate-800">
              <h3 className="text-xs font-medium text-white">Recent Bets</h3>
            </div>
            <div className="flex-1 overflow-auto p-1">
              {Array.from({ length: 20 }).map((_, idx) => (
                <div key={idx} className="mb-1 p-1.5 bg-slate-800 rounded-sm">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center">
                      <div className="h-5 w-5 rounded-full bg-slate-700 mr-1.5"></div>
                      <span className="text-[10px] text-white">User{idx + 1}</span>
                    </div>
                    <div className="text-[10px]">
                      <span className={idx % 2 === 0 ? 'text-blue-500' : 'text-red-500'}>
                        Player {idx % 2 === 0 ? 'A' : 'B'}
                      </span>
                      <span className="text-slate-400 ml-1">${(10 + idx * 5)}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Chat - SCROLLABLE */}
          <div className="h-48 bg-slate-900 rounded-sm border border-slate-800 flex flex-col">
            <div className="p-2 border-b border-slate-800">
              <h3 className="text-xs font-medium text-white">Live Chat</h3>
            </div>
            <div className="flex-1 overflow-auto p-2">
              {Array.from({ length: 15 }).map((_, idx) => (
                <div key={idx} className="mb-1">
                  <div className="flex items-start">
                    <div className="h-4 w-4 rounded-full bg-slate-700 mr-1 flex-shrink-0"></div>
                    <div>
                      <span className="text-[10px] font-medium text-white">User{idx + 1}: </span>
                      <span className="text-[10px] text-slate-300">
                        {['Go Player A!', 'That was close!', 'Easy win', 'Great game!', 'Player B is on fire!'][idx % 5]}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
            <div className="p-2 border-t border-slate-800">
              <div className="flex gap-1">
                <Input
                  value={chatMessage}
                  onChange={(e) => setChatMessage(e.target.value)}
                  placeholder="Type a message..."
                  className="h-7 text-xs"
                />
                <Button size="sm" className="h-7 px-2">
                  <Send className="h-3 w-3" />
                </Button>
              </div>
            </div>
          </div>

          {/* Leaderboard */}
          <div className="bg-slate-900 rounded-sm border border-slate-800 p-2">
            <h3 className="text-xs font-medium text-white mb-2">Top Predictors</h3>
            <div className="space-y-1">
              {Array.from({ length: 3 }).map((_, idx) => (
                <div key={idx} className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Badge className={`h-5 w-5 rounded-full mr-1.5 ${
                      idx === 0 ? 'bg-yellow-500' :
                      idx === 1 ? 'bg-slate-400' :
                      'bg-orange-600'
                    }`}>
                      {idx + 1}
                    </Badge>
                    <span className="text-xs text-white">TopBettor{idx + 1}</span>
                  </div>
                  <span className="text-xs text-green-500">+${(1250 + idx * -250)}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LiveSpectatorSection;