import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Card } from '../ui/card';
import { Badge } from '../ui/badge';
import { ScrollArea } from '../ui/scroll-area';
import { Progress } from '../ui/progress';
import { RadioGroup, RadioGroupItem } from '../ui/radio-group';
import { Label } from '../ui/label';
import { Tabs, TabsList, TabsTrigger } from '../ui/tabs';
import { useToast } from '../ui/use-toast';
import { 
  Clock, Users, Trophy, Send, Eye, Sparkles, Target, Brain, 
  Timer, Crown, Flame, Activity, ArrowLeft, Volume2, VolumeX,
  Maximize2, DollarSign, MessageSquare
} from 'lucide-react';

interface Player {
  id: string;
  name: string;
  score: number;
  isReady: boolean;
  guessesLeft: number;
  hintsUsed: number;
  avatar?: string;
  isHost?: boolean;
  timeBonus?: number;
  streak?: number;
}

interface ChatMessage {
  id: string;
  playerId: string;
  playerName: string;
  message: string;
  timestamp: Date;
  isSystem?: boolean;
  isGuess?: boolean;
  isCorrect?: boolean;
}

interface GameState {
  status: 'waiting' | 'starting' | 'playing' | 'round-end' | 'game-over';
  currentRound: number;
  totalRounds: number;
  currentImage?: string;
  answer?: string;
  blurLevel: number;
  timeRemaining: number;
  winner?: string;
  roundWinner?: string;
  correctGuesses: number;
  leaderboard?: Player[];
  nextRoundIn?: number;
  revealProgress?: number;
}

interface Hint {
  id: string;
  text: string;
  cost: number;
  used: boolean;
}

interface BlurDetectiveGameProps {
  gameId: string;
  playerId: string;
  playerName: string;
  isHost: boolean;
  onLeaveGame: () => void;
  players: Player[];
  gameState: GameState;
  chatMessages: ChatMessage[];
  onSendMessage: (message: string) => void;
  onStartGame: () => void;
  onMakeGuess: (guess: string) => void;
  onUseHint: (hintId: string) => void;
  betAmount: number;
  category: string;
  maxPlayers: number;
  rounds: number;
  timeLimit: number;
}

const CATEGORIES = {
  actors: { id: 'actors', name: 'Hollywood Stars', icon: '🎬', color: 'from-purple-500 to-pink-600' },
  musicians: { id: 'musicians', name: 'Music Icons', icon: '🎵', color: 'from-blue-500 to-purple-600' },
  athletes: { id: 'athletes', name: 'Sports Legends', icon: '⚽', color: 'from-green-500 to-blue-600' },
  mixed: { id: 'mixed', name: 'All Stars', icon: '🌟', color: 'from-yellow-500 to-red-600' }
};

const HINTS = [
  { id: '1', text: 'This is a famous Hollywood actor', cost: 10, used: false },
  { id: '2', text: 'Known for Titanic and Inception', cost: 20, used: false },
  { id: '3', text: 'Won Oscar for The Revenant', cost: 30, used: false }
];

// Available celebrity images
const CELEBRITY_IMAGES = [
  '/games/bd/images/celebrity1.jpg',
  '/games/bd/images/celebrity2.jpg'
];

export default function BlurDetectiveGame({
  gameId,
  playerId,
  playerName,
  isHost,
  onLeaveGame,
  players,
  gameState,
  chatMessages,
  onSendMessage,
  onStartGame,
  onMakeGuess,
  onUseHint,
  betAmount,
  category,
  maxPlayers,
  rounds,
  timeLimit
}: BlurDetectiveGameProps) {
  const [chatInput, setChatInput] = useState('');
  const [guessInput, setGuessInput] = useState('');
  const [hints, setHints] = useState<Hint[]>(HINTS);
  const [imageLoaded, setImageLoaded] = useState(false);
  const [selectedBet, setSelectedBet] = useState('');
  const [betAmountInput, setBetAmountInput] = useState('10');
  const [soundEnabled, setSoundEnabled] = useState(true);
  const [selectedTab, setSelectedTab] = useState('live');
  const [isFrozen, setIsFrozen] = useState(false);
  const [freezeTime, setFreezeTime] = useState<number | null>(null);
  const [revealStartTime, setRevealStartTime] = useState<number | null>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const chatScrollRef = useRef<HTMLDivElement>(null);
  const revealIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const { toast } = useToast();

  const currentPlayer = players.find(p => p.id === playerId);
  const currentCategory = CATEGORIES[category as keyof typeof CATEGORIES] || CATEGORIES.mixed;
  
  // Use provided image or fallback to celebrity images
  const currentImage = gameState.currentImage || CELEBRITY_IMAGES[gameState.currentRound % CELEBRITY_IMAGES.length];

  const [currentBlur, setCurrentBlur] = useState(gameState.blurLevel);
  
  // Progressive blur reveal effect
  useEffect(() => {
    if (gameState.status === 'playing' && !isFrozen) {
      if (!revealStartTime) {
        setRevealStartTime(Date.now());
      }
      
      const startTime = revealStartTime || Date.now();
      
      revealIntervalRef.current = setInterval(() => {
        const elapsed = (Date.now() - startTime) / 1000;
        const totalRevealTime = timeLimit; // Use timeLimit prop for reveal duration
        const progress = Math.min(elapsed / totalRevealTime, 1);
        
        // Calculate new blur level (starts at gameState.blurLevel, goes down to 5)
        const minBlur = 5;
        const maxBlur = gameState.blurLevel || 40;
        const newBlur = Math.max(minBlur, maxBlur - (maxBlur - minBlur) * progress);
        
        setCurrentBlur(newBlur);
        
        // Update time display
        const timeRemaining = Math.max(0, Math.floor(totalRevealTime - elapsed));
        const timeElement = document.querySelector('[data-time-remaining]');
        if (timeElement) {
          timeElement.textContent = timeRemaining + 's';
        }
        
        // Update clarity progress
        const clarityPercent = Math.round(progress * 100);
        const clarityElement = document.querySelector('[data-clarity-progress]');
        if (clarityElement) {
          clarityElement.textContent = clarityPercent + '%';
        }
        
        // Auto-reveal when time runs out
        if (progress >= 1) {
          handleAutoReveal();
        }
      }, 100); // Update every 100ms for smooth transition
      
      return () => {
        if (revealIntervalRef.current) {
          clearInterval(revealIntervalRef.current);
        }
      };
    }
  }, [gameState.status, isFrozen, revealStartTime, timeLimit, gameState.blurLevel]);

  // Apply blur effect to image
  useEffect(() => {
    if (currentImage && canvasRef.current) {
      const canvas = canvasRef.current;
      const ctx = canvas.getContext('2d');
      if (!ctx) return;

      const img = new Image();
      img.crossOrigin = 'anonymous';
      
      img.onload = () => {
        setImageLoaded(true);
        // Increase canvas size for better visibility
        canvas.width = 500;
        canvas.height = 400;
        
        // Clear canvas first
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        // Scale image to fit while maintaining aspect ratio (object-contain)
        const scale = Math.min(canvas.width / img.width, canvas.height / img.height);
        const scaledWidth = img.width * scale;
        const scaledHeight = img.height * scale;
        const x = (canvas.width - scaledWidth) / 2;
        const y = (canvas.height - scaledHeight) / 2;
        
        // Set background to match the game theme
        ctx.fillStyle = '#0f172a';
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        
        ctx.filter = `blur(${currentBlur}px)`;
        ctx.drawImage(img, x, y, scaledWidth, scaledHeight);

        if (gameState.status === 'round-end' && gameState.revealProgress) {
          const clearRadius = (Math.min(canvas.width, canvas.height) / 2) * (gameState.revealProgress / 100);
          const centerX = canvas.width / 2;
          const centerY = canvas.height / 2;

          ctx.globalCompositeOperation = 'destination-out';
          const gradient = ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, clearRadius);
          gradient.addColorStop(0, 'rgba(0,0,0,1)');
          gradient.addColorStop(0.8, 'rgba(0,0,0,0.3)');
          gradient.addColorStop(1, 'rgba(0,0,0,0)');
          
          ctx.fillStyle = gradient;
          ctx.fillRect(0, 0, canvas.width, canvas.height);

          ctx.globalCompositeOperation = 'destination-over';
          ctx.filter = 'none';
          ctx.drawImage(img, x, y, scaledWidth, scaledHeight);
        }
      };
      
      img.onerror = () => {
        console.error('Failed to load image:', currentImage);
        setImageLoaded(false);
      };
      
      img.src = currentImage;
    }
  }, [currentImage, currentBlur, gameState.status, gameState.revealProgress]);

  // Auto-scroll chat
  useEffect(() => {
    if (chatScrollRef.current) {
      chatScrollRef.current.scrollTop = chatScrollRef.current.scrollHeight;
    }
  }, [chatMessages]);

  // Reset freeze state on new round
  useEffect(() => {
    if (gameState.status === 'playing') {
      setIsFrozen(false);
      setFreezeTime(null);
      setRevealStartTime(Date.now());
      setCurrentBlur(gameState.blurLevel || 40);
    }
  }, [gameState.currentRound, gameState.blurLevel]);

  const handleSendChat = () => {
    if (chatInput.trim()) {
      onSendMessage(chatInput);
      setChatInput('');
    }
  };

  const handleMakeGuess = () => {
    if (guessInput.trim() && currentPlayer?.guessesLeft && currentPlayer.guessesLeft > 0) {
      onMakeGuess(guessInput);
      
      // Calculate time bonus if correct (for display purposes)
      const isCorrect = guessInput.toLowerCase().includes('leonardo') || guessInput.toLowerCase().includes('dicaprio');
      if (isCorrect && freezeTime !== null) {
        const timeBonus = Math.max(0, Math.floor((timeLimit - freezeTime) * 10));
        toast({
          title: "Correct! 🎉",
          description: `Base points: 100 + Time bonus: ${timeBonus} = ${100 + timeBonus} points!`,
          duration: 5000
        });
      }
      
      setGuessInput('');
    }
  };

  const handleUseHint = (hint: Hint) => {
    if (!hint.used && currentPlayer && currentPlayer.score >= hint.cost) {
      onUseHint(hint.id);
      setHints(hints.map(h => h.id === hint.id ? { ...h, used: true } : h));
      toast({
        title: "Hint Unlocked!",
        description: hint.text,
        duration: 5000
      });
    }
  };

  const handleFreezeImage = () => {
    if (!isFrozen && gameState.status === 'playing') {
      setIsFrozen(true);
      const elapsed = (Date.now() - (revealStartTime || Date.now())) / 1000;
      setFreezeTime(elapsed);
      
      // Stop the reveal timer
      if (revealIntervalRef.current) {
        clearInterval(revealIntervalRef.current);
      }
      
      toast({
        title: "Image Frozen!",
        description: `Frozen at ${elapsed.toFixed(1)}s - Now make your guess!`,
        duration: 3000
      });
      
      // Add system message
      const freezeMessage: ChatMessage = {
        id: Date.now().toString(),
        playerId: 'system',
        playerName: 'System',
        message: `${playerName} froze the image at ${elapsed.toFixed(1)}s!`,
        timestamp: new Date(),
        isSystem: true
      };
      onSendMessage(freezeMessage.message);
    }
  };

  const handleAutoReveal = () => {
    if (!isFrozen) {
      setIsFrozen(true);
      setFreezeTime(timeLimit);
      
      if (revealIntervalRef.current) {
        clearInterval(revealIntervalRef.current);
      }
      
      toast({
        title: "Time's Up!",
        description: "Image fully revealed - Make your guess now!",
        duration: 3000
      });
    }
  };

  return (
    <div className="min-h-screen bg-slate-950 text-white pt-16">
      {/* REDESIGNED LAYOUT - MATCHES APP THEME */}
      <div className="grid grid-cols-12 gap-1 p-1 min-h-[calc(100vh-8rem)]">
        {/* Left Sidebar - Game Info & Players */}
        <div className="col-span-2 flex flex-col gap-1">
          {/* Game Info */}
          <div className="bg-slate-900 rounded-sm border border-slate-800 p-2">
            <h3 className="text-xs font-medium text-white mb-2">Game Info</h3>
            <div className="space-y-1">
              <div className="flex justify-between text-[10px]">
                <span className="text-slate-400">Category:</span>
                <span className="text-white">{currentCategory.name}</span>
              </div>
              <div className="flex justify-between text-[10px]">
                <span className="text-slate-400">Round:</span>
                <span className="text-white">{gameState.currentRound}/{gameState.totalRounds}</span>
              </div>
              <div className="flex justify-between text-[10px]">
                <span className="text-slate-400">Pool:</span>
                <span className="text-yellow-500">₹{betAmount * players.length}</span>
              </div>
              <div className="flex justify-between text-[10px]">
                <span className="text-slate-400">Players:</span>
                <span className="text-white">{players.length}/{maxPlayers}</span>
              </div>
            </div>
          </div>

          {/* Players List - SCROLLABLE */}
          <div className="flex-1 bg-slate-900 rounded-sm border border-slate-800 flex flex-col overflow-hidden">
            <div className="p-2 border-b border-slate-800">
              <h3 className="text-xs font-medium text-white">Players</h3>
            </div>
            <div className="flex-1 overflow-auto p-1">
              {players
                .sort((a, b) => b.score - a.score)
                .map((player, index) => (
                  <div key={player.id} className="mb-1 p-2 bg-slate-800 rounded-sm border border-slate-700">
                    <div className="flex justify-between items-start mb-1">
                      <div className="flex items-center">
                        <div className={`w-6 h-6 rounded-full flex items-center justify-center text-[10px] font-bold mr-2 ${
                          index === 0 ? 'bg-yellow-500 text-black' :
                          index === 1 ? 'bg-gray-400 text-white' :
                          index === 2 ? 'bg-orange-600 text-white' :
                          'bg-slate-600 text-white'
                        }`}>
                          {index + 1}
                        </div>
                        <div>
                          <h4 className="text-[10px] font-medium text-white truncate max-w-[60px]">
                            {player.name}
                            {player.isHost && <Crown className="w-2.5 h-2.5 inline ml-1" />}
                          </h4>
                          <div className="text-[9px] text-slate-400">
                            {player.guessesLeft} guesses left
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-[10px] font-bold text-white">{player.score}</div>
                        {player.streak && player.streak > 0 && (
                          <div className="text-[8px] text-orange-500 flex items-center">
                            <Flame className="w-2 h-2 mr-0.5" />
                            {player.streak}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
            </div>
          </div>
        </div>

        {/* Main Game Area */}
        <div className="col-span-7 flex flex-col gap-1">
          {/* Game Header */}
          <div className="bg-slate-900 rounded-sm border border-slate-800 p-2 flex justify-between items-center">
            <div className="flex items-center gap-3">
              <Button
                variant="ghost"
                size="sm"
                onClick={onLeaveGame}
                className="h-6 w-6 p-0"
              >
                <ArrowLeft className="h-3 w-3" />
              </Button>
              <div className="flex items-center gap-2">
                <div className={`p-1 rounded bg-gradient-to-br ${currentCategory.color}`}>
                  <Eye className="h-4 w-4 text-white" />
                </div>
                <div>
                  <h2 className="text-sm font-bold text-white">Blur Detective</h2>
                  <div className="text-xs text-slate-400">{currentCategory.name}</div>
                </div>
              </div>
              <Badge className="bg-red-500 text-[10px] h-4 px-1">
                <Activity className="h-2.5 w-2.5 mr-0.5" />
                LIVE
              </Badge>
            </div>
            <div className="flex items-center gap-2">
              <div className="text-xs text-slate-400">
                Time: <span className="text-white font-mono" data-time-remaining>{gameState.timeRemaining}s</span>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSoundEnabled(!soundEnabled)}
                className="h-6 w-6 p-0"
              >
                {soundEnabled ? <Volume2 className="h-3 w-3" /> : <VolumeX className="h-3 w-3" />}
              </Button>
            </div>
          </div>

          {/* Main Game Content */}
          <div className="flex-1 bg-slate-900 rounded-sm border border-slate-800 p-4 overflow-hidden">
            {gameState.status === 'playing' ? (
              <div className="h-full flex flex-col">
                {/* Image Area - Larger and centered */}
                <div className="flex-1 flex items-center justify-center mb-4 min-h-[400px]">
                  <div className="relative bg-slate-800/50 rounded-lg overflow-hidden border border-slate-700 p-4">
                    <canvas
                      ref={canvasRef}
                      className="block"
                      style={{ width: '500px', height: '400px' }}
                    />
                    {!imageLoaded && (
                      <div className="absolute inset-0 flex items-center justify-center">
                        <div className="animate-spin rounded-full h-12 w-12 border-4 border-primary border-t-transparent" />
                      </div>
                    )}
                    
                    {/* Blur Level Indicator */}
                    <div className="absolute bottom-2 left-2 right-2">
                      <div className="bg-black/70 backdrop-blur-sm rounded p-2">
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-white text-xs">Clarity</span>
                          <span className="text-white text-xs font-mono" data-clarity-progress>
                            {Math.round(((gameState.blurLevel - currentBlur) / (gameState.blurLevel - 5)) * 100)}%
                          </span>
                        </div>
                        <Progress value={((gameState.blurLevel - currentBlur) / (gameState.blurLevel - 5)) * 100} className="h-1" />
                      </div>
                    </div>
                  </div>
                </div>

                {/* Game Controls */}
                <div className="bg-slate-800 rounded p-3">
                  {gameState.status === 'playing' && !isFrozen ? (
                    <div className="text-center">
                      <Button 
                        onClick={handleFreezeImage}
                        className="h-10 px-6 bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600"
                      >
                        <Timer className="w-4 h-4 mr-2" />
                        Freeze & Answer
                      </Button>
                      <div className="mt-2 text-xs text-slate-400">
                        Freeze the image when you think you know who it is!
                      </div>
                    </div>
                  ) : (
                    <div>
                      <form onSubmit={(e) => { e.preventDefault(); handleMakeGuess(); }} className="flex gap-2">
                        <Input
                          value={guessInput}
                          onChange={(e) => setGuessInput(e.target.value)}
                          placeholder="Enter your guess..."
                          disabled={!currentPlayer?.guessesLeft || currentPlayer.guessesLeft === 0}
                          className="flex-1 h-8"
                        />
                        <Button 
                          type="submit" 
                          disabled={!currentPlayer?.guessesLeft || currentPlayer.guessesLeft === 0}
                          className="h-8"
                        >
                          <Send className="w-3 h-3 mr-1" />
                          Guess
                        </Button>
                      </form>
                      <div className="mt-2 flex justify-between text-xs text-slate-400">
                        <span>Guesses remaining: {currentPlayer?.guessesLeft || 0}</span>
                        <span>Score: {currentPlayer?.score || 0}</span>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ) : gameState.status === 'waiting' ? (
              <div className="h-full flex items-center justify-center">
                <div className="text-center">
                  <Eye className="w-16 h-16 mx-auto mb-4 text-slate-600" />
                  <h3 className="text-lg font-semibold mb-2">Waiting for players...</h3>
                  <p className="text-sm text-slate-400">Game will start when ready</p>
                  {isHost && players.length >= 2 && (
                    <Button onClick={onStartGame} className="mt-4">
                      Start Game
                    </Button>
                  )}
                </div>
              </div>
            ) : gameState.status === 'round-end' ? (
              <div className="h-full flex items-center justify-center">
                <div className="text-center">
                  <Trophy className="w-16 h-16 mx-auto mb-4 text-yellow-500" />
                  <h3 className="text-lg font-semibold mb-2">Round Complete!</h3>
                  <p className="text-sm text-slate-400">The answer was: <strong>{gameState.answer}</strong></p>
                  {gameState.roundWinner && (
                    <p className="text-sm text-green-500">Winner: {players.find(p => p.id === gameState.roundWinner)?.name}</p>
                  )}
                </div>
              </div>
            ) : (
              <div className="h-full flex items-center justify-center">
                <div className="text-center">
                  <Crown className="w-16 h-16 mx-auto mb-4 text-yellow-500" />
                  <h3 className="text-lg font-semibold mb-2">Game Over!</h3>
                  <p className="text-sm text-slate-400">Thanks for playing!</p>
                </div>
              </div>
            )}
          </div>

          {/* Hints Bar */}
          <div className="bg-slate-900 rounded-sm border border-slate-800 p-2">
            <div className="flex items-center justify-between">
              <h4 className="text-xs font-medium text-white">Available Hints</h4>
              <div className="flex gap-1">
                {hints.map((hint, index) => (
                  <Button
                    key={hint.id}
                    variant={hint.used ? "secondary" : "outline"}
                    size="sm"
                    className="h-6 text-[10px] px-2"
                    onClick={() => handleUseHint(hint)}
                    disabled={hint.used || !currentPlayer || currentPlayer.score < hint.cost}
                  >
                    {hint.used ? `Hint ${index + 1}` : `Hint ${index + 1} (-${hint.cost})`}
                  </Button>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Right Sidebar - Betting & Chat - Extends to bottom */}
        <div className="col-span-3 flex flex-col gap-1 h-full">
          {/* Betting Panel */}
          <div className="bg-slate-900 rounded-sm border border-slate-800 p-2">
            <h3 className="text-xs font-medium text-white mb-2">Place Your Bet</h3>
            
            <div className="mb-2">
              <Label className="text-xs">Bet on Winner</Label>
              <RadioGroup value={selectedBet} onValueChange={setSelectedBet}>
                <div className="grid grid-cols-1 gap-1 mt-1">
                  {players.slice(0, 3).map((player) => (
                    <div key={player.id}>
                      <Label htmlFor={player.id} className="cursor-pointer">
                        <div className={`p-1.5 rounded-sm border text-xs ${selectedBet === player.id ? 'border-blue-500 bg-blue-500/10' : 'border-slate-700'}`}>
                          <RadioGroupItem value={player.id} id={player.id} className="sr-only" />
                          <div className="flex justify-between items-center">
                            <span className="text-white truncate">{player.name}</span>
                            <span className="text-blue-400 font-bold">2.5x</span>
                          </div>
                        </div>
                      </Label>
                    </div>
                  ))}
                </div>
              </RadioGroup>
            </div>

            <div className="mb-2">
              <Label className="text-xs">Amount</Label>
              <Input
                type="number"
                value={betAmountInput}
                onChange={(e) => setBetAmountInput(e.target.value)}
                className="h-6 text-xs"
                placeholder="₹10"
              />
            </div>

            <Button
              className="w-full h-6 bg-gradient-to-r from-purple-500 to-pink-500 text-xs"
              disabled={!selectedBet || !betAmountInput}
            >
              Place Bet
            </Button>
          </div>

          {/* Live Chat - SCROLLABLE - Extended to bottom */}
          <div className="flex-1 bg-slate-900 rounded-sm border border-slate-800 flex flex-col">
            <div className="p-2 border-b border-slate-800">
              <h3 className="text-xs font-medium text-white">Live Chat</h3>
            </div>
            <div className="flex-1 overflow-auto p-2">
              <div ref={chatScrollRef}>
                {chatMessages.map(msg => (
                  <div key={msg.id} className="mb-1">
                    <div className="flex items-start">
                      <div className={`h-4 w-4 rounded-full mr-1 flex-shrink-0 ${
                        msg.isSystem ? 'bg-purple-500' :
                        msg.playerId === playerId ? 'bg-blue-500' : 'bg-slate-700'
                      }`}></div>
                      <div>
                        <span className={`text-[10px] font-medium ${
                          msg.isSystem ? 'text-purple-400' :
                          msg.playerId === playerId ? 'text-blue-400' : 'text-white'
                        }`}>
                          {msg.isSystem ? 'SYSTEM' : msg.playerName}: 
                        </span>
                        <span className={`text-[10px] ${
                          msg.isGuess 
                            ? msg.isCorrect 
                              ? 'text-green-400 font-bold' 
                              : 'text-red-400'
                            : msg.isSystem 
                              ? 'text-purple-300' 
                              : 'text-slate-300'
                        }`}>
                          {msg.message}
                          {msg.isCorrect && ' ✅'}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            <div className="p-2 border-t border-slate-800">
              <div className="flex gap-1">
                <Input
                  value={chatInput}
                  onChange={(e) => setChatInput(e.target.value)}
                  onKeyPress={(e) => {
                    if (e.key === 'Enter' && chatInput.trim()) {
                      handleSendChat();
                    }
                  }}
                  placeholder="Type a message..."
                  className="h-7 text-xs"
                />
                <Button 
                  size="sm" 
                  className="h-7 px-2"
                  onClick={handleSendChat}
                >
                  <Send className="h-3 w-3" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Footer - Matches other screens */}
      <div className="bg-slate-900 border-t border-slate-800 p-4 mt-4">
        <div className="max-w-6xl mx-auto text-center">
          <div className="grid grid-cols-4 gap-4 text-xs text-slate-400">
            <div>
              <div className="font-medium text-white mb-1">Game Stats</div>
              <div>Round {gameState.currentRound}/{gameState.totalRounds}</div>
            </div>
            <div>
              <div className="font-medium text-white mb-1">Prize Pool</div>
              <div className="text-yellow-500">₹{betAmount * players.length}</div>
            </div>
            <div>
              <div className="font-medium text-white mb-1">Players</div>
              <div>{players.length}/{maxPlayers} Active</div>
            </div>
            <div>
              <div className="font-medium text-white mb-1">Category</div>
              <div>{currentCategory.name}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}