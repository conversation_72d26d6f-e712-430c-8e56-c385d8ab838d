import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import Breadcrumb, { generateGameBreadcrumbs } from '@/components/shared/Breadcrumb';
import GameInstanceCard, { GameInstance } from './GameInstanceCard';
import GameStatusBadge, { GameInstanceStatus } from './GameStatusBadge';
import {
  Plus,
  Filter,
  Search,
  Grid,
  List,
  Calendar,
  Users,
  DollarSign,
  RefreshCw
} from 'lucide-react';

interface GameInstancesPageProps {
  className?: string;
}

const GameInstancesPage: React.FC<GameInstancesPageProps> = ({ className = '' }) => {
  const { gameSlug } = useParams<{ gameSlug: string }>();
  const navigate = useNavigate();

  const [instances, setInstances] = useState<GameInstance[]>([]);
  const [filteredInstances, setFilteredInstances] = useState<GameInstance[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<GameInstanceStatus | 'all'>('all');
  const [stakesFilter, setStakesFilter] = useState<'all' | 'low' | 'medium' | 'high'>('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  // Mock data - replace with actual API call
  useEffect(() => {
    const loadInstances = async () => {
      setLoading(true);

      // Mock instances data
      const mockInstances: GameInstance[] = [
        {
          id: 'inst-1',
          hostName: 'ProGamer123',
          gameType: gameSlug,
          players: [
            { id: '1', name: 'ProGamer123' },
            { id: '2', name: 'ChessKing' }
          ],
          maxPlayers: 2,
          stakes: 50,
          status: 'live',
          viewers: 15,
          gameMode: 'Blitz',
          createdAt: new Date(Date.now() - 30 * 60 * 1000),
          timeLimit: 300
        },
        {
          id: 'inst-2',
          hostName: 'NewPlayer',
          gameType: gameSlug,
          players: [{ id: '3', name: 'NewPlayer' }],
          maxPlayers: 2,
          stakes: 25,
          status: 'waiting',
          gameMode: 'Classic',
          createdAt: new Date(Date.now() - 10 * 60 * 1000),
          timeLimit: 600
        },
        {
          id: 'inst-3',
          hostName: 'TourneyHost',
          gameType: gameSlug,
          players: [],
          maxPlayers: 4,
          stakes: 100,
          status: 'scheduled',
          startTime: new Date(Date.now() + 2 * 60 * 60 * 1000),
          gameMode: 'Tournament',
          createdAt: new Date(Date.now() - 60 * 60 * 1000)
        },
        {
          id: 'inst-4',
          hostName: 'VeteranPlayer',
          gameType: gameSlug,
          players: [
            { id: '4', name: 'VeteranPlayer' },
            { id: '5', name: 'Challenger' }
          ],
          maxPlayers: 2,
          stakes: 75,
          status: 'completed',
          gameMode: 'Rapid',
          createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000)
        }
      ];

      setInstances(mockInstances);
      setLoading(false);
    };

    loadInstances();
  }, [gameSlug]);

  // Filter instances based on search and filters
  useEffect(() => {
    let filtered = instances;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(instance =>
        instance.hostName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        instance.gameMode?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(instance => instance.status === statusFilter);
    }

    // Stakes filter
    if (stakesFilter !== 'all') {
      filtered = filtered.filter(instance => {
        switch (stakesFilter) {
          case 'low': return instance.stakes <= 25;
          case 'medium': return instance.stakes > 25 && instance.stakes <= 75;
          case 'high': return instance.stakes > 75;
          default: return true;
        }
      });
    }

    setFilteredInstances(filtered);
  }, [instances, searchTerm, statusFilter, stakesFilter]);

  const handleCreateInstance = () => {
    navigate(`/games/${gameSlug}/create`);
  };

  const handleJoinInstance = (instanceId: string) => {
    navigate(`/games/${gameSlug}/session/${instanceId}`);
  };

  const handleSpectateInstance = (instanceId: string) => {
    navigate(`/games/${gameSlug}/spectate/${instanceId}`);
  };

  const handleScheduleReminder = (instanceId: string) => {
    // Implement reminder functionality
    console.log('Setting reminder for instance:', instanceId);
  };

  const getGameDisplayName = (slug: string) => {
    return slug.split('-').map(word =>
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  const breadcrumbs = generateGameBreadcrumbs(gameSlug);

  if (loading) {
    return (
      <div className="min-h-screen bg-slate-950 pt-16">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-center h-64">
            <RefreshCw className="h-8 w-8 animate-spin text-slate-400" />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen bg-slate-950 pt-16 ${className}`}>
      <div className="container mx-auto px-4 py-6">
        {/* Breadcrumb Navigation */}
        <Breadcrumb items={breadcrumbs} className="mb-6" />

        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold text-white mb-2">
              {getGameDisplayName(gameSlug || '')} Instances
            </h1>
            <p className="text-slate-400">
              Join existing games or create your own instance
            </p>
          </div>

          <Button
            onClick={handleCreateInstance}
            className="mt-4 sm:mt-0 bg-green-600 hover:bg-green-700"
          >
            <Plus className="h-4 w-4 mr-2" />
            Create New Instance
          </Button>
        </div>

        {/* Filters and Search */}
        <div className="bg-slate-900 border border-slate-800 rounded-lg p-4 mb-6">
          <div className="flex flex-col lg:flex-row gap-4">
            {/* Search */}
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                <Input
                  placeholder="Search by host name or game mode..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 bg-slate-800 border-slate-700"
                />
              </div>
            </div>

            {/* Status Filter */}
            <div className="flex gap-2">
              {(['all', 'live', 'waiting', 'scheduled', 'completed'] as const).map((status) => (
                <Button
                  key={status}
                  variant={statusFilter === status ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setStatusFilter(status)}
                  className="capitalize"
                >
                  {status === 'all' ? 'All' : <GameStatusBadge status={status} showIcon={false} size="sm" />}
                </Button>
              ))}
            </div>

            {/* View Mode Toggle */}
            <div className="flex border border-slate-700 rounded-md">
              <Button
                variant={viewMode === 'grid' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('grid')}
                className="rounded-r-none"
              >
                <Grid className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('list')}
                className="rounded-l-none"
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Instances Grid/List */}
        {filteredInstances.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-slate-400 mb-4">
              No instances found matching your criteria
            </div>
            <Button onClick={handleCreateInstance} variant="outline">
              <Plus className="h-4 w-4 mr-2" />
              Create the First Instance
            </Button>
          </div>
        ) : (
          <div className={
            viewMode === 'grid'
              ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4'
              : 'space-y-4'
          }>
            {filteredInstances.map((instance) => (
              <GameInstanceCard
                key={instance.id}
                instance={instance}
                onJoin={handleJoinInstance}
                onSpectate={handleSpectateInstance}
                onSchedule={handleScheduleReminder}
                className={viewMode === 'list' ? 'max-w-none' : ''}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default GameInstancesPage;
