import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  Trophy,
  Users,
  DollarSign,
  Clock,
  Search,
  Plus,
  Filter,
  UserX,
  Timer,
  ChevronDown,
  Star,
  Zap,
  Brain,
  Sword,
  Gamepad,
  Eye,
  Image,
  Keyboard,
  Layers,
  Radio,
  PlayCircle,
  ListFilter,
  BookOpen,
  Grid3x3,
  Film
} from 'lucide-react';
import type { GameData } from './gameTypes';

interface ChallengeArenaGamesProps {
  onGameSelect: (game: GameData) => void;
}

// Main Challenge Arena Games Screen
const ChallengeArenaGames: React.FC<ChallengeArenaGamesProps> = ({ onGameSelect }) => {
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [showFilters, setShowFilters] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  // Helper function to create game data
  const createGameData = (id: string, name: string, type: string, prize: number, viewers: number, status: 'waiting' | 'live' | 'finished' = 'live'): GameData => ({
    id,
    name,
    type,
    players: {
      player1: { id: '1', name: 'Player A' },
      player2: { id: '2', name: 'Player B' }
    },
    currentRound: Math.floor(Math.random() * 3) + 1,
    totalRounds: 3,
    prize,
    viewers,
    status
  });

  // All available games
  const allGames = [
    {
      id: 'chess-blitz',
      name: 'Chess Blitz',
      icon: <Layers className="h-3.5 w-3.5" />,
      description: 'Rapid chess with 2-minute time limit',
      playerMode: '1v1',
      stakeRange: '$5-100',
      duration: '3 min',
      difficulty: 'Hard',
      skillType: 'Strategy',
      category: 'pvp',
      popularity: 92,
      isLive: true,
      activePlayers: 14,
      prize: 100,
      viewers: 234,
      type: 'strategy',
      status: 'live' as const
    },
    {
      id: 'speed-trivia',
      name: 'Speed Trivia',
      icon: <Brain className="h-3.5 w-3.5" />,
      description: 'Answer trivia questions faster than opponents',
      playerMode: '1vN',
      stakeRange: '$1-25',
      duration: '2 min',
      difficulty: 'Medium',
      skillType: 'Knowledge',
      category: 'pvp',
      popularity: 85,
      isLive: true,
      activePlayers: 28,
      prize: 25,
      viewers: 1234,
      type: 'trivia',
      status: 'live' as const
    },
    {
      id: 'tap-dash',
      name: 'Tap Dash',
      icon: <Zap className="h-3.5 w-3.5" />,
      description: 'Test your reflexes - tap when green!',
      playerMode: '1v1',
      stakeRange: '$1-20',
      duration: '1 min',
      difficulty: 'Easy',
      skillType: 'Reaction',
      category: 'pve',
      popularity: 76,
      isLive: true,
      activePlayers: 19,
      prize: 20,
      viewers: 89,
      type: 'reaction',
      status: 'live' as const
    },
    {
      id: 'spot-fake',
      name: 'Spot The Fake',
      icon: <Eye className="h-3.5 w-3.5" />,
      description: 'Find the incorrect image or fact',
      playerMode: '1vN',
      stakeRange: '$2-50',
      duration: '3 min',
      difficulty: 'Medium',
      skillType: 'Visual',
      category: 'pvp',
      popularity: 68,
      isLive: false,
      activePlayers: 7,
      prize: 50,
      viewers: 45,
      type: 'visual',
      status: 'waiting' as const
    },
    {
      id: 'blur-detective-classic',
      name: 'Blur Detective Classic',
      icon: <Image className="h-3.5 w-3.5" />,
      description: 'Identify progressively unblurring images',
      playerMode: '1v1',
      stakeRange: '$5-50',
      duration: '2 min',
      difficulty: 'Medium',
      skillType: 'Visual',
      category: 'pvp',
      popularity: 94,
      isLive: true,
      activePlayers: 31,
      prize: 50,
      viewers: 567,
      type: 'visual',
      status: 'live' as const
    },
    {
      id: 'checkers',
      name: 'Checkers',
      icon: <Grid3x3 className="h-3.5 w-3.5" />,
      description: 'Classic board game strategy',
      playerMode: '1v1',
      stakeRange: '$1-100',
      duration: '5-15 min',
      difficulty: 'Medium',
      skillType: 'Strategy',
      category: 'pvp',
      popularity: 88,
      isLive: true,
      activePlayers: 42,
      prize: 50,
      viewers: 234,
      type: 'checkers',
      status: 'waiting' as const
    },
    {
      id: 'chess',
      name: 'Chess',
      icon: <Layers className="h-3.5 w-3.5" />,
      description: 'The ultimate strategy board game',
      playerMode: '1v1',
      stakeRange: '$5-500',
      duration: '10-30 min',
      difficulty: 'Hard',
      skillType: 'Strategy',
      category: 'pvp',
      popularity: 95,
      isLive: true,
      activePlayers: 67,
      prize: 100,
      viewers: 412,
      type: 'chess',
      status: 'waiting' as const
    },
    {
      id: 'connect-four',
      name: 'Connect Four',
      icon: <Layers className="h-3.5 w-3.5" />,
      description: 'Line up 4 pieces to win',
      playerMode: '1v1',
      stakeRange: '$1-50',
      duration: '5 min',
      difficulty: 'Easy',
      skillType: 'Strategy',
      category: 'pvp',
      popularity: 82,
      isLive: true,
      activePlayers: 18,
      prize: 50,
      viewers: 156,
      type: 'strategy',
      status: 'live' as const
    },
    {
      id: 'quiz-arena',
      name: 'Quiz Arena',
      icon: <Brain className="h-3.5 w-3.5" />,
      description: 'Multi-topic quiz championship',
      playerMode: 'NvN',
      stakeRange: '$2-100',
      duration: '4 min',
      difficulty: 'Hard',
      skillType: 'Knowledge',
      category: 'pvp',
      popularity: 90,
      isLive: true,
      activePlayers: 35,
      prize: 100,
      viewers: 789,
      type: 'quiz-arena',
      status: 'live' as const
    },
    {
      id: 'word-jumble',
      name: 'Word Jumble',
      icon: <BookOpen className="h-3.5 w-3.5" />,
      description: 'Create the longest word from jumbled letters',
      playerMode: '1vN',
      stakeRange: '$1-50',
      duration: '2 min',
      difficulty: 'Medium',
      skillType: 'Knowledge',
      category: 'pvp',
      popularity: 79,
      isLive: true,
      activePlayers: 22,
      prize: 50,
      viewers: 345,
      type: 'word-jumble',
      status: 'live' as const
    },
    {
      id: 'highlight-hero',
      name: 'Highlight Hero',
      icon: <Film className="h-3.5 w-3.5" />,
      description: 'Watch video highlights and test your knowledge',
      playerMode: '1v1',
      stakeRange: '$10-200',
      duration: '5 min',
      difficulty: 'Medium',
      skillType: 'Knowledge',
      category: 'pvp',
      popularity: 88,
      isLive: true,
      activePlayers: 16,
      prize: 50,
      viewers: 523,
      type: 'highlight-hero',
      status: 'live' as const
    },
    {
      id: 'blur-detective',
      name: 'Blur Detective',
      icon: <Eye className="h-3.5 w-3.5" />,
      description: 'Guess the celebrity from blurred images',
      playerMode: '2-8',
      stakeRange: '$10-1000',
      duration: '5-10 min',
      difficulty: 'Medium',
      skillType: 'Visual',
      category: 'pvp',
      popularity: 92,
      isLive: true,
      activePlayers: 24,
      prize: 100,
      viewers: 687,
      type: 'blur-detective',
      status: 'live' as const
    }
  ];

  // Filter games based on search query and category
  const filteredGames = allGames.filter(game => {
    const matchesSearch = game.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         game.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || game.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  return (
    <div className="min-h-screen bg-slate-950 text-white pt-16">
      {/* Main Grid Layout */}
      <div className="grid grid-cols-12 gap-1 p-1 h-[calc(100vh-64px)]">
        {/* Left Sidebar - Game Categories & Filters */}
        <div className="col-span-2 flex flex-col gap-1 overflow-hidden">
          {/* Game Types Navigation */}
          <div className="bg-slate-900 border border-slate-800 rounded-sm p-1">
            <h3 className="text-xs font-medium text-white mb-1.5">Game Categories</h3>
            <div className="space-y-1">
              <CategoryButton
                id="all"
                label="All Games"
                icon={<Gamepad className="h-3 w-3" />}
                count={24}
                active={selectedCategory === 'all'}
                onClick={() => setSelectedCategory('all')}
              />
              <CategoryButton
                id="pvp"
                label="Player vs Player"
                icon={<Sword className="h-3 w-3" />}
                count={10}
                active={selectedCategory === 'pvp'}
                onClick={() => setSelectedCategory('pvp')}
              />
              <CategoryButton
                id="pve"
                label="Player vs Time"
                icon={<Clock className="h-3 w-3" />}
                count={8}
                active={selectedCategory === 'pve'}
                onClick={() => setSelectedCategory('pve')}
              />
              <CategoryButton
                id="arcade"
                label="Arcade Games"
                icon={<Gamepad className="h-3 w-3" />}
                count={6}
                active={selectedCategory === 'arcade'}
                onClick={() => setSelectedCategory('arcade')}
              />
            </div>
          </div>

          {/* Game Filters */}
          <div className="bg-slate-900 border border-slate-800 rounded-sm p-1 flex-1 flex flex-col overflow-hidden">
            <div className="flex items-center justify-between mb-1.5">
              <h3 className="text-xs font-medium text-white">Filters</h3>
              <Button
                variant="ghost"
                size="sm"
                className="h-5 w-5 p-0"
                onClick={() => setShowFilters(!showFilters)}
              >
                <ChevronDown className={`h-3 w-3 text-slate-400 ${showFilters ? 'rotate-180 transform' : ''}`} />
              </Button>
            </div>

            <div className="space-y-3 flex-1 overflow-auto">
              {/* Stake Range Filter */}
              <div>
                <span className="text-xs text-slate-400 mb-1 block">Stake Range</span>
                <div className="flex flex-wrap gap-1">
                  {['Any', '$1-5', '$5-25', '$25+'].map(amount => (
                    <Button
                      key={amount}
                      variant="outline"
                      size="sm"
                      className="h-5 px-2 py-0 text-[10px] rounded-sm bg-slate-800 border-slate-700 hover:bg-slate-700"
                    >
                      {amount}
                    </Button>
                  ))}
                </div>
              </div>

              {/* Players Count Filter */}
              <div>
                <span className="text-xs text-slate-400 mb-1 block">Players</span>
                <div className="flex flex-wrap gap-1">
                  {['1v1', '1vN', 'NvN', 'Any'].map(format => (
                    <Button
                      key={format}
                      variant="outline"
                      size="sm"
                      className="h-5 px-2 py-0 text-[10px] rounded-sm bg-slate-800 border-slate-700 hover:bg-slate-700"
                    >
                      {format}
                    </Button>
                  ))}
                </div>
              </div>

              {/* Difficulty Filter */}
              <div>
                <span className="text-xs text-slate-400 mb-1 block">Difficulty</span>
                <div className="flex flex-wrap gap-1">
                  {[
                    { name: 'Easy', color: 'bg-green-500' },
                    { name: 'Medium', color: 'bg-yellow-500' },
                    { name: 'Hard', color: 'bg-red-500' }
                  ].map(difficulty => (
                    <Button
                      key={difficulty.name}
                      variant="outline"
                      size="sm"
                      className="h-5 px-2 py-0 text-[10px] rounded-sm bg-slate-800 border-slate-700 hover:bg-slate-700"
                    >
                      <span className={`h-1.5 w-1.5 rounded-full ${difficulty.color} mr-1`}></span>
                      {difficulty.name}
                    </Button>
                  ))}
                </div>
              </div>

              {/* Duration Filter */}
              <div>
                <span className="text-xs text-slate-400 mb-1 block">Duration</span>
                <div className="flex flex-wrap gap-1">
                  {['1-2 min', '3-5 min', '5+ min'].map(duration => (
                    <Button
                      key={duration}
                      variant="outline"
                      size="sm"
                      className="h-5 px-2 py-0 text-[10px] rounded-sm bg-slate-800 border-slate-700 hover:bg-slate-700"
                    >
                      {duration}
                    </Button>
                  ))}
                </div>
              </div>

              {/* Skill Type Filter */}
              <div>
                <span className="text-xs text-slate-400 mb-1 block">Skill Type</span>
                <div className="space-y-1">
                  {[
                    { id: 'reaction', name: 'Reaction Speed', icon: <Zap className="h-3 w-3" /> },
                    { id: 'memory', name: 'Memory', icon: <Brain className="h-3 w-3" /> },
                    { id: 'visual', name: 'Visual', icon: <Eye className="h-3 w-3" /> },
                    { id: 'knowledge', name: 'Knowledge', icon: <BookOpen className="h-3 w-3" /> },
                    { id: 'strategy', name: 'Strategy', icon: <Layers className="h-3 w-3" /> }
                  ].map(skill => (
                    <div key={skill.id} className="flex items-center">
                      <input
                        type="checkbox"
                        id={skill.id}
                        className="h-3 w-3 rounded-sm border-slate-700"
                      />
                      <label htmlFor={skill.id} className="ml-1.5 text-xs text-slate-300 flex items-center">
                        {skill.icon}
                        <span className="ml-1">{skill.name}</span>
                      </label>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            <div className="pt-2 mt-auto border-t border-slate-800">
              <Button
                className="w-full h-6 text-xs bg-gradient-to-r from-green-500 to-cyan-500 hover:opacity-90 rounded-sm"
              >
                Apply Filters
              </Button>
            </div>
          </div>
        </div>

        {/* Main Content - Games Grid */}
        <div className="col-span-7 flex flex-col overflow-hidden">
          {/* Quick Stats Bar with Search */}
          <div className="h-7 bg-slate-900 border border-slate-800 rounded-sm px-2 mb-1 flex items-center">
            <div className="flex items-center space-x-3">
              <div className="flex items-center">
                <Trophy className="h-3 w-3 text-amber-500 mr-1" />
                <span className="text-xs text-white">Win Rate: 65.5%</span>
              </div>
              <div className="flex items-center">
                <DollarSign className="h-3 w-3 text-green-500 mr-1" />
                <span className="text-xs text-white">Earnings: $3,240</span>
              </div>
              <div className="flex items-center">
                <Star className="h-3 w-3 text-orange-500 mr-1" />
                <span className="text-xs text-white">Top Games: Chess, Trivia</span>
              </div>
            </div>

            <div className="ml-auto flex items-center space-x-2">
              <div className="relative">
                <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-3 w-3 text-slate-400" />
                <Input
                  placeholder="Find games..."
                  className="h-5 pl-7 pr-2 text-[10px] bg-slate-800 border-slate-700 rounded-sm w-32"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              <Button
                variant="ghost"
                size="sm"
                className="h-5 px-2 rounded-sm text-[10px] hover:bg-slate-800"
              >
                <ListFilter className="h-3 w-3 mr-1 text-slate-400" />
                Sort: Popular
              </Button>
            </div>
          </div>

          {/* Featured Games Carousel */}
          <div className="h-32 bg-slate-900 border border-slate-800 rounded-sm mb-1 relative overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-r from-slate-900/90 via-slate-900/50 to-slate-900/90 z-10"></div>
            <img
              src="https://via.placeholder.com/800x200"
              alt="Featured Games"
              className="absolute inset-0 object-cover w-full h-full"
            />
            <div className="relative z-20 h-full flex flex-col justify-center px-4">
              <Badge className="mb-2 w-fit bg-green-500 text-white text-[10px] h-4 px-1.5">NEW GAME</Badge>
              <h2 className="text-white font-bold text-lg mb-1">Blur Detective Challenge</h2>
              <p className="text-slate-300 text-xs mb-2">Identify blurred images faster than your opponent!</p>
              <div className="flex items-center space-x-3">
                <Button
                  size="sm"
                  className="h-6 px-3 text-xs bg-gradient-to-r from-green-500 to-cyan-500 hover:opacity-90"
                >
                  Browse/Play
                </Button>
                <div className="flex items-center space-x-2">
                  <Badge className="bg-slate-800 text-white text-[10px] h-4 px-1.5">
                    <DollarSign className="h-2 w-2 mr-0.5" />
                    $5-50
                  </Badge>
                  <Badge className="bg-slate-800 text-white text-[10px] h-4 px-1.5">
                    <Users className="h-2 w-2 mr-0.5" />
                    1v1
                  </Badge>
                  <Badge className="bg-slate-800 text-white text-[10px] h-4 px-1.5">
                    <Clock className="h-2 w-2 mr-0.5" />
                    2 min
                  </Badge>
                </div>
              </div>
            </div>
          </div>

          {/* Games Grid */}
          <div className="bg-slate-900 border border-slate-800 rounded-sm flex-1 overflow-hidden">
            <div className="grid grid-cols-3 gap-px bg-slate-800 p-px overflow-auto h-full">
              {filteredGames.map((game) => (
                <GameCard
                  key={game.id}
                  name={game.name}
                  icon={game.icon}
                  description={game.description}
                  playerMode={game.playerMode}
                  stakeRange={game.stakeRange}
                  duration={game.duration}
                  difficulty={game.difficulty}
                  skillType={game.skillType}
                  popularity={game.popularity}
                  isLive={game.isLive}
                  activePlayers={game.activePlayers}
                  onPlay={() => onGameSelect(createGameData(game.id, game.name, game.type || game.id, game.prize, game.viewers, game.status))}
                />
              ))}
            </div>
          </div>
        </div>

        {/* Right Sidebar - Active Challenges */}
        <div className="col-span-3 flex flex-col gap-1 overflow-hidden">
          {/* Your Active Challenges */}
          <div className="bg-slate-900 border border-slate-800 rounded-sm overflow-hidden flex-1">
            <div className="h-6 px-2 flex items-center justify-between border-b border-slate-800">
              <h3 className="text-xs font-medium text-white flex items-center">
                <Radio className="h-3 w-3 text-green-500 mr-1 animate-pulse" />
                Your Active Challenges
              </h3>
            </div>

            <div className="overflow-auto h-[calc(100%-1.5rem)]">
              <ActiveChallengeItem
                game="Chess Blitz"
                opponent="Player X"
                stake={25}
                timeLeft="1:32"
                status="your-turn"
              />

              <ActiveChallengeItem
                game="Speed Trivia"
                opponent="3 Players"
                stake={10}
                timeLeft="0:45"
                status="waiting"
              />
            </div>
          </div>

          {/* Open Challenges */}
          <div className="bg-slate-900 border border-slate-800 rounded-sm overflow-hidden flex-1">
            <div className="h-6 px-2 flex items-center justify-between border-b border-slate-800">
              <h3 className="text-xs font-medium text-white flex items-center">
                Open Challenges
              </h3>
              <Button
                variant="ghost"
                size="sm"
                className="h-5 w-5 p-0"
              >
                <Filter className="h-3 w-3 text-slate-400" />
              </Button>
            </div>

            <div className="overflow-auto h-[calc(100%-1.5rem)]">
              <OpenChallengeItem
                game="Blur Detective"
                creator="Player Y"
                stake={50}
                timeLimit="2:00"
                players="1/2"
              />

              <OpenChallengeItem
                game="Memory Sequence"
                creator="Player Z"
                stake={15}
                timeLimit="2:30"
                players="2/4"
              />

              <OpenChallengeItem
                game="Chess Blitz"
                creator="Player W"
                stake={100}
                timeLimit="3:00"
                players="0/2"
              />

              <OpenChallengeItem
                game="Spot The Fake"
                creator="Player V"
                stake={25}
                timeLimit="2:00"
                players="3/5"
              />
            </div>
          </div>

          {/* Quick Stats */}
          <div className="bg-slate-900 border border-slate-800 rounded-sm overflow-hidden">
            <div className="h-6 px-2 flex items-center justify-between border-b border-slate-800">
              <h3 className="text-xs font-medium text-white">Top Players</h3>
            </div>

            <div className="p-2 space-y-2">
              <div className="grid grid-cols-3 gap-2">
                <div className="bg-slate-800 p-1.5 rounded-sm text-center">
                  <p className="text-[10px] text-slate-400">Online</p>
                  <p className="text-white font-medium text-sm">186</p>
                </div>
                <div className="bg-slate-800 p-1.5 rounded-sm text-center">
                  <p className="text-[10px] text-slate-400">Challenges</p>
                  <p className="text-white font-medium text-sm">42</p>
                </div>
                <div className="bg-slate-800 p-1.5 rounded-sm text-center">
                  <p className="text-[10px] text-slate-400">Jackpot</p>
                  <p className="text-green-500 font-medium text-sm">$3.4K</p>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-xs text-slate-400">Your Rank:</span>
                <span className="text-xs font-medium text-white">#8 of 1,240</span>
              </div>

              <div className="flex items-center space-x-2">
                <Progress value={65} className="h-1.5 flex-1" />
                <span className="text-xs text-slate-400">Level 42</span>
              </div>
            </div>
          </div>

          {/* Create Challenge Button */}
          <Button className="h-8 bg-gradient-to-r from-green-500 to-cyan-500 hover:opacity-90">
            <Plus className="h-3.5 w-3.5 mr-1.5" />
            Create New Challenge
          </Button>
        </div>
      </div>
    </div>
  );
};

// Reusable Components

interface CategoryButtonProps {
  id: string;
  label: string;
  icon: React.ReactNode;
  count: number;
  active: boolean;
  onClick: () => void;
}

const CategoryButton: React.FC<CategoryButtonProps> = ({ id, label, icon, count, active, onClick }) => (
  <Button
    variant="ghost"
    className={`w-full justify-between h-6 px-2 text-xs rounded-sm ${
      active
        ? 'bg-slate-800 text-white'
        : 'text-slate-400 hover:text-white hover:bg-slate-800/50'
    }`}
    onClick={onClick}
  >
    <div className="flex items-center">
      {icon}
      <span className="ml-1.5">{label}</span>
    </div>
    <Badge className="h-4 px-1 text-[10px] bg-slate-800 text-slate-400">
      {count}
    </Badge>
  </Button>
);

// Game Card Component
interface GameCardProps {
  name: string;
  icon: React.ReactNode;
  description: string;
  playerMode: string;
  stakeRange: string;
  duration: string;
  difficulty: string;
  skillType: string;
  popularity: number;
  isLive: boolean;
  activePlayers: number;
  onPlay?: () => void;
}

const GameCard: React.FC<GameCardProps> = ({
  name,
  icon,
  description,
  playerMode,
  stakeRange,
  duration,
  difficulty,
  skillType,
  popularity,
  isLive,
  activePlayers,
  onPlay
}) => {
  // Determine difficulty color
  const getDifficultyColor = (diff: string) => {
    switch(diff) {
      case 'Easy': return 'text-green-500';
      case 'Medium': return 'text-yellow-500';
      case 'Hard': return 'text-red-500';
      default: return 'text-slate-400';
    }
  };

  return (
    <div className="bg-slate-900 p-2 hover:bg-slate-800/70 cursor-pointer">
      {/* Game Header */}
      <div className="flex items-start justify-between mb-1.5">
        <div className="flex items-center space-x-1.5">
          <div className="h-7 w-7 rounded-sm bg-gradient-to-br from-slate-700 to-slate-600 flex items-center justify-center">
            {icon}
          </div>
          <div>
            <h3 className="text-xs font-medium text-white">{name}</h3>
            <span className={`text-[10px] ${getDifficultyColor(difficulty)}`}>{difficulty}</span>
          </div>
        </div>
        {isLive && (
          <Badge className="bg-green-500 text-white text-[10px] h-4 px-1.5 animate-pulse">
            <Radio className="h-2 w-2 mr-0.5" />
            LIVE
          </Badge>
        )}
      </div>

      {/* Game Description */}
      <p className="text-[10px] text-slate-300 mb-2 line-clamp-2" style={{minHeight: "2rem"}}>
        {description}
      </p>

      {/* Game Stats */}
      <div className="grid grid-cols-2 gap-1 mb-2">
        <div className="flex items-center bg-slate-800 rounded-sm px-1.5 py-1">
          <Users className="h-2.5 w-2.5 text-blue-400 mr-1" />
          <span className="text-[10px] text-slate-300">{playerMode}</span>
        </div>
        <div className="flex items-center bg-slate-800 rounded-sm px-1.5 py-1">
          <DollarSign className="h-2.5 w-2.5 text-green-400 mr-1" />
          <span className="text-[10px] text-slate-300">{stakeRange}</span>
        </div>
        <div className="flex items-center bg-slate-800 rounded-sm px-1.5 py-1">
          <Clock className="h-2.5 w-2.5 text-orange-400 mr-1" />
          <span className="text-[10px] text-slate-300">{duration}</span>
        </div>
        <div className="flex items-center bg-slate-800 rounded-sm px-1.5 py-1">
          <Brain className="h-2.5 w-2.5 text-purple-400 mr-1" />
          <span className="text-[10px] text-slate-300">{skillType}</span>
        </div>
      </div>

      {/* Popularity & Players */}
      <div className="flex justify-between items-center mb-2">
        <div className="flex flex-col">
          <span className="text-[10px] text-slate-400">Popularity</span>
          <div className="flex items-center">
            <Progress value={popularity} className="h-1 w-12 mr-1.5" />
            <span className="text-[10px] text-white">{popularity}%</span>
          </div>
        </div>
        <div className="flex flex-col items-end">
          <span className="text-[10px] text-slate-400">Active Now</span>
          <span className="text-[10px] text-green-500 font-medium">{activePlayers} players</span>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="grid grid-cols-2 gap-1">
        <Button
          className="h-6 text-[10px] rounded-sm bg-gradient-to-r from-green-500 to-cyan-500 hover:opacity-90"
          onClick={(e) => {
            e.stopPropagation();
            if (onPlay) onPlay();
          }}
        >
          <Eye className="h-3 w-3 mr-1" />
          Browse/Play
        </Button>
        <Button
          variant="outline"
          className="h-6 text-[10px] rounded-sm bg-slate-800 border-slate-700 text-white hover:bg-slate-700 flex items-center justify-center"
        >
          <Star className="h-3 w-3 mr-1" />
          Favorite
        </Button>
      </div>
    </div>
  );
};

// Active Challenge Item Component
interface ActiveChallengeProps {
  game: string;
  opponent: string;
  stake: number;
  timeLeft: string;
  status: 'your-turn' | 'waiting';
}

const ActiveChallengeItem: React.FC<ActiveChallengeProps> = ({ game, opponent, stake, timeLeft, status }) => {
  return (
    <div className="px-2 py-1.5 border-b border-slate-800/50 hover:bg-slate-800/30">
      <div className="flex items-center justify-between mb-1">
        <span className="text-xs font-medium text-white">{game}</span>
        <Badge
          className={`text-[10px] h-4 px-1.5 ${
            status === 'your-turn'
              ? 'bg-green-500 text-white animate-pulse'
              : 'bg-slate-600 text-white'
          }`}
        >
          {status === 'your-turn' ? 'Your Turn' : 'Waiting'}
        </Badge>
      </div>

      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <UserX className="h-3 w-3 text-slate-400 mr-1" />
          <span className="text-[10px] text-slate-300">{opponent}</span>
        </div>
        <div className="flex items-center space-x-2">
          <span className="text-[10px] text-green-500 font-medium">${stake}</span>
          <div className="flex items-center text-[10px] text-orange-400">
            <Clock className="h-2.5 w-2.5 mr-0.5" />
            {timeLeft}
          </div>
        </div>
      </div>
    </div>
  );
};

// Open Challenge Item Component
interface OpenChallengeProps {
  game: string;
  creator: string;
  stake: number;
  timeLimit: string;
  players: string;
}

const OpenChallengeItem: React.FC<OpenChallengeProps> = ({ game, creator, stake, timeLimit, players }) => {
  return (
    <div className="px-2 py-1.5 border-b border-slate-800/50 hover:bg-slate-800/30">
      <div className="flex items-center justify-between mb-1">
        <span className="text-xs font-medium text-white">{game}</span>
        <span className="text-[10px] text-green-500 font-medium">${stake}</span>
      </div>

      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <UserX className="h-3 w-3 text-slate-400 mr-1" />
          <span className="text-[10px] text-slate-300">{creator}</span>
        </div>
        <div className="flex items-center space-x-2">
          <div className="flex items-center text-[10px] text-slate-400">
            <Timer className="h-2.5 w-2.5 mr-0.5" />
            {timeLimit}
          </div>
          <div className="flex items-center text-[10px] text-slate-400">
            <Users className="h-2.5 w-2.5 mr-0.5" />
            {players}
          </div>
          <Button
            size="sm"
            className="h-5 px-2 text-[10px] bg-green-600 hover:bg-green-700 rounded-sm"
          >
            Join
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ChallengeArenaGames;