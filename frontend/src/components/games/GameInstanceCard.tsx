import React from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import GameStatusBadge from './GameStatusBadge';
import GameThumbnailGenerator, { GameInstanceStatus } from './GameThumbnailGenerator';
import {
  Users,
  DollarSign,
  Clock,
  Eye,
  Play,
  Calendar,
  Trophy,
  Timer
} from 'lucide-react';

export interface GameInstance {
  id: string;
  hostName: string;
  gameType?: string; // Add game type for thumbnail generation
  players: Array<{
    id: string;
    name: string;
    avatar?: string;
  }>;
  maxPlayers: number;
  stakes: number;
  status: GameInstanceStatus;
  startTime?: Date;
  timeLimit?: number;
  viewers?: number;
  gameMode?: string;
  thumbnail?: string;
  createdAt: Date;
  currentState?: any; // For game-specific preview data
}

interface GameInstanceCardProps {
  instance: GameInstance;
  onJoin?: (instanceId: string) => void;
  onSpectate?: (instanceId: string) => void;
  onSchedule?: (instanceId: string) => void;
  className?: string;
}

const GameInstanceCard: React.FC<GameInstanceCardProps> = ({
  instance,
  onJoin,
  onSpectate,
  onSchedule,
  className = ''
}) => {
  const canJoin = instance.status === 'waiting' && instance.players.length < instance.maxPlayers;
  const canSpectate = instance.status === 'live' || instance.status === 'starting';
  const isScheduled = instance.status === 'scheduled';

  const formatTimeRemaining = (startTime: Date) => {
    const now = new Date();
    const diff = startTime.getTime() - now.getTime();

    if (diff <= 0) return 'Starting soon';

    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  };

  return (
    <Card className={`bg-slate-900 border-slate-800 hover:border-slate-700 transition-colors ${className}`}>
      <div className="p-4">
        {/* Header with status and thumbnail */}
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-center gap-3">
            {/* Game thumbnail with generated preview */}
            <GameThumbnailGenerator
              gameType={instance.gameType || 'unknown'}
              status={instance.status}
              players={instance.players}
              currentState={instance.currentState}
              size="md"
            />

            <div>
              <h3 className="font-semibold text-white text-sm">
                {instance.hostName}'s Game
              </h3>
              <p className="text-xs text-slate-400">
                Created {instance.createdAt.toLocaleDateString()}
              </p>
            </div>
          </div>

          <GameStatusBadge status={instance.status} size="sm" />
        </div>

        {/* Game details */}
        <div className="space-y-2 mb-4">
          {/* Players */}
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center gap-1 text-slate-400">
              <Users className="h-4 w-4" />
              <span>Players</span>
            </div>
            <span className="text-white">
              {instance.players.length}/{instance.maxPlayers}
            </span>
          </div>

          {/* Stakes */}
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center gap-1 text-slate-400">
              <DollarSign className="h-4 w-4" />
              <span>Stakes</span>
            </div>
            <span className="text-white font-medium">
              ${instance.stakes}
            </span>
          </div>

          {/* Time info */}
          {instance.timeLimit && (
            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center gap-1 text-slate-400">
                <Timer className="h-4 w-4" />
                <span>Time Limit</span>
              </div>
              <span className="text-white">
                {Math.floor(instance.timeLimit / 60)}m
              </span>
            </div>
          )}

          {/* Scheduled time */}
          {isScheduled && instance.startTime && (
            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center gap-1 text-slate-400">
                <Clock className="h-4 w-4" />
                <span>Starts in</span>
              </div>
              <span className="text-white">
                {formatTimeRemaining(instance.startTime)}
              </span>
            </div>
          )}

          {/* Viewers for live games */}
          {canSpectate && instance.viewers !== undefined && (
            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center gap-1 text-slate-400">
                <Eye className="h-4 w-4" />
                <span>Viewers</span>
              </div>
              <span className="text-white">
                {instance.viewers}
              </span>
            </div>
          )}

          {/* Game mode */}
          {instance.gameMode && (
            <div className="flex items-center justify-between text-sm">
              <span className="text-slate-400">Mode</span>
              <Badge variant="outline" className="text-xs">
                {instance.gameMode}
              </Badge>
            </div>
          )}
        </div>

        {/* Action buttons */}
        <div className="flex gap-2">
          {canJoin && (
            <Button
              onClick={() => onJoin?.(instance.id)}
              className="flex-1 bg-green-600 hover:bg-green-700"
              size="sm"
            >
              <Play className="h-4 w-4 mr-1" />
              Join Game
            </Button>
          )}

          {canSpectate && (
            <Button
              onClick={() => onSpectate?.(instance.id)}
              variant="outline"
              className="flex-1"
              size="sm"
            >
              <Eye className="h-4 w-4 mr-1" />
              Spectate
            </Button>
          )}

          {isScheduled && (
            <Button
              onClick={() => onSchedule?.(instance.id)}
              variant="outline"
              className="flex-1"
              size="sm"
            >
              <Calendar className="h-4 w-4 mr-1" />
              Set Reminder
            </Button>
          )}

          {instance.status === 'completed' && (
            <Button
              variant="outline"
              className="flex-1"
              size="sm"
              disabled
            >
              View Results
            </Button>
          )}
        </div>
      </div>
    </Card>
  );
};

export default GameInstanceCard;
