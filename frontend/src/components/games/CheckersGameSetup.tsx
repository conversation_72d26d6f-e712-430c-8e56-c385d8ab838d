import { useState } from 'react';
import {
  Dialog,
  Dialog<PERSON>ontent,
  Di<PERSON>Header,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { DollarSign, Clock, Users, Trophy } from 'lucide-react';

interface CheckersGameSetupProps {
  isOpen: boolean;
  onClose: () => void;
  onStartGame: (config: GameConfig) => void;
}

export interface GameConfig {
  wagerAmount: number;
  isTimedGame: boolean;
  timeLimit: number; // in seconds
  maxPlayers: 2;
  gameMode: 'classic' | 'fast';
}

const CheckersGameSetup: React.FC<CheckersGameSetupProps> = ({
  isOpen,
  onClose,
  onStartGame,
}) => {
  const [wagerAmount, setWagerAmount] = useState(10);
  const [isTimedGame, setIsTimedGame] = useState(false);
  const [timeLimit, setTimeLimit] = useState(300); // 5 minutes
  const [gameMode, setGameMode] = useState<'classic' | 'fast'>('classic');

  const handleStartGame = () => {
    const config: GameConfig = {
      wagerAmount,
      isTimedGame,
      timeLimit,
      maxPlayers: 2,
      gameMode,
    };
    // console.log('CheckersGameSetup - Starting game with config:', config);
    onStartGame(config);
    // Don't call onClose() here as it will navigate back
  };

  const predefinedWagers = [5, 10, 25, 50, 100];
  const timeLimits = [
    { label: '3 min', value: 180 },
    { label: '5 min', value: 300 },
    { label: '10 min', value: 600 },
    { label: '15 min', value: 900 },
  ];

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="bg-slate-900 border-slate-800 text-white max-w-md">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold flex items-center gap-2">
            <Trophy className="h-5 w-5 text-amber-500" />
            Checkers Game Setup
          </DialogTitle>
          <DialogDescription className="text-sm text-slate-400 mt-2">
            Configure your checkers game settings before starting the match
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {/* Wager Amount */}
          <div className="space-y-3">
            <Label className="text-sm font-medium flex items-center gap-2">
              <DollarSign className="h-4 w-4 text-green-500" />
              Wager Amount
            </Label>
            
            <div className="flex gap-2 flex-wrap">
              {predefinedWagers.map((amount) => (
                <Button
                  key={amount}
                  variant={wagerAmount === amount ? 'default' : 'outline'}
                  size="sm"
                  className={`h-9 px-4 ${
                    wagerAmount === amount
                      ? 'bg-purple-600 border-purple-600'
                      : 'border-slate-700'
                  }`}
                  onClick={() => setWagerAmount(amount)}
                >
                  ${amount}
                </Button>
              ))}
            </div>
            
            <div className="flex items-center gap-2">
              <span className="text-sm text-slate-400">Custom:</span>
              <Input
                type="number"
                value={wagerAmount}
                onChange={(e) => setWagerAmount(Number(e.target.value))}
                className="w-24 h-9 bg-slate-800 border-slate-700"
                min={1}
                step={1}
              />
            </div>
          </div>

          {/* Game Mode */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Game Mode</Label>
            
            <RadioGroup
              value={gameMode}
              onValueChange={(value) => setGameMode(value as 'classic' | 'fast')}
            >
              <div className="flex items-center space-x-3">
                <RadioGroupItem value="classic" id="classic" />
                <Label
                  htmlFor="classic"
                  className="cursor-pointer flex items-center gap-2"
                >
                  <span>Classic</span>
                  <Badge className="bg-blue-500 text-xs">Standard rules</Badge>
                </Label>
              </div>
              
              <div className="flex items-center space-x-3">
                <RadioGroupItem value="fast" id="fast" />
                <Label
                  htmlFor="fast"
                  className="cursor-pointer flex items-center gap-2"
                >
                  <span>Fast</span>
                  <Badge className="bg-orange-500 text-xs">Quick moves</Badge>
                </Label>
              </div>
            </RadioGroup>
          </div>

          {/* Time Control */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label className="text-sm font-medium flex items-center gap-2">
                <Clock className="h-4 w-4 text-amber-500" />
                Time Control
              </Label>
              <div className="flex items-center gap-2">
                <Switch
                  checked={isTimedGame}
                  onCheckedChange={setIsTimedGame}
                  className="data-[state=checked]:bg-purple-600"
                />
                <span className="text-sm text-slate-400">
                  {isTimedGame ? 'Enabled' : 'Disabled'}
                </span>
              </div>
            </div>

            {isTimedGame && (
              <div className="flex gap-2">
                {timeLimits.map((limit) => (
                  <Button
                    key={limit.value}
                    variant={timeLimit === limit.value ? 'default' : 'outline'}
                    size="sm"
                    className={`h-9 ${
                      timeLimit === limit.value
                        ? 'bg-purple-600 border-purple-600'
                        : 'border-slate-700'
                    }`}
                    onClick={() => setTimeLimit(limit.value)}
                  >
                    {limit.label}
                  </Button>
                ))}
              </div>
            )}
          </div>

          {/* Game Info */}
          <div className="bg-slate-800 rounded-lg p-4 space-y-2">
            <h4 className="text-sm font-medium mb-2">Game Details</h4>
            
            <div className="flex justify-between text-sm">
              <span className="text-slate-400">Players:</span>
              <span className="flex items-center gap-1">
                <Users className="h-3 w-3" />
                2
              </span>
            </div>
            
            <div className="flex justify-between text-sm">
              <span className="text-slate-400">Total Pot:</span>
              <span className="text-green-500 font-medium">
                ${(wagerAmount * 2).toFixed(2)}
              </span>
            </div>
            
            <div className="flex justify-between text-sm">
              <span className="text-slate-400">Winner Gets:</span>
              <span className="text-green-500 font-medium">
                ${(wagerAmount * 2 * 0.95).toFixed(2)}
              </span>
            </div>
            
            {isTimedGame && (
              <div className="flex justify-between text-sm">
                <span className="text-slate-400">Time per Player:</span>
                <span>{Math.floor(timeLimit / 60)} minutes</span>
              </div>
            )}
          </div>
        </div>

        <DialogFooter className="flex gap-2">
          <Button
            variant="outline"
            onClick={onClose}
            className="border-slate-700"
          >
            Cancel
          </Button>
          <Button
            onClick={handleStartGame}
            className="bg-gradient-to-r from-purple-500 to-pink-500"
          >
            Create Game - ${wagerAmount}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default CheckersGameSetup;