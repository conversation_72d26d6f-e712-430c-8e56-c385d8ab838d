import React, { useState, useEffect, useRef } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Input } from '@/components/ui/input';
import {
  Trophy,
  Clock,
  MessageSquare,
  Users,
  Settings,
  Star,
  Target,
  Zap,
  RotateCcw,
  Check,
  X,
  AlertCircle,
  DollarSign,
  ChevronDown,
  Phone,
  UsersRound,
  Split,
  HelpCircle,
  Crown,
  Timer,
  Award,
  TrendingUp,
  Eye,
  Percent,
  Lock,
  Unlock,
  Flame,
  Brain,
  Lightbulb
} from 'lucide-react';

// Game status
const GAME_STATUS = {
  SETUP: 'setup',
  WAITING: 'waiting',
  PLAYING: 'playing',
  QUESTION: 'question',
  LIFELINE: 'lifeline',
  ANSWER_REVEAL: 'answer_reveal',
  ROUND_END: 'round_end',
  GAME_END: 'game_end'
};

// Lifeline types
const LIFELINES = {
  FIFTY_FIFTY: 'fifty_fifty',
  PHONE_FRIEND: 'phone_friend',
  ASK_AUDIENCE: 'ask_audience',
  EXPERT_ADVICE: 'expert_advice'
};

// Prize ladder (simplified version)
const PRIZE_LADDER = [
  { level: 1, amount: 100, safe: false },
  { level: 2, amount: 200, safe: false },
  { level: 3, amount: 300, safe: false },
  { level: 4, amount: 500, safe: false },
  { level: 5, amount: 1000, safe: true },
  { level: 6, amount: 2000, safe: false },
  { level: 7, amount: 4000, safe: false },
  { level: 8, amount: 8000, safe: false },
  { level: 9, amount: 16000, safe: false },
  { level: 10, amount: 32000, safe: true },
  { level: 11, amount: 64000, safe: false },
  { level: 12, amount: 125000, safe: false },
  { level: 13, amount: 250000, safe: false },
  { level: 14, amount: 500000, safe: false },
  { level: 15, amount: 1000000, safe: false }
];

// Sample questions database
const QUESTIONS_DB = {
  easy: [
    {
      id: 1,
      question: "What is the capital of France?",
      options: ["London", "Berlin", "Paris", "Madrid"],
      correct: 2,
      category: "Geography",
      difficulty: "easy"
    },
    {
      id: 2,
      question: "How many legs does a spider have?",
      options: ["6", "8", "10", "12"],
      correct: 1,
      category: "Science",
      difficulty: "easy"
    },
    {
      id: 3,
      question: "What year did World War II end?",
      options: ["1944", "1945", "1946", "1947"],
      correct: 1,
      category: "History",
      difficulty: "easy"
    }
  ],
  medium: [
    {
      id: 4,
      question: "Which planet is known as the Red Planet?",
      options: ["Venus", "Mars", "Jupiter", "Saturn"],
      correct: 1,
      category: "Science",
      difficulty: "medium"
    },
    {
      id: 5,
      question: "Who wrote the novel '1984'?",
      options: ["Aldous Huxley", "George Orwell", "Ray Bradbury", "Ernest Hemingway"],
      correct: 1,
      category: "Literature",
      difficulty: "medium"
    },
    {
      id: 6,
      question: "What is the largest ocean on Earth?",
      options: ["Atlantic", "Indian", "Arctic", "Pacific"],
      correct: 3,
      category: "Geography",
      difficulty: "medium"
    }
  ],
  hard: [
    {
      id: 7,
      question: "In which year was the first iPhone released?",
      options: ["2006", "2007", "2008", "2009"],
      correct: 1,
      category: "Technology",
      difficulty: "hard"
    },
    {
      id: 8,
      question: "What is the chemical symbol for gold?",
      options: ["Go", "Gd", "Au", "Ag"],
      correct: 2,
      category: "Science",
      difficulty: "hard"
    },
    {
      id: 9,
      question: "Who composed 'The Four Seasons'?",
      options: ["Bach", "Mozart", "Vivaldi", "Beethoven"],
      correct: 2,
      category: "Music",
      difficulty: "hard"
    }
  ]
};

interface QuizArenaGameProps {
  onBack?: () => void;
  onGameEnd?: (result: any) => void;
}

const QuizArenaGame: React.FC<QuizArenaGameProps> = ({ onBack, onGameEnd }) => {
  // Game configuration
  const [config, setConfig] = useState({
    gameMode: '1v1', // '1v1' or '1vN'
    maxPlayers: 4,
    gameType: 'timed', // 'timed' or 'untimed'
    timePerQuestion: 30,
    questionCount: 15,
    wagerAmount: 100,
    prizePool: 500,
    allowLifelines: true,
    spectatorBetting: true
  });

  // Game state
  const [gameStatus, setGameStatus] = useState(GAME_STATUS.SETUP);
  const [currentLevel, setCurrentLevel] = useState(1);
  const [timeLeft, setTimeLeft] = useState(0);
  const [currentQuestion, setCurrentQuestion] = useState(null);
  const [selectedAnswer, setSelectedAnswer] = useState(null);
  const [isAnswerLocked, setIsAnswerLocked] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [lifelinesUsed, setLifelinesUsed] = useState({});
  const [activeLifeline, setActiveLifeline] = useState(null);
  const [audienceVotes, setAudienceVotes] = useState({});
  const [phoneAdvice, setPhoneAdvice] = useState(null);
  const [eliminatedOptions, setEliminatedOptions] = useState([]);

  // Players and competition
  const [players, setPlayers] = useState([
    { id: 1, name: 'You', level: 1, earnings: 0, isActive: true, isEliminated: false, lifelinesUsed: {} },
    { id: 2, name: 'Player 2', level: 1, earnings: 0, isActive: false, isEliminated: false, lifelinesUsed: {} },
    { id: 3, name: 'Player 3', level: 1, earnings: 0, isActive: false, isEliminated: false, lifelinesUsed: {} },
    { id: 4, name: 'Player 4', level: 1, earnings: 0, isActive: false, isEliminated: false, lifelinesUsed: {} }
  ]);

  // Game tracking
  const [gameHistory, setGameHistory] = useState([]);
  const [gameResult, setGameResult] = useState(null);
  const [totalGameTime, setTotalGameTime] = useState(0);

  // Refs
  const timerRef = useRef(null);
  const gameTimerRef = useRef(null);

  // Chat state
  const [messages, setMessages] = useState([
    { text: "Welcome to Quiz Arena! Test your knowledge and compete for the million-dollar prize!", type: "system" }
  ]);
  const [messageInput, setMessageInput] = useState('');
  const messageEndRef = useRef(null);

  // Effects
  useEffect(() => {
    if (messageEndRef.current) {
      messageEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  useEffect(() => {
    return () => {
      if (timerRef.current) clearInterval(timerRef.current);
      if (gameTimerRef.current) clearInterval(gameTimerRef.current);
    };
  }, []);

  // Get questions for difficulty
  const getQuestionsForDifficulty = (level) => {
    if (level <= 5) return QUESTIONS_DB.easy;
    if (level <= 10) return QUESTIONS_DB.medium;
    return QUESTIONS_DB.hard;
  };

  // Add message to chat
  const addMessage = (text, type = "player") => {
    setMessages(prev => [...prev, { text, type }]);
  };

  // Send chat message
  const sendMessage = () => {
    if (messageInput.trim()) {
      addMessage(`Spectator: ${messageInput}`);
      setMessageInput('');
    }
  };

  return (
    <div className="min-h-screen bg-slate-950 text-white pt-16">
      <div className="container mx-auto px-4 py-4">
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            {onBack && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onBack}
                className="h-8 w-8 p-0 mr-3"
              >
                <X className="h-4 w-4" />
              </Button>
            )}
            <div className="flex items-center">
              <div className="h-10 w-10 rounded bg-gradient-to-br from-yellow-500 to-yellow-600 flex items-center justify-center mr-3">
                <Brain className="h-5 w-5 text-black" />
              </div>
              <div>
                <h1 className="text-xl font-semibold">Quiz Arena</h1>
                <p className="text-sm text-slate-400">Who Wants to Be a Millionaire?</p>
              </div>
            </div>
          </div>
          
          <Badge className="bg-yellow-500 text-black px-3 py-1">
            <Crown className="h-4 w-4 mr-1" />
            Million Dollar Challenge
          </Badge>
        </div>

        {/* Game Content */}
        <div className="grid grid-cols-12 gap-4 h-[calc(100vh-200px)]">
          {/* Left Sidebar - Game Info & Prize Ladder */}
          <div className="col-span-3 space-y-4">
            {/* Game Info Component */}
            <div className="bg-slate-900 border border-slate-800 rounded-lg p-4">
              <h3 className="text-sm font-semibold text-white mb-3">Game Status</h3>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-xs text-slate-400">Level:</span>
                  <span className="text-sm font-bold text-white">{currentLevel}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-xs text-slate-400">Wager:</span>
                  <span className="text-sm font-bold text-white">${config.wagerAmount}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-xs text-slate-400">Prize Pool:</span>
                  <span className="text-sm font-bold text-yellow-500">${config.prizePool}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Main Game Area */}
          <div className="col-span-6">
            <div className="bg-slate-900 border border-slate-800 rounded-lg p-6 h-full flex flex-col justify-center">
              {gameStatus === GAME_STATUS.SETUP ? (
                <div className="text-center">
                  <div className="text-6xl mb-4">🧠</div>
                  <h2 className="text-3xl font-bold text-white mb-2">Quiz Arena</h2>
                  <p className="text-lg text-yellow-500 mb-4">Who Wants to Be a Millionaire?</p>
                  <p className="text-slate-400 mb-8">
                    Answer questions correctly to climb the prize ladder!<br />
                    Use lifelines wisely and compete for the ultimate prize.
                  </p>
                  
                  <Button
                    className="bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-400 hover:to-yellow-500 text-black font-bold px-8 py-3"
                    onClick={() => setGameStatus(GAME_STATUS.PLAYING)}
                  >
                    <Crown className="h-5 w-5 mr-2" />
                    Start Quiz
                  </Button>
                </div>
              ) : (
                <div className="text-center">
                  <p className="text-slate-400">Game implementation continues here...</p>
                  <p className="text-sm text-slate-500 mt-2">
                    Full Quiz Arena game logic will be implemented in the next iteration
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Right Sidebar - Chat & Spectators */}
          <div className="col-span-3 space-y-4">
            <div className="bg-slate-900 border border-slate-800 rounded-lg p-4">
              <h3 className="text-sm font-semibold text-white mb-3">Live Chat</h3>
              <div className="h-32 overflow-y-auto mb-3 space-y-1">
                {messages.map((msg, index) => (
                  <div key={index} className="text-xs">
                    <span className={msg.type === 'system' ? 'text-yellow-400' : 'text-slate-300'}>
                      {msg.text}
                    </span>
                  </div>
                ))}
                <div ref={messageEndRef} />
              </div>
              <div className="flex gap-2">
                <Input
                  value={messageInput}
                  onChange={(e) => setMessageInput(e.target.value)}
                  placeholder="Type a message..."
                  className="flex-1 h-8 text-xs bg-slate-800 border-slate-700"
                  onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
                />
                <Button size="sm" onClick={sendMessage} className="h-8 px-3">
                  <MessageSquare className="h-3 w-3" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default QuizArenaGame;
