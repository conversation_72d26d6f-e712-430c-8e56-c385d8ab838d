import React, { useState, useEffect, useRef } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Input } from '@/components/ui/input';
import {
  Trophy,
  Clock,
  MessageSquare,
  Users,
  Settings,
  Star,
  Target,
  Zap,
  RotateCcw,
  Check,
  X,
  AlertCircle,
  DollarSign,
  ChevronDown,
  Phone,
  UsersRound,
  Split,
  HelpCircle,
  Crown,
  Timer,
  Award,
  TrendingUp,
  Eye,
  Percent,
  Lock,
  Unlock,
  Flame,
  Brain,
  Lightbulb
} from 'lucide-react';

// Game status
const GAME_STATUS = {
  SETUP: 'setup',
  WAITING: 'waiting',
  PLAYING: 'playing',
  QUESTION: 'question',
  LIFELINE: 'lifeline',
  ANSWER_REVEAL: 'answer_reveal',
  ROUND_END: 'round_end',
  GAME_END: 'game_end'
};

// Lifeline types
const LIFELINES = {
  FIFTY_FIFTY: 'fifty_fifty',
  PHONE_FRIEND: 'phone_friend',
  ASK_AUDIENCE: 'ask_audience',
  EXPERT_ADVICE: 'expert_advice'
};

// Prize ladder (simplified version)
const PRIZE_LADDER = [
  { level: 1, amount: 100, safe: false },
  { level: 2, amount: 200, safe: false },
  { level: 3, amount: 300, safe: false },
  { level: 4, amount: 500, safe: false },
  { level: 5, amount: 1000, safe: true },
  { level: 6, amount: 2000, safe: false },
  { level: 7, amount: 4000, safe: false },
  { level: 8, amount: 8000, safe: false },
  { level: 9, amount: 16000, safe: false },
  { level: 10, amount: 32000, safe: true },
  { level: 11, amount: 64000, safe: false },
  { level: 12, amount: 125000, safe: false },
  { level: 13, amount: 250000, safe: false },
  { level: 14, amount: 500000, safe: false },
  { level: 15, amount: 1000000, safe: false }
];

// Sample questions database
const QUESTIONS_DB = {
  easy: [
    {
      id: 1,
      question: "What is the capital of France?",
      options: ["London", "Berlin", "Paris", "Madrid"],
      correct: 2,
      category: "Geography",
      difficulty: "easy"
    },
    {
      id: 2,
      question: "How many legs does a spider have?",
      options: ["6", "8", "10", "12"],
      correct: 1,
      category: "Science",
      difficulty: "easy"
    },
    {
      id: 3,
      question: "What year did World War II end?",
      options: ["1944", "1945", "1946", "1947"],
      correct: 1,
      category: "History",
      difficulty: "easy"
    }
  ],
  medium: [
    {
      id: 4,
      question: "Which planet is known as the Red Planet?",
      options: ["Venus", "Mars", "Jupiter", "Saturn"],
      correct: 1,
      category: "Science",
      difficulty: "medium"
    },
    {
      id: 5,
      question: "Who wrote the novel '1984'?",
      options: ["Aldous Huxley", "George Orwell", "Ray Bradbury", "Ernest Hemingway"],
      correct: 1,
      category: "Literature",
      difficulty: "medium"
    },
    {
      id: 6,
      question: "What is the largest ocean on Earth?",
      options: ["Atlantic", "Indian", "Arctic", "Pacific"],
      correct: 3,
      category: "Geography",
      difficulty: "medium"
    }
  ],
  hard: [
    {
      id: 7,
      question: "In which year was the first iPhone released?",
      options: ["2006", "2007", "2008", "2009"],
      correct: 1,
      category: "Technology",
      difficulty: "hard"
    },
    {
      id: 8,
      question: "What is the chemical symbol for gold?",
      options: ["Go", "Gd", "Au", "Ag"],
      correct: 2,
      category: "Science",
      difficulty: "hard"
    },
    {
      id: 9,
      question: "Who composed 'The Four Seasons'?",
      options: ["Bach", "Mozart", "Vivaldi", "Beethoven"],
      correct: 2,
      category: "Music",
      difficulty: "hard"
    }
  ]
};

interface QuizArenaGameProps {
  onBack?: () => void;
  onGameEnd?: (result: any) => void;
}

const QuizArenaGame: React.FC<QuizArenaGameProps> = ({ onBack, onGameEnd }) => {
  // Game configuration
  const [config, setConfig] = useState({
    gameMode: '1v1', // '1v1' or '1vN'
    maxPlayers: 4,
    gameType: 'timed', // 'timed' or 'untimed'
    timePerQuestion: 30,
    questionCount: 15,
    wagerAmount: 100,
    prizePool: 500,
    allowLifelines: true,
    spectatorBetting: true
  });

  // Game state
  const [gameStatus, setGameStatus] = useState(GAME_STATUS.SETUP);
  const [currentLevel, setCurrentLevel] = useState(1);
  const [timeLeft, setTimeLeft] = useState(0);
  const [currentQuestion, setCurrentQuestion] = useState(null);
  const [selectedAnswer, setSelectedAnswer] = useState(null);
  const [isAnswerLocked, setIsAnswerLocked] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [lifelinesUsed, setLifelinesUsed] = useState({});
  const [activeLifeline, setActiveLifeline] = useState(null);
  const [audienceVotes, setAudienceVotes] = useState({});
  const [phoneAdvice, setPhoneAdvice] = useState(null);
  const [eliminatedOptions, setEliminatedOptions] = useState([]);

  // Players and competition
  const [players, setPlayers] = useState([
    { id: 1, name: 'You', level: 1, earnings: 0, isActive: true, isEliminated: false, lifelinesUsed: {} },
    { id: 2, name: 'Player 2', level: 1, earnings: 0, isActive: false, isEliminated: false, lifelinesUsed: {} },
    { id: 3, name: 'Player 3', level: 1, earnings: 0, isActive: false, isEliminated: false, lifelinesUsed: {} },
    { id: 4, name: 'Player 4', level: 1, earnings: 0, isActive: false, isEliminated: false, lifelinesUsed: {} }
  ]);

  // Game tracking
  const [gameHistory, setGameHistory] = useState([]);
  const [gameResult, setGameResult] = useState(null);
  const [totalGameTime, setTotalGameTime] = useState(0);

  // Refs
  const timerRef = useRef(null);
  const gameTimerRef = useRef(null);

  // Chat state
  const [messages, setMessages] = useState([
    { text: "Welcome to Quiz Arena! Test your knowledge and compete for the million-dollar prize!", type: "system" }
  ]);
  const [messageInput, setMessageInput] = useState('');
  const messageEndRef = useRef(null);

  // Effects
  useEffect(() => {
    if (messageEndRef.current) {
      messageEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  useEffect(() => {
    return () => {
      if (timerRef.current) clearInterval(timerRef.current);
      if (gameTimerRef.current) clearInterval(gameTimerRef.current);
    };
  }, []);

  // Timer effect for questions
  useEffect(() => {
    if (gameStatus === GAME_STATUS.QUESTION && timeLeft > 0) {
      timerRef.current = setInterval(() => {
        setTimeLeft(prev => {
          if (prev <= 1) {
            // Time's up - auto submit
            handleTimeUp();
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    } else {
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    }

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    };
  }, [gameStatus, timeLeft]);

  // Get questions for difficulty
  const getQuestionsForDifficulty = (level) => {
    if (level <= 5) return QUESTIONS_DB.easy;
    if (level <= 10) return QUESTIONS_DB.medium;
    return QUESTIONS_DB.hard;
  };

  // Add message to chat
  const addMessage = (text, type = "player") => {
    setMessages(prev => [...prev, { text, type }]);
  };

  // Send chat message
  const sendMessage = () => {
    if (messageInput.trim()) {
      addMessage(`Spectator: ${messageInput}`);
      setMessageInput('');
    }
  };

  // Game logic functions
  const startGame = () => {
    setGameStatus(GAME_STATUS.PLAYING);
    loadNextQuestion();
    addMessage("Game started! Good luck!", "system");
  };

  const loadNextQuestion = () => {
    const questions = getQuestionsForDifficulty(currentLevel);
    const randomQuestion = questions[Math.floor(Math.random() * questions.length)];

    setCurrentQuestion(randomQuestion);
    setSelectedAnswer(null);
    setIsAnswerLocked(false);
    setTimeLeft(config.timePerQuestion);
    setGameStatus(GAME_STATUS.QUESTION);
    setEliminatedOptions([]);
    setActiveLifeline(null);

    addMessage(`Question ${currentLevel}: ${randomQuestion.category}`, "system");
  };

  const handleAnswerSelect = (answerIndex) => {
    if (isAnswerLocked || gameStatus !== GAME_STATUS.QUESTION) return;
    setSelectedAnswer(answerIndex);
  };

  const handleAnswerSubmit = () => {
    if (selectedAnswer === null || isAnswerLocked) return;

    setIsAnswerLocked(true);
    setGameStatus(GAME_STATUS.ANSWER_REVEAL);

    // Clear timer
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }

    setTimeout(() => {
      const isCorrect = selectedAnswer === currentQuestion.correct;

      if (isCorrect) {
        // Correct answer
        const newLevel = currentLevel + 1;
        const prize = PRIZE_LADDER[currentLevel - 1];

        addMessage(`Correct! You've won $${prize.amount}!`, "system");

        // Update player progress
        setPlayers(prev => prev.map(p =>
          p.id === 1 ? { ...p, level: newLevel, earnings: prize.amount } : p
        ));

        if (newLevel > 15) {
          // Won the million!
          setGameStatus(GAME_STATUS.GAME_END);
          setGameResult({
            won: true,
            amount: 1000000,
            level: 15,
            message: "CONGRATULATIONS! YOU'VE WON THE MILLION DOLLAR PRIZE!"
          });
          addMessage("🎉 MILLION DOLLAR WINNER! 🎉", "system");
        } else {
          setCurrentLevel(newLevel);
          setTimeout(() => {
            setGameStatus(GAME_STATUS.ROUND_END);
          }, 2000);
        }
      } else {
        // Wrong answer
        const safeAmount = getSafeAmount(currentLevel);
        setGameStatus(GAME_STATUS.GAME_END);
        setGameResult({
          won: false,
          amount: safeAmount,
          level: currentLevel - 1,
          message: `Game Over! You leave with $${safeAmount}`
        });
        addMessage(`Wrong answer! You leave with $${safeAmount}`, "system");
      }
    }, 3000);
  };

  const handleTimeUp = () => {
    if (gameStatus !== GAME_STATUS.QUESTION) return;

    setIsAnswerLocked(true);
    setGameStatus(GAME_STATUS.ANSWER_REVEAL);

    setTimeout(() => {
      const safeAmount = getSafeAmount(currentLevel);
      setGameStatus(GAME_STATUS.GAME_END);
      setGameResult({
        won: false,
        amount: safeAmount,
        level: currentLevel - 1,
        message: `Time's up! You leave with $${safeAmount}`
      });
      addMessage(`Time's up! You leave with $${safeAmount}`, "system");
    }, 2000);
  };

  const getSafeAmount = (level) => {
    if (level <= 5) return 0;
    if (level <= 10) return 1000;
    return 32000;
  };

  const handleWalkAway = () => {
    const currentPrize = currentLevel > 1 ? PRIZE_LADDER[currentLevel - 2].amount : 0;
    setGameStatus(GAME_STATUS.GAME_END);
    setGameResult({
      won: false,
      amount: currentPrize,
      level: currentLevel - 1,
      message: `You walked away with $${currentPrize}`
    });
    addMessage(`Player walked away with $${currentPrize}`, "system");
  };

  const continueToNextQuestion = () => {
    setGameStatus(GAME_STATUS.PLAYING);
    loadNextQuestion();
  };

  // Lifeline functions
  const useFiftyFifty = () => {
    if (lifelinesUsed[LIFELINES.FIFTY_FIFTY] || !currentQuestion) return;

    setLifelinesUsed(prev => ({ ...prev, [LIFELINES.FIFTY_FIFTY]: true }));

    // Remove 2 wrong answers
    const correctAnswer = currentQuestion.correct;
    const wrongAnswers = [0, 1, 2, 3].filter(i => i !== correctAnswer);
    const toEliminate = wrongAnswers.slice(0, 2);

    setEliminatedOptions(toEliminate);
    addMessage("50/50 used! Two wrong answers eliminated.", "system");
  };

  const usePhoneFriend = () => {
    if (lifelinesUsed[LIFELINES.PHONE_FRIEND] || !currentQuestion) return;

    setLifelinesUsed(prev => ({ ...prev, [LIFELINES.PHONE_FRIEND]: true }));
    setActiveLifeline(LIFELINES.PHONE_FRIEND);

    // Simulate friend's advice (80% chance of correct answer)
    const isCorrectAdvice = Math.random() < 0.8;
    const suggestedAnswer = isCorrectAdvice ?
      currentQuestion.correct :
      Math.floor(Math.random() * 4);

    const answerLetter = ['A', 'B', 'C', 'D'][suggestedAnswer];
    setPhoneAdvice(`I think it's ${answerLetter}. I'm pretty confident about this one!`);

    setTimeout(() => {
      setActiveLifeline(null);
      setPhoneAdvice(null);
    }, 10000);

    addMessage("Phone a Friend used!", "system");
  };

  const useAskAudience = () => {
    if (lifelinesUsed[LIFELINES.ASK_AUDIENCE] || !currentQuestion) return;

    setLifelinesUsed(prev => ({ ...prev, [LIFELINES.ASK_AUDIENCE]: true }));
    setActiveLifeline(LIFELINES.ASK_AUDIENCE);

    // Generate audience votes (weighted toward correct answer)
    const votes = { A: 0, B: 0, C: 0, D: 0 };
    const correctLetter = ['A', 'B', 'C', 'D'][currentQuestion.correct];

    // 60% chance audience is mostly right
    if (Math.random() < 0.6) {
      votes[correctLetter] = 45 + Math.floor(Math.random() * 20);
      const remaining = 100 - votes[correctLetter];
      const others = ['A', 'B', 'C', 'D'].filter(l => l !== correctLetter);
      others.forEach((letter, i) => {
        if (i === others.length - 1) {
          votes[letter] = remaining - votes[others[0]] - votes[others[1]];
        } else {
          votes[letter] = Math.floor(Math.random() * (remaining / 2));
        }
      });
    } else {
      // Audience is confused
      ['A', 'B', 'C', 'D'].forEach(letter => {
        votes[letter] = 20 + Math.floor(Math.random() * 15);
      });
    }

    setAudienceVotes(votes);

    setTimeout(() => {
      setActiveLifeline(null);
      setAudienceVotes({});
    }, 15000);

    addMessage("Ask the Audience used!", "system");
  };

  return (
    <div className="h-screen bg-slate-950 text-white overflow-hidden">
      <div className="container mx-auto px-4 pt-20 pb-4 h-full flex flex-col">
        {/* Header - Fixed Height */}
        <div className="flex items-center justify-between mb-4 flex-shrink-0">
          <div className="flex items-center">
            {onBack && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onBack}
                className="h-8 w-8 p-0 mr-3"
              >
                <X className="h-4 w-4" />
              </Button>
            )}
            <div className="flex items-center">
              <div className="h-10 w-10 rounded bg-gradient-to-br from-yellow-500 to-yellow-600 flex items-center justify-center mr-3">
                <Brain className="h-5 w-5 text-black" />
              </div>
              <div>
                <h1 className="text-xl font-semibold">Quiz Arena</h1>
                <p className="text-sm text-slate-400">Who Wants to Be a Millionaire?</p>
              </div>
            </div>
          </div>

          <Badge className="bg-yellow-500 text-black px-3 py-1">
            <Crown className="h-4 w-4 mr-1" />
            Million Dollar Challenge
          </Badge>
        </div>

        {/* Game Content - Flex 1 to fill remaining space */}
        <div className="grid grid-cols-12 gap-4 flex-1 min-h-0">
          {/* Left Sidebar - Game Participants & Data */}
          <div className="col-span-4 space-y-4 h-full flex flex-col">
            {/* Game Participants */}
            <div className="bg-slate-900 border border-slate-800 rounded-lg p-4">
              <h3 className="text-sm font-semibold text-white mb-3 flex items-center">
                <Users className="h-4 w-4 mr-2 text-blue-500" />
                Players ({players.filter(p => !p.isEliminated).length})
              </h3>
              <div className="space-y-2">
                {players.map((player) => (
                  <div
                    key={player.id}
                    className={`flex items-center justify-between p-2 rounded text-xs
                      ${player.isActive ? 'bg-yellow-600 text-black' :
                        player.isEliminated ? 'bg-red-800 text-red-200' :
                        'bg-slate-800 text-slate-300'}
                    `}
                  >
                    <div className="flex items-center">
                      <div className={`h-2 w-2 rounded-full mr-2 ${
                        player.isActive ? 'bg-black' :
                        player.isEliminated ? 'bg-red-500' : 'bg-green-500'
                      }`} />
                      <span className="font-medium">{player.name}</span>
                    </div>
                    <div className="text-right">
                      <div>Level {player.level}</div>
                      <div className="text-xs opacity-75">${player.earnings.toLocaleString()}</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Prize Ladder */}
            <div className="bg-slate-900 border border-slate-800 rounded-lg p-4 flex-1 min-h-0 flex flex-col">
              <h3 className="text-sm font-semibold text-white mb-3 flex items-center flex-shrink-0">
                <Trophy className="h-4 w-4 mr-2 text-yellow-500" />
                Prize Ladder
              </h3>
              <div className="space-y-1 overflow-y-auto flex-1 min-h-0">
                {PRIZE_LADDER.slice().reverse().map((prize, index) => {
                  const level = 15 - index;
                  const isCurrentLevel = level === currentLevel;
                  const isPassed = level < currentLevel;
                  const isSafe = prize.safe;

                  return (
                    <div
                      key={level}
                      className={`flex justify-between items-center p-2 rounded text-xs
                        ${isCurrentLevel ? 'bg-yellow-600 text-black font-bold' :
                          isPassed ? 'bg-green-800 text-green-200' :
                          'bg-slate-800 text-slate-400'}
                        ${isSafe ? 'border border-yellow-500' : ''}
                      `}
                    >
                      <span>{level}.</span>
                      <span className={isCurrentLevel ? 'text-black' : isSafe ? 'text-yellow-400' : ''}>
                        ${prize.amount.toLocaleString()}
                      </span>
                      {isSafe && <Star className="h-3 w-3 text-yellow-400" />}
                    </div>
                  );
                })}
              </div>
            </div>

            {/* Lifelines */}
            {config.allowLifelines && gameStatus === GAME_STATUS.QUESTION && (
              <div className="bg-slate-900 border border-slate-800 rounded-lg p-4">
                <h3 className="text-sm font-semibold text-white mb-3">Lifelines</h3>
                <div className="grid grid-cols-2 gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    disabled={lifelinesUsed[LIFELINES.FIFTY_FIFTY]}
                    onClick={useFiftyFifty}
                    className={`h-12 text-xs ${lifelinesUsed[LIFELINES.FIFTY_FIFTY] ? 'opacity-50' : 'hover:bg-slate-700'}`}
                  >
                    <Split className="h-4 w-4 mb-1" />
                    50/50
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    disabled={lifelinesUsed[LIFELINES.PHONE_FRIEND]}
                    onClick={usePhoneFriend}
                    className={`h-12 text-xs ${lifelinesUsed[LIFELINES.PHONE_FRIEND] ? 'opacity-50' : 'hover:bg-slate-700'}`}
                  >
                    <Phone className="h-4 w-4 mb-1" />
                    Phone
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    disabled={lifelinesUsed[LIFELINES.ASK_AUDIENCE]}
                    onClick={useAskAudience}
                    className={`h-12 text-xs ${lifelinesUsed[LIFELINES.ASK_AUDIENCE] ? 'opacity-50' : 'hover:bg-slate-700'}`}
                  >
                    <UsersRound className="h-4 w-4 mb-1" />
                    Audience
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    disabled={true}
                    className="h-12 text-xs opacity-50"
                  >
                    <HelpCircle className="h-4 w-4 mb-1" />
                    Expert
                  </Button>
                </div>
              </div>
            )}
          </div>

          {/* Main Game Area */}
          <div className="col-span-4 h-full">
            <div className="bg-slate-900 border border-slate-800 rounded-lg p-6 h-full flex flex-col">
              {gameStatus === GAME_STATUS.SETUP ? (
                <div className="text-center flex-1 flex flex-col justify-center">
                  <div className="text-6xl mb-4">🧠</div>
                  <h2 className="text-3xl font-bold text-white mb-2">Quiz Arena</h2>
                  <p className="text-lg text-yellow-500 mb-4">Who Wants to Be a Millionaire?</p>
                  <p className="text-slate-400 mb-8">
                    Answer questions correctly to climb the prize ladder!<br />
                    Use lifelines wisely and compete for the ultimate prize.
                  </p>

                  <Button
                    className="bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-400 hover:to-yellow-500 text-black font-bold px-8 py-3"
                    onClick={startGame}
                  >
                    <Crown className="h-5 w-5 mr-2" />
                    Start Quiz
                  </Button>
                </div>
              ) : gameStatus === GAME_STATUS.QUESTION ? (
                <div className="flex-1 flex flex-col">
                  {/* Question Header */}
                  <div className="text-center mb-6">
                    <div className="flex items-center justify-center mb-2">
                      <Badge className="bg-yellow-600 text-black px-3 py-1 text-sm font-bold">
                        Question {currentLevel} of 15
                      </Badge>
                    </div>
                    <div className="flex items-center justify-center mb-4">
                      <Timer className="h-5 w-5 mr-2 text-yellow-500" />
                      <span className={`text-2xl font-bold ${timeLeft <= 10 ? 'text-red-500 animate-pulse' : 'text-white'}`}>
                        {timeLeft}s
                      </span>
                    </div>
                    <Progress value={(timeLeft / config.timePerQuestion) * 100} className="w-full h-2" />
                  </div>

                  {/* Question */}
                  <div className="bg-slate-800 rounded-lg p-6 mb-6 flex-1 flex items-center justify-center">
                    <div className="text-center">
                      <Badge className="bg-blue-600 text-white px-2 py-1 text-xs mb-4">
                        {currentQuestion?.category}
                      </Badge>
                      <h3 className="text-xl font-semibold text-white leading-relaxed">
                        {currentQuestion?.question}
                      </h3>
                    </div>
                  </div>

                  {/* Answer Options */}
                  <div className="grid grid-cols-2 gap-4 mb-6">
                    {currentQuestion?.options.map((option, index) => {
                      const isSelected = selectedAnswer === index;
                      const isEliminated = eliminatedOptions.includes(index);
                      const isCorrect = gameStatus === GAME_STATUS.ANSWER_REVEAL && index === currentQuestion.correct;
                      const isWrong = gameStatus === GAME_STATUS.ANSWER_REVEAL && isSelected && index !== currentQuestion.correct;

                      return (
                        <Button
                          key={index}
                          variant="outline"
                          disabled={isAnswerLocked || isEliminated}
                          onClick={() => handleAnswerSelect(index)}
                          className={`h-16 text-left p-4 text-sm font-medium transition-all
                            ${isEliminated ? 'opacity-30 cursor-not-allowed' :
                              isCorrect ? 'bg-green-600 border-green-500 text-white' :
                              isWrong ? 'bg-red-600 border-red-500 text-white' :
                              isSelected ? 'bg-yellow-600 border-yellow-500 text-black' :
                              'bg-slate-800 border-slate-700 text-white hover:bg-slate-700'}
                          `}
                        >
                          <div className="flex items-center">
                            <span className="font-bold mr-3 text-lg">
                              {['A', 'B', 'C', 'D'][index]}:
                            </span>
                            <span>{option}</span>
                          </div>
                        </Button>
                      );
                    })}
                  </div>

                  {/* Action Buttons */}
                  <div className="flex justify-between items-center">
                    <Button
                      variant="outline"
                      onClick={handleWalkAway}
                      disabled={isAnswerLocked}
                      className="bg-red-800 border-red-700 text-white hover:bg-red-700"
                    >
                      Walk Away (${currentLevel > 1 ? PRIZE_LADDER[currentLevel - 2].amount.toLocaleString() : '0'})
                    </Button>

                    <Button
                      onClick={handleAnswerSubmit}
                      disabled={selectedAnswer === null || isAnswerLocked}
                      className="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-400 hover:to-green-500 text-white font-bold px-8"
                    >
                      {isAnswerLocked ? 'Submitting...' : 'Final Answer'}
                    </Button>
                  </div>
                </div>
              ) : gameStatus === GAME_STATUS.ANSWER_REVEAL ? (
                <div className="text-center flex-1 flex flex-col justify-center">
                  <div className="text-4xl mb-4">
                    {selectedAnswer === currentQuestion?.correct ? '🎉' : '😞'}
                  </div>
                  <h2 className="text-2xl font-bold text-white mb-4">
                    {selectedAnswer === currentQuestion?.correct ? 'Correct!' : 'Wrong Answer!'}
                  </h2>
                  <p className="text-lg text-slate-400">
                    The correct answer was: <span className="text-yellow-500 font-bold">
                      {['A', 'B', 'C', 'D'][currentQuestion?.correct]}: {currentQuestion?.options[currentQuestion?.correct]}
                    </span>
                  </p>
                </div>
              ) : gameStatus === GAME_STATUS.ROUND_END ? (
                <div className="text-center flex-1 flex flex-col justify-center">
                  <div className="text-4xl mb-4">🎯</div>
                  <h2 className="text-2xl font-bold text-white mb-4">Level {currentLevel - 1} Complete!</h2>
                  <p className="text-lg text-yellow-500 mb-6">
                    You've won ${PRIZE_LADDER[currentLevel - 2]?.amount.toLocaleString()}!
                  </p>
                  <Button
                    onClick={continueToNextQuestion}
                    className="bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-400 hover:to-yellow-500 text-black font-bold px-8 py-3"
                  >
                    Continue to Question {currentLevel}
                  </Button>
                </div>
              ) : gameStatus === GAME_STATUS.GAME_END ? (
                <div className="text-center flex-1 flex flex-col justify-center">
                  <div className="text-6xl mb-4">
                    {gameResult?.won ? '🏆' : '💰'}
                  </div>
                  <h2 className="text-3xl font-bold text-white mb-4">
                    {gameResult?.message}
                  </h2>
                  <p className="text-2xl text-yellow-500 mb-6">
                    Final Prize: ${gameResult?.amount.toLocaleString()}
                  </p>
                  <div className="space-x-4">
                    <Button
                      onClick={() => window.location.reload()}
                      className="bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-400 hover:to-yellow-500 text-black font-bold px-8 py-3"
                    >
                      Play Again
                    </Button>
                    <Button
                      variant="outline"
                      onClick={onBack}
                      className="border-slate-700 text-white hover:bg-slate-800"
                    >
                      Back to Games
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="text-center flex-1 flex flex-col justify-center">
                  <p className="text-slate-400">Loading...</p>
                </div>
              )}
            </div>
          </div>

          {/* Right Sidebar - Chat & Lifeline Results */}
          <div className="col-span-4 h-full flex flex-col space-y-4">
            {/* Lifeline Results */}
            {activeLifeline === LIFELINES.PHONE_FRIEND && phoneAdvice && (
              <div className="bg-blue-900 border border-blue-700 rounded-lg p-4">
                <h3 className="text-sm font-semibold text-white mb-2 flex items-center">
                  <Phone className="h-4 w-4 mr-2 text-blue-400" />
                  Phone a Friend
                </h3>
                <div className="bg-blue-800 rounded p-3">
                  <p className="text-sm text-blue-200">"{phoneAdvice}"</p>
                </div>
              </div>
            )}

            {activeLifeline === LIFELINES.ASK_AUDIENCE && Object.keys(audienceVotes).length > 0 && (
              <div className="bg-purple-900 border border-purple-700 rounded-lg p-4">
                <h3 className="text-sm font-semibold text-white mb-3 flex items-center">
                  <UsersRound className="h-4 w-4 mr-2 text-purple-400" />
                  Audience Poll
                </h3>
                <div className="space-y-2">
                  {Object.entries(audienceVotes).map(([letter, percentage]) => (
                    <div key={letter} className="flex items-center">
                      <span className="text-sm font-bold text-white w-6">{letter}:</span>
                      <div className="flex-1 bg-slate-800 rounded-full h-4 mx-2">
                        <div
                          className="bg-purple-500 h-4 rounded-full transition-all duration-1000"
                          style={{ width: `${percentage}%` }}
                        />
                      </div>
                      <span className="text-sm text-white w-8">{percentage}%</span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Game Stats - Fixed Height */}
            <div className="bg-slate-900 border border-slate-800 rounded-lg p-4 flex-shrink-0">
              <h3 className="text-sm font-semibold text-white mb-3">Game Stats</h3>
              <div className="space-y-2 text-xs">
                <div className="flex justify-between">
                  <span className="text-slate-400">Current Level:</span>
                  <span className="text-white font-bold">{currentLevel}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-400">Current Prize:</span>
                  <span className="text-yellow-500 font-bold">
                    ${currentLevel > 1 ? PRIZE_LADDER[currentLevel - 2].amount.toLocaleString() : '0'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-400">Next Prize:</span>
                  <span className="text-white">
                    ${PRIZE_LADDER[currentLevel - 1]?.amount.toLocaleString() || 'N/A'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-400">Lifelines Used:</span>
                  <span className="text-white">
                    {Object.keys(lifelinesUsed).filter(key => lifelinesUsed[key]).length}/4
                  </span>
                </div>
              </div>
            </div>

            {/* Live Chat - Flex 1 to fill remaining space */}
            <div className="bg-slate-900 border border-slate-800 rounded-lg p-4 flex-1 min-h-0 flex flex-col">
              <h3 className="text-sm font-semibold text-white mb-3 flex-shrink-0">Live Chat</h3>
              <div className="flex-1 overflow-y-auto mb-3 space-y-1 min-h-0">
                {messages.map((msg, index) => (
                  <div key={index} className="text-xs">
                    <span className={msg.type === 'system' ? 'text-yellow-400' : 'text-slate-300'}>
                      {msg.text}
                    </span>
                  </div>
                ))}
                <div ref={messageEndRef} />
              </div>
              <div className="flex gap-2 flex-shrink-0">
                <Input
                  value={messageInput}
                  onChange={(e) => setMessageInput(e.target.value)}
                  placeholder="Type a message..."
                  className="flex-1 h-8 text-xs bg-slate-800 border-slate-700"
                  onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
                />
                <Button size="sm" onClick={sendMessage} className="h-8 px-3">
                  <MessageSquare className="h-3 w-3" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default QuizArenaGame;
