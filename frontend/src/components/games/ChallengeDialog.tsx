import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Search, 
  User, 
  Swords, 
  DollarSign, 
  Clock,
  Trophy,
  Star
} from 'lucide-react';

interface ChallengeDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSendChallenge: (challenge: ChallengeData) => void;
  gameType: string;
}

export interface ChallengeData {
  opponent: string;
  opponentId: string;
  wagerAmount: number;
  gameType: string;
  timeLimit?: number;
  isRanked: boolean;
}

// Mock online users data
const mockOnlineUsers = [
  { id: '1', username: 'ProGamer88', rating: 1850, status: 'online', winRate: 72 },
  { id: '2', username: 'ChessKing', rating: 2100, status: 'online', winRate: 85 },
  { id: '3', username: 'QuickThinker', rating: 1650, status: 'in-game', winRate: 58 },
  { id: '4', username: 'StrategyMaster', rating: 1950, status: 'online', winRate: 69 },
  { id: '5', username: 'CasualPlayer', rating: 1400, status: 'online', winRate: 45 },
  { id: '6', username: 'Challenger99', rating: 1780, status: 'online', winRate: 61 },
];

const ChallengeDialog: React.FC<ChallengeDialogProps> = ({
  isOpen,
  onClose,
  onSendChallenge,
  gameType = 'checkers'
}) => {
  const [selectedUser, setSelectedUser] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [wagerAmount, setWagerAmount] = useState(10);
  const [timeLimit, setTimeLimit] = useState('300'); // 5 minutes default
  const [isRanked, setIsRanked] = useState(false);
  const [challengeType, setChallengeType] = useState<'specific' | 'random'>('specific');

  const filteredUsers = mockOnlineUsers.filter(user => 
    user.username.toLowerCase().includes(searchQuery.toLowerCase()) &&
    user.status === 'online'
  );

  const handleSendChallenge = () => {
    if (challengeType === 'random' || selectedUser) {
      const opponent = challengeType === 'random' 
        ? filteredUsers[Math.floor(Math.random() * filteredUsers.length)]
        : mockOnlineUsers.find(u => u.id === selectedUser);

      if (opponent) {
        onSendChallenge({
          opponent: opponent.username,
          opponentId: opponent.id,
          wagerAmount,
          gameType,
          timeLimit: parseInt(timeLimit),
          isRanked
        });
        onClose();
      }
    }
  };

  const predefinedWagers = [5, 10, 25, 50, 100];

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="bg-slate-900 border-slate-800 text-white max-w-md">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold flex items-center gap-2">
            <Swords className="h-5 w-5 text-purple-500" />
            Challenge to {gameType.charAt(0).toUpperCase() + gameType.slice(1)}
          </DialogTitle>
          <DialogDescription className="text-sm text-slate-400">
            Challenge another player to a {gameType} match
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          {/* Challenge Type */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Challenge Type</Label>
            <RadioGroup value={challengeType} onValueChange={(value: 'specific' | 'random') => setChallengeType(value)}>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="specific" id="specific" />
                <Label htmlFor="specific" className="cursor-pointer">
                  Specific Player
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="random" id="random" />
                <Label htmlFor="random" className="cursor-pointer">
                  Random Opponent
                </Label>
              </div>
            </RadioGroup>
          </div>

          {/* Player Selection (if specific) */}
          {challengeType === 'specific' && (
            <div className="space-y-3">
              <Label className="text-sm font-medium">Select Opponent</Label>
              
              {/* Search */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                <Input
                  type="text"
                  placeholder="Search players..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 bg-slate-800 border-slate-700 text-white"
                />
              </div>

              {/* Players List */}
              <ScrollArea className="h-48 rounded-md border border-slate-700">
                <div className="p-2 space-y-1">
                  {filteredUsers.map((user) => (
                    <div
                      key={user.id}
                      onClick={() => setSelectedUser(user.id)}
                      className={`p-3 rounded cursor-pointer transition-colors
                        ${selectedUser === user.id 
                          ? 'bg-purple-600 bg-opacity-20 border border-purple-600' 
                          : 'bg-slate-800 hover:bg-slate-700'}`}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <User className="h-4 w-4 text-slate-400" />
                          <div>
                            <p className="text-sm font-medium">{user.username}</p>
                            <div className="flex items-center gap-2 text-xs text-slate-400">
                              <Star className="h-3 w-3" />
                              <span>{user.rating}</span>
                              <span>•</span>
                              <span>{user.winRate}% WR</span>
                            </div>
                          </div>
                        </div>
                        <Badge className="bg-green-500 text-white text-xs">Online</Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </div>
          )}

          {/* Wager Amount */}
          <div className="space-y-3">
            <Label className="text-sm font-medium flex items-center gap-2">
              <DollarSign className="h-4 w-4 text-green-500" />
              Wager Amount
            </Label>
            
            <div className="flex gap-2 flex-wrap">
              {predefinedWagers.map((amount) => (
                <Button
                  key={amount}
                  variant={wagerAmount === amount ? 'default' : 'outline'}
                  size="sm"
                  className={`h-9 px-4 ${
                    wagerAmount === amount
                      ? 'bg-purple-600 border-purple-600'
                      : 'border-slate-700'
                  }`}
                  onClick={() => setWagerAmount(amount)}
                >
                  ${amount}
                </Button>
              ))}
            </div>
            
            <div className="flex items-center gap-2">
              <span className="text-sm text-slate-400">Custom:</span>
              <Input
                type="number"
                value={wagerAmount}
                onChange={(e) => setWagerAmount(Number(e.target.value))}
                className="w-24 h-9 bg-slate-800 border-slate-700"
                min={1}
                step={1}
              />
            </div>
          </div>

          {/* Time Control */}
          <div className="space-y-3">
            <Label className="text-sm font-medium flex items-center gap-2">
              <Clock className="h-4 w-4 text-amber-500" />
              Time Control
            </Label>
            
            <Select value={timeLimit} onValueChange={setTimeLimit}>
              <SelectTrigger className="w-full bg-slate-800 border-slate-700">
                <SelectValue />
              </SelectTrigger>
              <SelectContent className="bg-slate-800 border-slate-700">
                <SelectItem value="180">3 minutes</SelectItem>
                <SelectItem value="300">5 minutes</SelectItem>
                <SelectItem value="600">10 minutes</SelectItem>
                <SelectItem value="900">15 minutes</SelectItem>
                <SelectItem value="0">No time limit</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Ranked Game */}
          <div className="flex items-center justify-between">
            <Label className="text-sm font-medium flex items-center gap-2">
              <Trophy className="h-4 w-4 text-amber-500" />
              Ranked Game
            </Label>
            <div className="flex items-center gap-2">
              <Input
                type="checkbox"
                checked={isRanked}
                onChange={(e) => setIsRanked(e.target.checked)}
                className="w-4 h-4"
              />
              <span className="text-sm text-slate-400">
                {isRanked ? 'Yes' : 'No'}
              </span>
            </div>
          </div>
        </div>

        <DialogFooter className="flex gap-2">
          <Button
            variant="outline"
            onClick={onClose}
            className="border-slate-700"
          >
            Cancel
          </Button>
          <Button
            onClick={handleSendChallenge}
            disabled={challengeType === 'specific' && !selectedUser}
            className="bg-gradient-to-r from-purple-500 to-pink-500"
          >
            Send Challenge
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ChallengeDialog;