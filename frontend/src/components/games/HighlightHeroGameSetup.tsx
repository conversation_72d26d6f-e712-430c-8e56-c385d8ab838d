import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Film, DollarSign } from 'lucide-react';

interface HighlightHeroGameSetupProps {
  onComplete: (config: any) => void;
  onCancel: () => void;
}

const HighlightHeroGameSetup: React.FC<HighlightHeroGameSetupProps> = ({ onComplete, onCancel }) => {
  const [category, setCategory] = useState('soccer');
  const [rounds, setRounds] = useState('5');
  const [wagerAmount, setWagerAmount] = useState('50');
  const [timeLimit, setTimeLimit] = useState('30');
  const [allowReplays, setAllowReplays] = useState(true);
  const [maxReplays, setMaxReplays] = useState('2');
  const [difficulty, setDifficulty] = useState('mixed');

  const handleStartGame = () => {
    const config = {
      category,
      rounds: parseInt(rounds),
      wagerAmount: parseInt(wagerAmount),
      timeLimit: parseInt(timeLimit),
      allowReplays,
      maxReplays: parseInt(maxReplays),
      difficulty
    };
    onComplete(config);
  };

  return (
    <Dialog open={true} onOpenChange={(open) => !open && onCancel()}>
      <DialogContent className="sm:max-w-[500px] bg-slate-900 border-slate-800">
        <DialogHeader>
          <DialogTitle className="flex items-center text-white">
            <Film className="h-5 w-5 mr-2 text-purple-500" />
            HighlightHero Setup
          </DialogTitle>
          <DialogDescription className="text-slate-400">
            Configure your game settings for the video quiz challenge
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 mt-4">
          {/* Category Selection */}
          <div className="space-y-2">
            <Label className="text-white">Category</Label>
            <Select value={category} onValueChange={setCategory}>
              <SelectTrigger className="bg-slate-800 border-slate-700 text-white">
                <SelectValue />
              </SelectTrigger>
              <SelectContent className="bg-slate-800 border-slate-700">
                <SelectItem value="soccer" className="text-white hover:bg-slate-700">
                  ⚽ Soccer
                </SelectItem>
                <SelectItem value="movies" className="text-white hover:bg-slate-700">
                  🎬 Movies
                </SelectItem>
                <SelectItem value="music" className="text-white hover:bg-slate-700">
                  🎵 Music
                </SelectItem>
                <SelectItem value="sports" className="text-white hover:bg-slate-700">
                  🏀 Sports
                </SelectItem>
                <SelectItem value="gaming" className="text-white hover:bg-slate-700">
                  🎮 Gaming
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Number of Rounds */}
          <div className="space-y-2">
            <Label className="text-white">Number of Rounds</Label>
            <Select value={rounds} onValueChange={setRounds}>
              <SelectTrigger className="bg-slate-800 border-slate-700 text-white">
                <SelectValue />
              </SelectTrigger>
              <SelectContent className="bg-slate-800 border-slate-700">
                <SelectItem value="3" className="text-white hover:bg-slate-700">3 Rounds</SelectItem>
                <SelectItem value="5" className="text-white hover:bg-slate-700">5 Rounds</SelectItem>
                <SelectItem value="7" className="text-white hover:bg-slate-700">7 Rounds</SelectItem>
                <SelectItem value="10" className="text-white hover:bg-slate-700">10 Rounds</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Wager Amount */}
          <div className="space-y-2">
            <Label className="text-white">Wager Amount</Label>
            <div className="relative">
              <DollarSign className="absolute left-3 top-3 h-4 w-4 text-slate-400" />
              <Input
                type="number"
                value={wagerAmount}
                onChange={(e) => setWagerAmount(e.target.value)}
                min="10"
                max="1000"
                step="10"
                className="pl-10 bg-slate-800 border-slate-700 text-white"
              />
            </div>
          </div>

          {/* Time Limit */}
          <div className="space-y-2">
            <Label className="text-white">Answer Time Limit (seconds)</Label>
            <Select value={timeLimit} onValueChange={setTimeLimit}>
              <SelectTrigger className="bg-slate-800 border-slate-700 text-white">
                <SelectValue />
              </SelectTrigger>
              <SelectContent className="bg-slate-800 border-slate-700">
                <SelectItem value="15" className="text-white hover:bg-slate-700">15 seconds</SelectItem>
                <SelectItem value="30" className="text-white hover:bg-slate-700">30 seconds</SelectItem>
                <SelectItem value="45" className="text-white hover:bg-slate-700">45 seconds</SelectItem>
                <SelectItem value="60" className="text-white hover:bg-slate-700">60 seconds</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Difficulty */}
          <div className="space-y-2">
            <Label className="text-white">Difficulty</Label>
            <Select value={difficulty} onValueChange={setDifficulty}>
              <SelectTrigger className="bg-slate-800 border-slate-700 text-white">
                <SelectValue />
              </SelectTrigger>
              <SelectContent className="bg-slate-800 border-slate-700">
                <SelectItem value="easy" className="text-white hover:bg-slate-700">Easy</SelectItem>
                <SelectItem value="medium" className="text-white hover:bg-slate-700">Medium</SelectItem>
                <SelectItem value="hard" className="text-white hover:bg-slate-700">Hard</SelectItem>
                <SelectItem value="mixed" className="text-white hover:bg-slate-700">Mixed</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Allow Replays */}
          <div className="flex items-center justify-between">
            <Label htmlFor="allow-replays" className="text-white">Allow Video Replays</Label>
            <Switch
              id="allow-replays"
              checked={allowReplays}
              onCheckedChange={setAllowReplays}
              className="data-[state=checked]:bg-purple-500"
            />
          </div>

          {/* Max Replays (only show if replays are allowed) */}
          {allowReplays && (
            <div className="space-y-2">
              <Label className="text-white">Maximum Replays</Label>
              <Select value={maxReplays} onValueChange={setMaxReplays}>
                <SelectTrigger className="bg-slate-800 border-slate-700 text-white">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-slate-800 border-slate-700">
                  <SelectItem value="1" className="text-white hover:bg-slate-700">1 Replay</SelectItem>
                  <SelectItem value="2" className="text-white hover:bg-slate-700">2 Replays</SelectItem>
                  <SelectItem value="3" className="text-white hover:bg-slate-700">3 Replays</SelectItem>
                  <SelectItem value="5" className="text-white hover:bg-slate-700">5 Replays</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}

          {/* Total Stake Display */}
          <div className="bg-slate-800 p-3 rounded-sm">
            <div className="flex justify-between items-center">
              <span className="text-sm text-slate-400">Total Stake:</span>
              <span className="text-lg font-bold text-white">${wagerAmount}</span>
            </div>
            <div className="text-xs text-slate-400 mt-1">
              Potential payout: ${parseInt(wagerAmount) * 3} (3x for excellent performance)
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end space-x-3 mt-6">
          <Button 
            variant="outline" 
            onClick={onCancel}
            className="border-slate-700 text-slate-400 hover:text-white"
          >
            Cancel
          </Button>
          <Button 
            onClick={handleStartGame}
            className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600"
          >
            Start Game
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default HighlightHeroGameSetup;