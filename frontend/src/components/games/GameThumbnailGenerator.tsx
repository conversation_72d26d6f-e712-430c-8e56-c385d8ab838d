import React from 'react';
import { Badge } from '@/components/ui/badge';
import { GameInstanceStatus } from './GameStatusBadge';
import {
  Crown,
  Sword,
  Target,
  Zap,
  Brain,
  Gamepad2,
  Users,
  Clock,
  Trophy,
  Radio
} from 'lucide-react';

interface GameThumbnailProps {
  gameType: string;
  status: GameInstanceStatus;
  players?: Array<{ id: string; name: string; avatar?: string }>;
  currentState?: any;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

const GameThumbnailGenerator: React.FC<GameThumbnailProps> = ({
  gameType,
  status,
  players = [],
  currentState,
  size = 'md',
  className = ''
}) => {
  const sizeClasses = {
    sm: 'w-12 h-12',
    md: 'w-16 h-16',
    lg: 'w-24 h-24'
  };

  const getGameIcon = (type: string) => {
    switch (type) {
      case 'chess':
        return <Crown className="h-6 w-6 text-yellow-400" />;
      case 'checkers':
        return <Target className="h-6 w-6 text-red-400" />;
      case 'rock-paper-scissors':
        return <Sword className="h-6 w-6 text-blue-400" />;
      case 'highlight-hero':
        return <Zap className="h-6 w-6 text-purple-400" />;
      case 'blur-detective':
        return <Brain className="h-6 w-6 text-green-400" />;
      case 'word-jumble':
        return <Brain className="h-6 w-6 text-orange-400" />;
      case 'quiz-arena':
        return <Brain className="h-6 w-6 text-yellow-400" />;
      default:
        return <Gamepad2 className="h-6 w-6 text-slate-400" />;
    }
  };

  const getStatusOverlay = (status: GameInstanceStatus) => {
    switch (status) {
      case 'live':
        return (
          <div className="absolute top-1 right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse border border-white" />
        );
      case 'scheduled':
        return (
          <div className="absolute top-1 right-1 w-3 h-3 bg-blue-500 rounded-full border border-white">
            <Clock className="h-2 w-2 text-white absolute top-0.5 left-0.5" />
          </div>
        );
      case 'completed':
        return (
          <div className="absolute top-1 right-1 w-3 h-3 bg-gray-500 rounded-full border border-white">
            <Trophy className="h-2 w-2 text-white absolute top-0.5 left-0.5" />
          </div>
        );
      case 'waiting':
        return (
          <div className="absolute top-1 right-1 w-3 h-3 bg-green-500 rounded-full border border-white">
            <Users className="h-2 w-2 text-white absolute top-0.5 left-0.5" />
          </div>
        );
      default:
        return null;
    }
  };

  const renderGameSpecificPreview = () => {
    const isChess = gameType === 'chess';
    const isCheckers = gameType === 'checkers';
    const isBlurDetective = gameType === 'blur-detective';
    const isHighlightHero = gameType === 'highlight-hero';
    const isQuizArena = gameType === 'quiz-arena';

    if (isChess) {
      // Simplified chess board with some pieces
      return (
        <div className="absolute inset-0 bg-slate-800 rounded overflow-hidden">
          <div className="absolute inset-0 grid grid-cols-8 grid-rows-8">
            {Array.from({ length: 64 }).map((_, i) => {
              const row = Math.floor(i / 8);
              const col = i % 8;
              const isLight = (row + col) % 2 === 0;

              return (
                <div
                  key={i}
                  className={`relative ${isLight ? 'bg-slate-700' : 'bg-slate-600'}`}
                >
                  {/* Add some sample pieces */}
                  {(i === 0 || i === 7) && (
                    <div className="absolute inset-0.5 text-white text-xs flex items-center justify-center">♜</div>
                  )}
                  {(i === 56 || i === 63) && (
                    <div className="absolute inset-0.5 text-white text-xs flex items-center justify-center">♖</div>
                  )}
                </div>
              );
            })}
          </div>
        </div>
      );
    }

    if (isCheckers) {
      // Simplified checkers board
      return (
        <div className="absolute inset-0 bg-slate-800 rounded overflow-hidden">
          <div className="absolute inset-0 grid grid-cols-8 grid-rows-8">
            {Array.from({ length: 64 }).map((_, i) => {
              const row = Math.floor(i / 8);
              const col = i % 8;
              const isPlayable = (row + col) % 2 === 1;

              return (
                <div
                  key={i}
                  className={`relative ${isPlayable ? 'bg-slate-600' : 'bg-slate-700'}`}
                >
                  {/* Sample checker pieces */}
                  {isPlayable && i < 24 && i % 4 !== 3 && (
                    <div className="absolute inset-1 rounded-full bg-red-600 border border-red-700" />
                  )}
                  {isPlayable && i > 39 && i % 4 !== 0 && (
                    <div className="absolute inset-1 rounded-full bg-slate-900 border border-slate-800" />
                  )}
                </div>
              );
            })}
          </div>
        </div>
      );
    }

    if (isBlurDetective) {
      return (
        <div className="absolute inset-0 bg-gradient-to-br from-slate-800 to-slate-700 rounded flex items-center justify-center">
          <div className="relative">
            <div className="w-8 h-8 bg-blue-500/30 rounded blur-sm" />
            <div className="absolute inset-0 flex items-center justify-center">
              <Brain className="h-4 w-4 text-blue-400" />
            </div>
          </div>
        </div>
      );
    }

    if (isHighlightHero) {
      return (
        <div className="absolute inset-0 bg-gradient-to-br from-slate-800 to-slate-700 rounded flex items-center justify-center">
          <div className="relative">
            <div className="w-8 h-8 bg-purple-500/30 rounded-full animate-pulse" />
            <div className="absolute inset-0 flex items-center justify-center">
              <Zap className="h-4 w-4 text-purple-400" />
            </div>
          </div>
        </div>
      );
    }

    if (isQuizArena) {
      return (
        <div className="absolute inset-0 bg-gradient-to-br from-yellow-800 to-yellow-700 rounded flex items-center justify-center">
          <div className="relative">
            <div className="w-8 h-8 bg-yellow-500/30 rounded-full" />
            <div className="absolute inset-0 flex items-center justify-center">
              <Brain className="h-4 w-4 text-yellow-400" />
            </div>
            {/* Question mark overlay */}
            <div className="absolute -top-1 -right-1 w-3 h-3 bg-yellow-500 rounded-full flex items-center justify-center">
              <span className="text-xs text-black font-bold">?</span>
            </div>
          </div>
        </div>
      );
    }

    // Default preview
    return (
      <div className="absolute inset-0 bg-gradient-to-br from-slate-800 to-slate-700 rounded flex items-center justify-center">
        {getGameIcon(gameType)}
      </div>
    );
  };

  return (
    <div className={`${sizeClasses[size]} relative bg-slate-800 rounded-lg overflow-hidden ${className}`}>
      {/* Game-specific background */}
      {renderGameSpecificPreview()}

      {/* Status overlay for live games */}
      {status === 'live' && (
        <div className="absolute top-1 right-1">
          <Badge className="bg-green-500 text-white text-[9px] h-3.5 px-1 animate-pulse">
            <Radio className="h-2 w-2 mr-0.5" />
            LIVE
          </Badge>
        </div>
      )}

      {/* Player count indicator for multiplayer games */}
      {players.length > 0 && (
        <div className="absolute bottom-1 left-1 bg-black/50 rounded px-1 py-0.5">
          <div className="flex items-center gap-0.5">
            <Users className="h-2 w-2 text-white" />
            <span className="text-xs text-white font-medium">{players.length}</span>
          </div>
        </div>
      )}

      {/* Status indicators for other states */}
      {status === 'waiting' && (
        <div className="absolute top-1 right-1">
          <Badge className="bg-blue-600 text-white text-[9px] h-3.5 px-1">
            Open
          </Badge>
        </div>
      )}

      {status === 'scheduled' && (
        <div className="absolute top-1 right-1">
          <Badge className="bg-yellow-600 text-white text-[9px] h-3.5 px-1">
            <Clock className="h-2 w-2 mr-0.5" />
            Soon
          </Badge>
        </div>
      )}
    </div>
  );
};

export default GameThumbnailGenerator;
