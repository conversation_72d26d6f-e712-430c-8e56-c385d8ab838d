import React from 'react';
import { GameInstanceStatus } from './GameStatusBadge';
import { 
  Crown, 
  Sword, 
  Target, 
  Zap, 
  Brain, 
  Gamepad2,
  Users,
  Clock,
  Trophy
} from 'lucide-react';

interface GameThumbnailProps {
  gameType: string;
  status: GameInstanceStatus;
  players?: Array<{ id: string; name: string; avatar?: string }>;
  currentState?: any;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

const GameThumbnailGenerator: React.FC<GameThumbnailProps> = ({
  gameType,
  status,
  players = [],
  currentState,
  size = 'md',
  className = ''
}) => {
  const sizeClasses = {
    sm: 'w-12 h-12',
    md: 'w-16 h-16',
    lg: 'w-24 h-24'
  };

  const getGameIcon = (type: string) => {
    switch (type) {
      case 'chess':
        return <Crown className="h-6 w-6 text-yellow-400" />;
      case 'checkers':
        return <Target className="h-6 w-6 text-red-400" />;
      case 'rock-paper-scissors':
        return <Sword className="h-6 w-6 text-blue-400" />;
      case 'highlight-hero':
        return <Zap className="h-6 w-6 text-purple-400" />;
      case 'blur-detective':
        return <Brain className="h-6 w-6 text-green-400" />;
      case 'word-jumble':
        return <Brain className="h-6 w-6 text-orange-400" />;
      default:
        return <Gamepad2 className="h-6 w-6 text-slate-400" />;
    }
  };

  const getStatusOverlay = (status: GameInstanceStatus) => {
    switch (status) {
      case 'live':
        return (
          <div className="absolute top-1 right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse border border-white" />
        );
      case 'scheduled':
        return (
          <div className="absolute top-1 right-1 w-3 h-3 bg-blue-500 rounded-full border border-white">
            <Clock className="h-2 w-2 text-white absolute top-0.5 left-0.5" />
          </div>
        );
      case 'completed':
        return (
          <div className="absolute top-1 right-1 w-3 h-3 bg-gray-500 rounded-full border border-white">
            <Trophy className="h-2 w-2 text-white absolute top-0.5 left-0.5" />
          </div>
        );
      case 'waiting':
        return (
          <div className="absolute top-1 right-1 w-3 h-3 bg-green-500 rounded-full border border-white">
            <Users className="h-2 w-2 text-white absolute top-0.5 left-0.5" />
          </div>
        );
      default:
        return null;
    }
  };

  const renderGameSpecificPreview = () => {
    switch (gameType) {
      case 'chess':
        return (
          <div className="absolute inset-0 bg-gradient-to-br from-amber-900/20 to-amber-700/20">
            <div className="grid grid-cols-4 grid-rows-4 h-full w-full opacity-30">
              {Array.from({ length: 16 }).map((_, i) => (
                <div
                  key={i}
                  className={`${
                    (Math.floor(i / 4) + (i % 4)) % 2 === 0
                      ? 'bg-amber-100/20'
                      : 'bg-amber-800/20'
                  }`}
                />
              ))}
            </div>
          </div>
        );
      
      case 'checkers':
        return (
          <div className="absolute inset-0 bg-gradient-to-br from-red-900/20 to-red-700/20">
            <div className="grid grid-cols-4 grid-rows-4 h-full w-full opacity-30">
              {Array.from({ length: 16 }).map((_, i) => (
                <div
                  key={i}
                  className={`${
                    (Math.floor(i / 4) + (i % 4)) % 2 === 0
                      ? 'bg-red-100/20'
                      : 'bg-red-800/20'
                  }`}
                />
              ))}
            </div>
          </div>
        );
      
      case 'rock-paper-scissors':
        return (
          <div className="absolute inset-0 bg-gradient-to-br from-blue-900/20 to-blue-700/20">
            <div className="flex items-center justify-center h-full">
              <div className="text-2xl opacity-30">✂️</div>
            </div>
          </div>
        );
      
      case 'highlight-hero':
        return (
          <div className="absolute inset-0 bg-gradient-to-br from-purple-900/20 to-purple-700/20">
            <div className="flex items-center justify-center h-full">
              <div className="w-8 h-8 bg-purple-500/30 rounded-full animate-pulse" />
            </div>
          </div>
        );
      
      case 'blur-detective':
        return (
          <div className="absolute inset-0 bg-gradient-to-br from-green-900/20 to-green-700/20">
            <div className="flex items-center justify-center h-full">
              <div className="w-6 h-6 bg-green-500/30 rounded blur-sm" />
            </div>
          </div>
        );
      
      default:
        return (
          <div className="absolute inset-0 bg-gradient-to-br from-slate-800/20 to-slate-600/20" />
        );
    }
  };

  return (
    <div className={`${sizeClasses[size]} relative bg-slate-800 rounded-lg overflow-hidden ${className}`}>
      {/* Game-specific background */}
      {renderGameSpecificPreview()}
      
      {/* Main game icon */}
      <div className="absolute inset-0 flex items-center justify-center">
        {getGameIcon(gameType)}
      </div>
      
      {/* Status overlay */}
      {getStatusOverlay(status)}
      
      {/* Player count indicator for multiplayer games */}
      {players.length > 0 && (
        <div className="absolute bottom-1 left-1 bg-black/50 rounded px-1 py-0.5">
          <div className="flex items-center gap-0.5">
            <Users className="h-2 w-2 text-white" />
            <span className="text-xs text-white font-medium">{players.length}</span>
          </div>
        </div>
      )}
      
      {/* Live indicator animation */}
      {status === 'live' && (
        <div className="absolute inset-0 border-2 border-red-500/50 rounded-lg animate-pulse" />
      )}
    </div>
  );
};

export default GameThumbnailGenerator;
