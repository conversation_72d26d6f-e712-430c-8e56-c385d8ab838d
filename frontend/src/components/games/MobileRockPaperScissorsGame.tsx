import React, { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Card } from '@/components/ui/card';
import useWalletStore from '@/stores/walletStore';
import { walletService } from '@/services/wallet';
import {
  Trophy,
  Clock,
  DollarSign,
  ArrowLeft,
  Hand,
  Scissors,
  Settings,
  RotateCcw,
  Share2,
  Sparkles
} from 'lucide-react';

const CHOICES = {
  ROCK: 'rock',
  PAPER: 'paper',
  SCISSORS: 'scissors'
};

const GAME_STATUS = {
  SETUP: 'setup',
  PLAYING: 'playing',
  ROUND_END: 'round_end',
  GAME_END: 'game_end'
};

interface MobileRockPaperScissorsGameProps {
  onBack?: () => void;
  gameId?: string;
  wagerAmount?: number;
}

const MobileRockPaperScissorsGame: React.FC<MobileRockPaperScissorsGameProps> = ({ 
  onBack,
  gameId,
  wagerAmount: initialWager = 50
}) => {
  // Wallet store
  const { balance } = useWalletStore();

  // Game configuration
  const [config, setConfig] = useState({
    rounds: 3,
    tieBreakerEnabled: true,
    wagerAmount: initialWager,
  });

  // Game state
  const [currentGameId, setCurrentGameId] = useState<string | null>(null);
  const [gameStatus, setGameStatus] = useState(GAME_STATUS.SETUP);
  const [currentRound, setCurrentRound] = useState(1);
  const [scores, setScores] = useState({ player1: 0, player2: 0 });
  const [choices, setChoices] = useState({ player1: null, player2: null });
  const [roundCountdown, setRoundCountdown] = useState(10);
  const [countdown, setCountdown] = useState(0);
  const [roundHistory, setRoundHistory] = useState([]);
  const [roundResult, setRoundResult] = useState(null);
  const [gameResult, setGameResult] = useState(null);
  const [showResults, setShowResults] = useState(false);
  const [animating, setAnimating] = useState(false);
  const [aiThinking, setAiThinking] = useState(false);

  // Timers
  const countdownIntervalRef = useRef(null);
  const roundIntervalRef = useRef(null);

  // Clean up intervals on unmount
  useEffect(() => {
    return () => {
      if (countdownIntervalRef.current) clearInterval(countdownIntervalRef.current);
      if (roundIntervalRef.current) clearInterval(roundIntervalRef.current);
    };
  }, []);

  // Start the game
  const startGame = async () => {
    // Check if user has sufficient balance
    if (balance < config.wagerAmount) {
      alert("Insufficient balance!");
      return;
    }

    try {
      // Generate game ID and place bet via API
      const gameId = `rps-${Date.now()}`;
      setCurrentGameId(gameId);
      
      await walletService.placeBet(
        config.wagerAmount,
        gameId,
        `Rock Paper Scissors - ${config.rounds} rounds`
      );
      
      // Reset game state
      setScores({ player1: 0, player2: 0 });
      setChoices({ player1: null, player2: null });
      setCurrentRound(1);
      setRoundHistory([]);
      setRoundResult(null);
      setGameResult(null);

      // Set game status to playing
      setGameStatus(GAME_STATUS.PLAYING);

      // Start the round countdown
      startRoundCountdown();
    } catch (error) {
      // console.error('Failed to start game:', error);
      alert('Failed to place bet. Please try again.');
    }
  };

  // Start round countdown
  const startRoundCountdown = () => {
    setRoundCountdown(10);
    setChoices({ player1: null, player2: null });
    setShowResults(false);
    setRoundResult(null);
    
    const interval = setInterval(() => {
      setRoundCountdown(prev => {
        if (prev <= 1) {
          clearInterval(interval);
          // If timer reaches 0, make a random choice for player who hasn't chosen
          if (!choices.player1) {
            makeChoice(getRandomChoice(), 'player1');
          }
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
    
    roundIntervalRef.current = interval;
  };

  // Get random choice
  const getRandomChoice = () => {
    const options = [CHOICES.ROCK, CHOICES.PAPER, CHOICES.SCISSORS];
    return options[Math.floor(Math.random() * options.length)];
  };

  // Make a choice for a player
  const makeChoice = (choice, player) => {
    if (gameStatus !== GAME_STATUS.PLAYING || animating) return;

    setChoices(prev => ({ ...prev, [player]: choice }));

    // If this is player 1 and they just chose, have AI make a choice
    if (player === 'player1' && !choices.player2) {
      setAiThinking(true);
      
      // AI makes choice after a delay
      setTimeout(() => {
        const aiChoice = getSmartAiChoice();
        setChoices(prev => ({ ...prev, player2: aiChoice }));
        setAiThinking(false);
        
        // Start reveal animation
        startRevealAnimation();
      }, 1000);
    }
  };

  // Smart AI choice based on player's history
  const getSmartAiChoice = () => {
    // If less than 3 rounds, just choose randomly
    if (roundHistory.length < 3) {
      return getRandomChoice();
    }

    // Analyze player's pattern
    const recentChoices = roundHistory.slice(-3).map(round => round.player1Choice);

    // Check for patterns and choose accordingly
    const lastChoice = recentChoices[2];
    const frequency = {};
    recentChoices.forEach(ch => {
      frequency[ch] = (frequency[ch] || 0) + 1;
    });

    // Find most frequent choice
    const mostFrequent = Object.keys(frequency).reduce((a, b) => 
      frequency[a] > frequency[b] ? a : b
    );

    // Counter the most frequent choice
    if (mostFrequent === CHOICES.ROCK) return CHOICES.PAPER;
    if (mostFrequent === CHOICES.PAPER) return CHOICES.SCISSORS;
    if (mostFrequent === CHOICES.SCISSORS) return CHOICES.ROCK;

    return getRandomChoice();
  };

  // Start the animation for revealing choices
  const startRevealAnimation = () => {
    if (roundIntervalRef.current) {
      clearInterval(roundIntervalRef.current);
    }
    
    setAnimating(true);
    setCountdown(3);
    
    const interval = setInterval(() => {
      setCountdown(prev => {
        if (prev <= 1) {
          clearInterval(interval);
          determineRoundWinner();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
    
    countdownIntervalRef.current = interval;
  };

  // Determine the winner of the current round
  const determineRoundWinner = () => {
    const player1Choice = choices.player1;
    const player2Choice = choices.player2;

    let result = "";

    // Determine winner
    if (player1Choice === player2Choice) {
      result = "tie";
    } else if (
      (player1Choice === CHOICES.ROCK && player2Choice === CHOICES.SCISSORS) ||
      (player1Choice === CHOICES.PAPER && player2Choice === CHOICES.ROCK) ||
      (player1Choice === CHOICES.SCISSORS && player2Choice === CHOICES.PAPER)
    ) {
      result = "player1";
      setScores(prev => ({ ...prev, player1: prev.player1 + 1 }));
    } else {
      result = "player2";
      setScores(prev => ({ ...prev, player2: prev.player2 + 1 }));
    }

    // Update round history
    setRoundHistory(prev => [
      ...prev,
      {
        round: currentRound,
        player1Choice,
        player2Choice,
        result
      }
    ]);

    setRoundResult(result);
    setShowResults(true);
    setAnimating(false);

    // Check if game is over
    const isGameOver = checkGameOver(result);

    if (!isGameOver) {
      // Proceed to next round after delay
      setTimeout(() => {
        proceedToNextRound();
      }, 3000);
    }
  };

  // Check if the game is over
  const checkGameOver = (result) => {
    const newScores = {
      player1: result === "player1" ? scores.player1 + 1 : scores.player1,
      player2: result === "player2" ? scores.player2 + 1 : scores.player2
    };

    const maxRounds = config.rounds;
    const player1Wins = newScores.player1;
    const player2Wins = newScores.player2;
    const remainingRounds = maxRounds - currentRound;

    // Check if a player has mathematically won
    if (player1Wins > player2Wins + remainingRounds) {
      endGame("player1");
      return true;
    }

    if (player2Wins > player1Wins + remainingRounds) {
      endGame("player2");
      return true;
    }

    // Check if we've played all rounds
    if (currentRound >= maxRounds) {
      if (player1Wins === player2Wins && config.tieBreakerEnabled) {
        return false; // Continue to tie-breaker
      } else {
        if (player1Wins > player2Wins) {
          endGame("player1");
        } else if (player2Wins > player1Wins) {
          endGame("player2");
        } else {
          endGame("tie");
        }
        return true;
      }
    }

    return false;
  };

  // Proceed to the next round
  const proceedToNextRound = () => {
    setCurrentRound(prev => prev + 1);
    startRoundCountdown();
  };

  // End the game
  const endGame = async (winner) => {
    setGameStatus(GAME_STATUS.GAME_END);
    setGameResult(winner);

    // Handle payouts
    if (winner === "player1" && currentGameId) {
      const winAmount = config.wagerAmount * 2;
      try {
        await walletService.processWin(
          winAmount,
          currentGameId,
          `Rock Paper Scissors - Won ${config.rounds} rounds`
        );
      } catch (error) {
        // console.error('Failed to process win:', error);
      }
    } else if (winner === "tie" && currentGameId) {
      // Return wager
      try {
        await walletService.processWin(
          config.wagerAmount,
          currentGameId,
          `Rock Paper Scissors - Game tied, wager returned`
        );
      } catch (error) {
        // console.error('Failed to process tie refund:', error);
      }
    }
    
    // Clear game ID
    setCurrentGameId(null);
  };

  // Reset the game
  const resetGame = () => {
    setGameStatus(GAME_STATUS.SETUP);
    setScores({ player1: 0, player2: 0 });
    setChoices({ player1: null, player2: null });
    setCurrentRound(1);
    setRoundHistory([]);
    setRoundResult(null);
    setGameResult(null);
    setShowResults(false);
    setCurrentGameId(null);
    
    if (countdownIntervalRef.current) clearInterval(countdownIntervalRef.current);
    if (roundIntervalRef.current) clearInterval(roundIntervalRef.current);
  };

  // Get choice icon based on choice
  const getChoiceIcon = (choice) => {
    switch (choice) {
      case CHOICES.ROCK:
        return "🪨";
      case CHOICES.PAPER:
        return "📜";
      case CHOICES.SCISSORS:
        return "✂️";
      default:
        return "❓";
    }
  };

  // Get choice name
  const getChoiceName = (choice) => {
    switch (choice) {
      case CHOICES.ROCK:
        return "Rock";
      case CHOICES.PAPER:
        return "Paper";
      case CHOICES.SCISSORS:
        return "Scissors";
      default:
        return "Unknown";
    }
  };

  // Render choice buttons
  const renderChoiceButtons = () => {
    const buttons = [
      { choice: CHOICES.ROCK, icon: "🪨", label: "Rock", color: "from-gray-600 to-gray-800" },
      { choice: CHOICES.PAPER, icon: "📜", label: "Paper", color: "from-blue-600 to-blue-800" },
      { choice: CHOICES.SCISSORS, icon: "✂️", label: "Scissors", color: "from-red-600 to-red-800" }
    ];

    return (
      <div className="grid grid-cols-3 gap-2">
        {buttons.map(button => (
          <Button
            key={button.choice}
            className={`h-20 text-sm flex flex-col items-center justify-center bg-gradient-to-b ${button.color} ${
              choices.player1 === button.choice ? 'ring-2 ring-yellow-400' : ''
            }`}
            disabled={!!choices.player1 || gameStatus !== GAME_STATUS.PLAYING || animating}
            onClick={() => makeChoice(button.choice, 'player1')}
          >
            <div className="text-2xl mb-1">{button.icon}</div>
            <span>{button.label}</span>
          </Button>
        ))}
      </div>
    );
  };

  // Render setup screen
  const renderSetupScreen = () => {
    return (
      <div className="p-4 h-screen pt-16 flex flex-col">
        <div className="flex justify-between items-center mb-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={onBack}
            className="h-8 px-2"
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back
          </Button>
        </div>

        <div className="flex-1 flex flex-col items-center justify-center">
          <div className="text-5xl mb-4">🎮</div>
          <h1 className="text-2xl font-bold text-white mb-2">Rock Paper Scissors</h1>
          <p className="text-sm text-slate-400 mb-6 text-center px-8">
            Choose your settings and start playing!
          </p>

          <Card className="w-full max-w-sm p-4 bg-slate-900 border-slate-800 mb-6">
            <div className="space-y-4">
              <div>
                <label className="text-sm text-slate-400 block mb-2">Number of Rounds</label>
                <div className="flex space-x-2">
                  {[1, 3, 5].map(num => (
                    <Button
                      key={num}
                      variant={config.rounds === num ? "default" : "outline"}
                      size="sm"
                      className="flex-1"
                      onClick={() => setConfig({ ...config, rounds: num })}
                    >
                      {num}
                    </Button>
                  ))}
                </div>
              </div>

              <div>
                <label className="text-sm text-slate-400 block mb-2">Wager Amount</label>
                <div className="grid grid-cols-2 gap-2">
                  {[10, 25, 50, 100].map(amount => (
                    <Button
                      key={amount}
                      variant={config.wagerAmount === amount ? "default" : "outline"}
                      size="sm"
                      disabled={amount > balance}
                      onClick={() => setConfig({ ...config, wagerAmount: amount })}
                    >
                      ${amount}
                    </Button>
                  ))}
                </div>
                <div className="text-center mt-2">
                  <span className="text-xs text-slate-400">Balance: </span>
                  <span className="text-xs text-green-500 font-bold">${balance.toFixed(2)}</span>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm text-slate-400">Tie-Breaker</span>
                <Button
                  variant={config.tieBreakerEnabled ? "default" : "outline"}
                  size="sm"
                  onClick={() => setConfig({ ...config, tieBreakerEnabled: !config.tieBreakerEnabled })}
                >
                  {config.tieBreakerEnabled ? "On" : "Off"}
                </Button>
              </div>
            </div>
          </Card>

          <Button
            className="w-full max-w-sm h-12 bg-gradient-to-r from-purple-500 to-pink-500"
            disabled={config.wagerAmount > balance}
            onClick={startGame}
          >
            <Sparkles className="h-5 w-5 mr-2" />
            {config.wagerAmount > balance ? "Insufficient Balance" : "Start Game"}
          </Button>
        </div>
      </div>
    );
  };

  // Render playing screen
  const renderPlayingScreen = () => {
    return (
      <div className="flex flex-col h-screen pt-16">
        {/* Header */}
        <div className="bg-slate-900 border-b border-slate-800 p-3">
          <div className="flex justify-between items-center">
            <div className="flex items-center">
              <div className="mr-3">
                <div className="text-xs text-slate-400">Round</div>
                <div className="text-lg font-bold text-white">{currentRound}/{config.rounds}</div>
              </div>
              <div className="mr-3">
                <div className="text-xs text-slate-400">Score</div>
                <div className="text-lg font-bold text-white">
                  {scores.player1} - {scores.player2}
                </div>
              </div>
            </div>
            <div className="text-center">
              <div className="text-xs text-slate-400">Time</div>
              <div className="text-2xl font-bold text-white">{roundCountdown}</div>
            </div>
          </div>
          <Progress value={(10 - roundCountdown) * 10} className="mt-2 h-2" />
        </div>

        {/* Game Area */}
        <div className="flex-1 p-4 flex flex-col justify-between">
          {/* Opponent Choice */}
          <div className="text-center">
            <p className="text-sm text-slate-400 mb-2">Opponent</p>
            <div className="bg-slate-800 rounded-lg w-24 h-24 mx-auto flex items-center justify-center">
              {showResults ? (
                <div className="text-4xl">{getChoiceIcon(choices.player2)}</div>
              ) : aiThinking ? (
                <div className="text-center">
                  <div className="text-lg text-slate-400 animate-pulse">🤔</div>
                  <div className="text-xs text-slate-500">Thinking...</div>
                </div>
              ) : (
                <div className="text-4xl text-slate-700">❓</div>
              )}
            </div>
          </div>

          {/* Result Display */}
          {showResults && roundResult && (
            <div className="text-center py-4">
              <div className={`text-2xl font-bold mb-2 ${
                roundResult === 'player1' ? 'text-green-500' :
                roundResult === 'player2' ? 'text-red-500' :
                'text-yellow-500'
              }`}>
                {roundResult === 'player1' ? 'You Win!' :
                 roundResult === 'player2' ? 'You Lose!' :
                 'Tie!'}
              </div>
              <p className="text-sm text-slate-400">
                {getChoiceName(choices.player1)} vs {getChoiceName(choices.player2)}
              </p>
            </div>
          )}

          {/* Countdown Display */}
          {animating && countdown > 0 && (
            <div className="text-center">
              <div className="text-6xl font-bold text-white animate-pulse">{countdown}</div>
              <p className="text-sm text-slate-400 mt-2">Revealing choices...</p>
            </div>
          )}

          {/* Player Choice */}
          <div>
            <p className="text-sm text-slate-400 mb-2 text-center">Your Choice</p>
            {showResults ? (
              <div className="bg-slate-800 rounded-lg w-24 h-24 mx-auto flex items-center justify-center">
                <div className="text-4xl">{getChoiceIcon(choices.player1)}</div>
              </div>
            ) : (
              renderChoiceButtons()
            )}
          </div>
        </div>
      </div>
    );
  };

  // Render game over screen
  const renderGameOverScreen = () => {
    return (
      <div className="p-4 h-screen pt-16 flex flex-col items-center justify-center">
        <div className={`text-6xl mb-4 ${
          gameResult === 'player1' ? '🎉' :
          gameResult === 'player2' ? '😢' :
          '🤝'
        }`}>
          {gameResult === 'player1' ? '🎉' :
           gameResult === 'player2' ? '😢' :
           '🤝'}
        </div>
        
        <h2 className={`text-2xl font-bold mb-2 ${
          gameResult === 'player1' ? 'text-green-500' :
          gameResult === 'player2' ? 'text-red-500' :
          'text-yellow-500'
        }`}>
          {gameResult === 'player1' ? 'You Win!' :
           gameResult === 'player2' ? 'You Lose!' :
           'Game Tied!'}
        </h2>

        <div className="text-center mb-6">
          <p className="text-lg text-white mb-1">
            Final Score: {scores.player1} - {scores.player2}
          </p>
          <p className="text-sm text-slate-400">
            {gameResult === 'player1' ? `Won $${config.wagerAmount * 2}` :
             gameResult === 'tie' ? `Wager returned: $${config.wagerAmount}` :
             `Lost $${config.wagerAmount}`}
          </p>
        </div>

        <div className="space-y-3 w-full max-w-sm">
          <Button
            className="w-full h-12 bg-gradient-to-r from-purple-500 to-pink-500"
            onClick={resetGame}
          >
            <RotateCcw className="h-5 w-5 mr-2" />
            Play Again
          </Button>
          
          <Button
            variant="outline"
            className="w-full h-12"
            onClick={() => {
              // Share functionality
              alert('Share feature coming soon!');
            }}
          >
            <Share2 className="h-5 w-5 mr-2" />
            Share Result
          </Button>
          
          <Button
            variant="ghost"
            className="w-full h-12"
            onClick={onBack}
          >
            <ArrowLeft className="h-5 w-5 mr-2" />
            Back to Games
          </Button>
        </div>
      </div>
    );
  };

  // Main render
  if (gameStatus === GAME_STATUS.SETUP) {
    return renderSetupScreen();
  } else if (gameStatus === GAME_STATUS.GAME_END) {
    return renderGameOverScreen();
  } else {
    return renderPlayingScreen();
  }
};

export default MobileRockPaperScissorsGame;