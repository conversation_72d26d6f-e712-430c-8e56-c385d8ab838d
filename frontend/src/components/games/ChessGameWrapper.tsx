import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import ChessGame from './ChessGame';
import ChessGameWithState from './ChessGameWithState';
import MobileChessGame from './MobileChessGame';
import ChessGameSetup from './ChessGameSetup';
import Breadcrumb, { generateGameBreadcrumbs } from '@/components/shared/Breadcrumb';
import { useGameStateStore } from '@/stores/gameStateStore';
import type { GameData } from './gameTypes';
import type { ChessGameConfig } from './ChessGameSetup';

interface ChessGameWrapperProps {
  game?: GameData;
  onBack?: () => void;
}

const ChessGameWrapper: React.FC<ChessGameWrapperProps> = ({ game, onBack }) => {
  const params = useParams();
  const navigate = useNavigate();
  const sessionId = params.sessionId;

  const [showSetup, setShowSetup] = useState(!game && !sessionId);
  const [gameId, setGameId] = useState<string | null>(game?.id || sessionId || null);
  const [isCreatingGame, setIsCreatingGame] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  const handleBack = () => {
    if (onBack) {
      onBack();
    } else {
      navigate('/games');
    }
  };

  useEffect(() => {
    // Check if mobile on mount and resize
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const { createGame } = useGameStateStore();

  const handleStartGame = async (config: ChessGameConfig) => {
    setIsCreatingGame(true);

    try {
      // Create a new game instance
      const newGame = await createGame('chess', config.betAmount);

      if (newGame) {
        // Navigate to the game session URL
        navigate(`/games/chess/session/${newGame.id}`);
      }
    } catch (error) {
      console.error('Failed to create game:', error);
    } finally {
      setIsCreatingGame(false);
    }
  };

  const handleGameComplete = () => {
    // console.log('Chess game completed');
    handleBack();
  };

  if (showSetup) {
    return (
      <>
        <ChessGameSetup
          isOpen={true}
          onClose={handleBack}
          onStartGame={handleStartGame}
        />
        {isCreatingGame && <div>Creating game...</div>}
      </>
    );
  }

  if (!gameId) {
    return <div>Error: No game ID available</div>;
  }

  // Generate breadcrumbs for session page
  const breadcrumbs = generateGameBreadcrumbs('chess', sessionId, 'Chess');

  // Use the state-aware chess game component
  return (
    <div className="min-h-screen bg-slate-950">
      {/* Breadcrumb navigation */}
      <div className="pt-16">
        <div className="container mx-auto px-4 py-4">
          <Breadcrumb items={breadcrumbs} />
        </div>
      </div>

      {/* Game component */}
      {isMobile ? (
        <MobileChessGame
          gameId={gameId}
          onBack={onBack}
          onGameEnd={handleGameComplete}
          wagerAmount={50}
          currentPlayer="white"
        />
      ) : (
        <ChessGameWithState
          onGameEnd={handleGameComplete}
          onBack={handleBack}
          wagerAmount={50}
        />
      )}
    </div>
  );
};

export default ChessGameWrapper;