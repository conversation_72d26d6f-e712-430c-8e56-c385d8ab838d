import React, { useState } from 'react';
import { type GameData } from './gameTypes';
import HighlightHeroGameSetup from './HighlightHeroGameSetup';
import HighlightHeroGame from './HighlightHeroGame';
import HighlightHeroGameSimple from './HighlightHeroGameSimple';

interface HighlightHeroGameWrapperProps {
  game?: GameData;
  isMobile?: boolean;
  onBack?: () => void;
}

export interface HighlightHeroConfig {
  category: string;
  rounds: number;
  wagerAmount: number;
  timeLimit: number;
  allowReplays: boolean;
  maxReplays: number;
  difficulty: string;
}

const HighlightHeroGameWrapper: React.FC<HighlightHeroGameWrapperProps> = ({ 
  game, 
  isMobile = false,
  onBack 
}) => {
  const [showSetup, setShowSetup] = useState(!game);
  const [gameConfig, setGameConfig] = useState<HighlightHeroConfig | null>(null);

  const handleSetupComplete = (config: HighlightHeroConfig) => {
    setGameConfig(config);
    setShowSetup(false);
  };

  const handleSetupCancel = () => {
    if (onBack) {
      onBack();
    }
  };

  if (showSetup) {
    return (
      <HighlightHeroGameSetup
        onComplete={handleSetupComplete}
        onCancel={handleSetupCancel}
      />
    );
  }

  // For mobile, show simplified version
  if (isMobile) {
    return (
      <HighlightHeroGameSimple 
        game={game}
        config={gameConfig}
        onBack={onBack}
      />
    );
  }

  // Desktop version shows full game
  return <HighlightHeroGame />;
};

export default HighlightHeroGameWrapper;