import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useGameSocket } from '../../hooks/useGameSocket';
import useAuth from '../../hooks/useAuth';
import BlurDetectiveGame from './BlurDetectiveGame';
import BlurDetectiveGameSetup, { type GameSettings } from './BlurDetectiveGameSetup';
import BlurDetectiveGameSimple from './BlurDetectiveGameSimple';
import { Button } from '../ui/button';
import { Card } from '../ui/card';
import { Badge } from '../ui/badge';
import { Loader2, Brain, Users, Trophy } from 'lucide-react';
import { type GameData } from './gameTypes';

interface Player {
  id: string;
  name: string;
  score: number;
  isReady: boolean;
  guessesLeft: number;
  hintsUsed: number;
  avatar?: string;
  isHost?: boolean;
  timeBonus?: number;
  streak?: number;
}

interface ChatMessage {
  id: string;
  playerId: string;
  playerName: string;
  message: string;
  timestamp: Date;
  isSystem?: boolean;
  isGuess?: boolean;
  isCorrect?: boolean;
}

interface GameState {
  status: 'waiting' | 'starting' | 'playing' | 'round-end' | 'game-over';
  currentRound: number;
  totalRounds: number;
  currentImage?: string;
  answer?: string;
  blurLevel: number;
  timeRemaining: number;
  winner?: string;
  roundWinner?: string;
  correctGuesses: number;
  leaderboard?: Player[];
  nextRoundIn?: number;
  revealProgress?: number;
}

interface BlurDetectiveGameWrapperProps {
  game?: GameData;
  onBack?: () => void;
}

export default function BlurDetectiveGameWrapper({ game, onBack }: BlurDetectiveGameWrapperProps = {}) {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [gameMode, setGameMode] = useState<'menu' | 'setup' | 'simple' | 'multiplayer'>('multiplayer');
  const [isCreatingGame, setIsCreatingGame] = useState(false);
  const [gameData, setGameData] = useState<GameData | null>({
    id: 'demo-game',
    name: 'Blur Detective Demo',
    type: 'blur-detective',
    players: { player1: { id: '1', name: 'Player1' }, player2: { id: '2', name: 'CelebExpert' } },
    currentRound: 2,
    totalRounds: 5,
    prize: 400,
    viewers: 24,
    status: 'live',
    betAmount: 100,
    settings: { category: 'actors', maxPlayers: 8, rounds: 5, timeLimit: 60 }
  });

  // Game state for multiplayer with mock data
  const [players, setPlayers] = useState<Player[]>([
    { id: '1', name: 'Player1', score: 150, isReady: true, guessesLeft: 3, hintsUsed: 1, isHost: true, streak: 2 },
    { id: '2', name: 'CelebExpert', score: 120, isReady: true, guessesLeft: 2, hintsUsed: 0, streak: 1 },
    { id: '3', name: 'QuizMaster', score: 90, isReady: true, guessesLeft: 3, hintsUsed: 2 },
    { id: '4', name: 'BlurHunter', score: 75, isReady: true, guessesLeft: 1, hintsUsed: 1 }
  ]);
  const [gameState, setGameState] = useState<GameState>({
    status: 'playing',
    currentRound: 2,
    totalRounds: 5,
    blurLevel: 25,
    timeRemaining: 45,
    correctGuesses: 0,
    currentImage: '/games/bd/images/celebrity1.jpg'
  });
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([
    { id: '1', playerId: '2', playerName: 'CelebExpert', message: 'Is this a movie star?', timestamp: new Date() },
    { id: '2', playerId: '3', playerName: 'QuizMaster', message: 'Leonardo DiCaprio', timestamp: new Date(), isGuess: true },
    { id: '3', playerId: 'system', playerName: 'System', message: 'Player joined the game', timestamp: new Date(), isSystem: true }
  ]);

  // Temporarily disable WebSocket to prevent errors
  const socket = null;
  const connected = true; // Mock as connected for UI demo
  const joinGame = () => {};
  const leaveGame = () => {};
  const sendMessage = () => {};
  const sendGameAction = () => {};
  const error = null;

  // Show menu by default
  useEffect(() => {
    setGameMode('menu');
  }, []);

  // Socket event handlers
  useEffect(() => {
    if (!socket) return;

    socket.on('gameUpdate', (data: any) => {
      if (data.players) setPlayers(data.players);
      if (data.gameState) setGameState(data.gameState);
      if (data.gameData) setGameData(data.gameData);
    });

    socket.on('chatMessage', (message: ChatMessage) => {
      setChatMessages(prev => [...prev, message]);
    });

    socket.on('playerJoined', (player: Player) => {
      setPlayers(prev => [...prev, player]);
      setChatMessages(prev => [...prev, {
        id: Date.now().toString(),
        playerId: 'system',
        playerName: 'System',
        message: `${player.name} joined the game`,
        timestamp: new Date(),
        isSystem: true
      }]);
    });

    socket.on('playerLeft', (playerId: string) => {
      const player = players.find(p => p.id === playerId);
      setPlayers(prev => prev.filter(p => p.id !== playerId));
      if (player) {
        setChatMessages(prev => [...prev, {
          id: Date.now().toString(),
          playerId: 'system',
          playerName: 'System',
          message: `${player.name} left the game`,
          timestamp: new Date(),
          isSystem: true
        }]);
      }
    });

    socket.on('gameStarted', () => {
      setGameState(prev => ({ ...prev, status: 'starting' }));
    });

    socket.on('roundStart', (data: any) => {
      setGameState(prev => ({
        ...prev,
        status: 'playing',
        currentRound: data.round,
        currentImage: data.image,
        answer: data.answer,
        blurLevel: data.blurLevel,
        timeRemaining: data.timeLimit
      }));
    });

    socket.on('guessResult', (data: any) => {
      const { playerId, playerName, guess, isCorrect, score } = data;
      setChatMessages(prev => [...prev, {
        id: Date.now().toString(),
        playerId,
        playerName,
        message: guess,
        timestamp: new Date(),
        isGuess: true,
        isCorrect
      }]);

      if (isCorrect) {
        setPlayers(prev => prev.map(p => 
          p.id === playerId ? { ...p, score: p.score + score } : p
        ));
      }
    });

    socket.on('roundEnd', (data: any) => {
      setGameState(prev => ({
        ...prev,
        status: 'round-end',
        roundWinner: data.winner,
        answer: data.answer,
        nextRoundIn: data.nextRoundIn
      }));
    });

    socket.on('gameOver', (data: any) => {
      setGameState(prev => ({
        ...prev,
        status: 'game-over',
        winner: data.winner,
        leaderboard: data.leaderboard
      }));
    });

    return () => {
      socket.off('gameUpdate');
      socket.off('chatMessage');
      socket.off('playerJoined');
      socket.off('playerLeft');
      socket.off('gameStarted');
      socket.off('roundStart');
      socket.off('guessResult');
      socket.off('roundEnd');
      socket.off('gameOver');
    };
  }, [socket, players]);

  const handleCreateGame = async (settings: GameSettings) => {
    if (!user) return;
    
    setIsCreatingGame(true);
    
    // Mock game creation for demo - no API call
    setTimeout(() => {
      const mockGameData = {
        id: 'demo-game-' + Date.now(),
        name: 'Blur Detective Game',
        type: 'blur-detective',
        players: { player1: { id: user.id, name: user.username } },
        currentRound: 1,
        totalRounds: settings.rounds,
        prize: settings.betAmount * 4, // Mock prize pool
        viewers: Math.floor(Math.random() * 50) + 10,
        status: 'live' as const,
        betAmount: settings.betAmount,
        settings
      };
      
      setGameData(mockGameData);
      setGameMode('multiplayer');
      setIsCreatingGame(false);
    }, 1000); // Simulate API delay
  };

  const handleJoinGame = async (gameId: string) => {
    if (!user) return;

    // Mock join game for demo - no API call
    const mockGameData = {
      id: gameId,
      name: 'Blur Detective Game',
      type: 'blur-detective',
      players: { 
        player1: { id: '1', name: 'HostPlayer' },
        player2: { id: user.id, name: user.username }
      },
      currentRound: 2,
      totalRounds: 5,
      prize: 400,
      viewers: 24,
      status: 'live' as const,
      betAmount: 100,
      settings: { category: 'actors', maxPlayers: 8, rounds: 5, timeLimit: 60 }
    };
    
    setGameData(mockGameData);
    setGameMode('multiplayer');
  };

  const handleSendMessage = (message: string) => {
    // Mock message sending for demo
    const newMessage: ChatMessage = {
      id: Date.now().toString(),
      playerId: user?.id || 'player1',
      playerName: user?.username || 'You',
      message,
      timestamp: new Date(),
      isGuess: message.toLowerCase().includes('dicaprio') || message.toLowerCase().includes('leonardo')
    };
    setChatMessages(prev => [...prev, newMessage]);
  };

  const handleStartGame = () => {
    // Mock game start for demo
    setGameState(prev => ({
      ...prev,
      status: 'starting'
    }));
    
    setTimeout(() => {
      setGameState(prev => ({
        ...prev,
        status: 'playing',
        currentRound: 1,
        blurLevel: 40,
        timeRemaining: 60
      }));
    }, 2000);
  };

  const handleMakeGuess = (guess: string) => {
    // Mock guess handling for demo
    const isCorrect = guess.toLowerCase().includes('leonardo') || guess.toLowerCase().includes('dicaprio');
    
    // Add guess as chat message
    const guessMessage: ChatMessage = {
      id: Date.now().toString(),
      playerId: user?.id || 'player1',
      playerName: user?.username || 'You',
      message: guess,
      timestamp: new Date(),
      isGuess: true,
      isCorrect
    };
    setChatMessages(prev => [...prev, guessMessage]);
    
    // Update player guesses
    setPlayers(prev => prev.map(p => 
      p.id === (user?.id || 'player1') 
        ? { ...p, guessesLeft: Math.max(0, p.guessesLeft - 1), score: isCorrect ? p.score + 100 : p.score }
        : p
    ));
    
    // If correct, simulate round end
    if (isCorrect) {
      setTimeout(() => {
        setGameState(prev => ({
          ...prev,
          status: 'round-end',
          answer: 'Leonardo DiCaprio',
          roundWinner: user?.id || 'player1',
          revealProgress: 100
        }));
        
        // Start next round after delay
        setTimeout(() => {
          if (gameState.currentRound < gameState.totalRounds) {
            setGameState(prev => ({
              ...prev,
              status: 'playing',
              currentRound: prev.currentRound + 1,
              blurLevel: 40,
              timeRemaining: 60,
              currentImage: prev.currentRound % 2 === 0 ? '/games/bd/images/celebrity1.jpg' : '/games/bd/images/celebrity2.jpg'
            }));
            setPlayers(prev => prev.map(p => ({ ...p, guessesLeft: 3 })));
          } else {
            setGameState(prev => ({
              ...prev,
              status: 'game-over',
              winner: user?.id || 'player1'
            }));
          }
        }, 3000);
      }, 1000);
    }
  };

  const handleUseHint = (hintId: string) => {
    // Mock hint usage for demo
    const hintMessages: { [key: string]: string } = {
      '1': 'Hint: This is a famous Hollywood actor',
      '2': 'Hint: Known for Titanic and Inception',
      '3': 'Hint: Won Oscar for The Revenant'
    };
    
    if (hintMessages[hintId]) {
      const hintMessage: ChatMessage = {
        id: Date.now().toString(),
        playerId: 'system',
        playerName: 'System',
        message: hintMessages[hintId],
        timestamp: new Date(),
        isSystem: true
      };
      setChatMessages(prev => [...prev, hintMessage]);
      
      // Deduct points for hint
      const hintCost = hintId === '1' ? 10 : hintId === '2' ? 20 : 30;
      setPlayers(prev => prev.map(p => 
        p.id === (user?.id || 'player1') 
          ? { ...p, score: Math.max(0, p.score - hintCost), hintsUsed: p.hintsUsed + 1 }
          : p
      ));
    }
  };

  const handleLeaveGame = () => {
    if (gameData?.id) {
      leaveGame(gameData.id);
    }
    if (onBack) {
      onBack();
    } else {
      navigate('/games');
    }
  };

  const handleSimpleGameEnd = (score: number) => {
    // Could save practice score or show results
    if (onBack) {
      onBack();
    } else {
      navigate('/games');
    }
  };

  // Render loading state
  if (isCreatingGame || (gameMode === 'multiplayer' && !connected)) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="p-8">
          <div className="flex flex-col items-center space-y-4">
            <Loader2 className="w-8 h-8 animate-spin" />
            <p className="text-muted-foreground">
              {isCreatingGame ? 'Creating game...' : 'Connecting...'}
            </p>
          </div>
        </Card>
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="p-8">
          <div className="flex flex-col items-center space-y-4 text-center">
            <div className="text-red-500 text-xl">⚠️</div>
            <p className="font-semibold">Connection Error</p>
            <p className="text-muted-foreground">{error}</p>
            <Button onClick={() => onBack ? onBack() : navigate('/games')}>Back to Games</Button>
          </div>
        </Card>
      </div>
    );
  }

  // Render game menu
  if (gameMode === 'menu') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 to-pink-50 p-8">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold mb-2 flex items-center justify-center gap-3">
              <Brain className="w-10 h-10 text-primary" />
              Blur Detective
            </h1>
            <p className="text-xl text-muted-foreground">
              Guess the celebrity from blurred images!
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-6">
            <Card className="p-6 hover:shadow-lg transition-shadow cursor-pointer" onClick={() => setGameMode('simple')}>
              <div className="space-y-4">
                <div className="text-4xl">🎯</div>
                <h3 className="text-2xl font-semibold">Practice Mode</h3>
                <p className="text-muted-foreground">
                  Play solo to improve your skills and learn the game mechanics
                </p>
                <Badge variant="secondary">No betting</Badge>
              </div>
            </Card>

            <Card className="p-6 hover:shadow-lg transition-shadow cursor-pointer" onClick={() => setGameMode('setup')}>
              <div className="space-y-4">
                <div className="text-4xl">🏆</div>
                <h3 className="text-2xl font-semibold">Multiplayer Mode</h3>
                <p className="text-muted-foreground">
                  Compete against other players for real prizes
                </p>
                <div className="flex gap-2">
                  <Badge variant="default">2-8 Players</Badge>
                  <Badge variant="outline">Real Money</Badge>
                </div>
              </div>
            </Card>
          </div>

          <div className="mt-8 text-center">
            <Button variant="outline" onClick={() => onBack ? onBack() : navigate('/games')}>
              Back to Games
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // Render game setup
  if (gameMode === 'setup') {
    return (
      <BlurDetectiveGameSetup
        onStartGame={handleCreateGame}
        onCancel={() => onBack ? onBack() : navigate('/games')}
        minBet={10}
        maxBet={10000}
      />
    );
  }

  // Render simple game using the same redesigned layout
  if (gameMode === 'simple') {
    // Mock data for practice mode
    const practiceGameData = {
      id: 'practice-mode',
      name: 'Practice Mode',
      type: 'blur-detective',
      players: { player1: { id: 'player1', name: 'You' } },
      currentRound: 1,
      totalRounds: 3,
      prize: 0,
      viewers: 0,
      status: 'live' as const,
      betAmount: 0,
      settings: { category: 'mixed', maxPlayers: 1, rounds: 3, timeLimit: 60 }
    };

    const practicePlayerData = [
      { id: 'player1', name: 'You (Practice)', score: 0, isReady: true, guessesLeft: 3, hintsUsed: 0, isHost: true }
    ];

    const practiceGameState = {
      status: 'playing' as const,
      currentRound: 1,
      totalRounds: 3,
      blurLevel: 40,
      timeRemaining: 60,
      correctGuesses: 0,
      currentImage: '/games/bd/images/celebrity1.jpg'
    };

    return (
      <BlurDetectiveGame
        gameId="practice-mode"
        playerId="player1"
        playerName="You"
        isHost={true}
        onLeaveGame={() => onBack ? onBack() : navigate('/games')}
        players={practicePlayerData}
        gameState={practiceGameState}
        chatMessages={[]}
        onSendMessage={() => {}}
        onStartGame={() => {}}
        onMakeGuess={() => {}}
        onUseHint={() => {}}
        betAmount={0}
        category="mixed"
        maxPlayers={1}
        rounds={3}
        timeLimit={60}
      />
    );
  }

  // Render multiplayer game
  if (gameMode === 'multiplayer' && gameData) {
    const currentPlayer = players.find(p => p.id === user?.id);
    const isHost = currentPlayer?.isHost || false;

    return (
      <BlurDetectiveGame
        gameId={gameData.id}
        playerId={user?.id || ''}
        playerName={user?.username || ''}
        isHost={isHost}
        onLeaveGame={handleLeaveGame}
        players={players}
        gameState={gameState}
        chatMessages={chatMessages}
        onSendMessage={handleSendMessage}
        onStartGame={handleStartGame}
        onMakeGuess={handleMakeGuess}
        onUseHint={handleUseHint}
        betAmount={gameData.betAmount}
        category={gameData.settings?.category || 'mixed'}
        maxPlayers={gameData.settings?.maxPlayers || 4}
        rounds={gameData.settings?.rounds || 5}
        timeLimit={gameData.settings?.timeLimit || 60}
      />
    );
  }

  return null;
}