import React, { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Input } from '@/components/ui/input';
import {
  Play,
  Pause,
  RotateCcw,
  Volume2,
  VolumeX,
  Trophy,
  Clock,
  MessageSquare,
  Users,
  Settings,
  Star,
  Target,
  Zap,
  Eye,
  EyeOff,
  Film,
  Music,
  Gamepad2,
  Car,
  Tv,
  X,
  Check,
  AlertCircle,
  DollarSign,
  ChevronDown
} from 'lucide-react';

// Game categories
const CATEGORIES = {
  soccer: {
    id: 'soccer',
    name: 'Soccer',
    icon: '⚽',
    color: 'from-green-500 to-green-700'
  },
  movies: {
    id: 'movies',
    name: 'Movies',
    icon: '🎬',
    color: 'from-red-500 to-red-700'
  },
  music: {
    id: 'music',
    name: 'Music',
    icon: '🎵',
    color: 'from-purple-500 to-purple-700'
  },
  sports: {
    id: 'sports',
    name: 'Sports',
    icon: '🏀',
    color: 'from-orange-500 to-orange-700'
  },
  gaming: {
    id: 'gaming',
    name: 'Gaming',
    icon: '🎮',
    color: 'from-blue-500 to-blue-700'
  }
};

// Game status
const GAME_STATUS = {
  SETUP: 'setup',
  WATCHING: 'watching',
  ANSWERING: 'answering',
  RESULTS: 'results',
  GAME_END: 'game_end'
};

// Sample questions for different categories
const SAMPLE_QUESTIONS = {
  soccer: [
    {
      id: 1,
      videoPath: '/games/hh/videos/highlight.mp4',
      question: "Which team scored the winning goal in this highlight?",
      options: ["Real Madrid", "Barcelona", "Manchester United", "Liverpool"],
      correctAnswer: 0,
      difficulty: "medium",
      points: 100
    },
    {
      id: 2,
      videoPath: '/games/hh/videos/highlight.mp4',
      question: "What type of goal was scored in this clip?",
      options: ["Header", "Free Kick", "Penalty", "Bicycle Kick"],
      correctAnswer: 1,
      difficulty: "easy",
      points: 50
    }
  ],
  movies: [
    {
      id: 1,
      videoPath: '/games/hh/videos/highlight.mp4',
      question: "Which movie is this scene from?",
      options: ["The Matrix", "Inception", "Interstellar", "Blade Runner"],
      correctAnswer: 0,
      difficulty: "medium",
      points: 100
    }
  ],
  music: [
    {
      id: 1,
      videoPath: '/games/hh/videos/highlight.mp4',
      question: "Which artist performed in this music video?",
      options: ["Taylor Swift", "Beyoncé", "Ariana Grande", "Billie Eilish"],
      correctAnswer: 1,
      difficulty: "hard",
      points: 150
    }
  ]
};

// HighlightHero Game Component
const HighlightHeroGame = () => {
  // Game configuration
  const [config, setConfig] = useState({
    category: CATEGORIES.soccer.id,
    difficulty: 'mixed', // easy, medium, hard, mixed
    rounds: 5,
    wagerAmount: 50,
    timeLimit: 30, // seconds to answer
    blurIntensity: 3, // 1-5 blur level
    allowReplays: true,
    maxReplays: 2
  });

  // Game state
  const [gameStatus, setGameStatus] = useState(GAME_STATUS.SETUP);
  const [currentRound, setCurrentRound] = useState(1);
  const [score, setScore] = useState(0);
  const [timeLeft, setTimeLeft] = useState(0);
  const [isVideoPlaying, setIsVideoPlaying] = useState(false);
  const [isVideoMuted, setIsVideoMuted] = useState(false);
  const [videoBlurred, setVideoBlurred] = useState(true);
  const [selectedAnswer, setSelectedAnswer] = useState(null);
  const [answeredCorrectly, setAnsweredCorrectly] = useState(null);
  const [showSettings, setShowSettings] = useState(false);
  const [currentQuestion, setCurrentQuestion] = useState(null);
  const [replaysUsed, setReplaysUsed] = useState(0);
  const [roundHistory, setRoundHistory] = useState([]);
  const [totalGameTime, setTotalGameTime] = useState(0);
  const [gameResult, setGameResult] = useState(null);
  const [showVideoControls, setShowVideoControls] = useState(true);

  // Refs
  const videoRef = useRef(null);
  const timerRef = useRef(null);
  const gameTimerRef = useRef(null);

  // Chat state
  const [messages, setMessages] = useState([
    { text: "Welcome to HighlightHero! Test your knowledge with video highlights.", type: "system" }
  ]);
  const [messageInput, setMessageInput] = useState('');
  const messageEndRef = useRef(null);

  // Effects

  // Scroll chat to bottom
  useEffect(() => {
    if (messageEndRef.current) {
      messageEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  // Clean up timers
  useEffect(() => {
    return () => {
      if (timerRef.current) clearInterval(timerRef.current);
      if (gameTimerRef.current) clearInterval(gameTimerRef.current);
    };
  }, []);

  // Load questions for current category
  const getQuestionsForCategory = (category) => {
    return SAMPLE_QUESTIONS[category] || SAMPLE_QUESTIONS.soccer;
  };

  // Add message to chat
  const addMessage = (text, type = "player") => {
    setMessages(prev => [...prev, { text, type }]);
  };

  // Send chat message
  const sendMessage = () => {
    if (messageInput.trim()) {
      addMessage(`Player: ${messageInput}`);
      setMessageInput('');
    }
  };

  // Start the game
  const startGame = () => {
    const questions = getQuestionsForCategory(config.category);
    if (questions.length === 0) {
      addMessage("No questions available for this category!", "system");
      return;
    }

    // Reset game state
    setScore(0);
    setCurrentRound(1);
    setRoundHistory([]);
    setTotalGameTime(0);
    setGameResult(null);

    // Start game timer
    gameTimerRef.current = setInterval(() => {
      setTotalGameTime(prev => prev + 1);
    }, 1000);

    // Load first question
    loadQuestion(1);

    setGameStatus(GAME_STATUS.WATCHING);
    addMessage(`Game started! Category: ${CATEGORIES[config.category].name}`, "system");
  };

  // Load a question for the current round
  const loadQuestion = (roundNumber) => {
    const questions = getQuestionsForCategory(config.category);
    const questionIndex = (roundNumber - 1) % questions.length;
    const question = questions[questionIndex];

    setCurrentQuestion(question);
    setSelectedAnswer(null);
    setAnsweredCorrectly(null);
    setReplaysUsed(0);
    setVideoBlurred(true);
    setIsVideoPlaying(false);

    // Load video
    if (videoRef.current) {
      videoRef.current.currentTime = 0;
      videoRef.current.load();
    }

    addMessage(`Round ${roundNumber}: New highlight loaded!`, "system");
  };

  // Play/pause video
  const toggleVideo = () => {
    if (!videoRef.current) return;

    if (isVideoPlaying) {
      videoRef.current.pause();
      setIsVideoPlaying(false);
    } else {
      videoRef.current.play();
      setIsVideoPlaying(true);
    }
  };

  // Replay video
  const replayVideo = () => {
    if (!config.allowReplays || replaysUsed >= config.maxReplays) {
      addMessage("No more replays available!", "system");
      return;
    }

    if (videoRef.current) {
      videoRef.current.currentTime = 0;
      videoRef.current.play();
      setIsVideoPlaying(true);
      setReplaysUsed(prev => prev + 1);
      addMessage(`Replay ${replaysUsed + 1}/${config.maxReplays} used.`, "system");
    }
  };

  // Toggle video mute
  const toggleMute = () => {
    if (videoRef.current) {
      videoRef.current.muted = !videoRef.current.muted;
      setIsVideoMuted(!isVideoMuted);
    }
  };

  // Toggle video blur
  const toggleBlur = () => {
    setVideoBlurred(!videoBlurred);
    addMessage(videoBlurred ? "Video unblurred! Careful, this might give away the answer." : "Video blurred again.", "system");
  };

  // Start answering phase
  const startAnswering = () => {
    if (!currentQuestion) return;

    setGameStatus(GAME_STATUS.ANSWERING);
    setTimeLeft(config.timeLimit);

    // Start answer timer
    timerRef.current = setInterval(() => {
      setTimeLeft(prev => {
        if (prev <= 1) {
          clearInterval(timerRef.current);
          handleTimeout();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    // Pause video
    if (videoRef.current) {
      videoRef.current.pause();
      setIsVideoPlaying(false);
    }

    addMessage("Time to answer! Choose your option.", "system");
  };

  // Handle answer selection
  const selectAnswer = (answerIndex) => {
    if (gameStatus !== GAME_STATUS.ANSWERING || selectedAnswer !== null) return;

    setSelectedAnswer(answerIndex);

    // Clear timer
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }

    // Check if answer is correct
    const isCorrect = answerIndex === currentQuestion.correctAnswer;
    setAnsweredCorrectly(isCorrect);

    // Update score
    if (isCorrect) {
      const points = currentQuestion.points;
      setScore(prev => prev + points);
      addMessage(`Correct! +${points} points`, "system");
    } else {
      addMessage(`Wrong answer! The correct answer was: ${currentQuestion.options[currentQuestion.correctAnswer]}`, "system");
    }

    // Add to history
    setRoundHistory(prev => [...prev, {
      round: currentRound,
      question: currentQuestion.question,
      selectedAnswer: answerIndex,
      correctAnswer: currentQuestion.correctAnswer,
      isCorrect,
      points: isCorrect ? currentQuestion.points : 0,
      timeUsed: config.timeLimit - timeLeft,
      replaysUsed
    }]);

    // Show results
    setGameStatus(GAME_STATUS.RESULTS);

    // Auto-proceed to next round or end game
    setTimeout(() => {
      if (currentRound >= config.rounds) {
        endGame();
      } else {
        proceedToNextRound();
      }
    }, 3000);
  };

  // Handle timeout
  const handleTimeout = () => {
    setSelectedAnswer(-1); // -1 indicates timeout
    setAnsweredCorrectly(false);

    addMessage("Time's up! No answer selected.", "system");

    // Add to history
    setRoundHistory(prev => [...prev, {
      round: currentRound,
      question: currentQuestion.question,
      selectedAnswer: -1,
      correctAnswer: currentQuestion.correctAnswer,
      isCorrect: false,
      points: 0,
      timeUsed: config.timeLimit,
      replaysUsed
    }]);

    setGameStatus(GAME_STATUS.RESULTS);

    setTimeout(() => {
      if (currentRound >= config.rounds) {
        endGame();
      } else {
        proceedToNextRound();
      }
    }, 3000);
  };

  // Proceed to next round
  const proceedToNextRound = () => {
    const nextRound = currentRound + 1;
    setCurrentRound(nextRound);
    loadQuestion(nextRound);
    setGameStatus(GAME_STATUS.WATCHING);
  };

  // End the game
  const endGame = () => {
    setGameStatus(GAME_STATUS.GAME_END);

    // Clear timers
    if (timerRef.current) clearInterval(timerRef.current);
    if (gameTimerRef.current) clearInterval(gameTimerRef.current);

    // Calculate results
    const correctAnswers = roundHistory.filter(round => round.isCorrect).length;
    const accuracy = (correctAnswers / config.rounds) * 100;

    let result = {
      score,
      correctAnswers,
      totalRounds: config.rounds,
      accuracy,
      payout: 0
    };

    // Calculate payout based on performance
    if (accuracy >= 80) {
      result.payout = config.wagerAmount * 3; // 3x for excellent performance
      addMessage(`Excellent performance! 3x payout: $${result.payout.toFixed(2)}`, "system");
    } else if (accuracy >= 60) {
      result.payout = config.wagerAmount * 2; // 2x for good performance
      addMessage(`Good performance! 2x payout: $${result.payout.toFixed(2)}`, "system");
    } else if (accuracy >= 40) {
      result.payout = config.wagerAmount; // Return wager for okay performance
      addMessage(`Okay performance. Wager returned: $${result.payout.toFixed(2)}`, "system");
    } else {
      result.payout = 0; // No payout for poor performance
      addMessage("Better luck next time! No payout.", "system");
    }

    setGameResult(result);
  };

  // Reset the game
  const resetGame = () => {
    // Clear timers
    if (timerRef.current) clearInterval(timerRef.current);
    if (gameTimerRef.current) clearInterval(gameTimerRef.current);

    // Reset state
    setGameStatus(GAME_STATUS.SETUP);
    setScore(0);
    setCurrentRound(1);
    setRoundHistory([]);
    setTotalGameTime(0);
    setGameResult(null);
    setCurrentQuestion(null);

    addMessage("Game reset! Configure your settings and start a new game.", "system");
  };

  // Format time display
  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds < 10 ? '0' : ''}${remainingSeconds}`;
  };

  // Get difficulty color
  const getDifficultyColor = (difficulty) => {
    switch (difficulty) {
      case 'easy': return 'text-green-500';
      case 'medium': return 'text-yellow-500';
      case 'hard': return 'text-red-500';
      default: return 'text-slate-400';
    }
  };

  // Render video player
  const renderVideoPlayer = () => {
    return (
      <div className="relative bg-black rounded-sm overflow-hidden aspect-video">
        <video
          ref={videoRef}
          className={`w-full h-full object-cover ${videoBlurred ? `blur-sm` : ''}`}
          style={{ filter: videoBlurred ? `blur(${config.blurIntensity * 2}px)` : 'none' }}
          muted={isVideoMuted}
          playsInline
          onEnded={() => setIsVideoPlaying(false)}
          onError={() => addMessage("Error loading video. Using placeholder.", "system")}
        >
          <source src={currentQuestion?.videoPath || '/games/hh/videos/highlight.mp4'} type="video/mp4" />
          Your browser does not support the video tag.
        </video>

        {/* Video overlay controls */}
        {showVideoControls && (
          <div className="absolute inset-0 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity bg-black/20">
            <div className="flex space-x-2">
              <Button
                variant="ghost"
                size="sm"
                className="bg-black/50 text-white hover:bg-black/70"
                onClick={toggleVideo}
              >
                {isVideoPlaying ? <Pause className="h-5 w-5" /> : <Play className="h-5 w-5" />}
              </Button>
            </div>
          </div>
        )}

        {/* Video info overlay */}
        <div className="absolute top-2 left-2 flex space-x-2">
          <Badge className={`bg-gradient-to-r ${CATEGORIES[config.category].color}`}>
            {CATEGORIES[config.category].icon} {CATEGORIES[config.category].name}
          </Badge>

          {currentQuestion && (
            <Badge className={`${getDifficultyColor(currentQuestion.difficulty)} bg-black/70`}>
              {currentQuestion.difficulty} • {currentQuestion.points}pts
            </Badge>
          )}
        </div>

        {/* Replay count */}
        {config.allowReplays && (
          <div className="absolute top-2 right-2">
            <Badge className="bg-black/70">
              Replays: {replaysUsed}/{config.maxReplays}
            </Badge>
          </div>
        )}

        {/* Round info */}
        <div className="absolute bottom-2 left-2">
          <Badge className="bg-black/70">
            Round {currentRound} of {config.rounds}
          </Badge>
        </div>
      </div>
    );
  };

  // Render video controls
  const renderVideoControls = () => {
    return (
      <div className="flex justify-between items-center mt-2">
        <div className="flex space-x-2">
          <Button
            variant="outline"
            size="sm"
            className="h-8 text-xs"
            onClick={toggleVideo}
            disabled={gameStatus === GAME_STATUS.ANSWERING}
          >
            {isVideoPlaying ? <Pause className="h-3 w-3 mr-1" /> : <Play className="h-3 w-3 mr-1" />}
            {isVideoPlaying ? 'Pause' : 'Play'}
          </Button>

          <Button
            variant="outline"
            size="sm"
            className="h-8 text-xs"
            onClick={replayVideo}
            disabled={!config.allowReplays || replaysUsed >= config.maxReplays || gameStatus === GAME_STATUS.ANSWERING}
          >
            <RotateCcw className="h-3 w-3 mr-1" />
            Replay ({replaysUsed}/{config.maxReplays})
          </Button>

          <Button
            variant="outline"
            size="sm"
            className="h-8 text-xs"
            onClick={toggleMute}
          >
            {isVideoMuted ? <VolumeX className="h-3 w-3 mr-1" /> : <Volume2 className="h-3 w-3 mr-1" />}
            {isVideoMuted ? 'Unmute' : 'Mute'}
          </Button>

          <Button
            variant="outline"
            size="sm"
            className="h-8 text-xs"
            onClick={toggleBlur}
          >
            {videoBlurred ? <EyeOff className="h-3 w-3 mr-1" /> : <Eye className="h-3 w-3 mr-1" />}
            {videoBlurred ? 'Unblur' : 'Blur'}
          </Button>
        </div>

        <Button
          className="h-8 text-xs bg-gradient-to-r from-purple-500 to-pink-500"
          onClick={startAnswering}
          disabled={gameStatus !== GAME_STATUS.WATCHING}
        >
          <Target className="h-3 w-3 mr-1" />
          Ready to Answer
        </Button>
      </div>
    );
  };

  // Render answer options
  const renderAnswerOptions = () => {
    if (!currentQuestion || gameStatus !== GAME_STATUS.ANSWERING) return null;

    return (
      <div className="mt-4">
        <div className="flex justify-between items-center mb-3">
          <h3 className="text-sm font-medium text-white">{currentQuestion.question}</h3>
          <div className="flex items-center space-x-2">
            <Clock className="h-4 w-4 text-yellow-500" />
            <span className="text-sm font-bold text-white">{timeLeft}s</span>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-2">
          {currentQuestion.options.map((option, index) => (
            <Button
              key={index}
              variant="outline"
              className={`h-12 text-xs text-left justify-start p-3 ${
                selectedAnswer === index ? 'bg-purple-500 border-purple-400' : 'bg-slate-800 border-slate-700'
              }`}
              onClick={() => selectAnswer(index)}
              disabled={selectedAnswer !== null}
            >
              <span className="font-bold mr-2">{String.fromCharCode(65 + index)}.</span>
              {option}
            </Button>
          ))}
        </div>

        <div className="mt-2">
          <Progress value={(timeLeft / config.timeLimit) * 100} className="h-2" />
        </div>
      </div>
    );
  };

  // Render results
  const renderResults = () => {
    if (gameStatus !== GAME_STATUS.RESULTS || !currentQuestion) return null;

    return (
      <div className="mt-4 bg-slate-800 rounded-sm p-3">
        <h3 className="text-sm font-medium text-white mb-3 text-center">Round Result</h3>

        <div className="text-center mb-3">
          <div className={`text-3xl mb-2 ${answeredCorrectly ? 'text-green-500' : 'text-red-500'}`}>
            {answeredCorrectly ? '✓' : '✗'}
          </div>
          <div className={`text-lg font-bold ${answeredCorrectly ? 'text-green-500' : 'text-red-500'}`}>
            {answeredCorrectly ? 'Correct!' : 'Incorrect!'}
          </div>
        </div>

        <div className="space-y-2">
          <div className="flex justify-between">
            <span className="text-xs text-slate-400">Your Answer:</span>
            <span className="text-xs text-white">
              {selectedAnswer === -1 ? 'No answer (timeout)' :
               selectedAnswer !== null ? currentQuestion.options[selectedAnswer] : 'None'}
            </span>
          </div>

          <div className="flex justify-between">
            <span className="text-xs text-slate-400">Correct Answer:</span>
            <span className="text-xs text-green-400">
              {currentQuestion.options[currentQuestion.correctAnswer]}
            </span>
          </div>

          <div className="flex justify-between">
            <span className="text-xs text-slate-400">Points Earned:</span>
            <span className="text-xs text-white">
              {answeredCorrectly ? `+${currentQuestion.points}` : '0'}
            </span>
          </div>
        </div>

        <div className="mt-3 text-center text-xs text-slate-400">
          {currentRound < config.rounds ?
            `Proceeding to round ${currentRound + 1}...` :
            'Game complete! Calculating final results...'}
        </div>
      </div>
    );
  };

  // Render settings panel
  const renderSettings = () => {
    return (
      <div className="bg-slate-900 border border-slate-800 rounded-sm p-3 absolute top-0 left-0 right-0 z-10">
        <div className="flex justify-between items-center mb-3">
          <h3 className="text-sm font-medium text-white">Game Settings</h3>
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0"
            onClick={() => setShowSettings(false)}
          >
            <X className="h-3 w-3" />
          </Button>
        </div>

        <div className="space-y-3">
          <div>
            <label className="text-xs text-slate-400 block mb-1">Category</label>
            <div className="grid grid-cols-3 gap-1">
              {Object.values(CATEGORIES).map(category => (
                <Button
                  key={category.id}
                  variant={config.category === category.id ? "default" : "outline"}
                  size="sm"
                  className="h-8 text-xs flex items-center justify-center"
                  onClick={() => setConfig({ ...config, category: category.id })}
                >
                  <span className="mr-1">{category.icon}</span>
                  {category.name}
                </Button>
              ))}
            </div>
          </div>

          <div>
            <label className="text-xs text-slate-400 block mb-1">Number of Rounds</label>
            <div className="flex space-x-2">
              {[3, 5, 7, 10].map(num => (
                <Button
                  key={num}
                  variant={config.rounds === num ? "default" : "outline"}
                  size="sm"
                  className="flex-1 h-7 text-xs"
                  onClick={() => setConfig({ ...config, rounds: num })}
                >
                  {num}
                </Button>
              ))}
            </div>
          </div>

          <div>
            <label className="text-xs text-slate-400 block mb-1">Wager Amount</label>
            <div className="flex space-x-2">
              {[25, 50, 100, 200].map(amount => (
                <Button
                  key={amount}
                  variant={config.wagerAmount === amount ? "default" : "outline"}
                  size="sm"
                  className="flex-1 h-7 text-xs"
                  onClick={() => setConfig({ ...config, wagerAmount: amount })}
                >
                  ${amount}
                </Button>
              ))}
            </div>
          </div>

          <div>
            <label className="text-xs text-slate-400 block mb-1">Answer Time Limit</label>
            <div className="flex space-x-2">
              {[15, 30, 45, 60].map(time => (
                <Button
                  key={time}
                  variant={config.timeLimit === time ? "default" : "outline"}
                  size="sm"
                  className="flex-1 h-7 text-xs"
                  onClick={() => setConfig({ ...config, timeLimit: time })}
                >
                  {time}s
                </Button>
              ))}
            </div>
          </div>

          <div>
            <label className="text-xs text-slate-400 block mb-1">Blur Intensity</label>
            <div className="flex space-x-2">
              {[1, 2, 3, 4, 5].map(level => (
                <Button
                  key={level}
                  variant={config.blurIntensity === level ? "default" : "outline"}
                  size="sm"
                  className="flex-1 h-7 text-xs"
                  onClick={() => setConfig({ ...config, blurIntensity: level })}
                >
                  {level}
                </Button>
              ))}
            </div>
          </div>

          <div className="flex items-center justify-between">
            <label className="text-xs text-slate-400">Allow Replays</label>
            <Button
              variant={config.allowReplays ? "default" : "outline"}
              size="sm"
              className="h-7 w-14 text-xs"
              onClick={() => setConfig({ ...config, allowReplays: !config.allowReplays })}
            >
              {config.allowReplays ? "On" : "Off"}
            </Button>
          </div>

          {config.allowReplays && (
            <div>
              <label className="text-xs text-slate-400 block mb-1">Max Replays</label>
              <div className="flex space-x-2">
                {[1, 2, 3, 5].map(num => (
                  <Button
                    key={num}
                    variant={config.maxReplays === num ? "default" : "outline"}
                    size="sm"
                    className="flex-1 h-7 text-xs"
                    onClick={() => setConfig({ ...config, maxReplays: num })}
                  >
                    {num}
                  </Button>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    );
  };

  // Render setup screen
  const renderSetupScreen = () => {
    return (
      <div className="flex flex-col items-center justify-center h-full bg-slate-900 border border-slate-800 rounded-sm p-4">
        <div className="text-6xl mb-4">🎬</div>
        <h1 className="text-2xl font-bold text-white mb-1">HighlightHero</h1>
        <p className="text-sm text-slate-400 mb-6 text-center">
          Watch highlight reels and test your knowledge!<br />
          Configure your game settings and get ready to play.
        </p>

        <div className="w-full max-w-md mb-6">
          <div className="bg-slate-800 p-4 rounded-sm mb-4">
            <h3 className="text-sm font-medium text-white mb-3">Quick Setup</h3>

            <div className="space-y-3">
              <div>
                <label className="text-xs text-slate-400 block mb-1">Category</label>
                <div className="grid grid-cols-2 gap-2">
                  {Object.values(CATEGORIES).slice(0, 4).map(category => (
                    <Button
                      key={category.id}
                      variant={config.category === category.id ? "default" : "outline"}
                      size="sm"
                      className="h-8 text-xs"
                      onClick={() => setConfig({ ...config, category: category.id })}
                    >
                      <span className="mr-1">{category.icon}</span>
                      {category.name}
                    </Button>
                  ))}
                </div>
              </div>

              <div className="grid grid-cols-2 gap-3">
                <div>
                  <label className="text-xs text-slate-400 block mb-1">Rounds</label>
                  <div className="flex space-x-1">
                    {[3, 5, 7].map(num => (
                      <Button
                        key={num}
                        variant={config.rounds === num ? "default" : "outline"}
                        size="sm"
                        className="flex-1 h-7 text-xs"
                        onClick={() => setConfig({ ...config, rounds: num })}
                      >
                        {num}
                      </Button>
                    ))}
                  </div>
                </div>

                <div>
                  <label className="text-xs text-slate-400 block mb-1">Wager</label>
                  <div className="flex space-x-1">
                    {[25, 50, 100].map(amount => (
                      <Button
                        key={amount}
                        variant={config.wagerAmount === amount ? "default" : "outline"}
                        size="sm"
                        className="flex-1 h-7 text-xs"
                        onClick={() => setConfig({ ...config, wagerAmount: amount })}
                      >
                        ${amount}
                      </Button>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="flex space-x-3">
          <Button
            variant="outline"
            className="w-32 h-10 text-sm rounded-sm"
            onClick={() => setShowSettings(true)}
          >
            <Settings className="h-4 w-4 mr-2" />
            Advanced
          </Button>

          <Button
            className="w-32 h-10 text-sm rounded-sm bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600"
            onClick={startGame}
          >
            <Zap className="h-4 w-4 mr-2" />
            Start Game
          </Button>
        </div>
      </div>
    );
  };

  // Render game info
  const renderGameInfo = () => {
    return (
      <div className="bg-slate-900 border border-slate-800 rounded-sm p-2">
        <div className="flex justify-between items-center mb-2">
          <h3 className="text-sm font-medium text-white">
            {gameStatus === GAME_STATUS.SETUP ? "Game Setup" :
             gameStatus === GAME_STATUS.GAME_END ? "Game Complete" :
             `Round ${currentRound} of ${config.rounds}`}
          </h3>
          <Badge className={`
            ${gameStatus === GAME_STATUS.SETUP ? 'bg-yellow-500' :
              gameStatus === GAME_STATUS.GAME_END ? 'bg-blue-500' :
              'bg-green-500'}
          `}>
            {gameStatus === GAME_STATUS.SETUP ? 'SETUP' :
             gameStatus === GAME_STATUS.GAME_END ? 'FINISHED' :
             'LIVE'}
          </Badge>
        </div>

        <div className="grid grid-cols-2 gap-2">
          <div className="bg-slate-800 p-2 rounded-sm">
            <div className="text-xs text-slate-400">Score</div>
            <div className="text-sm font-bold text-white">{score} pts</div>
          </div>

          <div className="bg-slate-800 p-2 rounded-sm">
            <div className="text-xs text-slate-400">Wager</div>
            <div className="text-sm font-bold text-white">${config.wagerAmount}</div>
          </div>
        </div>
      </div>
    );
  };

  // Render round history
  const renderRoundHistory = () => {
    if (roundHistory.length === 0) {
      return <div className="text-xs text-slate-400 p-2">No rounds played yet</div>;
    }

    return (
      <div className="text-xs p-2 overflow-auto max-h-full">
        {roundHistory.map((round, index) => (
          <div key={index} className="mb-2 p-2 bg-slate-800 rounded-sm">
            <div className="flex justify-between items-center mb-1">
              <span className="font-medium text-white">Round {round.round}</span>
              <span className={round.isCorrect ? 'text-green-500' : 'text-red-500'}>
                {round.isCorrect ? '✓' : '✗'} {round.points} pts
              </span>
            </div>

            <div className="text-slate-300 mb-1 text-[10px]">
              {round.question}
            </div>

            <div className="flex justify-between text-[10px]">
              <span className="text-slate-400">
                Time: {round.timeUsed}s
              </span>
              <span className="text-slate-400">
                Replays: {round.replaysUsed}
              </span>
            </div>
          </div>
        ))}
      </div>
    );
  };

  // Main game layout
  return (
    <div className="min-h-screen bg-slate-950 text-white pt-16">
      <div className="grid grid-cols-12 gap-1 p-1 h-[calc(100vh-64px)]">
        {/* Game Information - Left column */}
        <div className="col-span-3 flex flex-col gap-1 h-full overflow-hidden">
        {renderGameInfo()}

        {/* Category Info */}
        {gameStatus !== GAME_STATUS.SETUP && (
          <div className="bg-slate-900 border border-slate-800 rounded-sm p-2">
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-sm font-medium text-white">Category</h3>
              <Badge className={`bg-gradient-to-r ${CATEGORIES[config.category].color}`}>
                {CATEGORIES[config.category].icon} {CATEGORIES[config.category].name}
              </Badge>
            </div>

            <div className="grid grid-cols-2 gap-2">
              <div className="bg-slate-800 p-2 rounded-sm text-center">
                <div className="text-xs text-slate-400">Correct</div>
                <div className="text-sm font-bold text-green-500">
                  {roundHistory.filter(r => r.isCorrect).length}
                </div>
              </div>

              <div className="bg-slate-800 p-2 rounded-sm text-center">
                <div className="text-xs text-slate-400">Accuracy</div>
                <div className="text-sm font-bold text-white">
                  {roundHistory.length > 0 ?
                    Math.round((roundHistory.filter(r => r.isCorrect).length / roundHistory.length) * 100) : 0}%
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Round History */}
        <div className="bg-slate-900 border border-slate-800 rounded-sm flex-1 flex flex-col overflow-hidden">
          <div className="p-2 border-b border-slate-800 flex justify-between items-center">
            <h3 className="text-sm font-medium text-white">Round History</h3>
            <span className="text-xs text-slate-400">{roundHistory.length} rounds</span>
          </div>

          <div className="flex-1 overflow-auto p-2">
            {renderRoundHistory()}
          </div>
        </div>

        {/* Game Result */}
        {gameStatus === GAME_STATUS.GAME_END && gameResult && (
          <div className="bg-slate-900 border border-slate-800 rounded-sm p-2">
            <h3 className="text-sm font-medium text-white mb-2">Final Results</h3>

            <div className="text-center mb-2">
              <div className="text-2xl font-bold text-white mb-1">{gameResult.score} pts</div>
              <div className="text-xs text-slate-400">
                {gameResult.correctAnswers}/{gameResult.totalRounds} correct ({gameResult.accuracy.toFixed(1)}%)
              </div>
            </div>

            <div className="bg-slate-800 p-2 rounded-sm mb-2">
              <div className="text-center">
                <div className="text-xs text-slate-400">Payout</div>
                <div className={`text-lg font-bold ${gameResult.payout > config.wagerAmount ? 'text-green-500' : gameResult.payout === config.wagerAmount ? 'text-yellow-500' : 'text-red-500'}`}>
                  ${gameResult.payout.toFixed(2)}
                </div>
              </div>
            </div>

            <Button
              onClick={resetGame}
              className="w-full h-7 text-xs rounded-sm bg-gradient-to-r from-purple-500 to-pink-500"
            >
              <RotateCcw className="h-3 w-3 mr-1" />
              Play Again
            </Button>
          </div>
        )}
      </div>

        {/* Game Board - Middle column */}
        <div className="col-span-6 flex flex-col h-full relative">
        {/* Game board */}
        <div className="flex-1 bg-slate-900 border border-slate-800 rounded-sm flex flex-col relative overflow-hidden pb-12">
          <div className="flex-1 overflow-auto p-3 flex flex-col">
            {gameStatus === GAME_STATUS.SETUP ? (
              renderSetupScreen()
            ) : (
              <div className="flex-1 flex flex-col">
                {/* Video Player */}
                {renderVideoPlayer()}

                {/* Video Controls */}
                {gameStatus !== GAME_STATUS.SETUP && renderVideoControls()}

                {/* Answer Section */}
                {renderAnswerOptions()}

                {/* Results */}
                {renderResults()}
              </div>
            )}

            {/* Settings overlay */}
            {showSettings && renderSettings()}
          </div>
        </div>

        {/* Game controls - Pinned to bottom */}
        <div className="absolute bottom-0 left-0 right-0 bg-slate-950 border-t border-slate-800 p-1 flex justify-between items-center">
          <Button
            variant="outline"
            size="sm"
            className="h-8 text-xs"
            onClick={resetGame}
          >
            <RotateCcw className="h-3 w-3 mr-1" />
            Reset Game
          </Button>

          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              className={`h-8 text-xs ${showSettings ? 'bg-slate-800' : ''}`}
              onClick={() => setShowSettings(!showSettings)}
            >
              <Settings className="h-3 w-3 mr-1" />
              Settings
            </Button>

            <Button
              variant={gameStatus === GAME_STATUS.SETUP ? 'default' : 'outline'}
              size="sm"
              className="h-8 text-xs"
              disabled={gameStatus !== GAME_STATUS.SETUP}
              onClick={startGame}
            >
              {gameStatus === GAME_STATUS.SETUP ? (
                <>
                  <Zap className="h-3 w-3 mr-1" />
                  Start Game
                </>
              ) : (
                <>
                  <Trophy className="h-3 w-3 mr-1" />
                  Game Active
                </>
              )}
            </Button>
          </div>
        </div>
      </div>

        {/* Chat and Stats - Right column */}
        <div className="col-span-3 flex flex-col gap-1 h-full overflow-hidden">
        {/* Live Stats */}
        <div className="bg-slate-900 border border-slate-800 rounded-sm p-2">
          <div className="flex justify-between items-center mb-2">
            <h3 className="text-sm font-medium text-white">Live Stats</h3>
            <Badge className="bg-blue-500">
              <Eye className="h-3 w-3 mr-1" />
              12
            </Badge>
          </div>

          <div className="space-y-2">
            <div className="flex justify-between text-xs">
              <span className="text-slate-400">Game Time</span>
              <span className="text-white">{formatTime(totalGameTime)}</span>
            </div>

            <div className="flex justify-between text-xs">
              <span className="text-slate-400">Current Streak</span>
              <span className="text-white">
                {roundHistory.slice(-3).every(r => r.isCorrect) && roundHistory.length >= 3 ? '🔥 Hot!' : '-'}
              </span>
            </div>

            {gameStatus === GAME_STATUS.ANSWERING && (
              <div>
                <div className="flex justify-between text-xs mb-1">
                  <span className="text-slate-400">Time Left</span>
                  <span className="text-white">{timeLeft}s</span>
                </div>
                <Progress value={(timeLeft / config.timeLimit) * 100} className="h-1.5" />
              </div>
            )}
          </div>
        </div>

        {/* Betting */}
        <div className="bg-slate-900 border border-slate-800 rounded-sm p-2">
          <div className="flex justify-between items-center mb-2">
            <h3 className="text-sm font-medium text-white">Side Bets</h3>
            <Badge className={gameStatus === GAME_STATUS.ANSWERING ? "bg-green-500" : "bg-red-500"}>
              {gameStatus === GAME_STATUS.ANSWERING ? "OPEN" : "CLOSED"}
            </Badge>
          </div>

          <div className="space-y-2">
            <div className="bg-slate-800 p-2 rounded-sm">
              <div className="flex justify-between items-center mb-1">
                <span className="text-xs text-slate-400">Correct Answer</span>
                <span className="text-xs font-bold text-white">2.5x</span>
              </div>
              <Button
                className="w-full h-6 text-[10px] rounded-sm bg-gradient-to-r from-green-500 to-green-600"
                disabled={gameStatus !== GAME_STATUS.ANSWERING}
              >
                Bet Correct
              </Button>
            </div>

            <div className="bg-slate-800 p-2 rounded-sm">
              <div className="flex justify-between items-center mb-1">
                <span className="text-xs text-slate-400">Time Under 15s</span>
                <span className="text-xs font-bold text-white">3.0x</span>
              </div>
              <Button
                className="w-full h-6 text-[10px] rounded-sm bg-gradient-to-r from-blue-500 to-blue-600"
                disabled={gameStatus !== GAME_STATUS.ANSWERING}
              >
                Bet Fast Answer
              </Button>
            </div>
          </div>
        </div>

        {/* Chat */}
        <div className="flex-1 bg-slate-900 border border-slate-800 rounded-sm flex flex-col overflow-hidden min-h-0">
          <div className="p-2 border-b border-slate-800 flex justify-between items-center">
            <div className="flex items-center">
              <MessageSquare className="h-4 w-4 text-slate-400 mr-1" />
              <h3 className="text-sm font-medium text-white">Game Chat</h3>
            </div>
            <div className="flex items-center">
              <Badge className="h-5 bg-green-500 mr-1 text-[10px]">
                <Users className="h-3 w-3 mr-1" />
                7
              </Badge>
              <Button
                variant="ghost"
                size="sm"
                className="h-5 w-5 p-0"
              >
                <ChevronDown className="h-3 w-3 text-slate-400" />
              </Button>
            </div>
          </div>

          {/* Chat messages - scrollable */}
          <div className="flex-1 overflow-auto p-2 space-y-1">
            {messages.map((msg, idx) => (
              <div
                key={idx}
                className={`text-xs p-1 rounded-sm ${
                  msg.type === 'system' ? 'text-slate-400 italic' : 'text-white'
                }`}
              >
                {msg.text}
              </div>
            ))}
            <div ref={messageEndRef} />
          </div>

          {/* Chat input */}
          <div className="p-2 border-t border-slate-800">
            <div className="flex space-x-2">
              <Input
                placeholder="Type a message..."
                className="h-8 text-xs bg-slate-800 border-slate-700"
                value={messageInput}
                onChange={(e) => setMessageInput(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    sendMessage();
                  }
                }}
              />
              <Button
                onClick={sendMessage}
                className="h-8 px-3 bg-gradient-to-r from-purple-500 to-pink-500"
              >
                Send
              </Button>
            </div>
          </div>
        </div>
        </div>
      </div>
    </div>
  );
};

export default HighlightHeroGame;