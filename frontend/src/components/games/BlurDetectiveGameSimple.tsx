import React, { useState, useEffect, useRef } from 'react';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Card } from '../ui/card';
import { Badge } from '../ui/badge';
import { Progress } from '../ui/progress';
import { Label } from '../ui/label';
import { Brain, Clock, Eye, Send, Sparkles, Trophy, X } from 'lucide-react';

interface BlurDetectiveGameSimpleProps {
  onEndGame: (score: number) => void;
  onQuit: () => void;
}

const PRACTICE_IMAGES = [
  { id: 1, src: '/games/bd/images/celebrity1.jpg', answer: 'Leonardo DiCaprio' },
  { id: 2, src: '/games/bd/images/celebrity2.jpg', answer: 'Taylor Swift' }
];

const HINTS = [
  { id: 1, text: 'This is a famous Hollywood actor', cost: 10 },
  { id: 2, text: 'Known for Titanic and Inception', cost: 20 },
  { id: 3, text: 'Won Oscar for The Revenant', cost: 30 }
];

export default function BlurDetectiveGameSimple({ onEndGame, onQuit }: BlurDetectiveGameSimpleProps) {
  const [currentRound, setCurrentRound] = useState(0);
  const [score, setScore] = useState(0);
  const [guess, setGuess] = useState('');
  const [guessesLeft, setGuessesLeft] = useState(3);
  const [blurLevel, setBlurLevel] = useState(40);
  const [timeRemaining, setTimeRemaining] = useState(60);
  const [gameStatus, setGameStatus] = useState<'playing' | 'round-end' | 'game-over'>('playing');
  const [revealedHints, setRevealedHints] = useState<number[]>([]);
  const [showCorrectAnswer, setShowCorrectAnswer] = useState(false);
  const [roundScore, setRoundScore] = useState(0);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const timerRef = useRef<NodeJS.Timeout>();

  const currentImage = PRACTICE_IMAGES[currentRound];

  // Timer effect
  useEffect(() => {
    if (gameStatus === 'playing' && timeRemaining > 0) {
      timerRef.current = setTimeout(() => {
        setTimeRemaining(prev => prev - 1);
        // Decrease blur gradually
        if (timeRemaining % 10 === 0 && blurLevel > 10) {
          setBlurLevel(prev => Math.max(10, prev - 5));
        }
      }, 1000);
    } else if (timeRemaining === 0 && gameStatus === 'playing') {
      endRound(false);
    }

    return () => {
      if (timerRef.current) clearTimeout(timerRef.current);
    };
  }, [timeRemaining, gameStatus, blurLevel]);

  // Apply blur effect
  useEffect(() => {
    if (currentImage && canvasRef.current) {
      const canvas = canvasRef.current;
      const ctx = canvas.getContext('2d');
      if (!ctx) return;

      const img = new Image();
      img.onload = () => {
        canvas.width = 400;
        canvas.height = 300;
        
        ctx.filter = `blur(${showCorrectAnswer ? 0 : blurLevel}px)`;
        ctx.drawImage(img, 0, 0, 400, 300);
      };
      img.src = currentImage.src;
    }
  }, [currentImage, blurLevel, showCorrectAnswer]);

  const handleGuess = () => {
    if (!guess.trim() || guessesLeft === 0) return;

    const isCorrect = guess.toLowerCase().includes(currentImage.answer.toLowerCase()) ||
                     currentImage.answer.toLowerCase().includes(guess.toLowerCase());

    if (isCorrect) {
      const timeBonus = Math.floor((timeRemaining / 60) * 100);
      const guessBonus = guessesLeft * 50;
      const totalScore = 100 + timeBonus + guessBonus;
      setRoundScore(totalScore);
      setScore(prev => prev + totalScore);
      endRound(true);
    } else {
      setGuessesLeft(prev => prev - 1);
      setGuess('');
      if (guessesLeft === 1) {
        endRound(false);
      }
    }
  };

  const handleUseHint = (hintId: number, cost: number) => {
    if (score >= cost && !revealedHints.includes(hintId)) {
      setScore(prev => prev - cost);
      setRevealedHints(prev => [...prev, hintId]);
      // Also reduce blur when hint is used
      setBlurLevel(prev => Math.max(10, prev - 5));
    }
  };

  const endRound = (success: boolean) => {
    setShowCorrectAnswer(true);
    setGameStatus('round-end');
    setRoundScore(success ? roundScore : 0);
    
    setTimeout(() => {
      if (currentRound < PRACTICE_IMAGES.length - 1) {
        nextRound();
      } else {
        setGameStatus('game-over');
        setTimeout(() => onEndGame(score), 2000);
      }
    }, 3000);
  };

  const nextRound = () => {
    setCurrentRound(prev => prev + 1);
    setGuess('');
    setGuessesLeft(3);
    setBlurLevel(40);
    setTimeRemaining(60);
    setGameStatus('playing');
    setRevealedHints([]);
    setShowCorrectAnswer(false);
    setRoundScore(0);
  };

  if (gameStatus === 'game-over') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 to-pink-50 p-8 flex items-center justify-center">
        <Card className="p-8 max-w-md w-full text-center space-y-6">
          <Trophy className="w-16 h-16 mx-auto text-yellow-500" />
          <div>
            <h2 className="text-3xl font-bold mb-2">Practice Complete!</h2>
            <p className="text-xl text-muted-foreground">Final Score: {score}</p>
          </div>
          <Button onClick={() => onEndGame(score)} className="w-full">
            Continue
          </Button>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-pink-50 p-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-4">
            <Badge variant="outline" className="text-lg px-3 py-1">
              Round {currentRound + 1}/{PRACTICE_IMAGES.length}
            </Badge>
            <Badge variant="secondary" className="text-lg px-3 py-1">
              Score: {score}
            </Badge>
          </div>
          <Button variant="outline" size="sm" onClick={onQuit}>
            <X className="w-4 h-4 mr-2" />
            Quit Practice
          </Button>
        </div>

        {/* Game Area */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Game */}
          <div className="lg:col-span-2 space-y-4">
            {/* Image */}
            <Card className="relative overflow-hidden bg-black/5">
              <div className="aspect-video relative">
                <canvas
                  ref={canvasRef}
                  className="w-full h-full object-contain"
                />
                
                {/* Blur Level Indicator */}
                <div className="absolute bottom-4 left-4 right-4">
                  <div className="bg-black/50 backdrop-blur-sm rounded-lg p-3">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-white text-sm">Blur Level</span>
                      <span className="text-white font-mono">{blurLevel}px</span>
                    </div>
                    <Progress value={100 - (blurLevel / 40) * 100} className="h-2" />
                  </div>
                </div>

                {/* Timer */}
                <div className="absolute top-4 right-4 bg-black/50 backdrop-blur-sm rounded-lg px-3 py-2">
                  <div className="flex items-center gap-2 text-white">
                    <Clock className="w-5 h-5" />
                    <span className="font-mono text-lg">{timeRemaining}s</span>
                  </div>
                </div>
              </div>
            </Card>

            {/* Guess Input */}
            <Card className="p-4">
              <form onSubmit={(e) => { e.preventDefault(); handleGuess(); }} className="space-y-3">
                <div className="flex items-center justify-between mb-2">
                  <Label className="text-sm">Make Your Guess</Label>
                  <Badge variant={guessesLeft > 0 ? "default" : "destructive"}>
                    {guessesLeft} guesses left
                  </Badge>
                </div>
                <div className="flex gap-2">
                  <Input
                    value={guess}
                    onChange={(e) => setGuess(e.target.value)}
                    placeholder="Enter celebrity name..."
                    disabled={guessesLeft === 0 || gameStatus !== 'playing'}
                    className="flex-1"
                  />
                  <Button 
                    type="submit" 
                    disabled={guessesLeft === 0 || gameStatus !== 'playing'}
                  >
                    <Send className="w-4 h-4 mr-2" />
                    Guess
                  </Button>
                </div>
              </form>
            </Card>

            {/* Round End Message */}
            {gameStatus === 'round-end' && (
              <Card className="p-6 bg-primary/10 border-primary">
                <div className="text-center space-y-2">
                  <h3 className="text-xl font-semibold">
                    {roundScore > 0 ? '🎉 Correct!' : '😔 Time\'s Up!'}
                  </h3>
                  <p className="text-lg">
                    The answer was: <strong>{currentImage.answer}</strong>
                  </p>
                  {roundScore > 0 && (
                    <p className="text-sm text-muted-foreground">
                      You earned {roundScore} points!
                    </p>
                  )}
                </div>
              </Card>
            )}
          </div>

          {/* Hints Panel */}
          <div>
            <Card className="p-4">
              <h3 className="font-semibold mb-3 flex items-center gap-2">
                <Sparkles className="w-5 h-5 text-primary" />
                Hints
              </h3>
              <div className="space-y-2">
                {HINTS.map((hint, index) => (
                  <div
                    key={hint.id}
                    className={`p-3 rounded-lg border ${
                      revealedHints.includes(hint.id) 
                        ? 'bg-muted' 
                        : 'hover:bg-muted/50 cursor-pointer'
                    }`}
                    onClick={() => !revealedHints.includes(hint.id) && handleUseHint(hint.id, hint.cost)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Badge variant={revealedHints.includes(hint.id) ? "secondary" : "default"}>
                          Hint {index + 1}
                        </Badge>
                        {revealedHints.includes(hint.id) && (
                          <span className="text-sm">{hint.text}</span>
                        )}
                        {!revealedHints.includes(hint.id) && (
                          <span className="text-sm text-muted-foreground">Click to reveal</span>
                        )}
                      </div>
                      {!revealedHints.includes(hint.id) && (
                        <Badge variant="outline">-{hint.cost}</Badge>
                      )}
                    </div>
                  </div>
                ))}
              </div>

              <div className="mt-4 p-3 bg-muted rounded-lg">
                <h4 className="font-medium mb-2 flex items-center gap-2">
                  <Brain className="w-4 h-4" />
                  How to Play
                </h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• Guess the celebrity from the blurred image</li>
                  <li>• Image gets clearer over time</li>
                  <li>• Use hints to help (costs points)</li>
                  <li>• Faster guesses = more points!</li>
                </ul>
              </div>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}