import React, { useState, useEffect, useRef, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Input } from '@/components/ui/input';
import {
  Trophy,
  Clock,
  MessageSquare,
  Users,
  Settings,
  Star,
  Target,
  Zap,
  RotateCcw,
  Check,
  X,
  AlertCircle,
  DollarSign,
  ChevronDown,
  Shuffle,
  Plus,
  Minus,
  Play,
  Pause,
  Crown,
  Award,
  Hand,
  Eye,
  EyeOff,
  RefreshCw,
  Spade,
  Heart,
  Diamond,
  Club,
  Timer,
  ArrowRight,
  ArrowLeft,
  SkipForward
} from 'lucide-react';

// Card suits and values
const SUITS = {
  HEARTS: 'hearts',
  DIAMONDS: 'diamonds',
  CLUBS: 'clubs',
  SPADES: 'spades'
};

const VALUES = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K'];

// Game status
const GAME_STATUS = {
  SETUP: 'setup',
  DEALING: 'dealing',
  PLAYING: 'playing',
  CHOOSING_SUIT: 'choosing_suit',
  ROUND_END: 'round_end',
  GAME_END: 'game_end'
};

// Card directions
const DIRECTIONS = {
  CLOCKWISE: 'clockwise',
  COUNTERCLOCKWISE: 'counterclockwise'
};

interface CrazyEightsGameProps {
  onBack?: () => void;
  onGameEnd?: (result: any) => void;
}

const CrazyEightsGame: React.FC<CrazyEightsGameProps> = ({ onBack, onGameEnd }) => {
  // Game configuration
  const [config, setConfig] = useState({
    numDecks: 1,
    numPlayers: 4,
    cardsPerPlayer: 7,
    wagerAmount: 25,
    enableSpecialRules: true,
    drawTwoPenalty: true,
    skipTurnOnQueen: true,
    reverseOnJack: true,
    timeLimit: 30 // seconds per turn
  });

  // Game state
  const [gameStatus, setGameStatus] = useState(GAME_STATUS.SETUP);
  const [deck, setDeck] = useState([]);
  const [discardPile, setDiscardPile] = useState([]);
  const [currentPlayer, setCurrentPlayer] = useState(0);
  const [direction, setDirection] = useState(DIRECTIONS.CLOCKWISE);
  const [currentSuit, setCurrentSuit] = useState(null);
  const [mustDraw, setMustDraw] = useState(0); // For draw-two penalty
  const [showSettings, setShowSettings] = useState(false);
  const [timeLeft, setTimeLeft] = useState(0);
  const [gameHistory, setGameHistory] = useState([]);
  const [gameResult, setGameResult] = useState(null);
  const [suitSelection, setSuitSelection] = useState(false);
  const [selectedCard, setSelectedCard] = useState(null);

  // Players
  const [players, setPlayers] = useState([
    { id: 1, name: 'You', hand: [], isAI: false, score: 0, isWinner: false },
    { id: 2, name: 'Player 2', hand: [], isAI: true, score: 0, isWinner: false },
    { id: 3, name: 'Player 3', hand: [], isAI: true, score: 0, isWinner: false },
    { id: 4, name: 'Player 4', hand: [], isAI: true, score: 0, isWinner: false }
  ]);

  // Refs
  const timerRef = useRef(null);
  const aiTimeoutRef = useRef(null);

  // Chat state
  const [messages, setMessages] = useState([
    { text: "Welcome to Crazy Eights! Match the suit or rank, and play your 8s wisely!", type: "system" }
  ]);
  const [messageInput, setMessageInput] = useState('');
  const messageEndRef = useRef(null);

  // Effects

  // Scroll chat to bottom
  useEffect(() => {
    if (messageEndRef.current) {
      messageEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  // Clean up timers
  useEffect(() => {
    return () => {
      if (timerRef.current) clearInterval(timerRef.current);
      if (aiTimeoutRef.current) clearTimeout(aiTimeoutRef.current);
    };
  }, []);

  // Global card ID counter
  const nextCardId = useRef(0);

  // Create a standard deck of cards
  const createDeck = useCallback(() => {
    const newDeck = [];

    for (let deckNum = 0; deckNum < config.numDecks; deckNum++) {
      Object.values(SUITS).forEach(suit => {
        VALUES.forEach(value => {
          newDeck.push({
            id: `${suit}_${value}_${deckNum}`,
            uniqueId: nextCardId.current++,
            suit,
            value,
            deckNumber: deckNum
          });
        });
      });
    }

    return newDeck;
  }, [config.numDecks]);

  // Shuffle deck
  const shuffleDeck = useCallback((deckToShuffle) => {
    const shuffled = [...deckToShuffle];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  }, []);

  // Add message to chat
  const addMessage = (text, type = "player") => {
    setMessages(prev => [...prev, { text, type }]);
  };

  // Send chat message
  const sendMessage = () => {
    if (messageInput.trim()) {
      addMessage(`You: ${messageInput}`);
      setMessageInput('');
    }
  };

  // Get card image path
  const getCardImagePath = (card) => {
    if (!card) return '/games/cards/png/back.png';

    // Convert suit names to match image file names
    const suitMap = {
      hearts: 'hearts',
      diamonds: 'diamonds',
      clubs: 'clubs',
      spades: 'spades'
    };

    // Handle different value formats
    let valueStr = card.value.toLowerCase();
    if (valueStr === 'a') valueStr = 'ace';
    if (valueStr === 'j') valueStr = 'jack';
    if (valueStr === 'q') valueStr = 'queen';
    if (valueStr === 'k') valueStr = 'king';

    return `/games/cards/png/${valueStr}_of_${suitMap[card.suit]}.png`;
  };

  // Get suit icon
  const getSuitIcon = (suit) => {
    switch (suit) {
      case SUITS.HEARTS:
        return <Heart className="h-4 w-4 text-red-500 fill-current" />;
      case SUITS.DIAMONDS:
        return <Diamond className="h-4 w-4 text-red-500 fill-current" />;
      case SUITS.CLUBS:
        return <Club className="h-4 w-4 text-black fill-current" />;
      case SUITS.SPADES:
        return <Spade className="h-4 w-4 text-black fill-current" />;
      default:
        return null;
    }
  };

  // Start the game
  const startGame = () => {
    // Create and shuffle deck
    const newDeck = shuffleDeck(createDeck());

    // Reset players
    const activePlayers = players.slice(0, config.numPlayers).map((player, index) => ({
      ...player,
      hand: [],
      score: 0,
      isWinner: false
    }));

    setPlayers(activePlayers);
    setDeck(newDeck);
    setDiscardPile([]);
    setCurrentPlayer(0);
    setDirection(DIRECTIONS.CLOCKWISE);
    setCurrentSuit(null);
    setMustDraw(0);
    setGameHistory([]);
    setGameResult(null);
    setSuitSelection(false);
    setSelectedCard(null);

    setGameStatus(GAME_STATUS.DEALING);
    addMessage("Game started! Cards are being dealt...", "system");
    
    // Deal cards after a short delay to show the dealing animation
    setTimeout(() => {
      dealCards(newDeck, activePlayers);
    }, 1000);
  };

  // Deal cards to players
  const dealCards = (deckToDeal, playersToServe) => {
    let currentDeck = [...deckToDeal];
    const updatedPlayers = [...playersToServe];

    // Deal cards to each player
    for (let round = 0; round < config.cardsPerPlayer; round++) {
      for (let playerIndex = 0; playerIndex < playersToServe.length; playerIndex++) {
        if (currentDeck.length > 0) {
          const card = currentDeck.pop();
          updatedPlayers[playerIndex].hand.push(card);
        }
      }
    }

    // Place first card on discard pile
    if (currentDeck.length > 0) {
      const firstCard = currentDeck.pop();
      setDiscardPile([firstCard]);
      setCurrentSuit(firstCard.suit);

      // If first card is an 8, let first player choose suit
      if (firstCard.value === '8') {
        setSuitSelection(true);
        setGameStatus(GAME_STATUS.CHOOSING_SUIT);
        addMessage("First card is an 8! Player 1, choose a suit.", "system");
      } else {
        setGameStatus(GAME_STATUS.PLAYING);
        setTimeout(() => startPlayerTurn(), 1000);
      }
    } else {
      // If no cards left for discard pile, just start the game
      setGameStatus(GAME_STATUS.PLAYING);
      addMessage("Warning: Not enough cards in deck. Starting game anyway.", "system");
      setTimeout(() => startPlayerTurn(), 1000);
    }

    setDeck(currentDeck);
    setPlayers(updatedPlayers);

    addMessage(`Cards dealt! Each player has ${config.cardsPerPlayer} cards.`, "system");
  };

  // Draw cards for a player
  const drawCards = (playerIndex, numCards) => {
    setPlayers(prev => {
      const updated = [...prev];
      let currentDeck = [...deck];

      for (let i = 0; i < numCards; i++) {
        if (currentDeck.length === 0) {
          // Reshuffle discard pile if deck is empty
          if (discardPile.length > 1) {
            const topCard = discardPile[discardPile.length - 1];
            const cardsToShuffle = discardPile.slice(0, -1);
            currentDeck = shuffleDeck(cardsToShuffle);
            setDiscardPile([topCard]);
            addMessage("Deck empty! Reshuffling discard pile...", "system");
          } else {
            break; // No more cards available
          }
        }

        if (currentDeck.length > 0) {
          const card = currentDeck.pop();
          updated[playerIndex].hand.push(card);
        }
      }

      setDeck(currentDeck);
      return updated;
    });
  };

  // Handle turn timeout
  const handleTurnTimeout = () => {
    const activePlayer = players[currentPlayer];
    addMessage(`${activePlayer.name} timed out and drew a card.`, "system");
    drawCards(currentPlayer, 1);
    nextPlayer();
  };

  // Start a player's turn
  const startPlayerTurn = () => {
    setTimeLeft(config.timeLimit);

    if (timerRef.current) {
      clearInterval(timerRef.current);
    }

    const activePlayer = players[currentPlayer];
    addMessage(`${activePlayer.name}'s turn!`, "system");

    // Handle draw penalty
    if (mustDraw > 0) {
      drawCards(currentPlayer, mustDraw);
      setMustDraw(0);
      addMessage(`${activePlayer.name} drew ${mustDraw} cards due to Draw Two penalty.`, "system");
      setTimeout(() => nextPlayer(), 1500);
      return;
    }

    // Start timer
    timerRef.current = setInterval(() => {
      setTimeLeft(prev => {
        if (prev <= 1) {
          clearInterval(timerRef.current);
          handleTurnTimeout();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    // Handle AI turn
    if (activePlayer.isAI) {
      aiTimeoutRef.current = setTimeout(() => {
        playAITurn();
      }, 1000 + Math.random() * 2000); // Random delay for realism
    }
  };

  // Move to next player
  const nextPlayer = () => {
    setCurrentPlayer(prev => {
      const nextIndex = direction === DIRECTIONS.CLOCKWISE
        ? (prev + 1) % config.numPlayers
        : (prev - 1 + config.numPlayers) % config.numPlayers;

      setTimeout(() => startPlayerTurn(), 500);
      return nextIndex;
    });
  };

  // Check if a card can be played
  const canPlayCard = (card) => {
    if (!discardPile.length) return false;

    const topCard = discardPile[discardPile.length - 1];

    // 8s can always be played
    if (card.value === '8') return true;

    // Match suit or value
    return card.suit === currentSuit || card.value === topCard.value;
  };

  // Play a card
  const playCard = (playerIndex, cardToPlay) => {
    const player = players[playerIndex];
    let card, cardIndex;
    
    // If cardToPlay is a number, it's an index (for AI players)
    if (typeof cardToPlay === 'number') {
      cardIndex = cardToPlay;
      card = player.hand[cardIndex];
    } else {
      // Otherwise it's the actual card object (for human player)
      card = cardToPlay;
      cardIndex = player.hand.findIndex(c => c.uniqueId === card.uniqueId);
      if (cardIndex === -1) {
        addMessage("Error: Card not found in hand!", "system");
        return false;
      }
    }

    if (!canPlayCard(card)) {
      addMessage("Invalid move! Card doesn't match suit or rank.", "system");
      return false;
    }

    // Clear timer
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }

    // Remove card from player's hand
    setPlayers(prev => {
      const updated = [...prev];
      updated[playerIndex].hand.splice(cardIndex, 1);
      return updated;
    });

    // Add card to discard pile
    setDiscardPile(prev => [...prev, card]);

    // Handle special cards
    if (card.value === '8') {
      // Player chooses new suit
      if (playerIndex === 0) { // Human player
        setSuitSelection(true);
        setGameStatus(GAME_STATUS.CHOOSING_SUIT);
        addMessage("Choose a suit for the 8!", "system");
      } else { // AI player
        const suits = Object.values(SUITS);
        const newSuit = suits[Math.floor(Math.random() * suits.length)];
        setCurrentSuit(newSuit);
        addMessage(`${player.name} played an 8 and chose ${newSuit}!`, "system");
        setTimeout(() => nextPlayer(), 1000);
      }
    } else if (card.value === '2' && config.drawTwoPenalty) {
      // Next player must draw 2
      setMustDraw(2);
      setCurrentSuit(card.suit);
      addMessage(`${player.name} played a 2! Next player draws 2 cards.`, "system");
      setTimeout(() => nextPlayer(), 1000);
    } else if (card.value === 'Q' && config.skipTurnOnQueen) {
      // Skip next player
      setCurrentSuit(card.suit);
      addMessage(`${player.name} played a Queen! Next player is skipped.`, "system");
      setTimeout(() => {
        nextPlayer();
        setTimeout(() => nextPlayer(), 500);
      }, 1000);
    } else if (card.value === 'J' && config.reverseOnJack) {
      // Reverse direction
      setDirection(prev => prev === DIRECTIONS.CLOCKWISE ? DIRECTIONS.COUNTERCLOCKWISE : DIRECTIONS.CLOCKWISE);
      setCurrentSuit(card.suit);
      addMessage(`${player.name} played a Jack! Direction reversed.`, "system");
      setTimeout(() => nextPlayer(), 1000);
    } else {
      // Normal card
      setCurrentSuit(card.suit);
      addMessage(`${player.name} played ${card.value} of ${card.suit}.`, "system");
      setTimeout(() => nextPlayer(), 1000);
    }

    // Check for win
    if (player.hand.length === 0) {
      setGameStatus(GAME_STATUS.GAME_END);
      setGameResult({ winner: player, winType: 'cards_out' });
      addMessage(`${player.name} wins! All cards played!`, "system");
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    }

    return true;
  };

  // AI turn logic
  const playAITurn = () => {
    const player = players[currentPlayer];
    const playableCards = player.hand.map((card, index) => ({ card, index }))
      .filter(({ card }) => canPlayCard(card));

    if (playableCards.length > 0) {
      // Play first playable card (simple AI)
      const { index } = playableCards[0];
      playCard(currentPlayer, index);
    } else {
      // Draw a card
      drawCards(currentPlayer, 1);
      addMessage(`${player.name} drew a card.`, "system");
      setTimeout(() => nextPlayer(), 1000);
    }
  };

  // Handle human player card click
  const handleCardClick = (card) => {
    if (currentPlayer !== 0 || gameStatus !== GAME_STATUS.PLAYING) return;

    if (canPlayCard(card)) {
      playCard(0, card);
    } else {
      addMessage("You can't play that card! It must match the suit or rank.", "system");
    }
  };

  // Handle draw card
  const handleDrawCard = () => {
    if (currentPlayer !== 0 || gameStatus !== GAME_STATUS.PLAYING) return;

    drawCards(0, 1);
    addMessage("You drew a card.", "system");
    setTimeout(() => nextPlayer(), 1000);
  };

  // Handle suit selection
  const handleSuitSelection = (suit) => {
    setCurrentSuit(suit);
    setSuitSelection(false);
    setGameStatus(GAME_STATUS.PLAYING);
    addMessage(`You chose ${suit}!`, "system");
    setTimeout(() => nextPlayer(), 1000);
  };

  return (
    <div className="h-screen bg-slate-950 text-white overflow-hidden">
      <div className="container mx-auto px-4 pt-20 pb-4 h-full flex flex-col">
        {/* Header - Fixed Height */}
        <div className="flex items-center justify-between mb-4 flex-shrink-0">
          <div className="flex items-center">
            {onBack && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onBack}
                className="h-8 w-8 p-0 mr-3"
              >
                <X className="h-4 w-4" />
              </Button>
            )}
            <div className="flex items-center">
              <div className="h-10 w-10 rounded bg-gradient-to-br from-red-500 to-red-600 flex items-center justify-center mr-3">
                <span className="text-xl">🃏</span>
              </div>
              <div>
                <h1 className="text-xl font-semibold">Crazy Eights</h1>
                <p className="text-sm text-slate-400">Match the suit or rank to win!</p>
              </div>
            </div>
          </div>

          <Badge className="bg-red-500 text-white px-3 py-1">
            <Trophy className="h-4 w-4 mr-1" />
            Card Game Challenge
          </Badge>
        </div>

        {/* Game Content - Flex 1 to fill remaining space */}
        <div className="grid grid-cols-12 gap-4 flex-1 min-h-0">
          {/* Left Sidebar - Game Info & Players */}
          <div className="col-span-3 space-y-4 h-full flex flex-col">
            {/* Game Settings */}
            {gameStatus === GAME_STATUS.SETUP && (
              <div className="bg-slate-900 border border-slate-800 rounded-lg p-4 flex-shrink-0">
                <h3 className="text-sm font-semibold text-white mb-3">Game Settings</h3>
                <div className="space-y-3">
                  <div>
                    <label className="text-xs text-slate-400 block mb-1">Players</label>
                    <div className="flex space-x-1">
                      {[2, 3, 4].map(num => (
                        <Button
                          key={num}
                          variant={config.numPlayers === num ? "default" : "outline"}
                          size="sm"
                          className="flex-1 h-7 text-xs"
                          onClick={() => setConfig({ ...config, numPlayers: num })}
                        >
                          {num}
                        </Button>
                      ))}
                    </div>
                  </div>

                  <div>
                    <label className="text-xs text-slate-400 block mb-1">Cards per Player</label>
                    <div className="flex space-x-1">
                      {[5, 7, 9].map(num => (
                        <Button
                          key={num}
                          variant={config.cardsPerPlayer === num ? "default" : "outline"}
                          size="sm"
                          className="flex-1 h-7 text-xs"
                          onClick={() => setConfig({ ...config, cardsPerPlayer: num })}
                        >
                          {num}
                        </Button>
                      ))}
                    </div>
                  </div>

                  <div>
                    <label className="text-xs text-slate-400 block mb-1">Wager</label>
                    <div className="flex space-x-1">
                      {[10, 25, 50].map(amount => (
                        <Button
                          key={amount}
                          variant={config.wagerAmount === amount ? "default" : "outline"}
                          size="sm"
                          className="flex-1 h-7 text-xs"
                          onClick={() => setConfig({ ...config, wagerAmount: amount })}
                        >
                          ${amount}
                        </Button>
                      ))}
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <label className="text-xs text-slate-400">Special Rules</label>
                    <Button
                      variant={config.enableSpecialRules ? "default" : "outline"}
                      size="sm"
                      className="h-7 w-14 text-xs"
                      onClick={() => setConfig({ ...config, enableSpecialRules: !config.enableSpecialRules })}
                    >
                      {config.enableSpecialRules ? "On" : "Off"}
                    </Button>
                  </div>
                </div>
              </div>
            )}

            {/* Players List */}
            {gameStatus !== GAME_STATUS.SETUP && (
              <div className="bg-slate-900 border border-slate-800 rounded-lg p-4 flex-1 min-h-0">
                <h3 className="text-sm font-semibold text-white mb-3">Players</h3>
                <div className="space-y-2">
                  {players.slice(0, config.numPlayers).map((player, index) => (
                    <div
                      key={player.id}
                      className={`p-2 rounded text-xs ${
                        index === currentPlayer ? 'bg-yellow-600 text-black' : 'bg-slate-800 text-white'
                      }`}
                    >
                      <div className="flex justify-between items-center">
                        <div className="flex items-center">
                          <div className={`h-2 w-2 rounded-full mr-2 ${
                            index === currentPlayer ? 'bg-black' : 'bg-green-500'
                          }`} />
                          <span className="font-medium">{player.name}</span>
                          {player.isWinner && <Crown className="h-3 w-3 ml-1 text-yellow-500" />}
                        </div>
                        <div className="text-right">
                          <div>{player.hand.length} cards</div>
                          <div className="text-xs opacity-75">{player.score} pts</div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Main Game Area */}
          <div className="col-span-6 h-full">
            <div className="bg-slate-900 border border-slate-800 rounded-lg p-6 h-full flex flex-col">
              {gameStatus === GAME_STATUS.SETUP ? (
                <div className="text-center flex-1 flex flex-col justify-center">
                  <div className="text-6xl mb-4">🃏</div>
                  <h2 className="text-3xl font-bold text-white mb-2">Crazy Eights</h2>
                  <p className="text-lg text-red-500 mb-4">Classic Card Game</p>
                  <p className="text-slate-400 mb-8">
                    Match the suit or rank to play your cards!<br />
                    Use your 8s wisely to change the suit.
                  </p>

                  <Button
                    className="bg-gradient-to-r from-red-500 to-red-600 hover:from-red-400 hover:to-red-500 text-white font-bold px-8 py-3"
                    onClick={startGame}
                  >
                    <Play className="h-5 w-5 mr-2" />
                    Start Game
                  </Button>
                </div>
              ) : gameStatus === GAME_STATUS.CHOOSING_SUIT ? (
                <div className="text-center flex-1 flex flex-col justify-center">
                  <h3 className="text-2xl font-bold text-white mb-6">Choose a Suit</h3>
                  <div className="grid grid-cols-2 gap-4 max-w-md mx-auto">
                    {Object.values(SUITS).map(suit => (
                      <Button
                        key={suit}
                        onClick={() => handleSuitSelection(suit)}
                        className="h-20 bg-slate-800 hover:bg-slate-700 border-2 border-slate-600 hover:border-slate-500"
                      >
                        <div className="flex flex-col items-center">
                          {getSuitIcon(suit)}
                          <span className="mt-2 capitalize">{suit}</span>
                        </div>
                      </Button>
                    ))}
                  </div>
                </div>
              ) : gameStatus === GAME_STATUS.DEALING ? (
                <div className="text-center flex-1 flex flex-col justify-center">
                  <div className="text-6xl mb-4 animate-pulse">🎴</div>
                  <h3 className="text-2xl font-bold text-white mb-2">Dealing Cards...</h3>
                  <p className="text-slate-400">Setting up your game</p>
                </div>
              ) : gameStatus === GAME_STATUS.PLAYING ? (
                <div className="flex-1 flex flex-col">
                  {/* Game Status Bar */}
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-4">
                      <Badge className="bg-blue-600 text-white">
                        Turn: {players[currentPlayer]?.name}
                      </Badge>
                      <Badge className="bg-green-600 text-white">
                        Current Suit: {currentSuit && (
                          <span className="ml-1 flex items-center">
                            {getSuitIcon(currentSuit)}
                            <span className="ml-1 capitalize">{currentSuit}</span>
                          </span>
                        )}
                      </Badge>
                      {direction === DIRECTIONS.COUNTERCLOCKWISE && (
                        <Badge className="bg-orange-600 text-white">
                          <ArrowLeft className="h-3 w-3 mr-1" />
                          Reversed
                        </Badge>
                      )}
                    </div>
                    <div className="flex items-center space-x-2">
                      <Timer className="h-4 w-4 text-yellow-500" />
                      <span className={`font-bold ${timeLeft <= 10 ? 'text-red-500 animate-pulse' : 'text-white'}`}>
                        {timeLeft}s
                      </span>
                    </div>
                  </div>

                  {/* Discard Pile and Deck */}
                  <div className="flex justify-center items-center space-x-8 mb-6">
                    {/* Deck */}
                    <div className="text-center">
                      <div className="w-20 h-28 bg-slate-700 border-2 border-slate-600 rounded-lg flex items-center justify-center mb-2 cursor-pointer hover:bg-slate-600"
                           onClick={handleDrawCard}>
                        <div className="text-white text-xs">DECK</div>
                      </div>
                      <p className="text-xs text-slate-400">{deck.length} cards</p>
                    </div>

                    {/* Discard Pile */}
                    <div className="text-center">
                      {discardPile.length > 0 && (
                        <div className="w-20 h-28 relative">
                          <img
                            src={getCardImagePath(discardPile[discardPile.length - 1])}
                            alt="Top card"
                            className="w-full h-full object-cover rounded-lg border-2 border-yellow-500"
                            onError={(e) => {
                              e.target.src = '/games/cards/png/back.png';
                            }}
                          />
                        </div>
                      )}
                      <p className="text-xs text-slate-400 mt-2">Discard</p>
                    </div>
                  </div>

                  {/* Player's Hand */}
                  <div className="flex-1 flex flex-col">
                    <h4 className="text-sm font-semibold text-white mb-2">Your Hand ({players[0]?.hand.length} cards)</h4>
                    <div className="flex flex-wrap gap-2 justify-center">
                      {players[0]?.hand.map((card, index) => (
                        <div
                          key={card.uniqueId}
                          className={`w-16 h-22 cursor-pointer transform transition-all duration-200 hover:scale-105 hover:-translate-y-2 ${
                            canPlayCard(card) ? 'border-2 border-green-500' : 'border border-slate-600'
                          }`}
                          onClick={() => handleCardClick(card)}
                        >
                          <img
                            src={getCardImagePath(card)}
                            alt={`${card.value} of ${card.suit}`}
                            className="w-full h-full object-cover rounded"
                            onError={(e) => {
                              e.target.src = '/games/cards/png/back.png';
                            }}
                          />
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              ) : gameStatus === GAME_STATUS.ROUND_END ? (
                <div className="text-center flex-1 flex flex-col justify-center">
                  <div className="text-4xl mb-4">🏁</div>
                  <h3 className="text-2xl font-bold text-white mb-2">Round Complete!</h3>
                  <p className="text-slate-400 mb-4">Calculating scores...</p>
                  <div className="max-w-md mx-auto">
                    {players.map((player, index) => (
                      <div key={player.id} className="flex justify-between items-center p-2 mb-1 bg-slate-800 rounded">
                        <span className="text-white">{player.name}</span>
                        <span className="text-yellow-500 font-bold">{player.score} pts</span>
                      </div>
                    ))}
                  </div>
                </div>
              ) : gameStatus === GAME_STATUS.GAME_END ? (
                <div className="text-center flex-1 flex flex-col justify-center">
                  <div className="text-6xl mb-4">
                    {gameResult?.winner?.id === 1 ? '🎉' : '😞'}
                  </div>
                  <h2 className="text-3xl font-bold text-white mb-4">
                    {gameResult?.winner?.name} Wins!
                  </h2>
                  <p className="text-lg text-slate-400 mb-6">
                    Game completed with {gameResult?.winType === 'cards_out' ? 'all cards played' : 'timeout'}
                  </p>
                  <div className="space-x-4">
                    <Button
                      onClick={startGame}
                      className="bg-gradient-to-r from-red-500 to-red-600 hover:from-red-400 hover:to-red-500 text-white font-bold px-8 py-3"
                    >
                      Play Again
                    </Button>
                    <Button
                      variant="outline"
                      onClick={onBack}
                      className="border-slate-700 text-white hover:bg-slate-800"
                    >
                      Back to Games
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="text-center flex-1 flex flex-col justify-center">
                  <p className="text-slate-400">Loading game...</p>
                </div>
              )}
            </div>
          </div>

          {/* Right Sidebar - Chat */}
          <div className="col-span-3 h-full flex flex-col space-y-4">
            {/* Live Chat */}
            <div className="bg-slate-900 border border-slate-800 rounded-lg p-4 flex-1 min-h-0 flex flex-col">
              <h3 className="text-sm font-semibold text-white mb-3 flex-shrink-0">Live Chat</h3>
              <div className="flex-1 overflow-y-auto mb-3 space-y-1 min-h-0">
                {messages.map((msg, index) => (
                  <div key={index} className="text-xs">
                    <span className={msg.type === 'system' ? 'text-red-400' : 'text-slate-300'}>
                      {msg.text}
                    </span>
                  </div>
                ))}
                <div ref={messageEndRef} />
              </div>
              <div className="flex gap-2 flex-shrink-0">
                <Input
                  value={messageInput}
                  onChange={(e) => setMessageInput(e.target.value)}
                  placeholder="Type a message..."
                  className="flex-1 h-8 text-xs bg-slate-800 border-slate-700"
                  onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
                />
                <Button size="sm" onClick={sendMessage} className="h-8 px-3">
                  <MessageSquare className="h-3 w-3" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CrazyEightsGame;
