import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Input } from '@/components/ui/input';
import {
  Trophy,
  Clock,
  MessageSquare,
  Users,
  Settings,
  Star,
  Target,
  Zap,
  RotateCcw,
  Check,
  X,
  AlertCircle,
  DollarSign,
  ChevronDown,
  Shuffle,
  Plus,
  Minus,
  Play,
  Pause,
  Crown,
  Award,
  Hand,
  Eye,
  EyeOff,
  RefreshCw,
  Spade,
  Heart,
  Diamond,
  Club
} from 'lucide-react';

// Card suits and values
const SUITS = {
  HEARTS: 'hearts',
  DIAMONDS: 'diamonds',
  CLUBS: 'clubs',
  SPADES: 'spades'
};

const VALUES = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K'];

// Game status
const GAME_STATUS = {
  SETUP: 'setup',
  DEALING: 'dealing',
  PLAYING: 'playing',
  CHOOSING_SUIT: 'choosing_suit',
  ROUND_END: 'round_end',
  GAME_END: 'game_end'
};

// Card directions
const DIRECTIONS = {
  CLOCKWISE: 'clockwise',
  COUNTERCLOCKWISE: 'counterclockwise'
};

interface CrazyEightsGameProps {
  onBack?: () => void;
  onGameEnd?: (result: any) => void;
}

const CrazyEightsGame: React.FC<CrazyEightsGameProps> = ({ onBack, onGameEnd }) => {
  // Game configuration
  const [config, setConfig] = useState({
    numDecks: 1,
    numPlayers: 4,
    cardsPerPlayer: 7,
    wagerAmount: 25,
    enableSpecialRules: true,
    drawTwoPenalty: true,
    skipTurnOnQueen: true,
    reverseOnJack: true,
    timeLimit: 30 // seconds per turn
  });

  // Game state
  const [gameStatus, setGameStatus] = useState(GAME_STATUS.SETUP);
  const [deck, setDeck] = useState([]);
  const [discardPile, setDiscardPile] = useState([]);
  const [currentPlayer, setCurrentPlayer] = useState(0);
  const [direction, setDirection] = useState(DIRECTIONS.CLOCKWISE);
  const [currentSuit, setCurrentSuit] = useState(null);
  const [mustDraw, setMustDraw] = useState(0); // For draw-two penalty
  const [showSettings, setShowSettings] = useState(false);
  const [timeLeft, setTimeLeft] = useState(0);
  const [gameHistory, setGameHistory] = useState([]);
  const [gameResult, setGameResult] = useState(null);
  const [suitSelection, setSuitSelection] = useState(false);
  const [selectedCard, setSelectedCard] = useState(null);

  // Players
  const [players, setPlayers] = useState([
    { id: 1, name: 'You', hand: [], isAI: false, score: 0, isWinner: false },
    { id: 2, name: 'Player 2', hand: [], isAI: true, score: 0, isWinner: false },
    { id: 3, name: 'Player 3', hand: [], isAI: true, score: 0, isWinner: false },
    { id: 4, name: 'Player 4', hand: [], isAI: true, score: 0, isWinner: false }
  ]);

  // Refs
  const timerRef = useRef(null);
  const aiTimeoutRef = useRef(null);

  // Chat state
  const [messages, setMessages] = useState([
    { text: "Welcome to Crazy Eights! Match the suit or rank, and play your 8s wisely!", type: "system" }
  ]);
  const [messageInput, setMessageInput] = useState('');
  const messageEndRef = useRef(null);

  // Effects

  // Scroll chat to bottom
  useEffect(() => {
    if (messageEndRef.current) {
      messageEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  // Clean up timers
  useEffect(() => {
    return () => {
      if (timerRef.current) clearInterval(timerRef.current);
      if (aiTimeoutRef.current) clearTimeout(aiTimeoutRef.current);
    };
  }, []);

  // Create a standard deck of cards
  const createDeck = useCallback(() => {
    const newDeck = [];

    for (let deckNum = 0; deckNum < config.numDecks; deckNum++) {
      Object.values(SUITS).forEach(suit => {
        VALUES.forEach(value => {
          newDeck.push({
            id: `${suit}_${value}_${deckNum}`,
            suit,
            value,
            deckNumber: deckNum
          });
        });
      });
    }

    return newDeck;
  }, [config.numDecks]);

  // Shuffle deck
  const shuffleDeck = useCallback((deckToShuffle) => {
    const shuffled = [...deckToShuffle];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  }, []);

  // Add message to chat
  const addMessage = (text, type = "player") => {
    setMessages(prev => [...prev, { text, type }]);
  };

  // Send chat message
  const sendMessage = () => {
    if (messageInput.trim()) {
      addMessage(`You: ${messageInput}`);
      setMessageInput('');
    }
  };

  // Get card image path
  const getCardImagePath = (card) => {
    if (!card) return '/games/cards/back.png';

    // Convert suit names to match image file names
    const suitMap = {
      hearts: 'hearts',
      diamonds: 'diamonds',
      clubs: 'clubs',
      spades: 'spades'
    };

    // Handle different value formats
    let valueStr = card.value.toLowerCase();
    if (valueStr === 'a') valueStr = 'ace';
    if (valueStr === 'j') valueStr = 'jack';
    if (valueStr === 'q') valueStr = 'queen';
    if (valueStr === 'k') valueStr = 'king';

    return `/games/cards/${valueStr}_of_${suitMap[card.suit]}.png`;
  };

  // Get suit icon
  const getSuitIcon = (suit) => {
    switch (suit) {
      case SUITS.HEARTS:
        return <Heart className="h-4 w-4 text-red-500 fill-current" />;
      case SUITS.DIAMONDS:
        return <Diamond className="h-4 w-4 text-red-500 fill-current" />;
      case SUITS.CLUBS:
        return <Club className="h-4 w-4 text-black fill-current" />;
      case SUITS.SPADES:
        return <Spade className="h-4 w-4 text-black fill-current" />;
      default:
        return null;
    }
  };

  // Start the game
  const startGame = () => {
    // Create and shuffle deck
    const newDeck = shuffleDeck(createDeck());

    // Reset players
    const activePlayers = players.slice(0, config.numPlayers).map((player, index) => ({
      ...player,
      hand: [],
      score: 0,
      isWinner: false
    }));

    setPlayers(activePlayers);
    setDeck(newDeck);
    setDiscardPile([]);
    setCurrentPlayer(0);
    setDirection(DIRECTIONS.CLOCKWISE);
    setCurrentSuit(null);
    setMustDraw(0);
    setGameHistory([]);
    setGameResult(null);
    setSuitSelection(false);
    setSelectedCard(null);

    // Deal cards
    dealCards(newDeck, activePlayers);

    setGameStatus(GAME_STATUS.DEALING);
    addMessage("Game started! Cards are being dealt...", "system");
  };

  // Deal cards to players
  const dealCards = (deckToDeal, playersToServe) => {
    let currentDeck = [...deckToDeal];
    const updatedPlayers = [...playersToServe];

    // Deal cards to each player
    for (let round = 0; round < config.cardsPerPlayer; round++) {
      for (let playerIndex = 0; playerIndex < playersToServe.length; playerIndex++) {
        if (currentDeck.length > 0) {
          const card = currentDeck.pop();
          updatedPlayers[playerIndex].hand.push(card);
        }
      }
    }

    // Place first card on discard pile
    if (currentDeck.length > 0) {
      const firstCard = currentDeck.pop();
      setDiscardPile([firstCard]);
      setCurrentSuit(firstCard.suit);

      // If first card is an 8, let first player choose suit
      if (firstCard.value === '8') {
        setSuitSelection(true);
        setGameStatus(GAME_STATUS.CHOOSING_SUIT);
        addMessage("First card is an 8! Player 1, choose a suit.", "system");
      } else {
        setGameStatus(GAME_STATUS.PLAYING);
        setTimeout(() => startPlayerTurn(), 1000);
      }
    }

    setDeck(currentDeck);
    setPlayers(updatedPlayers);

    addMessage(`Cards dealt! Each player has ${config.cardsPerPlayer} cards.`, "system");
  };

  // Start a player's turn
  const startPlayerTurn = () => {
    setTimeLeft(config.timeLimit);

    if (timerRef.current) {
      clearInterval(timerRef.current);
    }

    const activePlayer = players[currentPlayer];
    addMessage(`${activePlayer.name}'s turn!`, "system");

    // Handle draw penalty
    if (mustDraw > 0) {
      drawCards(currentPlayer, mustDraw);
      setMustDraw(0);
      addMessage(`${activePlayer.name} drew ${mustDraw} cards due to Draw Two penalty.`, "system");
      setTimeout(() => nextPlayer(), 1500);
      return;
    }

    // Start timer
    timerRef.current = setInterval(() => {
      setTimeLeft(prev => {
        if (prev <= 1) {
          clearInterval(timerRef.current);
          handleTurnTimeout();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    // Handle AI turn
    if (activePlayer.isAI) {
      aiTimeoutRef.current = setTimeout(() => {
        playAITurn();
      }, 1000 + Math.random() * 2000); // Random delay for realism
    }
  };

  // Handle turn timeout
  const handleTurnTimeout = () => {
    const activePlayer = players[currentPlayer];
    addMessage(`${activePlayer.name} timed out and drew a card.`, "system");
    drawCards(currentPlayer, 1);
    nextPlayer();
  };

  // Move to next player
  const nextPlayer = () => {
    setCurrentPlayer(prev => {
      const nextIndex = direction === DIRECTIONS.CLOCKWISE
        ? (prev + 1) % config.numPlayers
        : (prev - 1 + config.numPlayers) % config.numPlayers;

      setTimeout(() => startPlayerTurn(), 500);
      return nextIndex;
    });
  };

  // Check if a card can be played
  const canPlayCard = (card) => {
    if (!discardPile.length) return false;

    const topCard = discardPile[discardPile.length - 1];

    // 8s can always be played
    if (card.value === '8') return true;

    // Match suit or value
    return card.suit === currentSuit || card.value === topCard.value;
  };

  return (
    <div className="h-screen bg-slate-950 text-white overflow-hidden">
      <div className="container mx-auto px-4 pt-20 pb-4 h-full flex flex-col">
        {/* Header - Fixed Height */}
        <div className="flex items-center justify-between mb-4 flex-shrink-0">
          <div className="flex items-center">
            {onBack && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onBack}
                className="h-8 w-8 p-0 mr-3"
              >
                <X className="h-4 w-4" />
              </Button>
            )}
            <div className="flex items-center">
              <div className="h-10 w-10 rounded bg-gradient-to-br from-red-500 to-red-600 flex items-center justify-center mr-3">
                <span className="text-xl">🃏</span>
              </div>
              <div>
                <h1 className="text-xl font-semibold">Crazy Eights</h1>
                <p className="text-sm text-slate-400">Match the suit or rank to win!</p>
              </div>
            </div>
          </div>

          <Badge className="bg-red-500 text-white px-3 py-1">
            <Trophy className="h-4 w-4 mr-1" />
            Card Game Challenge
          </Badge>
        </div>

        {/* Game Content - Flex 1 to fill remaining space */}
        <div className="grid grid-cols-12 gap-4 flex-1 min-h-0">
          {/* Left Sidebar - Game Info & Players */}
          <div className="col-span-3 space-y-4 h-full flex flex-col">
            {/* Game Info */}
            <div className="bg-slate-900 border border-slate-800 rounded-lg p-4 flex-shrink-0">
              <h3 className="text-sm font-semibold text-white mb-3">Game Status</h3>
              <div className="space-y-2 text-xs">
                <div className="flex justify-between">
                  <span className="text-slate-400">Players:</span>
                  <span className="text-white font-bold">{config.numPlayers}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-400">Wager:</span>
                  <span className="text-white font-bold">${config.wagerAmount}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-400">Cards Each:</span>
                  <span className="text-white">{config.cardsPerPlayer}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-400">Special Rules:</span>
                  <span className="text-white">{config.enableSpecialRules ? 'On' : 'Off'}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Main Game Area */}
          <div className="col-span-6 h-full">
            <div className="bg-slate-900 border border-slate-800 rounded-lg p-6 h-full flex flex-col">
              {gameStatus === GAME_STATUS.SETUP ? (
                <div className="text-center flex-1 flex flex-col justify-center">
                  <div className="text-6xl mb-4">🃏</div>
                  <h2 className="text-3xl font-bold text-white mb-2">Crazy Eights</h2>
                  <p className="text-lg text-red-500 mb-4">Classic Card Game</p>
                  <p className="text-slate-400 mb-8">
                    Match the suit or rank to play your cards!<br />
                    Use your 8s wisely to change the suit.
                  </p>

                  <Button
                    className="bg-gradient-to-r from-red-500 to-red-600 hover:from-red-400 hover:to-red-500 text-white font-bold px-8 py-3"
                    onClick={startGame}
                  >
                    <Play className="h-5 w-5 mr-2" />
                    Start Game
                  </Button>
                </div>
              ) : (
                <div className="text-center flex-1 flex flex-col justify-center">
                  <p className="text-slate-400">Game in progress...</p>
                </div>
              )}
            </div>
          </div>

          {/* Right Sidebar - Chat */}
          <div className="col-span-3 h-full flex flex-col space-y-4">
            {/* Live Chat */}
            <div className="bg-slate-900 border border-slate-800 rounded-lg p-4 flex-1 min-h-0 flex flex-col">
              <h3 className="text-sm font-semibold text-white mb-3 flex-shrink-0">Live Chat</h3>
              <div className="flex-1 overflow-y-auto mb-3 space-y-1 min-h-0">
                {messages.map((msg, index) => (
                  <div key={index} className="text-xs">
                    <span className={msg.type === 'system' ? 'text-red-400' : 'text-slate-300'}>
                      {msg.text}
                    </span>
                  </div>
                ))}
                <div ref={messageEndRef} />
              </div>
              <div className="flex gap-2 flex-shrink-0">
                <Input
                  value={messageInput}
                  onChange={(e) => setMessageInput(e.target.value)}
                  placeholder="Type a message..."
                  className="flex-1 h-8 text-xs bg-slate-800 border-slate-700"
                  onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
                />
                <Button size="sm" onClick={sendMessage} className="h-8 px-3">
                  <MessageSquare className="h-3 w-3" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CrazyEightsGame;
