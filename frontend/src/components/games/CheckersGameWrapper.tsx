import { useState, useEffect } from 'react';
import CheckersGame from './CheckersGame';
import MobileCheckersGame from './MobileCheckersGame';
import CheckersGameSetup from './CheckersGameSetup';
import type { GameData } from './gameTypes';
import type { GameConfig } from './CheckersGameSetup';

interface CheckersGameWrapperProps {
  game: GameData;
  onBack: () => void;
}

const CheckersGameWrapper: React.FC<CheckersGameWrapperProps> = ({ game, onBack }) => {
  const [showSetup, setShowSetup] = useState(!game.config);
  const [isMobile, setIsMobile] = useState(false);
  const [gameConfig, setGameConfig] = useState<GameConfig | null>(
    game.config 
      ? {
          isTimedGame: game.config.isTimedGame || false,
          timeLimit: game.config.timeLimit || 300,
          wagerAmount: game.prize / 2,
          maxPlayers: 2,
          gameMode: game.config.gameMode || 'classic'
        }
      : null
  );

  // Check if mobile on mount and resize
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768); // md breakpoint
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const handleStartGame = (config: GameConfig) => {
    // console.log('Starting game with config:', config);
    setGameConfig(config);
    setShowSetup(false);
  };

  const handleGameComplete = (winner: string, payout: number) => {
    // console.log(`Game completed. Winner: ${winner}, Payout: ${payout}`);
    // Handle game completion - update user balance, save to database, etc.
  };

  if (showSetup) {
    return (
      <CheckersGameSetup
        isOpen={true}
        onClose={onBack}
        onStartGame={handleStartGame}
      />
    );
  }

  // Use config from game data if available, otherwise use the setup config
  const config = game.config || gameConfig;
  
  // console.log('CheckersGameWrapper - showSetup:', showSetup);
  // console.log('CheckersGameWrapper - config:', config);
  // console.log('CheckersGameWrapper - game:', game);
  // console.log('CheckersGameWrapper - isMobile:', isMobile);

  // Use mobile version if on mobile device
  if (isMobile) {
    return (
      <MobileCheckersGame
        isTimedGame={config?.isTimedGame || false}
        timeLimit={config?.timeLimit || 300}
        wagerAmount={gameConfig?.wagerAmount || game.prize / 2}
        onGameComplete={handleGameComplete}
        onBack={onBack}
      />
    );
  }

  return (
    <CheckersGame
      isTimedGame={config?.isTimedGame || false}
      timeLimit={config?.timeLimit || 300}
      wagerAmount={gameConfig?.wagerAmount || game.prize / 2}
      onGameComplete={handleGameComplete}
      onBack={onBack}
    />
  );
};

export default CheckersGameWrapper;