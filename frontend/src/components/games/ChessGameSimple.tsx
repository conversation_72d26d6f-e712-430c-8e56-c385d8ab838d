import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Crown } from 'lucide-react';

interface ChessGameSimpleProps {
  gameId: string;
  onGameEnd?: () => void;
}

const ChessGameSimple: React.FC<ChessGameSimpleProps> = ({ gameId, onGameEnd }) => {
  const [status, setStatus] = useState<string>('ready');

  return (
    <div className="w-full h-screen bg-slate-950 text-white flex flex-col">
      {/* Top Bar */}
      <div className="h-16 bg-slate-900 border-b border-slate-800 flex items-center justify-between px-6">
        <div className="flex items-center space-x-4">
          <h1 className="text-xl font-bold flex items-center">
            <Crown className="h-6 w-6 text-yellow-500 mr-2" />
            Chess Arena
          </h1>
          <Badge variant="outline" className="bg-purple-500/20 text-purple-400 border-purple-500">
            READY
          </Badge>
        </div>
      </div>
      
      {/* Main Content */}
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl mb-4">Game Created!</h2>
          <p>Game ID: {gameId}</p>
          <p className="mt-4 text-gray-400">Waiting for opponent to join...</p>
          <Button 
            className="mt-6"
            onClick={() => {
              if (onGameEnd) onGameEnd();
            }}
          >
            Back to Games
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ChessGameSimple;