import { useState } from 'react';
import { <PERSON><PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Slider } from '@/components/ui/slider';
import { 
  Trophy, 
  Clock, 
  DollarSign, 
  Users, 
  Zap,
  Brain,
  Target,
  Keyboard,
  Hand,
  Star
} from 'lucide-react';

export interface WordJumbleGameConfig {
  gameMode: '1v1' | '1vN';
  maxPlayers: number;
  difficulty: 'easy' | 'medium' | 'hard';
  rounds: number;
  timePerRound: number;
  betAmount: number;
  inputMode: 'drag' | 'type';
  minWordLength: number;
}

interface WordJumbleGameSetupProps {
  isOpen: boolean;
  onClose: () => void;
  onStartGame: (config: WordJumbleGameConfig) => void;
}

const WordJumbleGameSetup: React.FC<WordJumbleGameSetupProps> = ({ isOpen, onClose, onStartGame }) => {
  const [config, setConfig] = useState<WordJumbleGameConfig>({
    gameMode: '1v1',
    maxPlayers: 4,
    difficulty: 'medium',
    rounds: 5,
    timePerRound: 60,
    betAmount: 50,
    inputMode: 'drag',
    minWordLength: 3
  });

  const handleStartGame = () => {
    onStartGame(config);
  };

  const getDifficultyInfo = (difficulty: string) => {
    switch (difficulty) {
      case 'easy':
        return { color: 'text-green-500', description: '3-letter words', example: 'CAT, DOG, SUN' };
      case 'medium':
        return { color: 'text-yellow-500', description: '4-letter words', example: 'FLOW, WORK, TIME' };
      case 'hard':
        return { color: 'text-red-500', description: '5+ letter words', example: 'COMPUTER, READING' };
      default:
        return { color: 'text-slate-400', description: '', example: '' };
    }
  };

  const difficultyInfo = getDifficultyInfo(config.difficulty);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl bg-slate-900 border-slate-800">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold text-white flex items-center">
            <Brain className="h-8 w-8 mr-3 text-purple-500" />
            Word Jumble Setup
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6 mt-6">
          {/* Game Mode Selection */}
          <div>
            <h3 className="text-lg font-semibold text-white mb-3 flex items-center">
              <Users className="h-5 w-5 mr-2" />
              Game Mode
            </h3>
            <div className="grid grid-cols-2 gap-4">
              <button
                onClick={() => setConfig({ ...config, gameMode: '1v1' })}
                className={`p-4 rounded-lg border-2 transition-all ${
                  config.gameMode === '1v1'
                    ? 'bg-purple-900/30 border-purple-500'
                    : 'bg-slate-800 border-slate-700 hover:border-slate-600'
                }`}
              >
                <div className="text-white font-semibold mb-1">1v1 Mode</div>
                <div className="text-sm text-slate-400">Head-to-head competition</div>
              </button>
              
              <button
                onClick={() => setConfig({ ...config, gameMode: '1vN' })}
                className={`p-4 rounded-lg border-2 transition-all ${
                  config.gameMode === '1vN'
                    ? 'bg-purple-900/30 border-purple-500'
                    : 'bg-slate-800 border-slate-700 hover:border-slate-600'
                }`}
              >
                <div className="text-white font-semibold mb-1">Multiplayer</div>
                <div className="text-sm text-slate-400">Up to 8 players</div>
              </button>
            </div>

            {config.gameMode === '1vN' && (
              <div className="mt-4">
                <label className="text-sm text-slate-400 block mb-2">Max Players: {config.maxPlayers}</label>
                <Slider
                  value={[config.maxPlayers]}
                  onValueChange={([value]) => setConfig({ ...config, maxPlayers: value })}
                  min={3}
                  max={8}
                  step={1}
                  className="w-full"
                />
              </div>
            )}
          </div>

          {/* Difficulty Selection */}
          <div>
            <h3 className="text-lg font-semibold text-white mb-3 flex items-center">
              <Star className="h-5 w-5 mr-2" />
              Difficulty
            </h3>
            <div className="grid grid-cols-3 gap-3">
              {(['easy', 'medium', 'hard'] as const).map(diff => (
                <button
                  key={diff}
                  onClick={() => setConfig({ ...config, difficulty: diff })}
                  className={`p-4 rounded-lg border-2 transition-all ${
                    config.difficulty === diff
                      ? 'bg-purple-900/30 border-purple-500'
                      : 'bg-slate-800 border-slate-700 hover:border-slate-600'
                  }`}
                >
                  <div className={`font-semibold mb-1 capitalize ${getDifficultyInfo(diff).color}`}>
                    {diff}
                  </div>
                  <div className="text-xs text-slate-400">{getDifficultyInfo(diff).description}</div>
                </button>
              ))}
            </div>
            {difficultyInfo.example && (
              <div className="mt-2 text-xs text-slate-400">
                Example: {difficultyInfo.example}
              </div>
            )}
          </div>

          {/* Rounds and Time */}
          <div className="grid grid-cols-2 gap-6">
            <div>
              <h3 className="text-lg font-semibold text-white mb-3 flex items-center">
                <Trophy className="h-5 w-5 mr-2" />
                Rounds
              </h3>
              <div className="grid grid-cols-4 gap-2">
                {[3, 5, 7, 10].map(rounds => (
                  <Button
                    key={rounds}
                    variant={config.rounds === rounds ? "default" : "outline"}
                    size="sm"
                    onClick={() => setConfig({ ...config, rounds })}
                  >
                    {rounds}
                  </Button>
                ))}
              </div>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-white mb-3 flex items-center">
                <Clock className="h-5 w-5 mr-2" />
                Time per Round
              </h3>
              <div className="grid grid-cols-4 gap-2">
                {[30, 60, 90, 120].map(time => (
                  <Button
                    key={time}
                    variant={config.timePerRound === time ? "default" : "outline"}
                    size="sm"
                    onClick={() => setConfig({ ...config, timePerRound: time })}
                  >
                    {time}s
                  </Button>
                ))}
              </div>
            </div>
          </div>

          {/* Input Mode */}
          <div>
            <h3 className="text-lg font-semibold text-white mb-3 flex items-center">
              <Target className="h-5 w-5 mr-2" />
              Input Mode
            </h3>
            <div className="grid grid-cols-2 gap-4">
              <button
                onClick={() => setConfig({ ...config, inputMode: 'drag' })}
                className={`p-4 rounded-lg border-2 transition-all ${
                  config.inputMode === 'drag'
                    ? 'bg-purple-900/30 border-purple-500'
                    : 'bg-slate-800 border-slate-700 hover:border-slate-600'
                }`}
              >
                <Hand className="h-6 w-6 mb-2 mx-auto text-purple-400" />
                <div className="text-white font-semibold mb-1">Drag & Drop</div>
                <div className="text-sm text-slate-400">Move letters with mouse/touch</div>
              </button>
              
              <button
                onClick={() => setConfig({ ...config, inputMode: 'type' })}
                className={`p-4 rounded-lg border-2 transition-all ${
                  config.inputMode === 'type'
                    ? 'bg-purple-900/30 border-purple-500'
                    : 'bg-slate-800 border-slate-700 hover:border-slate-600'
                }`}
              >
                <Keyboard className="h-6 w-6 mb-2 mx-auto text-purple-400" />
                <div className="text-white font-semibold mb-1">Keyboard</div>
                <div className="text-sm text-slate-400">Type words directly</div>
              </button>
            </div>
          </div>

          {/* Bet Amount */}
          <div>
            <h3 className="text-lg font-semibold text-white mb-3 flex items-center">
              <DollarSign className="h-5 w-5 mr-2" />
              Wager Amount
            </h3>
            <div className="grid grid-cols-4 gap-3">
              {[25, 50, 100, 200].map(amount => (
                <Button
                  key={amount}
                  variant={config.betAmount === amount ? "default" : "outline"}
                  onClick={() => setConfig({ ...config, betAmount: amount })}
                  className="relative"
                >
                  ${amount}
                  {amount === 50 && (
                    <Badge className="absolute -top-2 -right-2 h-5 text-xs bg-green-500">
                      Popular
                    </Badge>
                  )}
                </Button>
              ))}
            </div>
          </div>

          {/* Game Summary */}
          <div className="bg-slate-800 rounded-lg p-4">
            <h4 className="text-white font-semibold mb-2">Game Summary</h4>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div className="text-slate-400">Mode:</div>
              <div className="text-white">{config.gameMode === '1v1' ? '1v1' : `1v${config.maxPlayers}`}</div>
              
              <div className="text-slate-400">Difficulty:</div>
              <div className={`capitalize ${difficultyInfo.color}`}>{config.difficulty}</div>
              
              <div className="text-slate-400">Duration:</div>
              <div className="text-white">{config.rounds} rounds × {config.timePerRound}s</div>
              
              <div className="text-slate-400">Total Wager:</div>
              <div className="text-green-500 font-bold">${config.betAmount}</div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3">
            <Button variant="outline" onClick={onClose} className="flex-1">
              Cancel
            </Button>
            <Button 
              onClick={handleStartGame}
              className="flex-1 bg-gradient-to-r from-purple-500 to-pink-500"
            >
              <Zap className="h-4 w-4 mr-2" />
              Start Word Challenge
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default WordJumbleGameSetup;