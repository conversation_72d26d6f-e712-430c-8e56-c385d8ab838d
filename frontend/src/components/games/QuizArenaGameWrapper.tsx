import React, { useState } from 'react';
import { type GameData } from './gameTypes';
import QuizArenaGame from './QuizArenaGame';

interface QuizArenaGameWrapperProps {
  game: GameData;
  onBack: () => void;
}

const QuizArenaGameWrapper: React.FC<QuizArenaGameWrapperProps> = ({ game, onBack }) => {
  const [gameResult, setGameResult] = useState(null);

  const handleGameEnd = (result: any) => {
    setGameResult(result);
    // Handle game end logic here
    console.log('Quiz Arena game ended:', result);
  };

  return (
    <QuizArenaGame
      onBack={onBack}
      onGameEnd={handleGameEnd}
    />
  );
};

export default QuizArenaGameWrapper;
