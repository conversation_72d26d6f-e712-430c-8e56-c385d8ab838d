import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Clock, Play, CheckCircle, Calendar, Users, Eye } from 'lucide-react';

export type GameInstanceStatus = 'live' | 'scheduled' | 'completed' | 'waiting' | 'starting';

interface GameStatusBadgeProps {
  status: GameInstanceStatus;
  size?: 'sm' | 'md' | 'lg';
  showIcon?: boolean;
  className?: string;
}

const GameStatusBadge: React.FC<GameStatusBadgeProps> = ({ 
  status, 
  size = 'md', 
  showIcon = true,
  className = '' 
}) => {
  const getStatusConfig = (status: GameInstanceStatus) => {
    switch (status) {
      case 'live':
        return {
          label: '🔴 LIVE',
          icon: <Play className="h-3 w-3" />,
          className: 'bg-red-500/20 text-red-400 border-red-500/30',
          pulse: true
        };
      case 'scheduled':
        return {
          label: '📅 SCHEDULED',
          icon: <Calendar className="h-3 w-3" />,
          className: 'bg-blue-500/20 text-blue-400 border-blue-500/30',
          pulse: false
        };
      case 'completed':
        return {
          label: '✅ COMPLETED',
          icon: <CheckCircle className="h-3 w-3" />,
          className: 'bg-gray-500/20 text-gray-400 border-gray-500/30',
          pulse: false
        };
      case 'waiting':
        return {
          label: '⏳ WAITING',
          icon: <Users className="h-3 w-3" />,
          className: 'bg-green-500/20 text-green-400 border-green-500/30',
          pulse: false
        };
      case 'starting':
        return {
          label: '🚀 STARTING',
          icon: <Clock className="h-3 w-3" />,
          className: 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30',
          pulse: true
        };
      default:
        return {
          label: status.toUpperCase(),
          icon: <Eye className="h-3 w-3" />,
          className: 'bg-slate-500/20 text-slate-400 border-slate-500/30',
          pulse: false
        };
    }
  };

  const config = getStatusConfig(status);
  
  const sizeClasses = {
    sm: 'text-xs px-1.5 py-0.5',
    md: 'text-xs px-2 py-1',
    lg: 'text-sm px-3 py-1.5'
  };

  return (
    <Badge 
      className={`
        ${config.className} 
        ${sizeClasses[size]} 
        ${config.pulse ? 'animate-pulse' : ''} 
        border font-medium
        ${className}
      `}
    >
      <div className="flex items-center gap-1">
        {showIcon && config.icon}
        <span>{config.label}</span>
      </div>
    </Badge>
  );
};

export default GameStatusBadge;
