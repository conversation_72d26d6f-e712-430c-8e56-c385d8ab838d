import React, { useState } from 'react';
import { Dialog } from '@/components/ui/dialog';
import { DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Clock, DollarSign, Users } from 'lucide-react';

export interface ChessGameConfig {
  timeControl: 'Bullet' | 'Blitz' | 'Rapid' | 'Classical';
  wagerAmount: number;
  isRated: boolean;
}

interface ChessGameSetupProps {
  isOpen: boolean;
  onClose: () => void;
  onStartGame: (config: ChessGameConfig) => void;
}

const ChessGameSetup: React.FC<ChessGameSetupProps> = ({ isOpen, onClose, onStartGame }) => {
  const [timeControl, setTimeControl] = useState<'Bullet' | 'Blitz' | 'Rapid' | 'Classical'>('Rapid');
  const [wagerAmount, setWagerAmount] = useState(10);
  const [isRated, setIsRated] = useState(true);

  const handleStartGame = () => {
    const config: ChessGameConfig = {
      timeControl,
      wagerAmount,
      isRated
    };
    onStartGame(config);
  };

  const TIME_CONTROLS = {
    'Bullet': { time: '1 min', increment: '1 sec' },
    'Blitz': { time: '3 min', increment: '2 sec' },
    'Rapid': { time: '10 min', increment: '10 sec' },
    'Classical': { time: '30 min', increment: '30 sec' }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Users className="h-5 w-5 mr-2" />
            New Chess Game
          </DialogTitle>
        </DialogHeader>
        
        <div className="grid gap-4 py-4">
          {/* Time Control */}
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="timeControl" className="text-right">
              Time Control
            </Label>
            <div className="col-span-3">
              <Select
                value={timeControl}
                onValueChange={(value) => setTimeControl(value as typeof timeControl)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(TIME_CONTROLS).map(([key, value]) => (
                    <SelectItem key={key} value={key}>
                      <div className="flex items-center">
                        <Clock className="h-4 w-4 mr-2" />
                        <span>{key}</span>
                        <span className="text-sm text-gray-500 ml-2">
                          ({value.time} + {value.increment})
                        </span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Wager Amount */}
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="wager" className="text-right">
              Wager
            </Label>
            <div className="col-span-3">
              <div className="relative">
                <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500" />
                <Input
                  id="wager"
                  type="number"
                  value={wagerAmount}
                  onChange={(e) => setWagerAmount(Number(e.target.value))}
                  className="pl-8"
                  min="5"
                  max="500"
                  step="5"
                />
              </div>
            </div>
          </div>

          {/* Rating */}
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="rated" className="text-right">
              Rated
            </Label>
            <div className="col-span-3">
              <input
                type="checkbox"
                id="rated"
                checked={isRated}
                onChange={(e) => setIsRated(e.target.checked)}
                className="h-5 w-5"
              />
            </div>
          </div>
        </div>

        <div className="flex justify-end space-x-2">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleStartGame}>
            Start Game
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ChessGameSetup;