import React, { useState } from 'react';
import { But<PERSON> } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { RadioGroup, RadioGroupItem } from '../ui/radio-group';
import { Slider } from '../ui/slider';
import { Badge } from '../ui/badge';
import { Brain, Clock, Trophy, Users, Sparkles, Target, Eye, Timer } from 'lucide-react';

export interface GameSettings {
  betAmount: number;
  category: string;
  maxPlayers: number;
  rounds: number;
  timeLimit: number;
  difficulty: 'easy' | 'medium' | 'hard';
  hintsEnabled: boolean;
  privateGame: boolean;
  roomCode?: string;
}

interface BlurDetectiveGameSetupProps {
  onStartGame: (settings: GameSettings) => void;
  onCancel: () => void;
  minBet?: number;
  maxBet?: number;
}

const CATEGORIES = [
  {
    id: 'actors',
    name: 'Hollywood Stars',
    icon: '🎬',
    description: 'Famous actors and actresses',
    color: 'from-purple-500 to-pink-600'
  },
  {
    id: 'musicians',
    name: 'Music Icons',
    icon: '🎵',
    description: 'Popular singers and bands',
    color: 'from-blue-500 to-purple-600'
  },
  {
    id: 'athletes',
    name: 'Sports Legends',
    icon: '⚽',
    description: 'Famous athletes and sports stars',
    color: 'from-green-500 to-blue-600'
  },
  {
    id: 'mixed',
    name: 'All Stars',
    icon: '🌟',
    description: 'Mix of all categories',
    color: 'from-yellow-500 to-red-600'
  }
];

const DIFFICULTY_SETTINGS = {
  easy: {
    name: 'Easy',
    icon: '😊',
    blurStart: 20,
    blurDecrease: 5,
    guesses: 5,
    timeBonus: 50,
    color: 'text-green-600'
  },
  medium: {
    name: 'Medium',
    icon: '🤔',
    blurStart: 35,
    blurDecrease: 3,
    guesses: 3,
    timeBonus: 100,
    color: 'text-yellow-600'
  },
  hard: {
    name: 'Hard',
    icon: '🔥',
    blurStart: 50,
    blurDecrease: 2,
    guesses: 2,
    timeBonus: 200,
    color: 'text-red-600'
  }
};

export default function BlurDetectiveGameSetup({
  onStartGame,
  onCancel,
  minBet = 10,
  maxBet = 10000
}: BlurDetectiveGameSetupProps) {
  const [betAmount, setBetAmount] = useState(100);
  const [category, setCategory] = useState('mixed');
  const [maxPlayers, setMaxPlayers] = useState(4);
  const [rounds, setRounds] = useState(5);
  const [timeLimit, setTimeLimit] = useState(60);
  const [difficulty, setDifficulty] = useState<'easy' | 'medium' | 'hard'>('medium');
  const [hintsEnabled, setHintsEnabled] = useState(true);
  const [privateGame, setPrivateGame] = useState(false);
  const [roomCode, setRoomCode] = useState('');

  const selectedCategory = CATEGORIES.find(c => c.id === category);
  const difficultySettings = DIFFICULTY_SETTINGS[difficulty];

  const handleStartGame = () => {
    onStartGame({
      betAmount,
      category,
      maxPlayers,
      rounds,
      timeLimit,
      difficulty,
      hintsEnabled,
      privateGame,
      roomCode: privateGame ? roomCode : undefined
    });
  };

  const estimatedPrize = betAmount * maxPlayers * 0.8;
  const estimatedDuration = rounds * (timeLimit + 20); // Include buffer time

  return (
    <div className="max-w-2xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="w-6 h-6" />
            Blur Detective Setup
          </CardTitle>
          <CardDescription>
            Guess the celebrity from increasingly clear images. Faster guesses earn more points!
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Category Selection */}
          <div className="space-y-3">
            <Label className="text-base">Choose Category</Label>
            <div className="grid grid-cols-2 gap-3">
              {CATEGORIES.map((cat) => (
                <Card
                  key={cat.id}
                  className={`cursor-pointer transition-all ${
                    category === cat.id ? 'ring-2 ring-primary' : ''
                  }`}
                  onClick={() => setCategory(cat.id)}
                >
                  <CardContent className="p-4">
                    <div className={`rounded-lg p-3 bg-gradient-to-br ${cat.color} text-white mb-2`}>
                      <div className="text-2xl text-center">{cat.icon}</div>
                    </div>
                    <h4 className="font-semibold">{cat.name}</h4>
                    <p className="text-sm text-muted-foreground">{cat.description}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Difficulty Selection */}
          <div className="space-y-3">
            <Label className="text-base">Select Difficulty</Label>
            <RadioGroup value={difficulty} onValueChange={(v) => setDifficulty(v as 'easy' | 'medium' | 'hard')}>
              <div className="grid grid-cols-3 gap-3">
                {Object.entries(DIFFICULTY_SETTINGS).map(([key, settings]) => (
                  <div key={key} className="relative">
                    <RadioGroupItem value={key} id={key} className="peer sr-only" />
                    <Label
                      htmlFor={key}
                      className={`block cursor-pointer rounded-lg border-2 p-4 hover:bg-accent peer-checked:border-primary peer-checked:bg-primary/10 transition-all`}
                    >
                      <div className="text-center space-y-2">
                        <div className="text-3xl">{settings.icon}</div>
                        <div className={`font-semibold ${settings.color}`}>{settings.name}</div>
                        <div className="text-xs text-muted-foreground space-y-1">
                          <div>Blur: {settings.blurStart}px</div>
                          <div>Guesses: {settings.guesses}</div>
                          <div>Bonus: +{settings.timeBonus}</div>
                        </div>
                      </div>
                    </Label>
                  </div>
                ))}
              </div>
            </RadioGroup>
          </div>

          {/* Bet Amount */}
          <div className="space-y-3">
            <Label htmlFor="bet" className="text-base">
              Bet Amount: ₹{betAmount}
            </Label>
            <Slider
              id="bet"
              min={minBet}
              max={maxBet}
              step={10}
              value={[betAmount]}
              onValueChange={(v) => setBetAmount(v[0])}
              className="w-full"
            />
            <div className="flex justify-between text-sm text-muted-foreground">
              <span>₹{minBet}</span>
              <span>₹{maxBet}</span>
            </div>
          </div>

          {/* Game Settings */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="players" className="text-sm">
                Max Players: {maxPlayers}
              </Label>
              <Slider
                id="players"
                min={2}
                max={8}
                step={1}
                value={[maxPlayers]}
                onValueChange={(v) => setMaxPlayers(v[0])}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="rounds" className="text-sm">
                Rounds: {rounds}
              </Label>
              <Slider
                id="rounds"
                min={3}
                max={10}
                step={1}
                value={[rounds]}
                onValueChange={(v) => setRounds(v[0])}
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="time" className="text-sm flex items-center gap-2">
              <Timer className="w-4 h-4" />
              Time per Round: {timeLimit}s
            </Label>
            <Slider
              id="time"
              min={30}
              max={120}
              step={10}
              value={[timeLimit]}
              onValueChange={(v) => setTimeLimit(v[0])}
            />
          </div>

          {/* Additional Options */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label htmlFor="hints" className="text-sm flex items-center gap-2">
                <Sparkles className="w-4 h-4" />
                Enable Hints
              </Label>
              <input
                type="checkbox"
                id="hints"
                checked={hintsEnabled}
                onChange={(e) => setHintsEnabled(e.target.checked)}
                className="w-4 h-4"
              />
            </div>
            <div className="flex items-center justify-between">
              <Label htmlFor="private" className="text-sm">
                Private Game
              </Label>
              <input
                type="checkbox"
                id="private"
                checked={privateGame}
                onChange={(e) => setPrivateGame(e.target.checked)}
                className="w-4 h-4"
              />
            </div>
            {privateGame && (
              <Input
                placeholder="Enter room code"
                value={roomCode}
                onChange={(e) => setRoomCode(e.target.value)}
              />
            )}
          </div>

          {/* Game Summary */}
          <Card className="bg-muted/50">
            <CardContent className="p-4 space-y-3">
              <h4 className="font-semibold flex items-center gap-2">
                <Target className="w-5 h-5" />
                Game Summary
              </h4>
              <div className="grid grid-cols-2 gap-3 text-sm">
                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground">Category:</span>
                  <Badge variant="outline">
                    {selectedCategory?.icon} {selectedCategory?.name}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground">Difficulty:</span>
                  <Badge variant="outline" className={difficultySettings.color}>
                    {difficultySettings.icon} {difficultySettings.name}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground">Prize Pool:</span>
                  <Badge variant="secondary">₹{estimatedPrize}</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground">Duration:</span>
                  <Badge variant="secondary">~{Math.ceil(estimatedDuration / 60)} min</Badge>
                </div>
              </div>
              <div className="pt-2 space-y-2 text-xs text-muted-foreground">
                <p className="flex items-center gap-1">
                  <Eye className="w-3 h-3" />
                  Images start at {difficultySettings.blurStart}px blur
                </p>
                <p className="flex items-center gap-1">
                  <Brain className="w-3 h-3" />
                  {difficultySettings.guesses} guesses per round
                </p>
                <p className="flex items-center gap-1">
                  <Clock className="w-3 h-3" />
                  +{difficultySettings.timeBonus} points for fast answers
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <div className="flex gap-3">
            <Button variant="outline" onClick={onCancel} className="flex-1">
              Cancel
            </Button>
            <Button onClick={handleStartGame} className="flex-1">
              <Trophy className="w-4 h-4 mr-2" />
              Create Game (₹{betAmount})
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}