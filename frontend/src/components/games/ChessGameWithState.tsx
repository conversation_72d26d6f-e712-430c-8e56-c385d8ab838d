import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useParams } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { useGameStateStore } from '@/stores/gameStateStore';
import useAuth from '@/hooks/useAuth';
import { useWebSocket } from '@/hooks/useGameWebSocket';
import { 
  Clock, 
  MessageSquare,
  Users,
  Crown,
  AlertCircle,
  RotateCcw,
  ChevronDown,
  X,
  Send,
  ArrowLeft
} from 'lucide-react';

// Chess constants
const FILES = ['a', 'b', 'c', 'd', 'e', 'f', 'g', 'h'];
const RANKS = ['8', '7', '6', '5', '4', '3', '2', '1'];

// Initial board setup
const INITIAL_BOARD = [
  ['r', 'n', 'b', 'q', 'k', 'b', 'n', 'r'], // Black pieces
  ['p', 'p', 'p', 'p', 'p', 'p', 'p', 'p'], // Black pawns
  ['.', '.', '.', '.', '.', '.', '.', '.'], // Empty squares
  ['.', '.', '.', '.', '.', '.', '.', '.'],
  ['.', '.', '.', '.', '.', '.', '.', '.'],
  ['.', '.', '.', '.', '.', '.', '.', '.'],
  ['P', 'P', 'P', 'P', 'P', 'P', 'P', 'P'], // White pawns
  ['R', 'N', 'B', 'Q', 'K', 'B', 'N', 'R'], // White pieces
];

// Piece images mapping
const PIECE_IMAGES = {
  'K': '/images/chess/white-king.png',
  'Q': '/images/chess/white-queen.png',
  'R': '/images/chess/white-rook.png',
  'B': '/images/chess/white-bishop.png',
  'N': '/images/chess/white-knight.png',
  'P': '/images/chess/white-pawn.png',
  'k': '/images/chess/black-king.png',
  'q': '/images/chess/black-queen.png',
  'r': '/images/chess/black-rook.png',
  'b': '/images/chess/black-bishop.png',
  'n': '/images/chess/black-knight.png',
  'p': '/images/chess/black-pawn.png',
};

interface ChessGameWithStateProps {
  isTimedGame?: boolean;
  timeLimit?: number; // in seconds
  wagerAmount?: number;
  onGameEnd?: () => void;
  onBack?: () => void;
}

const ChessGameWithState: React.FC<ChessGameWithStateProps> = ({ 
  isTimedGame = false,
  timeLimit = 600, // 10 minutes default
  wagerAmount = 100,
  onGameEnd, 
  onBack 
}) => {
  const { gameId } = useParams<{ gameId: string }>();
  const { user } = useAuth();
  const { 
    currentGame, 
    loadGame, 
    makeMove: makeGameMove,
    updateGameFromWebSocket 
  } = useGameStateStore();

  // Local state for UI
  const [selectedSquare, setSelectedSquare] = useState<number | null>(null);
  const [validMoves, setValidMoves] = useState<number[]>([]);
  const [boardState, setBoardState] = useState(INITIAL_BOARD);
  const [isPromoting, setIsPromoting] = useState(false);
  const [promotionSquare, setPromotionSquare] = useState<number | null>(null);
  const [gameTime, setGameTime] = useState(0);
  const [showChat, setShowChat] = useState(true);
  const [showRules, setShowRules] = useState(false);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  
  // Chat state
  const [messages, setMessages] = useState<any[]>([
    { text: "Game started! White player goes first.", type: "system" },
    { text: "Welcome to Chess Arena!", type: "system" }
  ]);
  const [messageInput, setMessageInput] = useState('');

  // WebSocket connection for real-time updates
  const { sendMessage: sendWebSocketMessage } = useWebSocket(gameId || '', {
    onMessage: (data) => {
      if (data.type === 'game_update') {
        updateGameFromWebSocket(data.game);
      } else if (data.type === 'chat_message') {
        addMessage(data.message, 'user');
      }
    }
  });

  // Load game on mount
  useEffect(() => {
    if (gameId) {
      loadGame(gameId);
    }
  }, [gameId, loadGame]);

  // Update board state from game state
  useEffect(() => {
    if (currentGame?.state?.board) {
      setBoardState(currentGame.state.board);
    }
  }, [currentGame]);

  // Initialize game timer
  useEffect(() => {
    const interval = setInterval(() => {
      setGameTime(prev => prev + 1);
    }, 1000);
    
    intervalRef.current = interval;
    return () => {
      if (intervalRef.current) clearInterval(intervalRef.current);
    };
  }, []);

  // Derive game state from currentGame
  const isWhitePlayer = currentGame?.players?.find(p => p.user_id === user?.id)?.position === 0;
  const currentTurn = currentGame?.state?.current_turn || 'white';
  const gameStatus = currentGame?.status || 'waiting';
  const whiteTime = currentGame?.state?.white_time || timeLimit;
  const blackTime = currentGame?.state?.black_time || timeLimit;
  const capturedPieces = currentGame?.state?.captured_pieces || { white: [], black: [] };
  const moveHistory = currentGame?.moves || [];

  // Format time display
  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds < 10 ? '0' : ''}${remainingSeconds}`;
  };
  
  // Add message to chat
  const addMessage = (text: string, type: 'user' | 'system' = 'system') => {
    setMessages(prev => [...prev, { text, type }]);
  };
  
  // Send chat message
  const sendMessage = () => {
    if (messageInput.trim()) {
      sendWebSocketMessage({
        type: 'chat_message',
        message: messageInput
      });
      addMessage(`${user?.username}: ${messageInput}`, 'user');
      setMessageInput('');
    }
  };

  // Utility function to check if a piece is white
  const isWhitePiece = (piece: string): boolean => {
    return piece === piece.toUpperCase() && piece !== '.';
  };

  // Get the piece at a given index
  const getPiece = (board: string[][], index: number): string => {
    const rank = Math.floor(index / 8);
    const file = index % 8;
    return board[rank]?.[file] || '.';
  };

  // Check if it's the player's turn
  const isMyTurn = (): boolean => {
    if (!currentGame || currentGame.status !== 'active') return false;
    const myPosition = currentGame.players.find(p => p.user_id === user?.id)?.position;
    if (myPosition === undefined) return false;
    
    const isWhite = myPosition === 0;
    return (currentTurn === 'white' && isWhite) || (currentTurn === 'black' && !isWhite);
  };

  // Calculate valid moves for a piece
  const calculateValidMoves = (rank: number, file: number): number[] => {
    // This is a simplified version - in production, you'd want full chess logic
    const piece = boardState[rank][file];
    const isWhite = isWhitePiece(piece);
    const pieceType = piece.toUpperCase();
    
    const moves: number[] = [];
    
    // Add basic move validation based on piece type
    switch (pieceType) {
      case 'P':
        // Pawn moves
        const direction = isWhite ? -1 : 1;
        const oneStep = (rank + direction) * 8 + file;
        if (getPiece(boardState, oneStep) === '.') {
          moves.push(oneStep);
        }
        break;
      case 'N':
        // Knight moves
        const knightMoves = [
          [-2, -1], [-2, 1], [-1, -2], [-1, 2],
          [1, -2], [1, 2], [2, -1], [2, 1]
        ];
        for (const [dr, df] of knightMoves) {
          const newRank = rank + dr;
          const newFile = file + df;
          if (newRank >= 0 && newRank < 8 && newFile >= 0 && newFile < 8) {
            const targetIndex = newRank * 8 + newFile;
            const targetPiece = getPiece(boardState, targetIndex);
            if (targetPiece === '.' || isWhitePiece(targetPiece) !== isWhite) {
              moves.push(targetIndex);
            }
          }
        }
        break;
      // Add other piece types...
    }
    
    return moves;
  };

  const handleSquareClick = async (index: number) => {
    if (!isMyTurn()) return;
    
    const piece = getPiece(boardState, index);
    const rank = Math.floor(index / 8);
    const file = index % 8;
    
    if (selectedSquare === null) {
      // Select a piece
      if (piece !== '.' && 
          ((currentTurn === 'white' && isWhitePiece(piece)) || 
           (currentTurn === 'black' && !isWhitePiece(piece)))) {
        setSelectedSquare(index);
        const moves = calculateValidMoves(rank, file);
        setValidMoves(moves);
      }
    } else {
      // Move selected piece
      if (validMoves.includes(index)) {
        // Check if this is a pawn promotion
        const movingPiece = boardState[Math.floor(selectedSquare / 8)][selectedSquare % 8];
        const toRank = Math.floor(index / 8);
        
        if (movingPiece.toUpperCase() === 'P' && (toRank === 0 || toRank === 7)) {
          setPromotionSquare(index);
          setIsPromoting(true);
        } else {
          // Make the move
          await makeMove(selectedSquare, index);
        }
      } else if (index === selectedSquare) {
        // Deselect
        setSelectedSquare(null);
        setValidMoves([]);
      } else if (piece !== '.' && 
                ((currentTurn === 'white' && isWhitePiece(piece)) || 
                 (currentTurn === 'black' && !isWhitePiece(piece)))) {
        // Select a different piece
        setSelectedSquare(index);
        const moves = calculateValidMoves(rank, file);
        setValidMoves(moves);
      }
    }
  };

  const makeMove = async (from: number, to: number, promotion?: string) => {
    if (!gameId) return;
    
    const moveData = {
      from,
      to,
      promotion
    };
    
    await makeGameMove(gameId, moveData);
    
    // Clear selection
    setSelectedSquare(null);
    setValidMoves([]);
  };

  const handlePromotion = async (pieceType: string) => {
    if (promotionSquare !== null && selectedSquare !== null) {
      await makeMove(selectedSquare, promotionSquare, pieceType);
      setIsPromoting(false);
      setPromotionSquare(null);
    }
  };

  const renderSquare = (index: number) => {
    const piece = getPiece(boardState, index);
    const rank = Math.floor(index / 8);
    const file = index % 8;
    const isLight = (rank + file) % 2 === 0;
    const isSelected = selectedSquare === index;
    const isValidMove = validMoves.includes(index);
    const isLastMove = moveHistory.length > 0 && 
      (moveHistory[moveHistory.length - 1].move_data?.from === index || 
       moveHistory[moveHistory.length - 1].move_data?.to === index);
    
    // Adjust square index for board orientation
    const displayIndex = isWhitePlayer ? index : 63 - index;
    
    return (
      <div
        key={displayIndex}
        onClick={() => handleSquareClick(index)}
        className={`
          relative flex items-center justify-center cursor-pointer aspect-square
          ${isLight ? 'bg-amber-100' : 'bg-amber-900'}
          ${isSelected ? 'ring-4 ring-yellow-400' : ''}
          ${isLastMove ? 'bg-yellow-400' : ''}
          ${isValidMove ? 'after:absolute after:w-4 after:h-4 after:bg-green-500 after:rounded-full after:opacity-60' : ''}
          ${validMoves.includes(index) && piece !== '.' ? 'ring-2 ring-red-500' : ''}
          hover:brightness-110 transition-all
        `}
      >
        {piece !== '.' && (
          <img 
            src={PIECE_IMAGES[piece as keyof typeof PIECE_IMAGES]}
            alt={piece}
            className="w-[70%] h-[70%] object-contain select-none absolute"
          />
        )}
        
        {/* Square coordinates */}
        {file === 0 && (
          <span className="absolute left-1 top-1 text-xs font-semibold text-gray-700">
            {isWhitePlayer ? RANKS[rank] : RANKS[7 - rank]}
          </span>
        )}
        {rank === 7 && (
          <span className="absolute right-1 bottom-1 text-xs font-semibold text-gray-700">
            {isWhitePlayer ? FILES[file] : FILES[7 - file]}
          </span>
        )}
      </div>
    );
  };

  // Render the game board
  const renderBoard = () => {
    return (
      <div className="aspect-square bg-gray-800 border-2 border-gray-600 rounded-lg">
        <div className="grid grid-cols-8 gap-0 w-full h-full">
          {Array.from({ length: 64 }).map((_, index) => renderSquare(index))}
        </div>
      </div>
    );
  };

  if (!currentGame) {
    return <div className="w-full min-h-screen bg-slate-950 flex items-center justify-center">
      <div className="text-white">Loading game...</div>
    </div>;
  }

  return (
    <div className="w-full min-h-screen bg-slate-950 flex flex-col pt-16">
      <style>{`
        /* Custom scrollbar styles */
        .custom-scrollbar::-webkit-scrollbar {
          width: 6px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
          background: rgba(51, 65, 85, 0.3);
          border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
          background: rgba(100, 116, 139, 0.5);
          border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
          background: rgba(100, 116, 139, 0.7);
        }
      `}</style>
      
      {/* Main content area with padding to ensure visibility above footer */}
      <div className="flex-1 p-2 pb-16 overflow-hidden">
        <div className="grid grid-cols-12 gap-2 h-full">
          {/* Left Panel - Game Info */}
          <div className="col-span-3 flex flex-col gap-2 h-full overflow-hidden">
            <div className="flex-1 overflow-y-auto custom-scrollbar pr-1 space-y-2">
              {/* Game Status */}
              <div className="bg-slate-900 border border-slate-800 rounded-sm p-3">
                <div className="flex justify-between items-center mb-3">
                  <h3 className="text-sm font-semibold text-white">Chess Match</h3>
                  <Badge className={`text-white text-xs ${
                    gameStatus === 'active' ? 'bg-green-500' : 
                    gameStatus === 'waiting' ? 'bg-yellow-500' : 
                    'bg-gray-500'
                  }`}>
                    {gameStatus.toUpperCase()}
                  </Badge>
                </div>
                
                <div className="space-y-2">
                  <div className="bg-slate-800 p-2 rounded">
                    <div className="text-xs text-slate-400">Current Turn</div>
                    <div className={`text-sm font-bold ${currentTurn === 'white' ? 'text-white' : 'text-gray-400'}`}>
                      {currentTurn === 'white' ? 'White' : 'Black'} to Move
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-2">
                    <div className="bg-slate-800 p-2 rounded">
                      <div className="text-xs text-slate-400">Wager</div>
                      <div className="text-sm font-bold text-white">${wagerAmount}</div>
                    </div>
                    <div className="bg-slate-800 p-2 rounded">
                      <div className="text-xs text-slate-400">Time</div>
                      <div className="text-sm font-bold text-white">{formatTime(gameTime)}</div>
                    </div>
                  </div>
                  
                  {currentGame?.state?.is_check && (
                    <div className="bg-red-900/50 border border-red-700 p-2 rounded">
                      <div className="text-sm font-bold text-red-400">CHECK!</div>
                    </div>
                  )}
                </div>
              </div>

              {/* Player Info */}
              <div className="bg-slate-900 border border-slate-800 rounded-sm p-3">
                <h3 className="text-sm font-semibold text-white mb-2">Players</h3>
                <div className="space-y-2">
                  {currentGame.players.map((player, index) => (
                    <div key={player.user_id} className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <div className={`w-3 h-3 rounded-full ${index === 0 ? 'bg-white' : 'bg-gray-800'}`}></div>
                        <span className="text-sm text-white">{player.username}</span>
                      </div>
                      <span className="text-xs text-green-500">${wagerAmount / 2}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Timer (for timed games) */}
              {isTimedGame && (
                <div className="bg-slate-900 border border-slate-800 rounded-sm p-3">
                  <h3 className="text-sm font-semibold text-white mb-2">Time Remaining</h3>
                  <div className="space-y-2">
                    <div>
                      <div className="flex justify-between text-xs mb-1">
                        <span className="text-white">White</span>
                        <span className="text-white">{formatTime(whiteTime)}</span>
                      </div>
                      <Progress value={(whiteTime / timeLimit) * 100} className="h-1.5" />
                    </div>
                    <div>
                      <div className="flex justify-between text-xs mb-1">
                        <span className="text-gray-400">Black</span>
                        <span className="text-white">{formatTime(blackTime)}</span>
                      </div>
                      <Progress value={(blackTime / timeLimit) * 100} className="h-1.5" />
                    </div>
                  </div>
                </div>
              )}

              {/* Captured Pieces */}
              <div className="bg-slate-900 border border-slate-800 rounded-sm p-3">
                <h3 className="text-sm font-semibold text-white mb-2">Captured</h3>
                <div className="space-y-2">
                  <div>
                    <span className="text-xs text-white">White Pieces:</span>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {capturedPieces.white.map((piece, i) => (
                        <span key={i} className="text-lg">{PIECE_UNICODE[piece as keyof typeof PIECE_UNICODE]}</span>
                      ))}
                    </div>
                  </div>
                  <div>
                    <span className="text-xs text-gray-400">Black Pieces:</span>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {capturedPieces.black.map((piece, i) => (
                        <span key={i} className="text-lg">{PIECE_UNICODE[piece as keyof typeof PIECE_UNICODE]}</span>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Center - Game Board */}
          <div className="col-span-6 flex flex-col items-center justify-center p-4 overflow-y-auto custom-scrollbar">
            <div className="w-full max-w-xl">
              {renderBoard()}
            </div>
            
            {/* Game Controls */}
            <div className="flex gap-3 mt-4">
              {onBack && (
                <Button onClick={onBack} size="sm" className="bg-blue-600 hover:bg-blue-700 text-xs">
                  <ChevronDown className="h-3 w-3 mr-1 rotate-90" />
                  Back
                </Button>
              )}
              <Button 
                onClick={() => setShowRules(!showRules)}
                size="sm" 
                variant="outline"
                className="border-slate-700 text-xs"
              >
                <AlertCircle className="h-3 w-3 mr-1" />
                Rules
              </Button>
            </div>

            {/* Rules Panel */}
            {showRules && (
              <div className="mt-3 bg-slate-900 border border-slate-800 rounded-sm p-3 max-w-md">
                <div className="flex justify-between items-center mb-2">
                  <h3 className="text-sm font-semibold text-white">Quick Rules</h3>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => setShowRules(false)}
                    className="h-6 w-6 p-0"
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </div>
                <div className="text-xs text-slate-300 space-y-1">
                  <p>• Click a piece to select, click square to move</p>
                  <p>• Each piece has specific movement patterns</p>
                  <p>• Capture by moving to opponent's square</p>
                  <p>• Protect your King - Check means it's under attack</p>
                  <p>• Castle by moving King two squares toward Rook</p>
                  <p>• En Passant: special pawn capture move</p>
                </div>
              </div>
            )}
          </div>

          {/* Right Panel - Chat & Spectators */}
          <div className="col-span-3 flex flex-col gap-2 h-full overflow-hidden">
            {/* Fixed spectators section */}
            <div className="flex flex-col gap-2">
              {/* Spectators */}
              <div className="bg-slate-900 border border-slate-800 rounded-sm p-3">
                <div className="flex justify-between items-center mb-2">
                  <h3 className="text-sm font-semibold text-white">Spectators</h3>
                  <div className="flex items-center gap-1">
                    <Users className="h-3 w-3 text-green-500" />
                    <span className="text-xs text-green-500">{currentGame.spectators || 0}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Chat - takes remaining space */}
            <div className="bg-slate-900 border border-slate-800 rounded-sm flex-1 flex flex-col overflow-hidden min-h-0 relative">
              <div className="p-3 border-b border-slate-800 flex justify-between items-center shrink-0">
                <h3 className="text-sm font-semibold text-white">Chat</h3>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => setShowChat(!showChat)}
                  className="h-6 w-6 p-0"
                >
                  {showChat ? <ChevronDown className="h-3 w-3" /> : <MessageSquare className="h-3 w-3" />}
                </Button>
              </div>
              
              {showChat && (
                <>
                  <div className="flex-1 overflow-y-auto p-3 space-y-1 custom-scrollbar min-h-0 pb-16">
                    {messages.map((msg, i) => (
                      <div key={i} className={`text-xs ${msg.type === 'system' ? 'text-slate-400 italic' : 'text-white'}`}>
                        {msg.text}
                      </div>
                    ))}
                  </div>
                  
                  <div className="absolute bottom-0 left-0 right-0 p-2 bg-slate-900 border-t border-slate-800">
                    <div className="flex gap-1">
                      <input
                        type="text"
                        value={messageInput}
                        onChange={(e) => setMessageInput(e.target.value)}
                        onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
                        placeholder="Type a message..."
                        className="flex-1 bg-slate-800 text-white text-xs rounded px-2 py-1 focus:outline-none focus:ring-1 focus:ring-purple-500"
                      />
                      <Button
                        onClick={sendMessage}
                        size="sm"
                        className="bg-purple-600 hover:bg-purple-700 h-7 px-2"
                      >
                        <Send className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Promotion Modal */}
      {isPromoting && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-slate-900 border border-slate-800 rounded-lg p-6">
            <h3 className="text-xl font-bold text-white mb-4">Choose Promotion</h3>
            <div className="grid grid-cols-4 gap-4">
              {['Q', 'R', 'B', 'N'].map((piece) => (
                <button
                  key={piece}
                  onClick={() => handlePromotion(piece)}
                  className="w-20 h-20 bg-slate-800 hover:bg-slate-700 border border-slate-700 rounded flex items-center justify-center text-4xl"
                >
                  <img 
                    src={PIECE_IMAGES[currentTurn === 'white' ? piece : piece.toLowerCase() as keyof typeof PIECE_IMAGES]}
                    alt={piece}
                    className="w-16 h-16 object-contain"
                  />
                </button>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// Define PIECE_UNICODE for captured pieces display
const PIECE_UNICODE = {
  'K': '♔',
  'Q': '♕',
  'R': '♖',
  'B': '♗',
  'N': '♘',
  'P': '♙',
  'k': '♚',
  'q': '♛',
  'r': '♜',
  'b': '♝',
  'n': '♞',
  'p': '♟',
};

export default ChessGameWithState;