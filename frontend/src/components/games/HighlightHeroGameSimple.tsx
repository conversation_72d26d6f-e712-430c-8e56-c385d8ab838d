import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  ArrowLeft, 
  Film, 
  Play, 
  Pause,
  RotateCcw,
  Volume2,
  VolumeX,
  Eye,
  EyeOff,
  Trophy,
  Clock,
  Check,
  X
} from 'lucide-react';
import type { GameData } from './gameTypes';
import type { HighlightHeroConfig } from './HighlightHeroGameWrapper';

interface HighlightHeroGameSimpleProps {
  game?: GameData;
  config?: HighlightHeroConfig | null;
  onBack?: () => void;
}

// Sample questions
const SAMPLE_QUESTIONS = {
  soccer: [
    {
      question: "Which team scored the winning goal?",
      options: ["Real Madrid", "Barcelona", "Manchester United", "Liverpool"],
      correctAnswer: 0
    }
  ],
  movies: [
    {
      question: "What movie is this scene from?",
      options: ["The Matrix", "Inception", "Interstellar", "Tenet"],
      correctAnswer: 1
    }
  ]
};

const HighlightHeroGameSimple: React.FC<HighlightHeroGameSimpleProps> = ({
  game,
  config,
  onBack
}) => {
  const [gameStatus, setGameStatus] = useState<'waiting' | 'playing' | 'answering' | 'result'>('waiting');
  const [currentRound, setCurrentRound] = useState(1);
  const [score, setScore] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [videoBlurred, setVideoBlurred] = useState(true);
  const [timeLeft, setTimeLeft] = useState(config?.timeLimit || 30);
  const [selectedAnswer, setSelectedAnswer] = useState<number | null>(null);
  const [showResult, setShowResult] = useState(false);
  const [isCorrect, setIsCorrect] = useState(false);
  
  const videoRef = useRef<HTMLVideoElement>(null);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  const currentQuestion = SAMPLE_QUESTIONS[config?.category || 'soccer']?.[0] || SAMPLE_QUESTIONS.soccer[0];

  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, []);

  const startRound = () => {
    setGameStatus('playing');
    setVideoBlurred(true);
    setSelectedAnswer(null);
    setShowResult(false);
    
    if (videoRef.current) {
      videoRef.current.play();
      setIsPlaying(true);
    }

    // Auto transition to answering phase after 5 seconds
    setTimeout(() => {
      setGameStatus('answering');
      startTimer();
      if (videoRef.current) {
        videoRef.current.pause();
        setIsPlaying(false);
      }
    }, 5000);
  };

  const startTimer = () => {
    setTimeLeft(config?.timeLimit || 30);
    timerRef.current = setInterval(() => {
      setTimeLeft((prev) => {
        if (prev <= 1) {
          if (timerRef.current) clearInterval(timerRef.current);
          handleTimeUp();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  const handleTimeUp = () => {
    if (selectedAnswer === null) {
      submitAnswer(-1); // No answer
    }
  };

  const submitAnswer = (answerIndex: number) => {
    if (timerRef.current) clearInterval(timerRef.current);
    
    setSelectedAnswer(answerIndex);
    const correct = answerIndex === currentQuestion.correctAnswer;
    setIsCorrect(correct);
    setShowResult(true);
    setVideoBlurred(false);
    
    if (correct) {
      setScore(score + 100);
    }

    setGameStatus('result');
  };

  const nextRound = () => {
    if (currentRound < (config?.rounds || 5)) {
      setCurrentRound(currentRound + 1);
      startRound();
    } else {
      // Game over
      setGameStatus('waiting');
    }
  };

  const togglePlay = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause();
      } else {
        videoRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  const toggleMute = () => {
    if (videoRef.current) {
      videoRef.current.muted = !isMuted;
      setIsMuted(!isMuted);
    }
  };

  const toggleBlur = () => {
    if (config?.allowReplays) {
      setVideoBlurred(!videoBlurred);
    }
  };

  return (
    <div className="h-screen bg-slate-950 flex flex-col pt-16">
      {/* Header */}
      <div className="bg-slate-900 border-b border-slate-800 p-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Button
              variant="ghost"
              size="sm"
              onClick={onBack}
              className="text-slate-400 hover:text-white p-0"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <div>
              <h1 className="text-base font-bold text-white flex items-center">
                <Film className="h-4 w-4 mr-1 text-purple-500" />
                HighlightHero
              </h1>
              {gameStatus !== 'waiting' && (
                <p className="text-xs text-slate-400">
                  Round {currentRound} of {config?.rounds || 5}
                </p>
              )}
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Badge className="bg-green-500 text-xs">
              {score} pts
            </Badge>
            <Badge className="bg-purple-500 text-xs">
              ${config?.wagerAmount || 50}
            </Badge>
          </div>
        </div>
      </div>

      {/* Game Content */}
      <div className="flex-1 flex flex-col p-3 space-y-3 overflow-auto">
        {gameStatus === 'waiting' ? (
          <div className="flex-1 flex flex-col justify-center">
            <div className="bg-slate-900 border border-slate-800 rounded-lg p-4 space-y-4">
              <div className="text-center">
                <div className="text-5xl mb-3">🎬</div>
                <h2 className="text-lg font-bold text-white mb-2">Ready to Play?</h2>
                <p className="text-sm text-slate-400">
                  Test your knowledge with video highlights!
                </p>
              </div>

              {/* Game Info */}
              <div className="space-y-2 text-sm">
                <div className="flex justify-between items-center">
                  <span className="text-slate-400">Category</span>
                  <span className="font-medium text-white">
                    {config?.category === 'soccer' && '⚽ Soccer'}
                    {config?.category === 'movies' && '🎬 Movies'}
                    {config?.category === 'music' && '🎵 Music'}
                    {config?.category === 'sports' && '🏀 Sports'}
                    {config?.category === 'gaming' && '🎮 Gaming'}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-slate-400">Rounds</span>
                  <span className="font-medium text-white">{config?.rounds || 5}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-slate-400">Time Limit</span>
                  <span className="font-medium text-white">{config?.timeLimit || 30}s</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-slate-400">Prize Pool</span>
                  <span className="font-medium text-green-500">
                    ${(config?.wagerAmount || 50) * 2}
                  </span>
                </div>
              </div>

              <Button 
                className="w-full bg-gradient-to-r from-purple-500 to-pink-500"
                onClick={startRound}
              >
                <Play className="h-4 w-4 mr-2" />
                Start Game
              </Button>
            </div>
          </div>
        ) : (
          <>
            {/* Video Player */}
            <div className="bg-slate-900 border border-slate-800 rounded-lg overflow-hidden">
              <div className="relative aspect-video bg-black">
                <video
                  ref={videoRef}
                  className={`w-full h-full object-contain ${
                    videoBlurred ? 'blur-md' : ''
                  } transition-all duration-300`}
                  src="/games/hh/videos/highlight.mp4"
                  muted={isMuted}
                />
                
                {/* Video Overlay */}
                {gameStatus === 'playing' && (
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="bg-black/70 rounded-lg p-4 text-center">
                      <p className="text-white font-medium">Watch carefully!</p>
                      <p className="text-sm text-slate-400">Question coming up...</p>
                    </div>
                  </div>
                )}

                {/* Video Controls */}
                <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-2">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center space-x-2">
                      <Button
                        size="sm"
                        variant="ghost"
                        className="h-8 w-8 p-0 text-white hover:bg-white/20"
                        onClick={togglePlay}
                      >
                        {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                      </Button>
                      <Button
                        size="sm"
                        variant="ghost"
                        className="h-8 w-8 p-0 text-white hover:bg-white/20"
                        onClick={toggleMute}
                      >
                        {isMuted ? <VolumeX className="h-4 w-4" /> : <Volume2 className="h-4 w-4" />}
                      </Button>
                    </div>
                    {config?.allowReplays && (
                      <Button
                        size="sm"
                        variant="ghost"
                        className="h-8 w-8 p-0 text-white hover:bg-white/20"
                        onClick={toggleBlur}
                      >
                        {videoBlurred ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Question and Timer */}
            {gameStatus === 'answering' && (
              <div className="bg-slate-900 border border-slate-800 rounded-lg p-3">
                <div className="flex justify-between items-center mb-2">
                  <h3 className="text-sm font-medium text-white">{currentQuestion.question}</h3>
                  <Badge className={timeLeft <= 10 ? 'bg-red-500' : 'bg-blue-500'}>
                    <Clock className="h-3 w-3 mr-1" />
                    {timeLeft}s
                  </Badge>
                </div>
                <Progress value={(timeLeft / (config?.timeLimit || 30)) * 100} className="h-1 mb-3" />
              </div>
            )}

            {/* Answer Options */}
            {(gameStatus === 'answering' || gameStatus === 'result') && (
              <div className="space-y-2">
                {currentQuestion.options.map((option, index) => (
                  <Button
                    key={index}
                    variant="outline"
                    className={`w-full justify-start h-12 ${
                      selectedAnswer === index
                        ? showResult
                          ? index === currentQuestion.correctAnswer
                            ? 'border-green-500 bg-green-500/20'
                            : 'border-red-500 bg-red-500/20'
                          : 'border-purple-500 bg-purple-500/20'
                        : showResult && index === currentQuestion.correctAnswer
                        ? 'border-green-500 bg-green-500/10'
                        : ''
                    }`}
                    onClick={() => gameStatus === 'answering' && submitAnswer(index)}
                    disabled={gameStatus !== 'answering'}
                  >
                    <span className="flex items-center justify-between w-full">
                      <span>{option}</span>
                      {showResult && index === currentQuestion.correctAnswer && (
                        <Check className="h-4 w-4 text-green-500" />
                      )}
                      {showResult && selectedAnswer === index && index !== currentQuestion.correctAnswer && (
                        <X className="h-4 w-4 text-red-500" />
                      )}
                    </span>
                  </Button>
                ))}
              </div>
            )}

            {/* Result */}
            {gameStatus === 'result' && (
              <div className={`bg-slate-900 border rounded-lg p-3 ${
                isCorrect ? 'border-green-500' : 'border-red-500'
              }`}>
                <div className="text-center">
                  <p className={`text-lg font-bold mb-1 ${
                    isCorrect ? 'text-green-500' : 'text-red-500'
                  }`}>
                    {isCorrect ? 'Correct! +100 pts' : 'Wrong Answer'}
                  </p>
                  <p className="text-sm text-slate-400">
                    The correct answer was: {currentQuestion.options[currentQuestion.correctAnswer]}
                  </p>
                </div>
              </div>
            )}

            {/* Next Round Button */}
            {gameStatus === 'result' && (
              <Button 
                className="w-full bg-gradient-to-r from-purple-500 to-pink-500"
                onClick={nextRound}
              >
                {currentRound < (config?.rounds || 5) ? 'Next Round' : 'View Results'}
              </Button>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default HighlightHeroGameSimple;