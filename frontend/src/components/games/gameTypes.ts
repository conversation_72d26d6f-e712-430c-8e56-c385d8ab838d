export interface GameData {
  id: string;
  name: string;
  type: string;
  players: {
    player1: { id: string; name: string; avatar?: string };
    player2: { id: string; name: string; avatar?: string };
  };
  currentRound: number;
  totalRounds: number;
  prize: number;
  viewers: number;
  status: 'waiting' | 'live' | 'finished';
  config?: {
    isTimedGame?: boolean;
    timeLimit?: number;
    gameMode?: 'classic' | 'fast';
  };
}

// Add a runtime export to ensure the module has content
export const GAME_TYPES = {
  CHESS: 'chess',
  CHECKERS: 'checkers',
  ROCK_PAPER_SCISSORS: 'rock-paper-scissors',
  HIGHLIGHT_HERO: 'highlight-hero',
  BLUR_DETECTIVE: 'blur-detective',
  TRIVIA: 'trivia',
  QUIZ_ARENA: 'quiz-arena',
  CRAZY_EIGHTS: 'crazy-eights'
} as const;
