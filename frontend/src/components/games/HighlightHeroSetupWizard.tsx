import React, { useState } from 'react';
import { <PERSON><PERSON>, Dialog<PERSON>ontent, <PERSON><PERSON>Header, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Progress } from '@/components/ui/progress';
import {
  Film,
  DollarSign,
  Clock,
  Settings,
  Check,
  ArrowRight,
  ArrowLeft,
  Play,
  Trophy,
  AlertCircle
} from 'lucide-react';

interface HighlightHeroSetupWizardProps {
  onComplete: (config: any) => void;
  onCancel: () => void;
}

// Step interface
interface Step {
  id: string;
  title: string;
  icon: React.ReactNode;
  isComplete: boolean;
}

const HighlightHeroSetupWizard: React.FC<HighlightHeroSetupWizardProps> = ({ onComplete, onCancel }) => {
  const [activeStep, setActiveStep] = useState(0);

  // Game configuration state
  const [category, setCategory] = useState('soccer');
  const [rounds, setRounds] = useState('5');
  const [wagerAmount, setWagerAmount] = useState('50');
  const [timeLimit, setTimeLimit] = useState('30');
  const [allowReplays, setAllowReplays] = useState(true);
  const [maxReplays, setMaxReplays] = useState('2');
  const [difficulty, setDifficulty] = useState('mixed');

  // Step metadata
  const steps: Step[] = [
    {
      id: 'game-type',
      title: 'Game Type',
      icon: <Film className="h-4 w-4" />,
      isComplete: false
    },
    {
      id: 'game-settings',
      title: 'Game Settings',
      icon: <Settings className="h-4 w-4" />,
      isComplete: false
    },
    {
      id: 'wager-confirm',
      title: 'Wager & Confirm',
      icon: <DollarSign className="h-4 w-4" />,
      isComplete: false
    }
  ];

  // Helper to advance to next step
  const nextStep = () => {
    if (activeStep < steps.length - 1) {
      setActiveStep(activeStep + 1);
    }
  };

  // Helper to go back to previous step
  const prevStep = () => {
    if (activeStep > 0) {
      setActiveStep(activeStep - 1);
    }
  };

  const handleStartGame = () => {
    const config = {
      category,
      rounds: parseInt(rounds),
      wagerAmount: parseInt(wagerAmount),
      timeLimit: parseInt(timeLimit),
      allowReplays,
      maxReplays: parseInt(maxReplays),
      difficulty
    };
    onComplete(config);
  };

  return (
    <Dialog open={true} onOpenChange={(open) => !open && onCancel()}>
      <DialogContent className="w-full sm:max-w-lg bg-slate-900 border-slate-800 max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-white flex items-center">
            <Film className="h-5 w-5 mr-2 text-purple-500" />
            HighlightHero Setup
          </DialogTitle>
          <DialogDescription className="text-slate-400">
            Configure your video quiz challenge
          </DialogDescription>
        </DialogHeader>

        {/* Steps indicator */}
        <div className="flex justify-between mb-6 mt-2">
          {steps.map((step, index) => (
            <div key={step.id} className="flex items-center">
              <div
                className={`flex items-center justify-center h-8 w-8 rounded-full
                  ${index === activeStep ? 'bg-gradient-to-r from-purple-500 to-pink-500' :
                    index < activeStep ? 'bg-green-500' : 'bg-slate-700'}
                  text-white font-medium`}
              >
                {index < activeStep ? <Check className="h-4 w-4" /> : index + 1}
              </div>
              {index < steps.length - 1 && (
                <div className={`h-px w-8 ml-2 ${index < activeStep ? 'bg-green-500' : 'bg-slate-700'}`} />
              )}
            </div>
          ))}
        </div>

        <div className="py-2">
          {/* Step 1: Game Type & Category */}
          {activeStep === 0 && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-white flex items-center">
                <Film className="h-5 w-5 mr-2 text-purple-500" />
                Game Type & Category
              </h3>

              {/* Category Selection */}
              <div className="space-y-3">
                <Label className="text-white">Video Category</Label>
                <Select value={category} onValueChange={setCategory}>
                  <SelectTrigger className="bg-slate-800 border-slate-700 text-white">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="bg-slate-800 border-slate-700">
                    <SelectItem value="soccer" className="text-white hover:bg-slate-700">⚽ Soccer Highlights</SelectItem>
                    <SelectItem value="basketball" className="text-white hover:bg-slate-700">🏀 Basketball Highlights</SelectItem>
                    <SelectItem value="football" className="text-white hover:bg-slate-700">🏈 Football Highlights</SelectItem>
                    <SelectItem value="tennis" className="text-white hover:bg-slate-700">🎾 Tennis Highlights</SelectItem>
                    <SelectItem value="mixed" className="text-white hover:bg-slate-700">🎯 Mixed Sports</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Difficulty */}
              <div className="space-y-3">
                <Label className="text-white">Difficulty Level</Label>
                <Select value={difficulty} onValueChange={setDifficulty}>
                  <SelectTrigger className="bg-slate-800 border-slate-700 text-white">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="bg-slate-800 border-slate-700">
                    <SelectItem value="easy" className="text-white hover:bg-slate-700">🟢 Easy - Clear highlights</SelectItem>
                    <SelectItem value="medium" className="text-white hover:bg-slate-700">🟡 Medium - Some blur</SelectItem>
                    <SelectItem value="hard" className="text-white hover:bg-slate-700">🔴 Hard - Heavy blur</SelectItem>
                    <SelectItem value="mixed" className="text-white hover:bg-slate-700">🎯 Mixed - Random difficulty</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Game Preview */}
              <Card className="bg-slate-800 border-slate-700">
                <CardContent className="p-4">
                  <div className="flex items-center space-x-2 mb-2">
                    <Play className="h-4 w-4 text-purple-400" />
                    <p className="text-sm text-purple-400 font-medium">How it works</p>
                  </div>
                  <p className="text-sm text-slate-400">
                    Watch blurred sports highlights and identify key moments, players, or outcomes.
                    The faster and more accurate you are, the higher your score and potential payout!
                  </p>
                </CardContent>
              </Card>

              <div className="flex justify-end mt-6">
                <Button
                  className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600"
                  onClick={nextStep}
                >
                  Next
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </div>
            </div>
          )}

          {/* Step 2: Game Settings */}
          {activeStep === 1 && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-white flex items-center">
                <Settings className="h-5 w-5 mr-2 text-blue-500" />
                Game Settings
              </h3>

              {/* Number of Rounds */}
              <div className="space-y-3">
                <Label className="text-white">Number of Rounds</Label>
                <Select value={rounds} onValueChange={setRounds}>
                  <SelectTrigger className="bg-slate-800 border-slate-700 text-white">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="bg-slate-800 border-slate-700">
                    <SelectItem value="3" className="text-white hover:bg-slate-700">3 Rounds (Quick)</SelectItem>
                    <SelectItem value="5" className="text-white hover:bg-slate-700">5 Rounds (Standard)</SelectItem>
                    <SelectItem value="10" className="text-white hover:bg-slate-700">10 Rounds (Extended)</SelectItem>
                    <SelectItem value="15" className="text-white hover:bg-slate-700">15 Rounds (Marathon)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Time Limit */}
              <div className="space-y-3">
                <Label className="text-white">Answer Time Limit</Label>
                <Select value={timeLimit} onValueChange={setTimeLimit}>
                  <SelectTrigger className="bg-slate-800 border-slate-700 text-white">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="bg-slate-800 border-slate-700">
                    <SelectItem value="15" className="text-white hover:bg-slate-700">⚡ 15 seconds (Fast)</SelectItem>
                    <SelectItem value="30" className="text-white hover:bg-slate-700">⏱️ 30 seconds (Standard)</SelectItem>
                    <SelectItem value="45" className="text-white hover:bg-slate-700">🕐 45 seconds (Relaxed)</SelectItem>
                    <SelectItem value="60" className="text-white hover:bg-slate-700">⏰ 60 seconds (Easy)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Replay Settings */}
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <Label className="text-white">Allow Video Replays</Label>
                  <Switch
                    checked={allowReplays}
                    onCheckedChange={setAllowReplays}
                  />
                </div>
                <p className="text-xs text-slate-400">
                  Let players rewatch highlights if they need a second look
                </p>
              </div>

              {/* Max Replays (only show if replays are allowed) */}
              {allowReplays && (
                <div className="space-y-3">
                  <Label className="text-white">Maximum Replays per Round</Label>
                  <Select value={maxReplays} onValueChange={setMaxReplays}>
                    <SelectTrigger className="bg-slate-800 border-slate-700 text-white">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="bg-slate-800 border-slate-700">
                      <SelectItem value="1" className="text-white hover:bg-slate-700">1 Replay</SelectItem>
                      <SelectItem value="2" className="text-white hover:bg-slate-700">2 Replays</SelectItem>
                      <SelectItem value="3" className="text-white hover:bg-slate-700">3 Replays</SelectItem>
                      <SelectItem value="5" className="text-white hover:bg-slate-700">5 Replays (Unlimited)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              )}

              {/* Game Duration Estimate */}
              <Card className="bg-slate-800 border-slate-700">
                <CardContent className="p-4">
                  <div className="flex items-center space-x-2 mb-2">
                    <Clock className="h-4 w-4 text-blue-400" />
                    <p className="text-sm text-blue-400 font-medium">Estimated Game Duration</p>
                  </div>
                  <p className="text-sm text-slate-400">
                    Approximately {Math.ceil((parseInt(rounds) * (parseInt(timeLimit) + 10)) / 60)} minutes
                    ({parseInt(rounds)} rounds × {timeLimit}s + loading time)
                  </p>
                </CardContent>
              </Card>

              <div className="flex justify-between mt-6">
                <Button
                  variant="outline"
                  className="bg-slate-800 border-slate-700 text-white hover:bg-slate-700"
                  onClick={prevStep}
                >
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Back
                </Button>
                <Button
                  className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600"
                  onClick={nextStep}
                >
                  Next
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </div>
            </div>
          )}

          {/* Step 3: Wager & Confirm */}
          {activeStep === 2 && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-white flex items-center">
                <DollarSign className="h-5 w-5 mr-2 text-green-500" />
                Wager & Confirm
              </h3>

              {/* Wager Amount */}
              <div className="space-y-3">
                <Label className="text-white">Wager Amount</Label>
                <div className="relative">
                  <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                  <Input
                    type="number"
                    value={wagerAmount}
                    onChange={(e) => setWagerAmount(e.target.value)}
                    className="pl-10 bg-slate-800 border-slate-700 text-white"
                    min="1"
                    max="500"
                  />
                </div>

                <div className="flex gap-2 mt-2">
                  {['10', '25', '50', '100'].map((amount) => (
                    <Button
                      key={amount}
                      variant="outline"
                      size="sm"
                      className="bg-slate-800 border-slate-700 text-white hover:bg-slate-700"
                      onClick={() => setWagerAmount(amount)}
                    >
                      ${amount}
                    </Button>
                  ))}
                </div>
              </div>

              {/* Game Summary */}
              <Card className="bg-slate-800 border-slate-700">
                <CardContent className="p-4 space-y-4">
                  <div>
                    <h4 className="text-white font-medium mb-2">Game Summary</h4>
                    <div className="text-sm space-y-1">
                      <div className="flex justify-between">
                        <span className="text-slate-400">Category:</span>
                        <span className="text-white capitalize">{category}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-slate-400">Rounds:</span>
                        <span className="text-white">{rounds}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-slate-400">Time per Round:</span>
                        <span className="text-white">{timeLimit} seconds</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-slate-400">Difficulty:</span>
                        <span className="text-white capitalize">{difficulty}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-slate-400">Replays:</span>
                        <span className="text-white">{allowReplays ? `${maxReplays} allowed` : 'Disabled'}</span>
                      </div>
                    </div>
                  </div>

                  <div className="h-px bg-slate-700"></div>

                  <div>
                    <h4 className="text-white font-medium mb-2">Potential Payouts</h4>
                    <div className="text-sm space-y-1">
                      <div className="flex justify-between">
                        <span className="text-slate-400">Your Stake:</span>
                        <span className="text-white font-medium">${wagerAmount}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-slate-400">Excellent (90%+):</span>
                        <span className="text-green-500 font-bold">${parseInt(wagerAmount) * 3}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-slate-400">Good (70-89%):</span>
                        <span className="text-blue-500 font-bold">${parseInt(wagerAmount) * 2}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-slate-400">Fair (50-69%):</span>
                        <span className="text-yellow-500 font-bold">${Math.floor(parseInt(wagerAmount) * 1.5)}</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Warning */}
              <div className="flex items-start space-x-2 p-3 bg-yellow-900/20 rounded-lg">
                <AlertCircle className="h-5 w-5 text-yellow-500 mt-0.5" />
                <div className="flex-1">
                  <p className="text-sm text-yellow-500 font-medium">Ready to Play?</p>
                  <p className="text-sm text-slate-300">
                    Your wager will be locked once the game starts. Good luck!
                  </p>
                </div>
              </div>

              <div className="flex justify-between mt-6">
                <Button
                  variant="outline"
                  className="bg-slate-800 border-slate-700 text-white hover:bg-slate-700"
                  onClick={prevStep}
                >
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Back
                </Button>
                <Button
                  className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600"
                  onClick={handleStartGame}
                >
                  <Trophy className="mr-2 h-4 w-4" />
                  Start Game (${wagerAmount})
                </Button>
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default HighlightHeroSetupWizard;
