import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Card } from '../ui/card';
import { Badge } from '../ui/badge';
import { ScrollArea } from '../ui/scroll-area';
import { Progress } from '../ui/progress';
import { RadioGroup, RadioGroupItem } from '../ui/radio-group';
import { Label } from '../ui/label';
import { Tabs, TabsList, TabsTrigger } from '../ui/tabs';
import { useToast } from '../ui/use-toast';
import { 
  Clock, Users, Trophy, Send, Eye, Sparkles, Target, Brain, 
  Timer, Crown, Flame, Activity, ArrowLeft, Volume2, VolumeX,
  Maximize2, DollarSign, MessageSquare
} from 'lucide-react';

interface Player {
  id: string;
  name: string;
  score: number;
  isReady: boolean;
  guessesLeft: number;
  hintsUsed: number;
  avatar?: string;
  isHost?: boolean;
  timeBonus?: number;
  streak?: number;
}

interface ChatMessage {
  id: string;
  playerId: string;
  playerName: string;
  message: string;
  timestamp: Date;
  isSystem?: boolean;
  isGuess?: boolean;
  isCorrect?: boolean;
}

interface GameState {
  status: 'waiting' | 'starting' | 'playing' | 'round-end' | 'game-over';
  currentRound: number;
  totalRounds: number;
  currentImage?: string;
  answer?: string;
  blurLevel: number;
  timeRemaining: number;
  winner?: string;
  roundWinner?: string;
  correctGuesses: number;
  leaderboard?: Player[];
  nextRoundIn?: number;
  revealProgress?: number;
}

interface Hint {
  id: string;
  text: string;
  cost: number;
  used: boolean;
}

interface BlurDetectiveGameProps {
  gameId: string;
  playerId: string;
  playerName: string;
  isHost: boolean;
  onLeaveGame: () => void;
  players: Player[];
  gameState: GameState;
  chatMessages: ChatMessage[];
  onSendMessage: (message: string) => void;
  onStartGame: () => void;
  onMakeGuess: (guess: string) => void;
  onUseHint: (hintId: string) => void;
  betAmount: number;
  category: string;
  maxPlayers: number;
  rounds: number;
  timeLimit: number;
}

const CATEGORIES = {
  actors: { id: 'actors', name: 'Hollywood Stars', icon: '🎬', color: 'from-purple-500 to-pink-600' },
  musicians: { id: 'musicians', name: 'Music Icons', icon: '🎵', color: 'from-blue-500 to-purple-600' },
  athletes: { id: 'athletes', name: 'Sports Legends', icon: '⚽', color: 'from-green-500 to-blue-600' },
  mixed: { id: 'mixed', name: 'All Stars', icon: '🌟', color: 'from-yellow-500 to-red-600' }
};

const HINTS = [
  { id: '1', text: 'This is a famous Hollywood actor', cost: 10, used: false },
  { id: '2', text: 'Known for Titanic and Inception', cost: 20, used: false },
  { id: '3', text: 'Won Oscar for The Revenant', cost: 30, used: false }
];

export default function BlurDetectiveGame({
  gameId,
  playerId,
  playerName,
  isHost,
  onLeaveGame,
  players,
  gameState,
  chatMessages,
  onSendMessage,
  onStartGame,
  onMakeGuess,
  onUseHint,
  betAmount,
  category,
  maxPlayers,
  rounds,
  timeLimit
}: BlurDetectiveGameProps) {
  const [chatInput, setChatInput] = useState('');
  const [guessInput, setGuessInput] = useState('');
  const [hints, setHints] = useState<Hint[]>(HINTS);
  const [imageLoaded, setImageLoaded] = useState(false);
  const [selectedBet, setSelectedBet] = useState('');
  const [betAmountInput, setBetAmountInput] = useState('10');
  const [soundEnabled, setSoundEnabled] = useState(true);
  const [selectedTab, setSelectedTab] = useState('live');
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const chatScrollRef = useRef<HTMLDivElement>(null);
  const { toast } = useToast();

  const currentPlayer = players.find(p => p.id === playerId);
  const currentCategory = CATEGORIES[category as keyof typeof CATEGORIES] || CATEGORIES.mixed;

  // Apply blur effect to image
  useEffect(() => {
    if (gameState.currentImage && canvasRef.current && imageLoaded) {
      const canvas = canvasRef.current;
      const ctx = canvas.getContext('2d');
      if (!ctx) return;

      const img = new Image();
      img.crossOrigin = 'anonymous';
      img.onload = () => {
        canvas.width = img.width;
        canvas.height = img.height;
        ctx.filter = `blur(${gameState.blurLevel}px)`;
        ctx.drawImage(img, 0, 0);

        if (gameState.status === 'round-end' && gameState.revealProgress) {
          const clearRadius = (Math.min(canvas.width, canvas.height) / 2) * (gameState.revealProgress / 100);
          const centerX = canvas.width / 2;
          const centerY = canvas.height / 2;

          ctx.globalCompositeOperation = 'destination-out';
          const gradient = ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, clearRadius);
          gradient.addColorStop(0, 'rgba(0,0,0,1)');
          gradient.addColorStop(0.8, 'rgba(0,0,0,0.3)');
          gradient.addColorStop(1, 'rgba(0,0,0,0)');
          
          ctx.fillStyle = gradient;
          ctx.fillRect(0, 0, canvas.width, canvas.height);

          ctx.globalCompositeOperation = 'destination-over';
          ctx.filter = 'none';
          ctx.drawImage(img, 0, 0);
        }
      };
      img.src = gameState.currentImage;
    }
  }, [gameState.currentImage, gameState.blurLevel, gameState.status, gameState.revealProgress, imageLoaded]);

  // Auto-scroll chat
  useEffect(() => {
    if (chatScrollRef.current) {
      chatScrollRef.current.scrollTop = chatScrollRef.current.scrollHeight;
    }
  }, [chatMessages]);

  const handleSendChat = () => {
    if (chatInput.trim()) {
      onSendMessage(chatInput);
      setChatInput('');
    }
  };

  const handleMakeGuess = () => {
    if (guessInput.trim() && currentPlayer?.guessesLeft && currentPlayer.guessesLeft > 0) {
      onMakeGuess(guessInput);
      setGuessInput('');
    }
  };

  const handleUseHint = (hint: Hint) => {
    if (!hint.used && currentPlayer && currentPlayer.score >= hint.cost) {
      onUseHint(hint.id);
      setHints(hints.map(h => h.id === hint.id ? { ...h, used: true } : h));
      toast({
        title: "Hint Unlocked!",
        description: hint.text,
        duration: 5000
      });
    }
  };

  return (
    <div className="h-full bg-slate-950 text-white">
      <div className="grid grid-cols-12 gap-1 p-1 h-full">
        {/* Left Sidebar - Game Info & Players */}
        <div className="col-span-2 flex flex-col gap-1">
          {/* Game Info */}
          <div className="bg-slate-900 rounded-sm border border-slate-800 p-2">
            <h3 className="text-xs font-medium text-white mb-2">Game Info</h3>
            <div className="space-y-1">
              <div className="flex justify-between text-[10px]">
                <span className="text-slate-400">Category:</span>
                <span className="text-white">{currentCategory.name}</span>
              </div>
              <div className="flex justify-between text-[10px]">
                <span className="text-slate-400">Round:</span>
                <span className="text-white">{gameState.currentRound}/{gameState.totalRounds}</span>
              </div>
              <div className="flex justify-between text-[10px]">
                <span className="text-slate-400">Pool:</span>
                <span className="text-yellow-500">₹{betAmount * players.length}</span>
              </div>
              <div className="flex justify-between text-[10px]">
                <span className="text-slate-400">Players:</span>
                <span className="text-white">{players.length}/{maxPlayers}</span>
              </div>
            </div>
          </div>

          {/* Players List - SCROLLABLE */}
          <div className="flex-1 bg-slate-900 rounded-sm border border-slate-800 flex flex-col overflow-hidden">
            <div className="p-2 border-b border-slate-800">
              <h3 className="text-xs font-medium text-white">Players</h3>
            </div>
            <div className="flex-1 overflow-auto p-1">
              {players
                .sort((a, b) => b.score - a.score)
                .map((player, index) => (
                  <div key={player.id} className="mb-1 p-2 bg-slate-800 rounded-sm border border-slate-700">
                    <div className="flex justify-between items-start mb-1">
                      <div className="flex items-center">
                        <div className={`w-6 h-6 rounded-full flex items-center justify-center text-[10px] font-bold mr-2 ${
                          index === 0 ? 'bg-yellow-500 text-black' :
                          index === 1 ? 'bg-gray-400 text-white' :
                          index === 2 ? 'bg-orange-600 text-white' :
                          'bg-slate-600 text-white'
                        }`}>
                          {index + 1}
                        </div>
                        <div>
                          <h4 className="text-[10px] font-medium text-white truncate max-w-[60px]">
                            {player.name}
                            {player.isHost && <Crown className="w-2.5 h-2.5 inline ml-1" />}
                          </h4>
                          <div className="text-[9px] text-slate-400">
                            {player.guessesLeft} guesses left
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-[10px] font-bold text-white">{player.score}</div>
                        {player.streak && player.streak > 0 && (
                          <div className="text-[8px] text-orange-500 flex items-center">
                            <Flame className="w-2 h-2 mr-0.5" />
                            {player.streak}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
            </div>
          </div>
        </div>

        {/* Main Game Area */}
        <div className="col-span-7 flex flex-col gap-1">
          {/* Game Header */}
          <div className="bg-slate-900 rounded-sm border border-slate-800 p-2 flex justify-between items-center">
            <div className="flex items-center gap-3">
              <Button
                variant="ghost"
                size="sm"
                onClick={onLeaveGame}
                className="h-6 w-6 p-0"
              >
                <ArrowLeft className="h-3 w-3" />
              </Button>
              <div className="flex items-center gap-2">
                <div className={`p-1 rounded bg-gradient-to-br ${currentCategory.color}`}>
                  <Eye className="h-4 w-4 text-white" />
                </div>
                <div>
                  <h2 className="text-sm font-bold text-white">Blur Detective</h2>
                  <div className="text-xs text-slate-400">{currentCategory.name}</div>
                </div>
              </div>
              <Badge className="bg-red-500 text-[10px] h-4 px-1">
                <Activity className="h-2.5 w-2.5 mr-0.5" />
                LIVE
              </Badge>
            </div>
            <div className="flex items-center gap-2">
              <div className="text-xs text-slate-400">
                Time: <span className="text-white font-mono">{gameState.timeRemaining}s</span>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSoundEnabled(!soundEnabled)}
                className="h-6 w-6 p-0"
              >
                {soundEnabled ? <Volume2 className="h-3 w-3" /> : <VolumeX className="h-3 w-3" />}
              </Button>
            </div>
          </div>

          {/* Main Game Content */}
          <div className="flex-1 bg-slate-900 rounded-sm border border-slate-800 p-4 overflow-hidden">
            {gameState.status === 'playing' ? (
              <div className="h-full flex flex-col">
                {/* Image Area */}
                <div className="flex-1 flex items-center justify-center mb-4">
                  <div className="relative bg-black/20 rounded-lg overflow-hidden max-w-full max-h-full">
                    <canvas
                      ref={canvasRef}
                      className="max-w-full max-h-full object-contain"
                      style={{ maxHeight: '400px', maxWidth: '600px' }}
                    />
                    {!imageLoaded && (
                      <div className="absolute inset-0 flex items-center justify-center">
                        <div className="animate-spin rounded-full h-12 w-12 border-4 border-primary border-t-transparent" />
                      </div>
                    )}
                    <img
                      src={gameState.currentImage}
                      alt="Mystery"
                      className="hidden"
                      onLoad={() => setImageLoaded(true)}
                      crossOrigin="anonymous"
                    />
                    
                    {/* Blur Level Indicator */}
                    <div className="absolute bottom-2 left-2 right-2">
                      <div className="bg-black/70 backdrop-blur-sm rounded p-2">
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-white text-xs">Clarity</span>
                          <span className="text-white text-xs font-mono">{100 - Math.round((gameState.blurLevel / 50) * 100)}%</span>
                        </div>
                        <Progress value={100 - (gameState.blurLevel / 50) * 100} className="h-1" />
                      </div>
                    </div>
                  </div>
                </div>

                {/* Guess Input */}
                <div className="bg-slate-800 rounded p-3">
                  <form onSubmit={(e) => { e.preventDefault(); handleMakeGuess(); }} className="flex gap-2">
                    <Input
                      value={guessInput}
                      onChange={(e) => setGuessInput(e.target.value)}
                      placeholder="Enter your guess..."
                      disabled={!currentPlayer?.guessesLeft || currentPlayer.guessesLeft === 0}
                      className="flex-1 h-8"
                    />
                    <Button 
                      type="submit" 
                      disabled={!currentPlayer?.guessesLeft || currentPlayer.guessesLeft === 0}
                      className="h-8"
                    >
                      <Send className="w-3 h-3 mr-1" />
                      Guess
                    </Button>
                  </form>
                  <div className="mt-2 flex justify-between text-xs text-slate-400">
                    <span>Guesses remaining: {currentPlayer?.guessesLeft || 0}</span>
                    <span>Score: {currentPlayer?.score || 0}</span>
                  </div>
                </div>
              </div>
            ) : gameState.status === 'waiting' ? (
              <div className="h-full flex items-center justify-center">
                <div className="text-center">
                  <Eye className="w-16 h-16 mx-auto mb-4 text-slate-600" />
                  <h3 className="text-lg font-semibold mb-2">Waiting for players...</h3>
                  <p className="text-sm text-slate-400">Game will start when ready</p>
                  {isHost && players.length >= 2 && (
                    <Button onClick={onStartGame} className="mt-4">
                      Start Game
                    </Button>
                  )}
                </div>
              </div>
            ) : gameState.status === 'round-end' ? (
              <div className="h-full flex items-center justify-center">
                <div className="text-center">
                  <Trophy className="w-16 h-16 mx-auto mb-4 text-yellow-500" />
                  <h3 className="text-lg font-semibold mb-2">Round Complete!</h3>
                  <p className="text-sm text-slate-400">The answer was: <strong>{gameState.answer}</strong></p>
                  {gameState.roundWinner && (
                    <p className="text-sm text-green-500">Winner: {players.find(p => p.id === gameState.roundWinner)?.name}</p>
                  )}
                </div>
              </div>
            ) : (
              <div className="h-full flex items-center justify-center">
                <div className="text-center">
                  <Crown className="w-16 h-16 mx-auto mb-4 text-yellow-500" />
                  <h3 className="text-lg font-semibold mb-2">Game Over!</h3>
                  <p className="text-sm text-slate-400">Thanks for playing!</p>
                </div>
              </div>
            )}
          </div>

          {/* Hints Bar */}
          <div className="bg-slate-900 rounded-sm border border-slate-800 p-2">
            <div className="flex items-center justify-between">
              <h4 className="text-xs font-medium text-white">Available Hints</h4>
              <div className="flex gap-1">
                {hints.map((hint, index) => (
                  <Button
                    key={hint.id}
                    variant={hint.used ? "secondary" : "outline"}
                    size="sm"
                    className="h-6 text-[10px] px-2"
                    onClick={() => handleUseHint(hint)}
                    disabled={hint.used || !currentPlayer || currentPlayer.score < hint.cost}
                  >
                    {hint.used ? `Hint ${index + 1}` : `Hint ${index + 1} (-${hint.cost})`}
                  </Button>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Right Sidebar - Betting & Chat */}
        <div className="col-span-3 flex flex-col gap-1">
          {/* Betting Panel */}
          <div className="bg-slate-900 rounded-sm border border-slate-800 p-2">
            <h3 className="text-xs font-medium text-white mb-2">Place Your Bet</h3>
            
            <div className="mb-2">
              <Label className="text-xs">Bet on Winner</Label>
              <RadioGroup value={selectedBet} onValueChange={setSelectedBet}>
                <div className="grid grid-cols-1 gap-1 mt-1">
                  {players.slice(0, 3).map((player) => (
                    <div key={player.id}>
                      <Label htmlFor={player.id} className="cursor-pointer">
                        <div className={`p-1.5 rounded-sm border text-xs ${selectedBet === player.id ? 'border-blue-500 bg-blue-500/10' : 'border-slate-700'}`}>
                          <RadioGroupItem value={player.id} id={player.id} className="sr-only" />
                          <div className="flex justify-between items-center">
                            <span className="text-white truncate">{player.name}</span>
                            <span className="text-blue-400 font-bold">2.5x</span>
                          </div>
                        </div>
                      </Label>
                    </div>
                  ))}
                </div>
              </RadioGroup>
            </div>

            <div className="mb-2">
              <Label className="text-xs">Amount</Label>
              <Input
                type="number"
                value={betAmountInput}
                onChange={(e) => setBetAmountInput(e.target.value)}
                className="h-6 text-xs"
                placeholder="₹10"
              />
            </div>

            <Button
              className="w-full h-6 bg-gradient-to-r from-purple-500 to-pink-500 text-xs"
              disabled={!selectedBet || !betAmountInput}
            >
              Place Bet
            </Button>
          </div>

          {/* Live Chat - SCROLLABLE */}
          <div className="flex-1 bg-slate-900 rounded-sm border border-slate-800 flex flex-col overflow-hidden">
            <div className="p-2 border-b border-slate-800">
              <h3 className="text-xs font-medium text-white">Live Chat</h3>
            </div>
            <div className="flex-1 overflow-auto p-1">
              <div ref={chatScrollRef} className="space-y-1">
                {chatMessages.map(msg => (
                  <div
                    key={msg.id}
                    className={`text-xs p-1.5 rounded-sm ${
                      msg.isSystem ? 'text-center text-slate-400 italic bg-slate-800/50' :
                      msg.playerId === playerId ? 'bg-blue-900/50' : 'bg-slate-800'
                    }`}
                  >
                    {!msg.isSystem && (
                      <div>
                        <span className="font-medium text-white">{msg.playerName}:</span>
                        <span className={`ml-1 ${msg.isGuess ? 'font-mono text-yellow-400' : 'text-slate-300'}`}>
                          {msg.message}
                          {msg.isCorrect && <span className="ml-1 text-green-500">✅</span>}
                        </span>
                      </div>
                    )}
                    {msg.isSystem && <p className="text-slate-400">{msg.message}</p>}
                  </div>
                ))}
              </div>
            </div>
            <div className="p-2 border-t border-slate-800">
              <form onSubmit={(e) => { e.preventDefault(); handleSendChat(); }} className="flex gap-1">
                <Input
                  value={chatInput}
                  onChange={(e) => setChatInput(e.target.value)}
                  placeholder="Type message..."
                  className="flex-1 h-6 text-xs"
                />
                <Button type="submit" size="sm" className="h-6 w-6 p-0">
                  <Send className="w-3 h-3" />
                </Button>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}