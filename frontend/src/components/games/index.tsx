import { useEffect, useState } from 'react';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';
import ChallengeArenaGames from './ChallengeArenaGames';
import MobileChallengeGames from './MobileChallengeGames';
import LiveSpectatorSection from './LiveSpectatorSection';
import MobileLiveSpectatorSection from './MobileLiveSpectatorSection';
import GameDetailsScreen from './GameDetailsScreen';
import type { GameData } from './gameTypes';
import { generateGameSessionUrl } from '@/config/routes';

const Games = () => {
  const navigate = useNavigate();
  const params = useParams();
  const [searchParams] = useSearchParams();
  const [isMobile, setIsMobile] = useState(false);

  // Extract route parameters for the three-tier hierarchy
  const { gameSlug, sessionId } = params;

  // Check if we're in spectate mode
  const isSpectating = gameSlug && sessionId && window.location.pathname.includes('/spectate/');

  useEffect(() => {
    // Check if mobile on mount
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768); // md breakpoint
    };

    checkMobile();

    // Add event listener for resize
    window.addEventListener('resize', checkMobile);

    // Cleanup
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const handleGameSelect = (game: GameData) => {
    // Navigate to the game instances page (second tier)
    navigate(`/games/${game.type}`);
  };

  const handleBackToList = () => {
    navigate('/games');
  };

  // Route handling for three-tier hierarchy

  // Tier 3: Game Session Page (spectate mode)
  if (isSpectating && gameSlug && sessionId) {
    const spectateGame: GameData = {
      id: sessionId,
      name: gameSlug.charAt(0).toUpperCase() + gameSlug.slice(1) + ' Match',
      type: gameSlug,
      players: {
        player1: { id: '1', name: 'Player A' },
        player2: { id: '2', name: 'Player B' }
      },
      currentRound: 2,
      totalRounds: 3,
      prize: 100,
      viewers: 250,
      status: 'live'
    };

    return isMobile ? (
      <MobileLiveSpectatorSection
        game={spectateGame}
        onBack={handleBackToList}
      />
    ) : (
      <LiveSpectatorSection
        game={spectateGame}
        onBack={handleBackToList}
      />
    );
  }

  // Tier 2: Game Instances Page
  if (gameSlug && !sessionId) {
    return <GameInstancesPage />;
  }

  // Tier 1: Games Catalog Page (default)
  return isMobile ? (
    <MobileChallengeGames onGameSelect={handleGameSelect} />
  ) : (
    <ChallengeArenaGames onGameSelect={handleGameSelect} />
  );
};

export default Games;