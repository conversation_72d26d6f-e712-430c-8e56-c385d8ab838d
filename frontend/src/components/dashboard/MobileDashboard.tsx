import { useState } from 'react';
import { 
  BarChart3, 
  Trophy, 
  Gamepad2, 
  Activity,
  TrendingUp,
  Users,
  Flame,
  Clock,
  ChevronRight,
  Menu,
  X
} from 'lucide-react';
import CompactHeader from '@/components/shared/CompactHeader';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Card } from '@/components/ui/card';

// Demo data
const liveMatches = [
  {
    id: '1',
    league: 'Champions League',
    homeTeam: 'Real Madrid',
    awayTeam: 'Barcelona',
    status: 'live' as const,
    time: "78'",
    score: { home: 2, away: 1 },
    mainMarket: {
      type: '1X2',
      selections: [
        { name: '1', odds: 1.65 },
        { name: 'X', odds: 3.80 },
        { name: '2', odds: 4.50 }
      ]
    }
  },
  {
    id: '2',
    league: 'NBA',
    homeTeam: 'Lakers',
    awayTeam: 'Warriors',
    status: 'live' as const,
    time: 'Q3 5:42',
    score: { home: 78, away: 72 },
    mainMarket: {
      type: 'Money Line',
      selections: [
        { name: 'LAL', odds: 2.10 },
        { name: 'GSW', odds: 1.65 }
      ]
    }
  }
];

const activeGames = [
  {
    id: '1',
    name: 'Word Challenge',
    category: 'word',
    players: 8,
    amount: 450,
    difficulty: 'Medium',
    status: 'In Progress',
    timeLeft: '1:23',
  },
  {
    id: '2',
    name: 'Sports Trivia',
    category: 'trivia',
    players: 12,
    amount: 780,
    difficulty: 'Hard',
    status: 'Starting Soon',
    timeLeft: '0:45',
  }
];

const bets = [
  { id: '1', name: 'User1 vs AI', gameName: 'Word Challenge', amount: 250, odds: { for: 1.85, against: 2.10 } },
  { id: '2', name: 'Team A vs Team B', gameName: 'Sports Quiz', amount: 500, odds: { for: 1.95, against: 1.90 } },
  { id: '3', name: 'Pro vs Novice', gameName: 'Logic Puzzle', amount: 750, odds: { for: 1.70, against: 2.30 } },
];

const leaders = [
  { id: '1', name: 'Alex S.', amount: 5240, isCurrentUser: false },
  { id: '2', name: 'Maria J.', amount: 4120, isCurrentUser: false },
  { id: '3', name: 'John D.', amount: 3890, isCurrentUser: false },
  { id: '8', name: 'You', amount: 3240, isCurrentUser: true },
];

const MobileDashboard = () => {
  const [activeSection, setActiveSection] = useState('overview');
  const [showMenu, setShowMenu] = useState(false);
  
  const sections = [
    { id: 'overview', name: 'Overview', icon: <BarChart3 className="h-5 w-5" /> },
    { id: 'live', name: 'Live Matches', icon: <Activity className="h-5 w-5" /> },
    { id: 'games', name: 'Active Games', icon: <Gamepad2 className="h-5 w-5" /> },
    { id: 'bets', name: 'Marketplace', icon: <TrendingUp className="h-5 w-5" /> },
    { id: 'leaderboard', name: 'Leaderboard', icon: <Trophy className="h-5 w-5" /> }
  ];
  
  return (
    <div className="min-h-screen bg-slate-950 pt-10">
      {/* Use the compact header */}
      <CompactHeader 
        isAuthenticated={true}
        username="User"
        balance={1250.00}
        betslipCount={3}
        onLogout={() => {}}
      />
      
      {/* Mobile Navigation */}
      <div className="sticky top-16 z-20 bg-slate-900 border-b border-slate-800">
        <div className="flex items-center justify-between p-3">
          <h1 className="text-lg font-semibold text-white">Dashboard</h1>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowMenu(!showMenu)}
            className="h-8 w-8 p-0"
          >
            {showMenu ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
          </Button>
        </div>
        
        {/* Horizontal scrollable tabs */}
        <div className="flex space-x-2 px-3 pb-3 overflow-x-auto scrollbar-hide">
          {sections.map(section => (
            <Button
              key={section.id}
              variant={activeSection === section.id ? 'default' : 'ghost'}
              size="sm"
              className={`h-8 px-3 text-xs whitespace-nowrap ${
                activeSection === section.id 
                  ? 'bg-purple-600 text-white' 
                  : 'text-slate-400'
              }`}
              onClick={() => setActiveSection(section.id)}
            >
              {section.icon}
              <span className="ml-1">{section.name}</span>
            </Button>
          ))}
        </div>
      </div>
      
      {/* Menu Overlay */}
      {showMenu && (
        <div className="fixed inset-0 z-30 bg-slate-900/95">
          <div className="p-4">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-lg font-semibold text-white">Menu</h2>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowMenu(false)}
                className="h-8 w-8 p-0"
              >
                <X className="h-5 w-5" />
              </Button>
            </div>
            
            <div className="space-y-2">
              {sections.map(section => (
                <Button
                  key={section.id}
                  variant="ghost"
                  className="w-full justify-start h-12 px-4 text-sm"
                  onClick={() => {
                    setActiveSection(section.id);
                    setShowMenu(false);
                  }}
                >
                  {section.icon}
                  <span className="ml-3">{section.name}</span>
                  <ChevronRight className="h-4 w-4 ml-auto" />
                </Button>
              ))}
            </div>
          </div>
        </div>
      )}
      
      {/* Main Content */}
      <div className="p-3 pb-20 mt-5">
        {activeSection === 'overview' && <OverviewSection />}
        {activeSection === 'live' && <LiveMatchesSection matches={liveMatches} />}
        {activeSection === 'games' && <ActiveGamesSection games={activeGames} />}
        {activeSection === 'bets' && <BetMarketplaceSection bets={bets} />}
        {activeSection === 'leaderboard' && <LeaderboardSection leaders={leaders} />}
      </div>
    </div>
  );
};

// Overview Section
const OverviewSection = () => (
  <div className="space-y-4">
    {/* Quick Stats */}
    <div className="grid grid-cols-2 gap-3">
      <Card className="p-4 bg-slate-900 border-slate-800">
        <div className="flex items-center justify-between mb-2">
          <span className="text-xs text-slate-400">Total Profit</span>
          <TrendingUp className="h-4 w-4 text-green-500" />
        </div>
        <p className="text-lg font-bold text-white">$1,250</p>
        <p className="text-xs text-green-500">+12.5%</p>
      </Card>
      
      <Card className="p-4 bg-slate-900 border-slate-800">
        <div className="flex items-center justify-between mb-2">
          <span className="text-xs text-slate-400">Active Bets</span>
          <Activity className="h-4 w-4 text-purple-500" />
        </div>
        <p className="text-lg font-bold text-white">8</p>
        <p className="text-xs text-slate-400">3 winning</p>
      </Card>
    </div>
    
    {/* Win Rate Chart */}
    <Card className="p-4 bg-slate-900 border-slate-800">
      <h3 className="text-sm font-medium text-white mb-3">Win Rate</h3>
      <div className="flex items-center justify-between mb-2">
        <span className="text-xs text-slate-400">Games</span>
        <span className="text-xs font-medium text-white">72%</span>
      </div>
      <Progress value={72} className="h-2 mb-3" />
      
      <div className="flex items-center justify-between mb-2">
        <span className="text-xs text-slate-400">Sports</span>
        <span className="text-xs font-medium text-white">65%</span>
      </div>
      <Progress value={65} className="h-2 mb-3" />
      
      <div className="flex items-center justify-between mb-2">
        <span className="text-xs text-slate-400">Overall</span>
        <span className="text-xs font-medium text-white">68%</span>
      </div>
      <Progress value={68} className="h-2" />
    </Card>
    
    {/* Recent Activity */}
    <Card className="p-4 bg-slate-900 border-slate-800">
      <h3 className="text-sm font-medium text-white mb-3">Recent Activity</h3>
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="h-2 w-2 rounded-full bg-green-500 mr-2" />
            <div>
              <p className="text-xs text-white">Won bet vs User123</p>
              <p className="text-xs text-slate-400">Word Challenge • 5m ago</p>
            </div>
          </div>
          <span className="text-xs font-medium text-green-500">+$250</span>
        </div>
        
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="h-2 w-2 rounded-full bg-yellow-500 mr-2" />
            <div>
              <p className="text-xs text-white">Placed counter bet</p>
              <p className="text-xs text-slate-400">Lakers vs Warriors • 12m ago</p>
            </div>
          </div>
          <span className="text-xs font-medium text-white">$100</span>
        </div>
        
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="h-2 w-2 rounded-full bg-red-500 mr-2" />
            <div>
              <p className="text-xs text-white">Lost bet vs TeamPro</p>
              <p className="text-xs text-slate-400">Sports Quiz • 1h ago</p>
            </div>
          </div>
          <span className="text-xs font-medium text-red-500">-$150</span>
        </div>
      </div>
    </Card>
  </div>
);

// Live Matches Section
const LiveMatchesSection = ({ matches }: { matches: any[] }) => (
  <div className="space-y-3">
    {matches.map(match => (
      <Card key={match.id} className="p-4 bg-slate-900 border-slate-800">
        <div className="flex items-center justify-between mb-3">
          <Badge className="bg-red-600 text-white text-xs">
            <span className="h-1.5 w-1.5 rounded-full bg-white animate-pulse mr-1" />
            LIVE
          </Badge>
          <span className="text-xs text-slate-400">{match.league}</span>
        </div>
        
        <div className="grid grid-cols-3 items-center mb-3">
          <div className="text-center">
            <p className="text-sm font-medium text-white">{match.homeTeam}</p>
          </div>
          <div className="text-center">
            <p className="text-lg font-bold text-white">
              {match.score.home} - {match.score.away}
            </p>
            <p className="text-xs text-slate-400">{match.time}</p>
          </div>
          <div className="text-center">
            <p className="text-sm font-medium text-white">{match.awayTeam}</p>
          </div>
        </div>
        
        <div className="grid grid-cols-3 gap-2">
          {match.mainMarket.selections.map((selection: any, idx: number) => (
            <Button
              key={idx}
              variant="outline"
              className="h-8 text-xs border-slate-700"
            >
              <span className="text-slate-400 mr-1">{selection.name}</span>
              <span className="font-medium text-white">{selection.odds}</span>
            </Button>
          ))}
        </div>
      </Card>
    ))}
  </div>
);

// Active Games Section
const ActiveGamesSection = ({ games }: { games: any[] }) => (
  <div className="space-y-3">
    {games.map(game => (
      <Card key={game.id} className="p-4 bg-slate-900 border-slate-800">
        <div className="flex items-start justify-between mb-3">
          <div>
            <h3 className="text-sm font-medium text-white">{game.name}</h3>
            <p className="text-xs text-slate-400">{game.category} • {game.difficulty}</p>
          </div>
          <Badge className={`text-xs ${
            game.status === 'In Progress' ? 'bg-green-600' : 'bg-yellow-600'
          } text-white`}>
            {game.status}
          </Badge>
        </div>
        
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center">
            <Users className="h-4 w-4 text-slate-400 mr-1" />
            <span className="text-xs text-white">{game.players} players</span>
          </div>
          <div className="flex items-center">
            <Clock className="h-4 w-4 text-slate-400 mr-1" />
            <span className="text-xs text-white">{game.timeLeft}</span>
          </div>
        </div>
        
        <div className="flex items-center justify-between">
          <span className="text-sm font-bold text-green-500">${game.amount}</span>
          <Button size="sm" className="h-7 px-3 text-xs bg-gradient-to-r from-purple-500 to-pink-500">
            Join Game
          </Button>
        </div>
      </Card>
    ))}
  </div>
);

// Marketplace Section (Compact)
const BetMarketplaceSection = ({ bets }: { bets: any[] }) => (
  <div className="space-y-3">
    <div className="flex items-center justify-between mb-3">
      <h3 className="text-sm font-medium text-white">Hot Bets</h3>
      <Button variant="ghost" size="sm" className="text-xs text-purple-500">
        View All
        <ChevronRight className="h-3 w-3 ml-1" />
      </Button>
    </div>
    
    {bets.map(bet => (
      <Card key={bet.id} className="p-4 bg-slate-900 border-slate-800">
        <div className="flex items-start justify-between mb-2">
          <div>
            <h4 className="text-sm font-medium text-white">{bet.name}</h4>
            <p className="text-xs text-slate-400">{bet.gameName}</p>
          </div>
          <Badge className="bg-orange-600 text-white text-xs">
            <Flame className="h-3 w-3 mr-1" />
            Hot
          </Badge>
        </div>
        
        <div className="flex items-center justify-between">
          <span className="text-sm font-bold text-green-500">${bet.amount}</span>
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" className="h-7 px-2 text-xs border-slate-700">
              For {bet.odds.for}
            </Button>
            <Button variant="outline" size="sm" className="h-7 px-2 text-xs border-slate-700">
              Against {bet.odds.against}
            </Button>
          </div>
        </div>
      </Card>
    ))}
  </div>
);

// Leaderboard Section
const LeaderboardSection = ({ leaders }: { leaders: any[] }) => (
  <div className="space-y-3">
    <Card className="p-4 bg-slate-900 border-slate-800">
      <h3 className="text-sm font-medium text-white mb-4">Top Players</h3>
      <div className="space-y-3">
        {leaders.map((leader, index) => (
          <div 
            key={leader.id}
            className={`flex items-center justify-between p-2 rounded ${
              leader.isCurrentUser ? 'bg-slate-800' : ''
            }`}
          >
            <div className="flex items-center">
              <span className={`w-6 text-center text-sm font-bold ${
                index === 0 ? 'text-yellow-500' :
                index === 1 ? 'text-slate-300' :
                index === 2 ? 'text-amber-600' :
                'text-slate-500'
              }`}>
                {index + 1}
              </span>
              <div className="ml-3">
                <p className="text-sm font-medium text-white">
                  {leader.name}
                  {leader.isCurrentUser && <Badge className="ml-2 text-xs bg-purple-600">You</Badge>}
                </p>
              </div>
            </div>
            <span className="text-sm font-bold text-green-500">${leader.amount}</span>
          </div>
        ))}
      </div>
    </Card>
    
    {/* Your Stats */}
    <Card className="p-4 bg-slate-900 border-slate-800">
      <h3 className="text-sm font-medium text-white mb-3">Your Stats</h3>
      <div className="grid grid-cols-2 gap-3">
        <div>
          <p className="text-xs text-slate-400">Ranking</p>
          <p className="text-lg font-bold text-white">#8</p>
        </div>
        <div>
          <p className="text-xs text-slate-400">Total Won</p>
          <p className="text-lg font-bold text-green-500">$3,240</p>
        </div>
        <div>
          <p className="text-xs text-slate-400">Win Rate</p>
          <p className="text-lg font-bold text-white">68%</p>
        </div>
        <div>
          <p className="text-xs text-slate-400">Games Played</p>
          <p className="text-lg font-bold text-white">142</p>
        </div>
      </div>
    </Card>
  </div>
);

export default MobileDashboard;