import { Loader2 } from 'lucide-react';

interface LoadingSpinnerProps {
  size?: 'small' | 'medium' | 'large';
  color?: string;
  centered?: boolean;
  text?: string;
}

const LoadingSpinner = ({ 
  size = 'medium', 
  color = 'text-purple-500', 
  centered = false,
  text
}: LoadingSpinnerProps) => {
  const sizeClasses = {
    small: 'h-4 w-4',
    medium: 'h-8 w-8',
    large: 'h-12 w-12',
  };

  const spinner = (
    <div className={`flex items-center ${text ? 'space-x-3' : ''}`}>
      <Loader2 className={`animate-spin ${sizeClasses[size]} ${color}`} />
      {text && <p className={`${color} font-medium`}>{text}</p>}
    </div>
  );

  if (centered) {
    return (
      <div className="flex justify-center items-center h-full w-full">
        {spinner}
      </div>
    );
  }

  return spinner;
};

export default LoadingSpinner;