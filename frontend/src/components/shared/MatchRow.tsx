import { useState } from 'react';
import { ChevronDown, CircleDot, Radio } from 'lucide-react';

interface Market {
  type: string;
  selections: {
    name: string;
    odds: number;
  }[];
}

interface MatchRowProps {
  match: {
    id: string;
    league: string;
    homeTeam: string;
    awayTeam: string;
    score?: { home: number; away: number };
    time?: string;
    period?: string;
    status: 'live' | 'upcoming' | 'ended';
    mainMarket: Market;
    additionalMarkets?: Market[];
  };
  expanded?: boolean;
  onToggleExpand?: () => void;
}

const MatchRow = ({ match, expanded = false, onToggleExpand }: MatchRowProps) => {
  const [isExpanded, setIsExpanded] = useState(expanded);
  
  const handleToggle = () => {
    if (onToggleExpand) {
      onToggleExpand();
    } else {
      setIsExpanded(!isExpanded);
    }
  };

  return (
    <div className="border-b border-slate-800 text-xs transition-all">
      {/* Match header row - always visible */}
      <div 
        className="flex h-7 items-center px-1.5 hover:bg-slate-800/30 cursor-pointer transition-colors"
        onClick={handleToggle}
      >
        <div className="flex items-center w-7 justify-center">
          {match.status === 'live' ? (
            <CircleDot className="h-3 w-3 text-red-500 animate-pulse" />
          ) : match.status === 'upcoming' ? (
            <Radio className="h-3 w-3 text-green-500" />
          ) : (
            <span className="h-1.5 w-1.5 rounded-full bg-slate-600" />
          )}
        </div>
        
        <div className="w-24 truncate text-slate-400">{match.league}</div>
        
        <div className="flex-1 flex items-center">
          <span className="text-white">{match.homeTeam}</span>
          {match.score ? (
            <span className="mx-1.5 text-white font-medium">
              {match.score.home}-{match.score.away}
            </span>
          ) : (
            <span className="mx-1.5 text-slate-600 text-xxs">vs</span>
          )}
          <span className="text-white">{match.awayTeam}</span>
          {match.time && (
            <span className="ml-2 text-slate-400">
              {match.status === 'live' ? match.time : `@ ${match.time}`}
            </span>
          )}
        </div>
        
        {/* Main market odds - always visible */}
        <div className="flex space-x-1">
          {match.mainMarket.selections.map((selection, idx) => (
            <button
              key={idx}
              className="bg-slate-800 hover:bg-slate-700 active:bg-slate-600 w-10 h-5 flex items-center justify-center rounded-sm text-white font-medium transition-colors"
              onClick={(e) => {
                e.stopPropagation();
                // Handle bet selection
              }}
            >
              {selection.odds.toFixed(2)}
            </button>
          ))}
        </div>
        
        <button className="ml-1 p-0.5 text-slate-500 hover:text-slate-300">
          <ChevronDown 
            className={`h-3 w-3 transition-transform ${isExpanded ? 'rotate-180' : ''}`} 
          />
        </button>
      </div>
      
      {/* Expanded markets - visible when expanded */}
      {isExpanded && match.additionalMarkets && (
        <div className="px-1.5 py-1 bg-slate-800/30">
          {match.additionalMarkets.map((market, idx) => (
            <div key={idx} className="mb-1 last:mb-0">
              <div className="text-slate-400 mb-0.5 text-xxs">{market.type}</div>
              <div className="flex flex-wrap gap-1">
                {market.selections.map((selection, selIdx) => (
                  <button
                    key={selIdx}
                    className="bg-slate-800 hover:bg-slate-700 active:bg-slate-600 px-2 h-5 flex items-center rounded-sm transition-colors"
                    onClick={(e) => {
                      e.stopPropagation();
                      // Handle bet selection
                    }}
                  >
                    <span className="text-slate-300 text-xxs mr-1">{selection.name}</span>
                    <span className="text-white text-xs font-medium">{selection.odds.toFixed(2)}</span>
                  </button>
                ))}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default MatchRow;