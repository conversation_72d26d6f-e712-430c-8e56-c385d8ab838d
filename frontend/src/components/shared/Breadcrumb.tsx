import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { ChevronRight, Home } from 'lucide-react';

interface BreadcrumbItem {
  label: string;
  path?: string;
  isActive?: boolean;
}

interface BreadcrumbProps {
  items: BreadcrumbItem[];
  className?: string;
}

const Breadcrumb: React.FC<BreadcrumbProps> = ({ items, className = '' }) => {
  return (
    <nav className={`flex items-center space-x-1 text-sm ${className}`} aria-label="Breadcrumb">
      <ol className="flex items-center space-x-1">
        {items.map((item, index) => (
          <li key={index} className="flex items-center">
            {index > 0 && (
              <ChevronRight className="h-4 w-4 text-slate-400 mx-1" />
            )}
            {item.path && !item.isActive ? (
              <Link
                to={item.path}
                className="text-slate-400 hover:text-white transition-colors"
              >
                {item.label}
              </Link>
            ) : (
              <span className={item.isActive ? 'text-white font-medium' : 'text-slate-400'}>
                {item.label}
              </span>
            )}
          </li>
        ))}
      </ol>
    </nav>
  );
};

// Helper function to generate breadcrumbs for games
export const generateGameBreadcrumbs = (
  gameSlug?: string,
  sessionId?: string,
  gameName?: string
): BreadcrumbItem[] => {
  const breadcrumbs: BreadcrumbItem[] = [
    { label: 'Games', path: '/games' }
  ];

  if (gameSlug) {
    const displayName = gameName || gameSlug.split('-').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
    
    if (sessionId) {
      // We're in a session, so the game instances page is not active
      breadcrumbs.push({ 
        label: displayName, 
        path: `/games/${gameSlug}` 
      });
      breadcrumbs.push({ 
        label: `Session ${sessionId.slice(0, 8)}...`, 
        isActive: true 
      });
    } else {
      // We're on the game instances page
      breadcrumbs.push({ 
        label: displayName, 
        isActive: true 
      });
    }
  }

  return breadcrumbs;
};

export default Breadcrumb;
