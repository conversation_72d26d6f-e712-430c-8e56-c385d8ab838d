import { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { 
  Menu, 
  X, 
  Trophy,
  Wallet,
  User,
  LogOut,
  LayoutDashboard,
  Store,
  Gamepad2,
  Dumbbell,
  Medal,
  ShoppingBag,
  Bell,
  Shield,
  ChevronDown,
  Settings,
  UserCircle,
  TrendingUp
} from 'lucide-react';
import BetSlip from '@/components/marketplace/BetSlip';
import useUserStore from '../../stores/userStore';
import useAuth from '@/hooks/useAuth';
import walletStore from '@/stores/walletStore';

interface CompactHeaderProps {
  isAuthenticated: boolean;
  username?: string;
  balance?: number;
  betslipCount?: number;
  onLogout?: () => void;
}

const CompactHeader = ({ isAuthenticated, username, balance: propBalance, betslipCount = 0, onLogout }: CompactHeaderProps) => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [betslipOpen, setBetslipOpen] = useState(false);
  const [showNotifications, setShowNotifications] = useState(false);
  const [showUserMenu, setShowUserMenu] = useState(false);
  const navigate = useNavigate();
  const { user, logout } = useAuth();
  const { balance } = walletStore();
  const actualBalance = balance || propBalance || 0;

  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        setSidebarOpen(false);
        setBetslipOpen(false);
        setShowNotifications(false);
        setShowUserMenu(false);
      }
    };
    
    const handleClickOutside = () => {
      setShowNotifications(false);
      setShowUserMenu(false);
    };
    
    window.addEventListener('keydown', handleEscape);
    return () => window.removeEventListener('keydown', handleEscape);
  }, []);

  const handleLogout = () => {
    logout();
    navigate('/');
  };

  const navItems = [
    { id: 'dashboard', name: 'Dashboard', icon: LayoutDashboard, path: '/dashboard', requiresAuth: true },
    { id: 'marketplace', name: 'Marketplace', icon: Store, path: '/marketplace', requiresAuth: true },
    { id: 'games', name: 'Games', icon: Gamepad2, path: '/games', requiresAuth: false },
    { id: 'sports', name: 'Sports', icon: Dumbbell, path: '/sports', requiresAuth: false },
    { id: 'expert-picks', name: 'Expert Picks', icon: TrendingUp, path: '/expert-picks', requiresAuth: true },
    { id: 'leaderboard', name: 'Leaderboard', icon: Medal, path: '/leaderboard', requiresAuth: false },
    ...(user?.is_superuser ? [{ id: 'admin', name: 'Admin', icon: Shield, path: '/admin', requiresAuth: true }] : []),
  ];

  const notifications = [
    { id: 1, text: 'Challenge accepted by SportsMaster', time: '15m ago', type: 'challenge' },
    { id: 2, text: 'You won $120 on Lakers vs Warriors', time: '2h ago', type: 'win' },
    { id: 3, text: 'New bonus available: Refer a Friend', time: '1d ago', type: 'bonus' }
  ];

  return (
    <>
      {/* Compact top navigation bar */}
      <header className="h-16 fixed top-0 left-0 right-0 bg-slate-900/90 backdrop-blur-sm z-50 flex items-center px-4 border-b border-slate-800">
        <div className="flex items-center">
          <button 
            className="p-1.5 hover:bg-slate-800 rounded" 
            onClick={() => setSidebarOpen(true)}
            aria-label="Toggle menu"
          >
            <Menu className="h-4 w-4 text-slate-300" />
          </button>
          
          <Link to="/" className="flex items-center ml-3 space-x-2">
            <div className="h-8 w-8 rounded bg-gradient-to-r from-red-500 via-yellow-500 to-purple-500 flex items-center justify-center">
              <Trophy className="h-5 w-5 text-white" />
            </div>
            <span className="text-lg font-bold text-white">BetBet</span>
          </Link>

          {/* Quick nav icons - visible on larger screens */}
          <div className="hidden sm:flex items-center space-x-1 ml-6">
            {navItems.map(item => (
              (!item.requiresAuth || isAuthenticated) && (
                <Link
                  key={item.id}
                  to={item.path}
                  className="p-2 rounded hover:bg-slate-800 transition-colors"
                  title={item.name}
                >
                  <item.icon className="h-5 w-5 text-slate-300" />
                </Link>
              )
            ))}
          </div>
        </div>

        {/* Right section */}
        <div className="ml-auto flex items-center space-x-4">
          {isAuthenticated ? (
            <>
              {/* Notifications */}
              <div className="relative">
                <button 
                  onClick={() => { setShowNotifications(!showNotifications); setShowUserMenu(false); }}
                  className="relative p-2 rounded-md hover:bg-slate-800"
                >
                  <Bell className="h-5 w-5 text-slate-300 hover:text-white" />
                  <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">
                    3
                  </span>
                </button>
                
                {showNotifications && (
                  <div className="absolute right-0 mt-2 w-80 bg-slate-800 rounded-lg shadow-lg border border-slate-700 z-50">
                    <div className="p-4">
                      <h3 className="font-medium mb-3">Notifications</h3>
                      <div className="space-y-3">
                        {notifications.map(notif => (
                          <div key={notif.id} className="flex items-start space-x-3 pb-3 border-b border-slate-700 last:border-0">
                            <div className="flex-1">
                              <p className="text-sm">{notif.text}</p>
                              <p className="text-xs text-slate-400 mt-1">{notif.time}</p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Wallet */}
              <button 
                onClick={() => navigate('/wallet')}
                className="flex items-center space-x-2 px-4 py-2 bg-slate-800 rounded-md hover:bg-slate-700"
              >
                <Wallet className="h-5 w-5 text-green-400" />
                <span className="font-medium">${actualBalance.toLocaleString()}</span>
              </button>

              {/* Betslip */}
              <button 
                className="p-2 relative hover:bg-slate-800 rounded"
                onClick={() => setBetslipOpen(!betslipOpen)}
              >
                <ShoppingBag className="h-5 w-5 text-slate-300" />
                {betslipCount > 0 && (
                  <span className="absolute -top-0.5 -right-0.5 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-bold">
                    {betslipCount}
                  </span>
                )}
              </button>

              {/* User Menu */}
              <div className="relative">
                <button 
                  onClick={() => { setShowUserMenu(!showUserMenu); setShowNotifications(false); }}
                  className="flex items-center space-x-2 p-2 rounded-md hover:bg-slate-800"
                >
                  <div className="h-8 w-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                    <span className="text-sm font-bold">{user?.username?.charAt(0).toUpperCase() || 'U'}</span>
                  </div>
                  <ChevronDown className="h-4 w-4 text-slate-300" />
                </button>
                
                {showUserMenu && (
                  <div className="absolute right-0 mt-2 w-48 bg-slate-800 rounded-lg shadow-lg border border-slate-700 z-50">
                    <div className="p-3 border-b border-slate-700">
                      <div className="text-sm font-medium">{user?.username || 'User'}</div>
                      <div className="text-xs text-slate-400">{user?.email}</div>
                    </div>
                    
                    <div className="py-1">
                      <button
                        onClick={() => {
                          setShowUserMenu(false);
                          navigate('/profile');
                        }}
                        className="w-full px-3 py-2 text-sm text-white hover:bg-slate-700 flex items-center"
                      >
                        <UserCircle className="h-4 w-4 mr-2" />
                        Profile
                      </button>
                      
                      <button
                        onClick={() => {
                          setShowUserMenu(false);
                          navigate('/settings');
                        }}
                        className="w-full px-3 py-2 text-sm text-white hover:bg-slate-700 flex items-center"
                      >
                        <Settings className="h-4 w-4 mr-2" />
                        Settings
                      </button>
                      
                      <div className="border-t border-slate-700 my-1"></div>
                      
                      <button
                        onClick={handleLogout}
                        className="w-full px-3 py-2 text-sm text-white hover:bg-slate-700 flex items-center"
                      >
                        <LogOut className="h-4 w-4 mr-2" />
                        Logout
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </>
          ) : (
            <div className="flex items-center space-x-2">
              <Link
                to="/login"
                className="px-3 py-1.5 text-sm rounded bg-slate-800 hover:bg-slate-700 text-white transition-colors"
              >
                Login
              </Link>
              <Link
                to="/register"
                className="px-3 py-1.5 text-sm rounded bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white transition-colors"
              >
                Register
              </Link>
            </div>
          )}
        </div>
      </header>

      {/* Collapsible sidebar */}
      <div className={`fixed inset-0 z-50 ${sidebarOpen ? 'pointer-events-auto' : 'pointer-events-none'}`}>
        {/* Backdrop */}
        <div 
          className={`absolute inset-0 bg-black transition-opacity ${sidebarOpen ? 'opacity-50' : 'opacity-0'}`}
          onClick={() => setSidebarOpen(false)}
        />
        
        {/* Sidebar */}
        <div className={`absolute left-0 top-0 bottom-0 w-64 bg-slate-900 border-r border-slate-800 transition-transform ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}`}>
          <div className="flex items-center justify-between p-4 border-b border-slate-800">
            <h2 className="text-lg font-semibold text-white">Navigation</h2>
            <button 
              onClick={() => setSidebarOpen(false)}
              className="p-1 hover:bg-slate-800 rounded"
            >
              <X className="h-4 w-4 text-slate-300" />
            </button>
          </div>
          
          <nav className="p-3">
            {navItems.map(item => (
              (!item.requiresAuth || isAuthenticated) && (
                <Link
                  key={item.id}
                  to={item.path}
                  className="flex items-center space-x-3 px-3 py-2 rounded hover:bg-slate-800 text-slate-300 hover:text-white transition-colors"
                  onClick={() => setSidebarOpen(false)}
                >
                  <item.icon className="h-5 w-5" />
                  <span className="text-sm">{item.name}</span>
                </Link>
              )
            ))}
          </nav>
          
          {isAuthenticated && (
            <>
              <div className="border-t border-slate-800 mt-2 pt-2 px-3">
                <Link
                  to="/wallet"
                  className="flex items-center space-x-3 px-3 py-2 rounded hover:bg-slate-800 text-slate-300 hover:text-white transition-colors"
                  onClick={() => setSidebarOpen(false)}
                >
                  <Wallet className="h-5 w-5" />
                  <span className="text-sm">Wallet</span>
                </Link>
                <Link
                  to="/profile"
                  className="flex items-center space-x-3 px-3 py-2 rounded hover:bg-slate-800 text-slate-300 hover:text-white transition-colors"
                  onClick={() => setSidebarOpen(false)}
                >
                  <User className="h-5 w-5" />
                  <span className="text-sm">Profile</span>
                </Link>
              </div>
              
              <div className="absolute bottom-0 left-0 right-0 p-3 border-t border-slate-800">
                <button
                  onClick={handleLogout}
                  className="flex items-center space-x-3 px-3 py-2 rounded hover:bg-slate-800 text-slate-300 hover:text-white transition-colors w-full"
                >
                  <LogOut className="h-5 w-5" />
                  <span className="text-sm">Logout</span>
                </button>
              </div>
            </>
          )}
        </div>
      </div>

      {/* BetSlip component */}
      <BetSlip 
        isOpen={betslipOpen}
        onClose={() => setBetslipOpen(false)}
        balance={actualBalance}
      />
    </>
  );
};

export default CompactHeader;