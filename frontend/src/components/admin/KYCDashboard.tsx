import React, { useState, useEffect } from 'react';
import { Card } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '../ui/dialog';
import api from '../../services/api';
import { CheckCircle, XCircle, User, Clock, Search } from 'lucide-react';
import { Input } from '../ui/input';
import { Select } from '../ui/select';

interface KYCSubmission {
  id: string;
  username: string;
  email: string;
  real_name: string;
  id_card_url: string;
  kyc_status: string;
  kyc_submitted_at: string;
  kyc_verified_at?: string;
}

const KYCDashboard: React.FC = () => {
  const [submissions, setSubmissions] = useState<KYCSubmission[]>([]);
  const [filteredSubmissions, setFilteredSubmissions] = useState<KYCSubmission[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedUser, setSelectedUser] = useState<KYCSubmission | null>(null);
  const [actionType, setActionType] = useState<'approve' | 'reject' | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [idCardPreview, setIdCardPreview] = useState<string | null>(null);

  useEffect(() => {
    fetchKYCSubmissions();
  }, []);

  useEffect(() => {
    filterSubmissions();
  }, [submissions, searchTerm, statusFilter]);

  const fetchKYCSubmissions = async () => {
    try {
      setLoading(true);
      const response = await api.get('/kyc/admin/pending');
      setSubmissions(response.data);
    } catch (error) {
      // console.error('Failed to fetch KYC submissions:', error);
    } finally {
      setLoading(false);
    }
  };

  const filterSubmissions = () => {
    let filtered = [...submissions];

    if (searchTerm) {
      filtered = filtered.filter(
        (sub) =>
          sub.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
          sub.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
          sub.real_name.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (statusFilter !== 'all') {
      filtered = filtered.filter((sub) => sub.kyc_status === statusFilter);
    }

    setFilteredSubmissions(filtered);
  };

  const handleAction = async () => {
    if (!selectedUser || !actionType) return;

    try {
      const endpoint = actionType === 'approve' 
        ? `/kyc/admin/verify/${selectedUser.id}`
        : `/kyc/admin/reject/${selectedUser.id}`;

      await api.put(endpoint, {
        reason: actionType === 'reject' ? 'Documents not clear' : undefined
      });

      // Update local state
      setSubmissions(prev => 
        prev.map(sub => 
          sub.id === selectedUser.id 
            ? { ...sub, kyc_status: actionType === 'approve' ? 'approved' : 'rejected' }
            : sub
        )
      );

      setSelectedUser(null);
      setActionType(null);
    } catch (error) {
      // console.error('Failed to update KYC status:', error);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge className="bg-amber-500"><Clock className="w-3 h-3 mr-1" />Pending</Badge>;
      case 'approved':
        return <Badge className="bg-green-500"><CheckCircle className="w-3 h-3 mr-1" />Approved</Badge>;
      case 'rejected':
        return <Badge variant="destructive"><XCircle className="w-3 h-3 mr-1" />Rejected</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const viewIdCard = async (url: string) => {
    try {
      // In production, this would fetch the actual image URL
      setIdCardPreview(url);
    } catch (error) {
      // console.error('Failed to load ID card:', error);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4">
      <div className="mb-6">
        <h1 className="text-2xl font-bold mb-4">KYC Verification Dashboard</h1>
        
        <div className="flex gap-4 mb-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <Input
                placeholder="Search by username, email, or name..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          
          <Select
            value={statusFilter}
            onValueChange={setStatusFilter}
          >
            <option value="all">All Status</option>
            <option value="pending">Pending</option>
            <option value="approved">Approved</option>
            <option value="rejected">Rejected</option>
          </Select>
        </div>

        <div className="grid grid-cols-3 gap-4 mb-6">
          <Card className="p-4">
            <div className="text-2xl font-bold">{submissions.filter(s => s.kyc_status === 'pending').length}</div>
            <div className="text-gray-600">Pending Verifications</div>
          </Card>
          <Card className="p-4">
            <div className="text-2xl font-bold">{submissions.filter(s => s.kyc_status === 'approved').length}</div>
            <div className="text-gray-600">Approved</div>
          </Card>
          <Card className="p-4">
            <div className="text-2xl font-bold">{submissions.filter(s => s.kyc_status === 'rejected').length}</div>
            <div className="text-gray-600">Rejected</div>
          </Card>
        </div>
      </div>

      <div className="space-y-4">
        {filteredSubmissions.map((submission) => (
          <Card key={submission.id} className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="bg-gray-100 rounded-full p-3">
                  <User className="w-6 h-6" />
                </div>
                <div>
                  <div className="font-semibold text-lg">{submission.username}</div>
                  <div className="text-gray-600">{submission.email}</div>
                  <div className="text-sm text-gray-500">Real Name: {submission.real_name}</div>
                  <div className="text-xs text-gray-400">
                    Submitted: {new Date(submission.kyc_submitted_at).toLocaleDateString()}
                  </div>
                </div>
              </div>

              <div className="flex items-center space-x-4">
                {getStatusBadge(submission.kyc_status)}
                
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => viewIdCard(submission.id_card_url)}
                  >
                    View ID
                  </Button>
                  
                  {submission.kyc_status === 'pending' && (
                    <>
                      <Button
                        className="bg-green-500 hover:bg-green-600 text-white"
                        size="sm"
                        onClick={() => {
                          setSelectedUser(submission);
                          setActionType('approve');
                        }}
                      >
                        Approve
                      </Button>
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={() => {
                          setSelectedUser(submission);
                          setActionType('reject');
                        }}
                      >
                        Reject
                      </Button>
                    </>
                  )}
                </div>
              </div>
            </div>
          </Card>
        ))}
      </div>

      {/* Confirmation Dialog */}
      <Dialog open={!!selectedUser && !!actionType} onOpenChange={() => {
        setSelectedUser(null);
        setActionType(null);
      }}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {actionType === 'approve' ? 'Approve KYC' : 'Reject KYC'}
            </DialogTitle>
            <DialogDescription>
              Are you sure you want to {actionType} KYC verification for {selectedUser?.username}?
              {actionType === 'reject' && ' This will require the user to resubmit their documents.'}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => {
              setSelectedUser(null);
              setActionType(null);
            }}>
              Cancel
            </Button>
            <Button onClick={handleAction}>
              {actionType === 'approve' ? 'Approve' : 'Reject'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* ID Card Preview Modal */}
      {idCardPreview && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" onClick={() => setIdCardPreview(null)}>
          <div className="bg-white p-4 rounded-lg max-w-3xl max-h-screen overflow-auto">
            <img src={idCardPreview} alt="ID Card" className="max-w-full h-auto" />
            <Button className="mt-4 w-full" onClick={() => setIdCardPreview(null)}>Close</Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default KYCDashboard;