import React, { useState, useEffect } from 'react';
import { Card } from '../ui/card';
import api from '../../services/api';
import { Users, DollarSign, FileCheck, TrendingUp, Activity, AlertCircle } from 'lucide-react';
import { Progress } from '../ui/progress';

interface DashboardStats {
  totalUsers: number;
  activeUsers: number;
  pendingKYC: number;
  totalRevenue: number;
  dailyActive: number;
  weeklyGrowth: number;
}

const AdminDashboard: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats>({
    totalUsers: 0,
    activeUsers: 0,
    pendingKYC: 0,
    totalRevenue: 0,
    dailyActive: 0,
    weeklyGrowth: 0,
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDashboardStats();
  }, []);

  const fetchDashboardStats = async () => {
    try {
      setLoading(true);
      // In a real app, this would fetch from an admin stats endpoint
      const response = await api.get('/admin/stats');
      setStats(response.data);
    } catch (error) {
      // console.error('Failed to fetch dashboard stats:', error);
      // Mock data for demonstration
      setStats({
        totalUsers: 1284,
        activeUsers: 892,
        pendingKYC: 23,
        totalRevenue: 158420,
        dailyActive: 342,
        weeklyGrowth: 12.5,
      });
    } finally {
      setLoading(false);
    }
  };

  const statCards = [
    {
      title: 'Total Users',
      value: stats.totalUsers.toLocaleString(),
      icon: Users,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
    },
    {
      title: 'Active Users',
      value: stats.activeUsers.toLocaleString(),
      icon: Activity,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
    },
    {
      title: 'Pending KYC',
      value: stats.pendingKYC.toLocaleString(),
      icon: FileCheck,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-100',
    },
    {
      title: 'Total Revenue',
      value: `$${stats.totalRevenue.toLocaleString()}`,
      icon: DollarSign,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
    },
  ];

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  return (
    <div>
      <div className="mb-8">
        <h1 className="text-2xl font-bold mb-2">Dashboard Overview</h1>
        <p className="text-gray-600">Monitor your platform's performance and user activity</p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {statCards.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <Card key={index} className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className={`p-3 rounded-full ${stat.bgColor}`}>
                  <Icon className={`w-6 h-6 ${stat.color}`} />
                </div>
                <span className="text-sm text-gray-500">{stat.title}</span>
              </div>
              <div className="text-2xl font-bold">{stat.value}</div>
            </Card>
          );
        })}
      </div>

      {/* Additional Metrics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">User Activity</h3>
          <div className="space-y-4">
            <div>
              <div className="flex justify-between mb-2">
                <span className="text-gray-600">Daily Active Users</span>
                <span className="font-semibold">{stats.dailyActive}</span>
              </div>
              <Progress value={(stats.dailyActive / stats.totalUsers) * 100} />
            </div>
            <div>
              <div className="flex justify-between mb-2">
                <span className="text-gray-600">Weekly Growth</span>
                <span className="font-semibold text-green-600">+{stats.weeklyGrowth}%</span>
              </div>
              <Progress value={stats.weeklyGrowth} />
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Platform Health</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <AlertCircle className="w-5 h-5 text-yellow-600" />
                <span className="text-gray-700">Pending KYC Verifications</span>
              </div>
              <span className="font-semibold text-yellow-600">{stats.pendingKYC}</span>
            </div>
            <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <TrendingUp className="w-5 h-5 text-green-600" />
                <span className="text-gray-700">Revenue Growth</span>
              </div>
              <span className="font-semibold text-green-600">+18.3%</span>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default AdminDashboard;