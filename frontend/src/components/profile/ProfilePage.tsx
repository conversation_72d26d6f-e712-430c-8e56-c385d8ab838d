import { useState } from 'react';
import {
  User,
  Trophy,
  GamepadIcon,
  Wallet,
  Settings,
  Award,
  Target,
  FileStack,
  Users,
  Video,
  Shield,
  LogOut,
  Edit,
  Camera,
  CheckCircle,
  Bell,
  MapPin,
  Clock,
  DollarSign,
  BarChart2,
  TrendingUp,
  Eye,
  EyeOff,
  Star,
  Download,
  Globe,
  Lock,
  Zap,
  HelpCircle,
  MessageSquare,
  Share,
  Plus,
  CreditCard
} from 'lucide-react';

// Sample user data
const userData = {
  username: "GamerPro99",
  avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=GamerPro",
  coverPhoto: "https://api.dicebear.com/7.x/micah/svg?seed=background",
  level: 28,
  xp: 7850,
  nextLevelXp: 10000,
  country: "United States",
  status: "Pro Player",
  bio: "Competitive gamer since 2010. I specialize in strategy games and sports betting. Always up for a challenge!",
  leagueRank: "Diamond III",
  openToChallenges: true,
  joinDate: "Feb 2021",
  lastActive: "2 hours ago",
  stats: {
    gamesPlayed: 1248,
    winRate: 68,
    totalWagered: 25800,
    totalEarned: 31450,
    winStreak: 14,
    mostPlayedGame: "Chess Masters",
    totalMatches: 1352,
    challengesIssued: 89,
    challengesAccepted: 72,
    challengesDeclined: 17
  },
  wallet: {
    balance: 2450,
    bonusTokens: 350,
    paymentMethods: [
      { id: 1, type: "Credit Card", last4: "4242", default: true },
      { id: 2, type: "PayPal", email: "<EMAIL>", default: false }
    ],
    spendingLimit: {
      daily: 500,
      weekly: 2000,
      monthly: 5000
    }
  },
  achievements: [
    { id: 1, name: "First Blood", description: "Win your first match", earned: true, date: "Feb 15, 2021" },
    { id: 2, name: "High Roller", description: "Place a bet of $1000 or more", earned: true, date: "Mar 22, 2021" },
    { id: 3, name: "Winning Streak", description: "Win 10 matches in a row", earned: true, date: "Jul 4, 2021" },
    { id: 4, name: "Comeback King", description: "Win after being down by 50%", earned: true, date: "Aug 17, 2021" },
    { id: 5, name: "Master Strategist", description: "Win 100 strategy games", earned: false, progress: 84 },
    { id: 6, name: "The Millionaire", description: "Have $1,000,000 in total earnings", earned: false, progress: 31 }
  ],
  bets: {
    pending: [
      { id: 101, game: "Football League", opponent: "SportsMaster", amount: 500, date: "May 19, 2025" },
      { id: 102, game: "Chess Tournament", opponent: "ChessPro", amount: 250, date: "May 20, 2025" }
    ],
    settled: [
      { id: 103, game: "Poker Night", opponent: "CardShark", amount: 1000, result: "win", profit: 950, date: "May 15, 2025" },
      { id: 104, game: "Basketball Finals", opponent: "HoopsDreams", amount: 800, result: "loss", loss: 800, date: "May 12, 2025" },
      { id: 105, game: "Word Challenge", opponent: "Wordsmith", amount: 300, result: "win", profit: 285, date: "May 10, 2025" }
    ]
  },
  friends: {
    list: [
      { id: 201, username: "ChessMaster", avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=ChessMaster", status: "online", level: 32 },
      { id: 202, username: "BetKing", avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=BetKing", status: "online", level: 45 },
      { id: 203, username: "PokerFace", avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=PokerFace", status: "offline", level: 27 },
      { id: 204, username: "SportsGuru", avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=SportsGuru", status: "online", level: 38 }
    ]
  },
  recentActivity: [
    { id: 1, type: 'game', action: 'Won Chess match', time: '2 hours ago', opponent: 'ChessMaster', amount: '+$190' },
    { id: 2, type: 'achievement', action: 'Unlocked "Comeback King"', time: '5 hours ago' },
    { id: 3, type: 'friend', action: 'Added new friend', time: '1 day ago', user: 'BetKing' },
    { id: 4, type: 'bet', action: 'Created new bet', time: '2 days ago', amount: '$500' }
  ],
  gameHistory: [
    { id: 401, game: "Chess", opponent: "ChessMaster", result: "win", stake: 200, profit: 190, date: "May 16, 2025" },
    { id: 402, game: "Poker", opponent: "CardShark", result: "loss", stake: 500, loss: 500, date: "May 15, 2025" },
    { id: 403, game: "Sports Betting", event: "NBA Finals", result: "win", stake: 1000, profit: 850, date: "May 14, 2025" },
    { id: 404, game: "Word Challenge", opponent: "BookWorm", result: "win", stake: 300, profit: 285, date: "May 13, 2025" },
    { id: 405, game: "Chess", opponent: "RookTaker", result: "win", stake: 250, profit: 237, date: "May 12, 2025" }
  ]
};

const ProfilePage = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [isEditing, setIsEditing] = useState(false);
  const [editableUser, setEditableUser] = useState({
    username: userData.username,
    bio: userData.bio,
    openToChallenges: userData.openToChallenges
  });

  const startEditing = () => {
    setIsEditing(true);
  };

  const saveProfile = () => {
    setIsEditing(false);
  };

  const toggleChallenges = () => {
    setEditableUser({
      ...editableUser,
      openToChallenges: !editableUser.openToChallenges
    });
  };

  return (
    <div className="min-h-screen bg-slate-950 text-white pt-16">
      <div className="grid grid-cols-12 gap-1 p-1 h-[calc(100vh-64px)]">
        {/* Left Sidebar - Navigation & Stats */}
        <div className="col-span-2 flex flex-col gap-1 overflow-hidden">
          {/* Profile Overview Card */}
          <div className="bg-slate-900 border border-slate-800 rounded-sm overflow-hidden">
            <div className="relative h-24 bg-gradient-to-r from-blue-600 to-purple-600">
              {isEditing && (
                <button className="absolute bottom-1 right-1 bg-slate-800 p-1 rounded text-white hover:bg-slate-700">
                  <Camera size={14} />
                </button>
              )}
            </div>
            
            <div className="p-3">
              <div className="flex flex-col items-center -mt-10">
                <div className="relative">
                  <img
                    className="h-16 w-16 rounded-full border-3 border-slate-900 bg-slate-700"
                    src={userData.avatar}
                    alt={userData.username}
                  />
                  {isEditing && (
                    <button className="absolute bottom-0 right-0 bg-blue-500 p-0.5 rounded-full text-white">
                      <Camera size={10} />
                    </button>
                  )}
                </div>
                
                <div className="mt-2 text-center">
                  {isEditing ? (
                    <input
                      type="text"
                      value={editableUser.username}
                      onChange={(e) => setEditableUser({...editableUser, username: e.target.value})}
                      className="text-sm font-bold bg-transparent border-b border-slate-600 focus:outline-none text-center"
                    />
                  ) : (
                    <h3 className="text-sm font-bold">{userData.username}</h3>
                  )}
                  <p className="text-xs text-slate-400">{userData.status}</p>
                </div>
              </div>

              <div className="mt-3 space-y-1 text-xs">
                <div className="flex items-center gap-1">
                  <Trophy size={12} className="text-yellow-500" />
                  <span className="text-slate-400">{userData.leagueRank}</span>
                </div>
                <div className="flex items-center gap-1">
                  <MapPin size={12} className="text-slate-400" />
                  <span className="text-slate-400">{userData.country}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Clock size={12} className="text-slate-400" />
                  <span className="text-slate-400">Joined {userData.joinDate}</span>
                </div>
              </div>

              <div className="mt-3">
                <div className="flex justify-between text-xs mb-1">
                  <span>Level {userData.level}</span>
                  <span>{userData.xp}/{userData.nextLevelXp}</span>
                </div>
                <div className="w-full bg-slate-700 rounded-full h-1.5">
                  <div
                    className="bg-gradient-to-r from-blue-500 to-purple-500 h-1.5 rounded-full"
                    style={{ width: `${(userData.xp / userData.nextLevelXp) * 100}%` }}
                  />
                </div>
              </div>

              <button
                onClick={isEditing ? saveProfile : startEditing}
                className="mt-3 w-full bg-slate-800 hover:bg-slate-700 text-white py-1.5 rounded-sm text-xs flex items-center justify-center gap-1"
              >
                {isEditing ? <CheckCircle size={12} /> : <Edit size={12} />}
                {isEditing ? 'Save Profile' : 'Edit Profile'}
              </button>
            </div>
          </div>

          {/* Quick Stats */}
          <div className="bg-slate-900 border border-slate-800 rounded-sm p-3">
            <h4 className="text-xs font-medium mb-2">Quick Stats</h4>
            <div className="space-y-2">
              <div className="flex justify-between text-xs">
                <span className="text-slate-400">Win Rate</span>
                <span className="text-green-400">{userData.stats.winRate}%</span>
              </div>
              <div className="flex justify-between text-xs">
                <span className="text-slate-400">Total Games</span>
                <span>{userData.stats.gamesPlayed}</span>
              </div>
              <div className="flex justify-between text-xs">
                <span className="text-slate-400">Profit/Loss</span>
                <span className="text-green-400">
                  +${userData.stats.totalEarned - userData.stats.totalWagered}
                </span>
              </div>
              <div className="flex justify-between text-xs">
                <span className="text-slate-400">Win Streak</span>
                <span className="text-yellow-400">{userData.stats.winStreak}</span>
              </div>
            </div>
          </div>

          {/* Navigation */}
          <div className="bg-slate-900 border border-slate-800 rounded-sm p-2 flex-1">
            <nav className="space-y-1">
              <button
                onClick={() => setActiveTab('overview')}
                className={`w-full text-left px-2 py-1.5 rounded-sm text-xs flex items-center gap-1.5 ${
                  activeTab === 'overview' ? 'bg-slate-800 text-white' : 'text-slate-400 hover:text-white hover:bg-slate-800/50'
                }`}
              >
                <User size={14} />
                Overview
              </button>
              <button
                onClick={() => setActiveTab('stats')}
                className={`w-full text-left px-2 py-1.5 rounded-sm text-xs flex items-center gap-1.5 ${
                  activeTab === 'stats' ? 'bg-slate-800 text-white' : 'text-slate-400 hover:text-white hover:bg-slate-800/50'
                }`}
              >
                <BarChart2 size={14} />
                Statistics
              </button>
              <button
                onClick={() => setActiveTab('games')}
                className={`w-full text-left px-2 py-1.5 rounded-sm text-xs flex items-center gap-1.5 ${
                  activeTab === 'games' ? 'bg-slate-800 text-white' : 'text-slate-400 hover:text-white hover:bg-slate-800/50'
                }`}
              >
                <GamepadIcon size={14} />
                Game History
              </button>
              <button
                onClick={() => setActiveTab('achievements')}
                className={`w-full text-left px-2 py-1.5 rounded-sm text-xs flex items-center gap-1.5 ${
                  activeTab === 'achievements' ? 'bg-slate-800 text-white' : 'text-slate-400 hover:text-white hover:bg-slate-800/50'
                }`}
              >
                <Award size={14} />
                Achievements
              </button>
              <button
                onClick={() => setActiveTab('wallet')}
                className={`w-full text-left px-2 py-1.5 rounded-sm text-xs flex items-center gap-1.5 ${
                  activeTab === 'wallet' ? 'bg-slate-800 text-white' : 'text-slate-400 hover:text-white hover:bg-slate-800/50'
                }`}
              >
                <Wallet size={14} />
                Wallet
              </button>
              <button
                onClick={() => setActiveTab('friends')}
                className={`w-full text-left px-2 py-1.5 rounded-sm text-xs flex items-center gap-1.5 ${
                  activeTab === 'friends' ? 'bg-slate-800 text-white' : 'text-slate-400 hover:text-white hover:bg-slate-800/50'
                }`}
              >
                <Users size={14} />
                Friends
              </button>
              <button
                onClick={() => setActiveTab('settings')}
                className={`w-full text-left px-2 py-1.5 rounded-sm text-xs flex items-center gap-1.5 ${
                  activeTab === 'settings' ? 'bg-slate-800 text-white' : 'text-slate-400 hover:text-white hover:bg-slate-800/50'
                }`}
              >
                <Settings size={14} />
                Settings
              </button>
            </nav>
          </div>
        </div>

        {/* Main Content */}
        <div className="col-span-7 bg-slate-900 border border-slate-800 rounded-sm p-3 overflow-hidden flex flex-col">
          {/* Overview Tab */}
          {activeTab === 'overview' && (
            <div className="flex-1 overflow-y-auto">
              <h3 className="text-lg font-medium mb-3">Profile Overview</h3>
              
              {/* Bio Section */}
              <div className="mb-4">
                <h4 className="text-sm font-medium mb-2">About Me</h4>
                {isEditing ? (
                  <textarea
                    value={editableUser.bio}
                    onChange={(e) => setEditableUser({...editableUser, bio: e.target.value})}
                    className="w-full text-sm bg-slate-800 border border-slate-700 rounded p-2 focus:outline-none focus:border-blue-500"
                    rows={3}
                  />
                ) : (
                  <p className="text-sm text-slate-400">{userData.bio}</p>
                )}
              </div>

              {/* Challenge Status */}
              <div className="mb-4 flex items-center justify-between p-3 bg-slate-800 rounded">
                <div>
                  <p className="text-sm font-medium">Open to Challenges</p>
                  <p className="text-xs text-slate-400">Allow others to challenge you to games</p>
                </div>
                <button
                  onClick={toggleChallenges}
                  className={`relative inline-flex h-5 w-10 items-center rounded-full ${
                    editableUser.openToChallenges ? 'bg-blue-600' : 'bg-slate-600'
                  }`}
                >
                  <span
                    className={`inline-block h-3 w-3 transform rounded-full bg-white transition ${
                      editableUser.openToChallenges ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>

              {/* Recent Activity */}
              <div>
                <h4 className="text-sm font-medium mb-2">Recent Activity</h4>
                <div className="space-y-2">
                  {userData.recentActivity.map(activity => (
                    <div key={activity.id} className="flex items-center gap-3 p-2 bg-slate-800 rounded">
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                        activity.type === 'game' ? 'bg-blue-900/50' :
                        activity.type === 'achievement' ? 'bg-yellow-900/50' :
                        activity.type === 'friend' ? 'bg-green-900/50' :
                        'bg-purple-900/50'
                      }`}>
                        {activity.type === 'game' ? <GamepadIcon size={14} /> :
                         activity.type === 'achievement' ? <Trophy size={14} className="text-yellow-400" /> :
                         activity.type === 'friend' ? <Users size={14} /> :
                         <FileStack size={14} />}
                      </div>
                      <div className="flex-1">
                        <p className="text-sm">{activity.action}</p>
                        <p className="text-xs text-slate-400">{activity.time}</p>
                      </div>
                      {activity.amount && (
                        <span className={`text-sm font-medium ${
                          activity.amount.startsWith('+') ? 'text-green-400' : 'text-white'
                        }`}>
                          {activity.amount}
                        </span>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Stats Tab */}
          {activeTab === 'stats' && (
            <div className="flex-1 overflow-y-auto">
              <h3 className="text-lg font-medium mb-3">Performance Statistics</h3>
              
              <div className="grid grid-cols-3 gap-2 mb-4">
                <div className="bg-slate-800 rounded p-3">
                  <h4 className="text-xs font-medium text-slate-400 mb-2">Game Overview</h4>
                  <div className="space-y-2">
                    <div>
                      <p className="text-xs text-slate-400">Games Played</p>
                      <p className="text-xl font-bold">{userData.stats.gamesPlayed}</p>
                    </div>
                    <div>
                      <p className="text-xs text-slate-400">Win Rate</p>
                      <p className="text-xl font-bold text-green-400">{userData.stats.winRate}%</p>
                    </div>
                  </div>
                </div>

                <div className="bg-slate-800 rounded p-3">
                  <h4 className="text-xs font-medium text-slate-400 mb-2">Financial Overview</h4>
                  <div className="space-y-2">
                    <div>
                      <p className="text-xs text-slate-400">Total Wagered</p>
                      <p className="text-xl font-bold">${userData.stats.totalWagered}</p>
                    </div>
                    <div>
                      <p className="text-xs text-slate-400">Total Earned</p>
                      <p className="text-xl font-bold text-green-400">${userData.stats.totalEarned}</p>
                    </div>
                  </div>
                </div>

                <div className="bg-slate-800 rounded p-3">
                  <h4 className="text-xs font-medium text-slate-400 mb-2">Social Overview</h4>
                  <div className="space-y-2">
                    <div>
                      <p className="text-xs text-slate-400">Challenges Issued</p>
                      <p className="text-xl font-bold">{userData.stats.challengesIssued}</p>
                    </div>
                    <div>
                      <p className="text-xs text-slate-400">Challenges Won</p>
                      <p className="text-xl font-bold text-green-400">{userData.stats.challengesAccepted}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Performance Chart */}
              <div className="bg-slate-800 rounded p-3 h-64 flex items-center justify-center">
                <p className="text-slate-400">Performance graph would show win/loss trends over time</p>
              </div>
            </div>
          )}

          {/* Game History Tab */}
          {activeTab === 'games' && (
            <div className="flex-1 overflow-y-auto">
              <h3 className="text-lg font-medium mb-3">Game History</h3>
              
              <div className="space-y-2">
                {userData.gameHistory.map(game => (
                  <div key={game.id} className="flex items-center justify-between p-3 bg-slate-800 rounded">
                    <div>
                      <p className="text-sm font-medium">{game.game}</p>
                      <p className="text-xs text-slate-400">
                        vs {game.opponent || game.event} • {game.date}
                      </p>
                    </div>
                    <div className="text-right">
                      <span className={`inline-block px-2 py-0.5 text-xs rounded ${
                        game.result === 'win'
                          ? 'bg-green-900/50 text-green-300'
                          : 'bg-red-900/50 text-red-300'
                      }`}>
                        {game.result.toUpperCase()}
                      </span>
                      <p className="text-sm mt-1">
                        <span className="text-slate-400">Stake: ${game.stake}</span>
                        <span className={`ml-2 ${
                          game.result === 'win' ? 'text-green-400' : 'text-red-400'
                        }`}>
                          {game.result === 'win' ? `+$${game.profit}` : `-$${game.loss}`}
                        </span>
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Achievements Tab */}
          {activeTab === 'achievements' && (
            <div className="flex-1 overflow-y-auto">
              <h3 className="text-lg font-medium mb-3">Achievements & Badges</h3>
              
              <div className="text-sm text-slate-400 mb-3">
                {userData.achievements.filter(a => a.earned).length} / {userData.achievements.length} Unlocked
              </div>

              <div className="grid grid-cols-2 gap-3">
                {userData.achievements.map(achievement => (
                  <div
                    key={achievement.id}
                    className={`p-3 rounded border ${
                      achievement.earned
                        ? 'bg-slate-800 border-slate-700'
                        : 'bg-slate-900 border-slate-800'
                    }`}
                  >
                    <div className="flex items-start gap-3">
                      <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                        achievement.earned
                          ? 'bg-gradient-to-br from-yellow-400 to-yellow-600'
                          : 'bg-slate-700'
                      }`}>
                        {achievement.earned ? (
                          <Trophy className="h-5 w-5 text-white" />
                        ) : (
                          <Lock className="h-4 w-4 text-slate-400" />
                        )}
                      </div>
                      <div className="flex-1">
                        <h4 className={`text-sm font-medium ${
                          achievement.earned ? '' : 'text-slate-400'
                        }`}>
                          {achievement.name}
                        </h4>
                        <p className="text-xs text-slate-400 mt-1">
                          {achievement.description}
                        </p>
                        {achievement.earned ? (
                          <p className="text-xs text-slate-400 mt-2">
                            Earned on {achievement.date}
                          </p>
                        ) : (
                          <div className="mt-2">
                            <div className="flex items-center">
                              <div className="w-full bg-slate-700 rounded-full h-1.5 mr-2">
                                <div
                                  className="bg-blue-500 h-1.5 rounded-full"
                                  style={{ width: `${achievement.progress}%` }}
                                />
                              </div>
                              <span className="text-xs text-slate-400">
                                {achievement.progress}%
                              </span>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Wallet Tab */}
          {activeTab === 'wallet' && (
            <div className="flex-1 overflow-y-auto">
              <h3 className="text-lg font-medium mb-3">Wallet Overview</h3>
              
              <div className="grid grid-cols-2 gap-3 mb-4">
                <div className="bg-slate-800 rounded p-3">
                  <p className="text-sm text-slate-400 mb-1">Main Balance</p>
                  <p className="text-2xl font-bold text-green-400">${userData.wallet.balance}</p>
                </div>
                <div className="bg-slate-800 rounded p-3">
                  <p className="text-sm text-slate-400 mb-1">Bonus Tokens</p>
                  <p className="text-2xl font-bold text-blue-400">{userData.wallet.bonusTokens}</p>
                </div>
              </div>

              <div className="mb-4">
                <h4 className="text-sm font-medium mb-2">Payment Methods</h4>
                <div className="space-y-2">
                  {userData.wallet.paymentMethods.map(method => (
                    <div key={method.id} className="flex items-center justify-between p-3 bg-slate-800 rounded">
                      <div className="flex items-center gap-3">
                        <div className="bg-slate-700 p-2 rounded">
                          {method.type === 'Credit Card' ? (
                            <CreditCard size={16} className="text-blue-400" />
                          ) : (
                            <DollarSign size={16} className="text-green-400" />
                          )}
                        </div>
                        <div>
                          <p className="text-sm font-medium">
                            {method.type}
                            {method.default && (
                              <span className="ml-2 text-xs text-green-400">Default</span>
                            )}
                          </p>
                          <p className="text-xs text-slate-400">
                            {method.type === 'Credit Card' ? `**** ${method.last4}` : method.email}
                          </p>
                        </div>
                      </div>
                      <Edit size={14} className="text-slate-400" />
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <h4 className="text-sm font-medium mb-2">Spending Limits</h4>
                <div className="space-y-3">
                  <div>
                    <div className="flex justify-between text-xs mb-1">
                      <span className="text-slate-400">Daily Limit</span>
                      <span>${userData.wallet.spendingLimit.daily}</span>
                    </div>
                    <div className="w-full bg-slate-700 rounded-full h-1.5">
                      <div className="bg-blue-500 h-1.5 rounded-full" style={{ width: '30%' }} />
                    </div>
                  </div>
                  <div>
                    <div className="flex justify-between text-xs mb-1">
                      <span className="text-slate-400">Weekly Limit</span>
                      <span>${userData.wallet.spendingLimit.weekly}</span>
                    </div>
                    <div className="w-full bg-slate-700 rounded-full h-1.5">
                      <div className="bg-blue-500 h-1.5 rounded-full" style={{ width: '45%' }} />
                    </div>
                  </div>
                  <div>
                    <div className="flex justify-between text-xs mb-1">
                      <span className="text-slate-400">Monthly Limit</span>
                      <span>${userData.wallet.spendingLimit.monthly}</span>
                    </div>
                    <div className="w-full bg-slate-700 rounded-full h-1.5">
                      <div className="bg-blue-500 h-1.5 rounded-full" style={{ width: '60%' }} />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Friends Tab */}
          {activeTab === 'friends' && (
            <div className="flex-1 overflow-y-auto">
              <h3 className="text-lg font-medium mb-3">Friends</h3>
              
              <div className="space-y-2">
                {userData.friends.list.map(friend => (
                  <div key={friend.id} className="flex items-center justify-between p-3 bg-slate-800 rounded">
                    <div className="flex items-center gap-3">
                      <img
                        src={friend.avatar}
                        alt={friend.username}
                        className="w-10 h-10 rounded-full"
                      />
                      <div>
                        <p className="text-sm font-medium">{friend.username}</p>
                        <p className="text-xs text-slate-400">Level {friend.level}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className={`w-2 h-2 rounded-full ${
                        friend.status === 'online' ? 'bg-green-400' : 'bg-slate-600'
                      }`} />
                      <span className="text-xs text-slate-400">{friend.status}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Settings Tab */}
          {activeTab === 'settings' && (
            <div className="flex-1 overflow-y-auto">
              <h3 className="text-lg font-medium mb-3">Account Settings</h3>
              
              <div className="space-y-4">
                <div>
                  <h4 className="text-sm font-medium mb-2">Profile Information</h4>
                  <div className="space-y-3">
                    <div>
                      <label className="block text-xs text-slate-400 mb-1">Username</label>
                      <input
                        type="text"
                        defaultValue={userData.username}
                        className="w-full bg-slate-800 border border-slate-700 rounded px-3 py-2 text-sm focus:outline-none focus:border-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-xs text-slate-400 mb-1">Email</label>
                      <input
                        type="email"
                        defaultValue="<EMAIL>"
                        className="w-full bg-slate-800 border border-slate-700 rounded px-3 py-2 text-sm focus:outline-none focus:border-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-xs text-slate-400 mb-1">Country</label>
                      <select
                        defaultValue={userData.country}
                        className="w-full bg-slate-800 border border-slate-700 rounded px-3 py-2 text-sm focus:outline-none focus:border-blue-500"
                      >
                        <option>United States</option>
                        <option>Canada</option>
                        <option>United Kingdom</option>
                        <option>Australia</option>
                      </select>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="text-sm font-medium mb-2">Security</h4>
                  <div className="space-y-3">
                    <div>
                      <label className="block text-xs text-slate-400 mb-1">Current Password</label>
                      <input
                        type="password"
                        className="w-full bg-slate-800 border border-slate-700 rounded px-3 py-2 text-sm focus:outline-none focus:border-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-xs text-slate-400 mb-1">New Password</label>
                      <input
                        type="password"
                        className="w-full bg-slate-800 border border-slate-700 rounded px-3 py-2 text-sm focus:outline-none focus:border-blue-500"
                      />
                    </div>
                    <div className="flex items-center gap-2">
                      <input
                        type="checkbox"
                        id="two-factor"
                        className="rounded border-slate-600"
                      />
                      <label htmlFor="two-factor" className="text-sm">
                        Enable two-factor authentication
                      </label>
                    </div>
                  </div>
                </div>

                <div className="flex gap-2">
                  <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded text-sm">
                    Save Changes
                  </button>
                  <button className="bg-slate-700 hover:bg-slate-600 text-white px-4 py-2 rounded text-sm">
                    Cancel
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Right Sidebar */}
        <div className="col-span-3 flex flex-col gap-1 overflow-hidden">
          {/* Pending Bets */}
          <div className="bg-slate-900 border border-slate-800 rounded-sm p-3">
            <h4 className="text-sm font-medium mb-2">Pending Bets</h4>
            <div className="space-y-2">
              {userData.bets.pending.map(bet => (
                <div key={bet.id} className="p-2 bg-slate-800 rounded">
                  <p className="text-xs font-medium">{bet.game}</p>
                  <p className="text-xs text-slate-400">vs {bet.opponent}</p>
                  <div className="flex justify-between mt-1">
                    <span className="text-xs text-slate-400">{bet.date}</span>
                    <span className="text-xs font-medium">${bet.amount}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Recent Wins */}
          <div className="bg-slate-900 border border-slate-800 rounded-sm p-3">
            <h4 className="text-sm font-medium mb-2">Recent Wins</h4>
            <div className="space-y-2">
              {userData.bets.settled
                .filter(bet => bet.result === 'win')
                .slice(0, 3)
                .map(bet => (
                  <div key={bet.id} className="p-2 bg-slate-800 rounded">
                    <p className="text-xs font-medium">{bet.game}</p>
                    <p className="text-xs text-slate-400">vs {bet.opponent}</p>
                    <div className="flex justify-between mt-1">
                      <span className="text-xs text-slate-400">{bet.date}</span>
                      <span className="text-xs font-medium text-green-400">+${bet.profit}</span>
                    </div>
                  </div>
                ))}
            </div>
          </div>

          {/* Quick Actions */}
          <div className="bg-slate-900 border border-slate-800 rounded-sm p-3">
            <h4 className="text-sm font-medium mb-2">Quick Actions</h4>
            <div className="space-y-2">
              <button className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 rounded text-sm">
                Add Funds
              </button>
              <button className="w-full bg-green-600 hover:bg-green-700 text-white py-2 rounded text-sm">
                Challenge Friend
              </button>
              <button className="w-full bg-purple-600 hover:bg-purple-700 text-white py-2 rounded text-sm">
                Create Bet
              </button>
            </div>
          </div>

          {/* Online Friends */}
          <div className="bg-slate-900 border border-slate-800 rounded-sm p-3 flex-1">
            <h4 className="text-sm font-medium mb-2">Online Friends</h4>
            <div className="space-y-2">
              {userData.friends.list
                .filter(friend => friend.status === 'online')
                .map(friend => (
                  <div key={friend.id} className="flex items-center gap-2 p-2 bg-slate-800 rounded">
                    <img
                      src={friend.avatar}
                      alt={friend.username}
                      className="w-8 h-8 rounded-full"
                    />
                    <div className="flex-1">
                      <p className="text-xs font-medium">{friend.username}</p>
                      <p className="text-xs text-slate-400">Level {friend.level}</p>
                    </div>
                    <button className="text-xs bg-green-600 hover:bg-green-700 text-white px-2 py-1 rounded">
                      Challenge
                    </button>
                  </div>
                ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfilePage;