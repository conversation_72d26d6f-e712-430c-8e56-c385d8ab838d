import { useState } from 'react';
import {
  ArrowLeft,
  Trophy,
  GamepadIcon,
  Wallet,
  Settings,
  Award,
  CheckCircle,
  Edit,
  Camera,
  Bell,
  MapPin,
  Clock,
  DollarSign,
  BarChart2,
  Star,
  Download,
  Shield,
  LogOut,
  Plus,
  CreditCard,
  Lock,
  Zap,
  Target,
  FileStack,
  Users,
  Video
} from 'lucide-react';
import { <PERSON><PERSON> } from '../ui/button/index';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '../ui/tabs/index';

// Same user data as desktop
const userData = {
  username: "GamerPro99",
  avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=GamerPro",
  coverPhoto: "https://api.dicebear.com/7.x/micah/svg?seed=background",
  level: 28,
  xp: 7850,
  nextLevelXp: 10000,
  country: "United States",
  status: "Pro Player",
  bio: "Competitive gamer since 2010. I specialize in strategy games and sports betting. Always up for a challenge!",
  leagueRank: "Diamond III",
  openToChallenges: true,
  joinDate: "Feb 2021",
  lastActive: "2 hours ago",
  stats: {
    gamesPlayed: 1248,
    winRate: 68,
    totalWagered: 25800,
    totalEarned: 31450,
    winStreak: 14,
    mostPlayedGame: "Chess Masters",
    totalMatches: 1352,
    challengesIssued: 89,
    challengesAccepted: 72,
    challengesDeclined: 17
  },
  wallet: {
    balance: 2450,
    bonusTokens: 350,
    paymentMethods: [
      { id: 1, type: "Credit Card", last4: "4242", default: true },
      { id: 2, type: "PayPal", email: "<EMAIL>", default: false }
    ],
    spendingLimit: {
      daily: 500,
      weekly: 2000,
      monthly: 5000
    }
  },
  achievements: [
    { id: 1, name: "First Blood", description: "Win your first match", earned: true, date: "Feb 15, 2021" },
    { id: 2, name: "High Roller", description: "Place a bet of $1000 or more", earned: true, date: "Mar 22, 2021" },
    { id: 3, name: "Winning Streak", description: "Win 10 matches in a row", earned: true, date: "Jul 4, 2021" },
    { id: 4, name: "Comeback King", description: "Win after being down by 50%", earned: true, date: "Aug 17, 2021" },
    { id: 5, name: "Master Strategist", description: "Win 100 strategy games", earned: false, progress: 84 },
    { id: 6, name: "The Millionaire", description: "Have $1,000,000 in total earnings", earned: false, progress: 31 }
  ],
  gameHistory: [
    { id: 401, game: "Chess", opponent: "ChessMaster", result: "win", stake: 200, profit: 190, date: "May 16, 2025" },
    { id: 402, game: "Poker", opponent: "CardShark", result: "loss", stake: 500, loss: 500, date: "May 15, 2025" },
    { id: 403, game: "Sports Betting", event: "NBA Finals", result: "win", stake: 1000, profit: 850, date: "May 14, 2025" }
  ],
  friends: {
    list: [
      { id: 201, username: "ChessMaster", avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=ChessMaster", status: "online", level: 32 },
      { id: 202, username: "BetKing", avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=BetKing", status: "online", level: 45 }
    ]
  }
};

interface MobileProfilePageProps {
  onBack: () => void;
}

const MobileProfilePage: React.FC<MobileProfilePageProps> = ({ onBack }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editableUser, setEditableUser] = useState({
    username: userData.username,
    bio: userData.bio,
    openToChallenges: userData.openToChallenges
  });
  const [selectedTab, setSelectedTab] = useState('stats');

  const startEditing = () => {
    setIsEditing(true);
  };

  const saveProfile = () => {
    setIsEditing(false);
  };

  const toggleChallenges = () => {
    setEditableUser({
      ...editableUser,
      openToChallenges: !editableUser.openToChallenges
    });
  };

  return (
    <div className="h-[100dvh] bg-slate-950 flex flex-col pt-16">
      {/* Header */}
      <div className="bg-slate-900 border-b border-slate-800 p-3">
        <div className="flex items-center justify-between">
          <button
            onClick={onBack}
            className="text-slate-400 p-1"
          >
            <ArrowLeft className="h-5 w-5" />
          </button>
          <h1 className="text-white font-medium">Profile</h1>
          <button
            onClick={isEditing ? saveProfile : startEditing}
            className="bg-slate-800 p-1.5 rounded text-white"
          >
            {isEditing ? <CheckCircle size={16} /> : <Edit size={16} />}
          </button>
        </div>
      </div>

      {/* Profile Info */}
      <div className="bg-slate-900 border-b border-slate-800">
        <div className="relative h-24 bg-gradient-to-r from-blue-600 to-purple-600">
          {isEditing && (
            <button className="absolute bottom-2 right-2 bg-slate-800 p-1.5 rounded text-white">
              <Camera size={16} />
            </button>
          )}
        </div>
        
        <div className="px-4 pb-4">
          <div className="flex items-start -mt-10">
            <div className="relative">
              <img
                className="h-20 w-20 rounded-full border-4 border-slate-900"
                src={userData.avatar}
                alt={userData.username}
              />
              {isEditing && (
                <button className="absolute bottom-0 right-0 bg-blue-500 p-1 rounded-full text-white">
                  <Camera size={12} />
                </button>
              )}
            </div>
            
            <div className="ml-3 mt-10 flex-1">
              <div className="flex items-center justify-between">
                <div>
                  {isEditing ? (
                    <input
                      type="text"
                      value={editableUser.username}
                      onChange={(e) => setEditableUser({...editableUser, username: e.target.value})}
                      className="text-lg font-bold bg-transparent border-b border-slate-600 focus:outline-none"
                    />
                  ) : (
                    <h2 className="text-lg font-bold">{userData.username}</h2>
                  )}
                  <span className="text-xs text-slate-400">Level {userData.level}</span>
                </div>
                <span className="px-2 py-0.5 text-xs bg-purple-900/50 text-purple-300 rounded">
                  {userData.status}
                </span>
              </div>
              
              <div className="mt-1 flex items-center gap-3 text-xs text-slate-400">
                <div className="flex items-center gap-1">
                  <MapPin size={12} />
                  <span>{userData.country}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Trophy size={12} className="text-yellow-500" />
                  <span>{userData.leagueRank}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Bio */}
          <div className="mt-3">
            {isEditing ? (
              <textarea
                value={editableUser.bio}
                onChange={(e) => setEditableUser({...editableUser, bio: e.target.value})}
                className="w-full text-sm bg-transparent border border-slate-600 rounded p-2 focus:outline-none"
                rows={2}
              />
            ) : (
              <p className="text-sm text-slate-400">{userData.bio}</p>
            )}
          </div>

          {/* Level Progress */}
          <div className="mt-3">
            <div className="flex justify-between text-xs mb-1">
              <span>Level {userData.level}</span>
              <span>{userData.xp}/{userData.nextLevelXp} XP</span>
            </div>
            <div className="w-full bg-slate-700 rounded-full h-2">
              <div
                className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full"
                style={{ width: `${(userData.xp / userData.nextLevelXp) * 100}%` }}
              />
            </div>
          </div>

          {/* Challenge Toggle */}
          <div className="mt-3 flex items-center justify-between">
            <span className="text-sm">Open to Challenges</span>
            <button
              onClick={toggleChallenges}
              className={`relative inline-flex h-5 w-10 items-center rounded-full ${
                editableUser.openToChallenges ? 'bg-blue-600' : 'bg-slate-600'
              }`}
            >
              <span
                className={`inline-block h-3 w-3 transform rounded-full bg-white transition ${
                  editableUser.openToChallenges ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
          </div>

          {/* Action Buttons */}
          <div className="mt-3 flex gap-2">
            <Button size="sm" className="flex-1 bg-blue-600 hover:bg-blue-700">
              Add Friend
            </Button>
            <Button size="sm" className="flex-1 bg-green-600 hover:bg-green-700">
              Challenge
            </Button>
            <Button size="sm" className="bg-slate-700 hover:bg-slate-600 px-3">
              <Bell size={16} />
            </Button>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <Tabs value={selectedTab} onValueChange={setSelectedTab} className="flex-1 flex flex-col">
        <TabsList className="bg-slate-900 rounded-none border-b border-slate-800 p-0 h-auto">
          <TabsTrigger
            value="stats"
            className="flex-1 rounded-none border-r border-slate-800 data-[state=active]:bg-slate-800 py-3"
          >
            <BarChart2 size={16} className="mr-1" />
            Stats
          </TabsTrigger>
          <TabsTrigger
            value="games"
            className="flex-1 rounded-none border-r border-slate-800 data-[state=active]:bg-slate-800 py-3"
          >
            <GamepadIcon size={16} className="mr-1" />
            Games
          </TabsTrigger>
          <TabsTrigger
            value="wallet"
            className="flex-1 rounded-none border-r border-slate-800 data-[state=active]:bg-slate-800 py-3"
          >
            <Wallet size={16} className="mr-1" />
            Wallet
          </TabsTrigger>
          <TabsTrigger
            value="settings"
            className="flex-1 rounded-none data-[state=active]:bg-slate-800 py-3"
          >
            <Settings size={16} className="mr-1" />
            Settings
          </TabsTrigger>
        </TabsList>

        <div className="flex-1 overflow-y-auto">
          <TabsContent value="stats" className="p-4 m-0">
            <div className="space-y-4">
              {/* Game Stats */}
              <div className="bg-slate-900 rounded p-3">
                <h3 className="font-medium mb-3">Game Overview</h3>
                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <p className="text-sm text-slate-400">Games Played</p>
                    <p className="text-xl font-bold">{userData.stats.gamesPlayed}</p>
                  </div>
                  <div>
                    <p className="text-sm text-slate-400">Win Rate</p>
                    <p className="text-xl font-bold text-green-400">{userData.stats.winRate}%</p>
                  </div>
                  <div>
                    <p className="text-sm text-slate-400">Win Streak</p>
                    <p className="text-xl font-bold text-yellow-400">{userData.stats.winStreak}</p>
                  </div>
                  <div>
                    <p className="text-sm text-slate-400">Total Matches</p>
                    <p className="text-xl font-bold">{userData.stats.totalMatches}</p>
                  </div>
                </div>
              </div>

              {/* Financial Stats */}
              <div className="bg-slate-900 rounded p-3">
                <h3 className="font-medium mb-3">Financial Overview</h3>
                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <p className="text-sm text-slate-400">Total Wagered</p>
                    <p className="text-xl font-bold">${userData.stats.totalWagered}</p>
                  </div>
                  <div>
                    <p className="text-sm text-slate-400">Total Earned</p>
                    <p className="text-xl font-bold text-green-400">${userData.stats.totalEarned}</p>
                  </div>
                </div>
                <div className="mt-3">
                  <p className="text-sm text-slate-400 mb-1">Profit/Loss</p>
                  <div className="w-full bg-slate-700 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full ${
                        userData.stats.totalEarned > userData.stats.totalWagered
                          ? 'bg-green-500'
                          : 'bg-red-500'
                      }`}
                      style={{ width: `${Math.min(100, (userData.stats.totalEarned / userData.stats.totalWagered) * 100)}%` }}
                    />
                  </div>
                  <p className="text-sm text-right mt-1">
                    {userData.stats.totalEarned > userData.stats.totalWagered ? (
                      <span className="text-green-400">+${userData.stats.totalEarned - userData.stats.totalWagered}</span>
                    ) : (
                      <span className="text-red-400">-${userData.stats.totalWagered - userData.stats.totalEarned}</span>
                    )}
                  </p>
                </div>
              </div>

              {/* Achievements */}
              <div className="bg-slate-900 rounded p-3">
                <h3 className="font-medium mb-3">Recent Achievements</h3>
                <div className="space-y-2">
                  {userData.achievements.filter(a => a.earned).slice(0, 3).map(achievement => (
                    <div key={achievement.id} className="flex items-center gap-3">
                      <div className="w-10 h-10 rounded-full bg-gradient-to-br from-yellow-400 to-yellow-600 flex items-center justify-center">
                        <Trophy className="h-5 w-5 text-white" />
                      </div>
                      <div className="flex-1">
                        <p className="text-sm font-medium">{achievement.name}</p>
                        <p className="text-xs text-slate-400">{achievement.date}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="games" className="p-4 m-0">
            <div className="space-y-3">
              <h3 className="font-medium">Recent Games</h3>
              {userData.gameHistory.map(game => (
                <div key={game.id} className="bg-slate-900 rounded p-3">
                  <div className="flex justify-between items-start">
                    <div>
                      <p className="font-medium">{game.game}</p>
                      <p className="text-sm text-slate-400">
                        vs {game.opponent || game.event}
                      </p>
                    </div>
                    <span className={`px-2 py-0.5 text-xs rounded ${
                      game.result === 'win'
                        ? 'bg-green-900/50 text-green-300'
                        : 'bg-red-900/50 text-red-300'
                    }`}>
                      {game.result.toUpperCase()}
                    </span>
                  </div>
                  <div className="mt-2 flex justify-between text-sm">
                    <span className="text-slate-400">Stake: ${game.stake}</span>
                    <span className={game.result === 'win' ? 'text-green-400' : 'text-red-400'}>
                      {game.result === 'win' ? `+$${game.profit}` : `-$${game.loss}`}
                    </span>
                  </div>
                  <p className="text-xs text-slate-400 mt-1">{game.date}</p>
                </div>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="wallet" className="p-4 m-0">
            <div className="space-y-4">
              {/* Balance */}
              <div className="bg-slate-900 rounded p-3">
                <h3 className="font-medium mb-3">Balance</h3>
                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <p className="text-sm text-slate-400">Main Balance</p>
                    <p className="text-xl font-bold text-green-400">${userData.wallet.balance}</p>
                  </div>
                  <div>
                    <p className="text-sm text-slate-400">Bonus Tokens</p>
                    <p className="text-xl font-bold text-blue-400">{userData.wallet.bonusTokens}</p>
                  </div>
                </div>
                <div className="mt-3 flex gap-2">
                  <Button size="sm" className="flex-1 bg-green-600 hover:bg-green-700">
                    Deposit
                  </Button>
                  <Button size="sm" className="flex-1 bg-blue-600 hover:bg-blue-700">
                    Withdraw
                  </Button>
                </div>
              </div>

              {/* Payment Methods */}
              <div className="bg-slate-900 rounded p-3">
                <h3 className="font-medium mb-3">Payment Methods</h3>
                <div className="space-y-2">
                  {userData.wallet.paymentMethods.map(method => (
                    <div key={method.id} className="flex items-center justify-between p-2 bg-slate-800 rounded">
                      <div className="flex items-center gap-3">
                        <div className="bg-slate-700 p-2 rounded">
                          {method.type === 'Credit Card' ? (
                            <CreditCard size={20} className="text-blue-400" />
                          ) : (
                            <DollarSign size={20} className="text-green-400" />
                          )}
                        </div>
                        <div>
                          <p className="text-sm font-medium">
                            {method.type}
                            {method.default && (
                              <span className="ml-2 text-xs text-green-400">Default</span>
                            )}
                          </p>
                          <p className="text-xs text-slate-400">
                            {method.type === 'Credit Card' ? `**** ${method.last4}` : method.email}
                          </p>
                        </div>
                      </div>
                      <Edit size={16} className="text-slate-400" />
                    </div>
                  ))}
                  <Button size="sm" className="w-full bg-slate-800 hover:bg-slate-700">
                    <Plus size={16} className="mr-1" />
                    Add Payment Method
                  </Button>
                </div>
              </div>

              {/* Spending Limits */}
              <div className="bg-slate-900 rounded p-3">
                <h3 className="font-medium mb-3">Spending Limits</h3>
                <div className="space-y-3">
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span className="text-slate-400">Daily Limit</span>
                      <span>${userData.wallet.spendingLimit.daily}</span>
                    </div>
                    <div className="w-full bg-slate-700 rounded-full h-2">
                      <div className="bg-blue-500 h-2 rounded-full" style={{ width: '30%' }} />
                    </div>
                  </div>
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span className="text-slate-400">Weekly Limit</span>
                      <span>${userData.wallet.spendingLimit.weekly}</span>
                    </div>
                    <div className="w-full bg-slate-700 rounded-full h-2">
                      <div className="bg-blue-500 h-2 rounded-full" style={{ width: '45%' }} />
                    </div>
                  </div>
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span className="text-slate-400">Monthly Limit</span>
                      <span>${userData.wallet.spendingLimit.monthly}</span>
                    </div>
                    <div className="w-full bg-slate-700 rounded-full h-2">
                      <div className="bg-blue-500 h-2 rounded-full" style={{ width: '60%' }} />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="settings" className="p-4 m-0">
            <div className="space-y-4">
              <div className="bg-slate-900 rounded p-3">
                <h3 className="font-medium mb-3">Account Settings</h3>
                <div className="space-y-3">
                  <button className="w-full flex items-center justify-between p-2 bg-slate-800 rounded hover:bg-slate-700">
                    <div className="flex items-center gap-3">
                      <Bell size={20} className="text-slate-400" />
                      <span className="text-sm">Notifications</span>
                    </div>
                    <span className="text-xs text-slate-400">On</span>
                  </button>
                  <button className="w-full flex items-center justify-between p-2 bg-slate-800 rounded hover:bg-slate-700">
                    <div className="flex items-center gap-3">
                      <Shield size={20} className="text-slate-400" />
                      <span className="text-sm">Privacy</span>
                    </div>
                  </button>
                  <button className="w-full flex items-center justify-between p-2 bg-slate-800 rounded hover:bg-slate-700">
                    <div className="flex items-center gap-3">
                      <Lock size={20} className="text-slate-400" />
                      <span className="text-sm">Security</span>
                    </div>
                  </button>
                </div>
              </div>

              <div className="bg-slate-900 rounded p-3">
                <h3 className="font-medium mb-3">Preferences</h3>
                <div className="space-y-3">
                  <button className="w-full flex items-center justify-between p-2 bg-slate-800 rounded hover:bg-slate-700">
                    <div className="flex items-center gap-3">
                      <Globe size={20} className="text-slate-400" />
                      <span className="text-sm">Language</span>
                    </div>
                    <span className="text-xs text-slate-400">English</span>
                  </button>
                  <button className="w-full flex items-center justify-between p-2 bg-slate-800 rounded hover:bg-slate-700">
                    <div className="flex items-center gap-3">
                      <DollarSign size={20} className="text-slate-400" />
                      <span className="text-sm">Currency</span>
                    </div>
                    <span className="text-xs text-slate-400">USD</span>
                  </button>
                </div>
              </div>

              <div className="bg-slate-900 rounded p-3">
                <h3 className="font-medium mb-3">Account Actions</h3>
                <div className="space-y-2">
                  <Button size="sm" className="w-full bg-slate-800 hover:bg-slate-700">
                    <Download size={16} className="mr-1" />
                    Download Your Data
                  </Button>
                  <Button size="sm" className="w-full bg-yellow-900/50 hover:bg-yellow-900/70 text-yellow-300">
                    <Clock size={16} className="mr-1" />
                    Set Time Limits
                  </Button>
                  <Button size="sm" className="w-full bg-red-900/50 hover:bg-red-900/70 text-red-300">
                    <LogOut size={16} className="mr-1" />
                    Log Out
                  </Button>
                </div>
              </div>
            </div>
          </TabsContent>
        </div>
      </Tabs>
    </div>
  );
};

export default MobileProfilePage;