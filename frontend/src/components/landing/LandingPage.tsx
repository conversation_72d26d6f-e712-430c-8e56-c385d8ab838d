import React, { useState, useEffect, useRef } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Link, useNavigate } from 'react-router-dom';
import { 
  Trophy, 
  Zap, 
  Users, 
  Medal,
  Shield,
  ChevronRight,
  Play,
  Star,
  Clock,
  DollarSign,
  CheckCircle,
  ArrowRight,
  Sparkles,
  Globe,
  ChevronLeft
} from 'lucide-react';

// Testimonials Carousel Component
const TestimonialsCarousel = ({ testimonials }: { testimonials: any[] }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const autoPlayRef = useRef<NodeJS.Timeout | null>(null);

  const nextSlide = () => {
    setCurrentIndex((prevIndex) => (prevIndex + 1) % testimonials.length);
  };

  const prevSlide = () => {
    setCurrentIndex((prevIndex) => (prevIndex - 1 + testimonials.length) % testimonials.length);
  };

  const goToSlide = (index: number) => {
    setCurrentIndex(index);
  };

  // Auto-play functionality
  useEffect(() => {
    autoPlayRef.current = setInterval(() => {
      nextSlide();
    }, 5000); // Change slide every 5 seconds

    return () => {
      if (autoPlayRef.current) {
        clearInterval(autoPlayRef.current);
      }
    };
  }, [currentIndex]);

  return (
    <div className="relative">
      <div className="flex items-center justify-center">
        <button
          onClick={prevSlide}
          className="absolute left-0 z-10 p-2 rounded-full bg-slate-800 hover:bg-slate-700 transition-colors"
          aria-label="Previous testimonial"
        >
          <ChevronLeft className="h-6 w-6 text-white" />
        </button>

        <div className="overflow-hidden mx-12">
          <div 
            className="flex transition-transform duration-500 ease-in-out"
            style={{ transform: `translateX(-${currentIndex * 100}%)` }}
          >
            {testimonials.map((testimonial, index) => (
              <div key={index} className="w-full flex-shrink-0 px-4">
                <Card className="bg-slate-800 border-slate-700 max-w-2xl mx-auto">
                  <CardContent className="p-8">
                    <div className="flex items-center space-x-4 mb-6">
                      <div className="h-16 w-16 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center text-3xl">
                        {testimonial.avatar}
                      </div>
                      <div>
                        <p className="text-xl font-medium text-white">{testimonial.name}</p>
                        <p className="text-slate-400">{testimonial.role}</p>
                      </div>
                    </div>
                    <div className="flex mb-4">
                      {[...Array(testimonial.rating)].map((_, i) => (
                        <Star key={i} className="h-5 w-5 text-yellow-500 fill-yellow-500" />
                      ))}
                    </div>
                    <p className="text-lg text-slate-300 text-center italic">"{testimonial.content}"</p>
                  </CardContent>
                </Card>
              </div>
            ))}
          </div>
        </div>

        <button
          onClick={nextSlide}
          className="absolute right-0 z-10 p-2 rounded-full bg-slate-800 hover:bg-slate-700 transition-colors"
          aria-label="Next testimonial"
        >
          <ChevronRight className="h-6 w-6 text-white" />
        </button>
      </div>

      {/* Dots indicator */}
      <div className="flex justify-center space-x-2 mt-6">
        {testimonials.map((_, index) => (
          <button
            key={index}
            onClick={() => goToSlide(index)}
            className={`h-2 w-2 rounded-full transition-all ${
              index === currentIndex
                ? 'bg-purple-500 w-8'
                : 'bg-slate-600'
            }`}
            aria-label={`Go to testimonial ${index + 1}`}
          />
        ))}
      </div>
    </div>
  );
};

const LandingPage = () => {
  const [email, setEmail] = useState('');
  const [jackpotAmount, setJackpotAmount] = useState(125000);
  const [userCount, setUserCount] = useState(15234);
  const navigate = useNavigate();

  // Animate counter
  useEffect(() => {
    const interval = setInterval(() => {
      setJackpotAmount(prev => prev + Math.floor(Math.random() * 100));
      setUserCount(prev => prev + Math.floor(Math.random() * 5));
    }, 3000);
    return () => clearInterval(interval);
  }, []);

  const handleEmailSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    navigate('/register', { state: { email } });
  };

  const features = [
    {
      icon: <Users className="h-6 w-6" />,
      title: "True P2P Gaming",
      description: "No house edge. Compete directly with other players for better odds."
    },
    {
      icon: <Zap className="h-6 w-6" />,
      title: "Instant Payouts",
      description: "Quick 30-second to 5-minute games with immediate rewards."
    },
    {
      icon: <Medal className="h-6 w-6" />,
      title: "Skill-Based Rewards",
      description: "Higher skills unlock better odds and exclusive tournaments."
    },
    {
      icon: <Shield className="h-6 w-6" />,
      title: "Secure & Fair",
      description: "Transparent odds, secure transactions, and responsible gaming."
    }
  ];

  const gameCategories = [
    { name: "Trivia Battles", icon: "🧠", players: "2.3k playing" },
    { name: "Word Games", icon: "📝", players: "1.8k playing" },
    { name: "Logic Puzzles", icon: "🧩", players: "1.5k playing" },
    { name: "Sports Trivia", icon: "⚽", players: "3.1k playing" },
    { name: "Quick Reaction", icon: "⚡", players: "2.7k playing" },
    { name: "Visual Games", icon: "👁️", players: "1.9k playing" }
  ];

  const testimonials = [
    {
      name: "Alex Chen",
      avatar: "🎯",
      role: "Pro Gamer",
      content: "Finally, a platform where my skills actually matter! Won $2,500 last month.",
      rating: 5
    },
    {
      name: "Sarah Johnson",
      avatar: "🏆",
      role: "Sports Enthusiast",
      content: "Best odds I've found anywhere. Love the combination of games and sports betting.",
      rating: 5
    },
    {
      name: "Mike Williams",
      avatar: "🎮",
      role: "Casual Player",
      content: "Quick games fit perfectly into my lunch break. Already up $800!",
      rating: 5
    }
  ];

  const stats = [
    { value: "$10M+", label: "Monthly Payouts" },
    { value: "50K+", label: "Active Players" },
    { value: "95%", label: "Payout Rate" },
    { value: "24/7", label: "Support" }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-b from-slate-950 via-slate-900 to-slate-950">
      {/* Navigation */}
      <nav className="sticky top-0 z-50 backdrop-blur-lg bg-slate-950/80 border-b border-slate-800">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="h-10 w-10 rounded-lg bg-gradient-to-r from-red-500 via-yellow-500 to-purple-500 flex items-center justify-center">
                <Trophy className="h-6 w-6 text-white" />
              </div>
              <h1 className="text-2xl font-bold text-white">BetBet</h1>
            </div>
            <div className="flex items-center space-x-4">
              <Button variant="ghost" className="text-slate-300 hover:text-white">
                How it Works
              </Button>
              <Button variant="ghost" className="text-slate-300 hover:text-white">
                Games
              </Button>
              <Button variant="ghost" className="text-slate-300 hover:text-white">
                Sports
              </Button>
              <Link to="/login">
                <Button className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600">
                  Get Started
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="pt-20 pb-32 px-4 relative">
        {/* Background image with overlay */}
        <div className="absolute inset-0 z-0">
          <img 
            src="/wheel.avif" 
            alt="Background" 
            className="w-full h-full object-cover object-center"
          />
          <div className="absolute inset-0 bg-black bg-opacity-60"></div>
          <div className="absolute inset-0 bg-gradient-to-b from-transparent via-slate-950/50 to-slate-950"></div>
        </div>
        <div className="container mx-auto max-w-7xl relative z-10">
          <div className="text-center">
            <Badge className="mb-4 bg-gradient-to-r from-green-500 to-emerald-500 text-white border-0 px-4 py-1">
              <Sparkles className="h-3 w-3 mr-1" />
              Join {userCount.toLocaleString()} players winning daily
            </Badge>
            
            <h1 className="text-5xl md:text-7xl font-bold text-white mb-6 leading-tight">
              Where Skill Meets
              <span className="block bg-gradient-to-r from-red-500 via-yellow-500 to-purple-500 bg-clip-text text-transparent">
                Opportunity
              </span>
            </h1>
            
            <p className="text-xl text-slate-300 mb-8 max-w-3xl mx-auto">
              The world's first P2P gaming platform combining skill-based games with live sports betting. 
              No house edge, just pure competition.
            </p>

            <form onSubmit={handleEmailSubmit} className="flex flex-col sm:flex-row items-center justify-center gap-4 mb-12">
              <div className="relative">
                <Input
                  type="email"
                  placeholder="Enter your email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="w-64 h-12 px-4 bg-slate-800 border-slate-700 text-white placeholder-slate-400"
                  required
                />
              </div>
              <Button 
                type="submit"
                size="lg" 
                className="h-12 px-8 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600"
              >
                Start Playing Free
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </form>

            <div className="flex items-center justify-center space-x-8">
              <div className="text-center">
                <p className="text-3xl font-bold text-green-500">${jackpotAmount.toLocaleString()}</p>
                <p className="text-sm text-slate-400">Current Jackpot</p>
              </div>
              <div className="h-12 w-px bg-slate-700"></div>
              <div className="text-center">
                <p className="text-3xl font-bold text-purple-500">{userCount.toLocaleString()}</p>
                <p className="text-sm text-slate-400">Players Online</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 px-4 bg-gradient-to-r from-purple-900/10 via-pink-900/10 to-purple-900/10">
        <div className="container mx-auto max-w-6xl">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <p className="text-3xl font-bold text-white mb-1">{stat.value}</p>
                <p className="text-slate-400">{stat.label}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 px-4">
        <div className="container mx-auto max-w-6xl">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-bold text-white mb-4">Why Players Choose BetBet</h2>
            <p className="text-xl text-slate-400">Experience the future of online gaming</p>
          </div>
          <div className="grid md:grid-cols-2 gap-8">
            {features.map((feature, index) => (
              <Card key={index} className="bg-slate-900 border-slate-800 hover:border-slate-700 transition-all h-full">
                <CardContent className="p-8">
                  <div className="mb-4 h-12 w-12 rounded-lg bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center">
                    {feature.icon}
                  </div>
                  <h3 className="text-lg font-semibold text-white mb-2">{feature.title}</h3>
                  <p className="text-slate-400">{feature.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Game Categories */}
      <section className="py-20 px-4 bg-slate-900/50">
        <div className="container mx-auto max-w-7xl">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-bold text-white mb-4">Choose Your Challenge</h2>
            <p className="text-xl text-slate-400">6 game categories, 100+ games, unlimited fun</p>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
            {gameCategories.map((category, index) => (
              <div key={index} className="group cursor-pointer">
                <div className="bg-slate-800 rounded-lg p-4 border border-slate-700 hover:border-purple-500 transition-all h-full">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      <span className="text-3xl">{category.icon}</span>
                      <h3 className="text-lg font-semibold text-white">{category.name}</h3>
                    </div>
                    <ChevronRight className="h-5 w-5 text-slate-400 group-hover:text-purple-500 transition-colors" />
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-green-500">{category.players}</span>
                    <div className="flex -space-x-2">
                      {[1,2,3].map((i) => (
                        <div key={i} className="h-6 w-6 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 border-2 border-slate-800"></div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Live Sports Section */}
      <section className="py-20 px-4">
        <div className="container mx-auto max-w-7xl">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <Badge className="mb-4 bg-gradient-to-r from-red-500 to-orange-500 text-white border-0">
                <Clock className="h-3 w-3 mr-1" />
                LIVE NOW
              </Badge>
              <h2 className="text-4xl font-bold text-white mb-6">
                Bet on Live Sports
                <span className="block text-transparent bg-clip-text bg-gradient-to-r from-green-500 to-blue-500">
                  With Real Players
                </span>
              </h2>
              <p className="text-xl text-slate-400 mb-8">
                Get the best odds on soccer, basketball, tennis, cricket, and more. 
                No bookmaker margins, just pure P2P betting.
              </p>
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <CheckCircle className="h-5 w-5 text-green-500" />
                  <span className="text-slate-300">Live odds updated every second</span>
                </div>
                <div className="flex items-center space-x-3">
                  <CheckCircle className="h-5 w-5 text-green-500" />
                  <span className="text-slate-300">Bet against real players, not the house</span>
                </div>
                <div className="flex items-center space-x-3">
                  <CheckCircle className="h-5 w-5 text-green-500" />
                  <span className="text-slate-300">Cash out anytime during the match</span>
                </div>
              </div>
              <Button size="lg" className="mt-8 bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600">
                Explore Live Matches
                <Play className="ml-2 h-5 w-5" />
              </Button>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <Card className="bg-slate-800 border-slate-700">
                <CardContent className="p-4">
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="text-2xl">⚽</span>
                    <Badge className="bg-red-500 text-white">LIVE</Badge>
                  </div>
                  <p className="text-white font-medium">Barcelona vs Real Madrid</p>
                  <p className="text-2xl font-bold text-white mt-1">2-1</p>
                  <p className="text-sm text-slate-400">78' min</p>
                </CardContent>
              </Card>
              <Card className="bg-slate-800 border-slate-700">
                <CardContent className="p-4">
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="text-2xl">🏀</span>
                    <Badge className="bg-red-500 text-white">LIVE</Badge>
                  </div>
                  <p className="text-white font-medium">Lakers vs Warriors</p>
                  <p className="text-2xl font-bold text-white mt-1">89-92</p>
                  <p className="text-sm text-slate-400">Q3 5:23</p>
                </CardContent>
              </Card>
              <Card className="bg-slate-800 border-slate-700">
                <CardContent className="p-4">
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="text-2xl">🎾</span>
                    <Badge className="bg-orange-500 text-white">SOON</Badge>
                  </div>
                  <p className="text-white font-medium">Djokovic vs Alcaraz</p>
                  <p className="text-sm text-slate-400 mt-1">Starting in 30 min</p>
                  <div className="flex space-x-2 mt-2">
                    <Badge variant="outline" className="border-slate-600">1.95</Badge>
                    <Badge variant="outline" className="border-slate-600">1.85</Badge>
                  </div>
                </CardContent>
              </Card>
              <Card className="bg-slate-800 border-slate-700">
                <CardContent className="p-4">
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="text-2xl">🏏</span>
                    <Badge className="bg-red-500 text-white">LIVE</Badge>
                  </div>
                  <p className="text-white font-medium">India vs Australia</p>
                  <p className="text-2xl font-bold text-white mt-1">245/6</p>
                  <p className="text-sm text-slate-400">42.3 overs</p>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Carousel */}
      <section className="py-20 px-4 bg-slate-900/50">
        <div className="container mx-auto max-w-7xl">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-bold text-white mb-4">Players Love BetBet</h2>
            <p className="text-xl text-slate-400">Join thousands of satisfied winners</p>
          </div>
          
          <TestimonialsCarousel testimonials={testimonials} />
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4">
        <div className="container mx-auto max-w-4xl">
          <Card className="bg-gradient-to-r from-purple-900/50 to-pink-900/50 border-purple-800">
            <CardContent className="p-12 text-center">
              <h2 className="text-4xl font-bold text-white mb-4">
                Ready to Start Winning?
              </h2>
              <p className="text-xl text-slate-300 mb-8">
                Sign up now and get $50 FREE to start playing. No deposit required!
              </p>
              <form onSubmit={handleEmailSubmit} className="flex flex-col sm:flex-row items-center justify-center gap-4">
                <Input
                  type="email"
                  placeholder="Enter your email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="w-full sm:w-64 h-12 px-4 bg-slate-800 border-slate-700 text-white placeholder:text-slate-400"
                  required
                />
                <Button 
                  type="submit"
                  size="lg" 
                  className="h-12 px-8 bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600"
                >
                  Claim Your $50
                  <DollarSign className="ml-2 h-5 w-5" />
                </Button>
              </form>
              <p className="text-sm text-slate-400 mt-4">
                No credit card required • Instant access • Start winning in minutes
              </p>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-16 px-4 border-t border-slate-800">
        <div className="container mx-auto max-w-7xl">
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center space-x-3 mb-4">
                <div className="h-10 w-10 rounded-lg bg-gradient-to-r from-red-500 via-yellow-500 to-purple-500 flex items-center justify-center">
                  <Trophy className="h-6 w-6 text-white" />
                </div>
                <h3 className="text-xl font-bold text-white">BetBet</h3>
              </div>
              <p className="text-slate-400">The future of P2P gaming and sports betting.</p>
            </div>
            
            <div>
              <h4 className="text-white font-medium mb-4">Games</h4>
              <ul className="space-y-2 text-slate-400">
                <li><a href="#" className="hover:text-white transition-colors">Trivia</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Word Games</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Logic Puzzles</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Sports Trivia</a></li>
              </ul>
            </div>
            
            <div>
              <h4 className="text-white font-medium mb-4">Sports</h4>
              <ul className="space-y-2 text-slate-400">
                <li><a href="#" className="hover:text-white transition-colors">Soccer</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Basketball</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Tennis</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Cricket</a></li>
              </ul>
            </div>
            
            <div>
              <h4 className="text-white font-medium mb-4">Support & Legal</h4>
              <ul className="space-y-2 text-slate-400">
                <li><a href="#" className="hover:text-white transition-colors">How It Works</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Help Center</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Terms of Service</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Privacy Policy</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Responsible Gaming</a></li>
              </ul>
            </div>
          </div>
          
          <div className="mt-8 pt-8 border-t border-slate-800 flex flex-col md:flex-row items-center justify-between">
            <p className="text-slate-400 text-sm">© {new Date().getFullYear()} BetBet. All rights reserved.</p>
            <div className="flex items-center space-x-4 mt-4 md:mt-0">
              <Badge variant="outline" className="border-slate-700">
                <Shield className="h-3 w-3 mr-1" />
                SSL Secured
              </Badge>
              <Badge variant="outline" className="border-slate-700">
                <Globe className="h-3 w-3 mr-1" />
                Licensed & Regulated
              </Badge>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default LandingPage;