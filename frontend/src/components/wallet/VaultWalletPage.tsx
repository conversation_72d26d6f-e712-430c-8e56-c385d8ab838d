import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import PaymentModal from '../payment/PaymentModal';
import useWalletStore from '../../stores/walletStore';
import { 
  Wallet,
  ArrowDown,
  ArrowUp,
  ArrowRight,
  CreditCard,
  DollarSign,
  Clock,
  RefreshCw,
  Shield,
  Plus,
  Users,
  Filter,
  Search,
  Smartphone,
  Lock,
  Eye,
  EyeOff,
  Building,
  X,
  Gamepad,
  Trophy,
  Gift,
  Bitcoin,
  Pencil,
  Calendar
} from 'lucide-react';

// Main Vault Wallet Management Component
const VaultWalletPage = () => {
  const { balance } = useWalletStore();
  const [showBalance, setShowBalance] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const [paymentModalOpen, setPaymentModalOpen] = useState(false);
  const [paymentMode, setPaymentMode] = useState<'deposit' | 'withdraw'>('deposit');
  
  return (
    <div className="h-full bg-slate-950 text-white pt-16">
      {/* Payment Modal */}
      <PaymentModal
        isOpen={paymentModalOpen}
        onClose={() => setPaymentModalOpen(false)}
        mode={paymentMode}
        onSuccess={() => {
          // Refresh wallet data after successful transaction
          window.location.reload();
        }}
      />
      {/* Main Grid Layout - Removed header since it's handled by the app layout */}
      <div className="grid grid-cols-12 gap-1 p-1 h-full">
        {/* Column 1: Side Navigation */}
        <div className="col-span-2 bg-slate-900 border border-slate-800 rounded-sm p-1 space-y-1 overflow-y-auto">
          <SidebarNavItem 
            icon={<Wallet className="h-3 w-3" />}
            label="Overview"
            active={activeTab === 'overview'}
            onClick={() => setActiveTab('overview')}
          />
          <SidebarNavItem 
            icon={<ArrowDown className="h-3 w-3" />}
            label="Deposit"
            active={activeTab === 'deposit'}
            onClick={() => setActiveTab('deposit')}
          />
          <SidebarNavItem 
            icon={<ArrowUp className="h-3 w-3" />}
            label="Withdraw"
            active={activeTab === 'withdraw'}
            onClick={() => setActiveTab('withdraw')}
          />
          <SidebarNavItem 
            icon={<ArrowRight className="h-3 w-3" />}
            label="Transfer"
            active={activeTab === 'transfer'}
            onClick={() => setActiveTab('transfer')}
          />
          <SidebarNavItem 
            icon={<CreditCard className="h-3 w-3" />}
            label="Buy Tokens"
            active={activeTab === 'tokens'}
            onClick={() => setActiveTab('tokens')}
          />
          <SidebarNavItem 
            icon={<Clock className="h-3 w-3" />}
            label="History"
            active={activeTab === 'history'}
            onClick={() => setActiveTab('history')}
          />
          <SidebarNavItem 
            icon={<Lock className="h-3 w-3" />}
            label="Locked Funds"
            active={activeTab === 'locked'}
            onClick={() => setActiveTab('locked')}
            badge="3"
          />
          <SidebarNavItem 
            icon={<Shield className="h-3 w-3" />}
            label="Risk Controls"
            active={activeTab === 'risk'}
            onClick={() => setActiveTab('risk')}
          />
          <SidebarNavItem 
            icon={<Users className="h-3 w-3" />}
            label="Pool Wallets"
            active={activeTab === 'pool'}
            onClick={() => setActiveTab('pool')}
          />
          <SidebarNavItem 
            icon={<RefreshCw className="h-3 w-3" />}
            label="Auto Rules"
            active={activeTab === 'auto'}
            onClick={() => setActiveTab('auto')}
          />
        </div>
        
        {/* Column 2-3: Main Content */}
        <div className="col-span-10 grid grid-cols-10 gap-1">
          {/* Main Content Area */}
          <div className="col-span-7 grid grid-rows-5 gap-1">
            {/* Wallet Balance Card */}
            <div className="row-span-2 bg-slate-900 border border-slate-800 rounded-sm p-2 overflow-hidden">
              <div className="flex items-center justify-between mb-3">
                <h2 className="text-xs font-medium text-white">Wallet Balance</h2>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-5 w-5 p-0.5"
                  onClick={() => setShowBalance(!showBalance)}
                >
                  {showBalance ? <EyeOff className="h-3 w-3" /> : <Eye className="h-3 w-3" />}
                </Button>
              </div>
              
              <div className="mb-3">
                <p className="text-xs text-slate-400 mb-1">Total Balance</p>
                {showBalance ? (
                  <p className="text-2xl font-bold text-white">${balance.toFixed(2)}</p>
                ) : (
                  <p className="text-2xl font-bold text-white">••••••</p>
                )}
              </div>
              
              <div className="grid grid-cols-2 gap-3">
                {/* Main Wallet */}
                <Card className="bg-slate-800 border-slate-700">
                  <CardContent className="p-2.5">
                    <div className="flex items-center justify-between mb-1.5">
                      <p className="text-[10px] text-slate-400">Main Wallet</p>
                      <div className="h-4 w-4 rounded-full bg-gradient-to-r from-green-500 to-emerald-500 flex items-center justify-center">
                        <Wallet className="h-2 w-2 text-white" />
                      </div>
                    </div>
                    {showBalance ? (
                      <p className="text-sm font-semibold text-white">${(balance * 0.68).toFixed(2)}</p>
                    ) : (
                      <p className="text-sm font-semibold text-white">••••••</p>
                    )}
                  </CardContent>
                </Card>
                
                {/* Game Wallet */}
                <Card className="bg-slate-800 border-slate-700">
                  <CardContent className="p-2.5">
                    <div className="flex items-center justify-between mb-1.5">
                      <p className="text-[10px] text-slate-400">Game Wallet</p>
                      <div className="h-4 w-4 rounded-full bg-gradient-to-r from-purple-500 to-blue-500 flex items-center justify-center">
                        <Gamepad className="h-2 w-2 text-white" />
                      </div>
                    </div>
                    {showBalance ? (
                      <p className="text-sm font-semibold text-white">$250.00</p>
                    ) : (
                      <p className="text-sm font-semibold text-white">••••••</p>
                    )}
                  </CardContent>
                </Card>
                
                {/* Locked Funds */}
                <Card className="bg-slate-800 border-slate-700">
                  <CardContent className="p-2.5">
                    <div className="flex items-center justify-between mb-1.5">
                      <p className="text-[10px] text-slate-400">Locked Funds</p>
                      <div className="h-4 w-4 rounded-full bg-gradient-to-r from-orange-500 to-red-500 flex items-center justify-center">
                        <Lock className="h-2 w-2 text-white" />
                      </div>
                    </div>
                    {showBalance ? (
                      <p className="text-sm font-semibold text-white">$100.00</p>
                    ) : (
                      <p className="text-sm font-semibold text-white">••••••</p>
                    )}
                  </CardContent>
                </Card>
                
                {/* Bonus Wallet */}
                <Card className="bg-slate-800 border-slate-700">
                  <CardContent className="p-2.5">
                    <div className="flex items-center justify-between mb-1.5">
                      <p className="text-[10px] text-slate-400">Bonus Wallet</p>
                      <div className="h-4 w-4 rounded-full bg-gradient-to-r from-yellow-500 to-amber-500 flex items-center justify-center">
                        <DollarSign className="h-2 w-2 text-white" />
                      </div>
                    </div>
                    {showBalance ? (
                      <p className="text-sm font-semibold text-white">$50.00</p>
                    ) : (
                      <p className="text-sm font-semibold text-white">••••••</p>
                    )}
                  </CardContent>
                </Card>
              </div>
              
              {/* Quick Actions */}
              <div className="grid grid-cols-4 gap-1.5 mt-3">
                <QuickActionButton
                  icon={<ArrowDown className="h-3 w-3" />}
                  label="Deposit"
                  colorClass="from-green-500 to-emerald-500"
                  onClick={() => {
                    setPaymentMode('deposit');
                    setPaymentModalOpen(true);
                  }}
                />
                <QuickActionButton
                  icon={<ArrowUp className="h-3 w-3" />}
                  label="Withdraw"
                  colorClass="from-blue-500 to-cyan-500"
                  onClick={() => {
                    setPaymentMode('withdraw');
                    setPaymentModalOpen(true);
                  }}
                />
                <QuickActionButton
                  icon={<ArrowRight className="h-3 w-3" />}
                  label="Transfer"
                  colorClass="from-purple-500 to-pink-500"
                  onClick={() => setActiveTab('transfer')}
                />
                <QuickActionButton
                  icon={<CreditCard className="h-3 w-3" />}
                  label="Buy Tokens"
                  colorClass="from-amber-500 to-orange-500"
                  onClick={() => setActiveTab('tokens')}
                />
              </div>
            </div>
            
            {/* Recent Activity */}
            <div className="row-span-3 bg-slate-900 border border-slate-800 rounded-sm overflow-hidden">
              <div className="h-6 px-2 flex items-center justify-between border-b border-slate-800">
                <h3 className="text-xs font-medium text-white">Recent Transactions</h3>
                <div className="flex items-center space-x-2">
                  <div className="relative">
                    <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-3 w-3 text-slate-400" />
                    <Input 
                      placeholder="Search..." 
                      className="h-5 pl-7 pr-2 text-[10px] bg-slate-800 border-slate-700 rounded-sm w-32"
                    />
                  </div>
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    className="h-5 w-5 p-0.5"
                  >
                    <Filter className="h-3 w-3 text-slate-400" />
                  </Button>
                </div>
              </div>
              
              <div className="overflow-auto" style={{ height: 'calc(100% - 1.5rem)' }}>
                <TransactionItem 
                  type="deposit"
                  description="Deposit via Credit Card"
                  amount={200}
                  time="Today, 10:45 AM"
                  status="completed"
                />
                
                <TransactionItem 
                  type="game-win"
                  description="Chess Blitz Win"
                  amount={75}
                  time="Yesterday, 8:30 PM"
                  status="completed"
                />
                
                <TransactionItem 
                  type="transfer"
                  description="Transfer to Game Wallet"
                  amount={-150}
                  time="Yesterday, 7:15 PM"
                  status="completed"
                />
                
                <TransactionItem 
                  type="withdrawal"
                  description="Withdrawal to Bank Account"
                  amount={-300}
                  time="Aug 15, 2:30 PM"
                  status="completed"
                />
                
                <TransactionItem 
                  type="bonus"
                  description="Weekly Bonus Credited"
                  amount={25}
                  time="Aug 14, 12:00 PM"
                  status="completed"
                />
                
                <TransactionItem 
                  type="shared-bet"
                  description="Shared Bet with Player Z"
                  amount={-50}
                  time="Aug 12, 6:45 PM"
                  status="pending"
                />
                
                <TransactionItem 
                  type="deposit"
                  description="Deposit via Bank Transfer"
                  amount={500}
                  time="Aug 10, 9:20 AM"
                  status="completed"
                />
                
                <TransactionItem 
                  type="withdrawal"
                  description="Withdrawal to PayPal"
                  amount={-120}
                  time="Aug 8, 4:15 PM"
                  status="completed"
                />
                
                <TransactionItem 
                  type="game-win"
                  description="Blur Detective Win"
                  amount={95}
                  time="Aug 7, 7:30 PM"
                  status="completed"
                />
              </div>
            </div>
          </div>
          
          {/* Right Sidebar - Stats and Info */}
          <div className="col-span-3 grid grid-rows-5 gap-1">
            {/* Risk Status Indicators */}
            <div className="row-span-1 bg-slate-900 border border-slate-800 rounded-sm overflow-hidden">
              <div className="h-6 px-2 flex items-center justify-between border-b border-slate-800">
                <h3 className="text-xs font-medium text-white">Risk Status</h3>
              </div>
              
              <div className="p-2 space-y-2">
                {/* Daily Loss Cap */}
                <div className="space-y-1">
                  <div className="flex items-center justify-between">
                    <p className="text-[10px] text-slate-400">Daily Loss Cap ($50)</p>
                    <p className="text-[10px] text-green-500">$25 / $50</p>
                  </div>
                  <Progress value={50} className="h-1.5" />
                </div>
                
                {/* Betting Frequency */}
                <div className="space-y-1">
                  <div className="flex items-center justify-between">
                    <p className="text-[10px] text-slate-400">Betting Frequency</p>
                    <p className="text-[10px] text-yellow-500">Moderate</p>
                  </div>
                  <Progress value={65} className="h-1.5" />
                </div>
                
                <div className="flex items-center space-x-1 text-[10px] text-slate-400">
                  <Shield className="h-3 w-3 text-green-500" />
                  <p>Stop-Loss Protection Active</p>
                </div>
              </div>
            </div>
            
            {/* Payment Methods */}
            <div className="row-span-2 bg-slate-900 border border-slate-800 rounded-sm overflow-hidden">
              <div className="h-6 px-2 flex items-center justify-between border-b border-slate-800">
                <h3 className="text-xs font-medium text-white">Payment Methods</h3>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="h-5 w-5 p-0.5"
                >
                  <Plus className="h-3 w-3 text-slate-400" />
                </Button>
              </div>
              
              <div className="overflow-auto" style={{ height: 'calc(100% - 1.5rem)' }}>
                <PaymentMethodItem 
                  type="credit-card"
                  name="Visa •••• 4242"
                  details="Expires 09/25"
                  isDefault={true}
                />
                
                <PaymentMethodItem 
                  type="bank"
                  name="Bank Account"
                  details="•••• 7890"
                  isDefault={false}
                />
                
                <PaymentMethodItem 
                  type="mobile"
                  name="Mobile Money"
                  details="(+254) •••• 5678"
                  isDefault={false}
                />
                
                <PaymentMethodItem 
                  type="crypto"
                  name="Bitcoin Wallet"
                  details="3Ft8c••••••"
                  isDefault={false}
                />
              </div>
            </div>
            
            {/* Scheduled Actions */}
            <div className="row-span-2 bg-slate-900 border border-slate-800 rounded-sm overflow-hidden">
              <div className="h-6 px-2 flex items-center justify-between border-b border-slate-800">
                <h3 className="text-xs font-medium text-white">Scheduled Actions</h3>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="h-5 w-5 p-0.5"
                >
                  <Plus className="h-3 w-3 text-slate-400" />
                </Button>
              </div>
              
              <div className="overflow-auto" style={{ height: 'calc(100% - 1.5rem)' }}>
                <ScheduledActionItem 
                  type="auto-reload"
                  description="Auto-reload Game Wallet"
                  details="When balance < $50, add $100"
                  nextRun="Aug 18 (est.)"
                  isActive={true}
                />
                
                <ScheduledActionItem 
                  type="auto-withdraw"
                  description="Auto-withdraw to Bank"
                  details="When Main Wallet > $1,000"
                  nextRun="No estimate"
                  isActive={true}
                />
                
                <ScheduledActionItem 
                  type="recurring-transfer"
                  description="Weekly Pool Contribution"
                  details="$10 to Team Alpha"
                  nextRun="Aug 21"
                  isActive={false}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Reusable Components

// Sidebar Navigation Item
interface SidebarNavItemProps {
  icon: React.ReactNode;
  label: string;
  active: boolean;
  onClick: () => void;
  badge?: string | null;
}

const SidebarNavItem: React.FC<SidebarNavItemProps> = ({ icon, label, active, onClick, badge = null }) => (
  <Button 
    variant="ghost"
    className={`w-full justify-between h-6 px-2 text-xs rounded-sm ${
      active 
        ? 'bg-slate-800 text-white' 
        : 'text-slate-400 hover:text-white hover:bg-slate-800/50'
    }`}
    onClick={onClick}
  >
    <div className="flex items-center">
      {icon}
      <span className="ml-1.5">{label}</span>
    </div>
    {badge && (
      <Badge className="h-4 px-1 text-[10px] bg-green-500 text-white">
        {badge}
      </Badge>
    )}
  </Button>
);

// Quick Action Button
interface QuickActionButtonProps {
  icon: React.ReactNode;
  label: string;
  colorClass: string;
  onClick: () => void;
}

const QuickActionButton: React.FC<QuickActionButtonProps> = ({ icon, label, colorClass, onClick }) => (
  <Button 
    className={`h-7 text-[10px] rounded-sm bg-gradient-to-r ${colorClass} hover:opacity-90 p-0`}
    onClick={onClick}
  >
    <div className="flex flex-col items-center py-1 w-full">
      {icon}
      <span className="mt-0.5">{label}</span>
    </div>
  </Button>
);

// Transaction Item Component
interface TransactionItemProps {
  type: string;
  description: string;
  amount: number;
  time: string;
  status: string;
}

const TransactionItem: React.FC<TransactionItemProps> = ({ type, description, amount, time, status }) => {
  // Type-based icon and color
  const getTypeIcon = () => {
    switch(type) {
      case 'deposit':
        return <ArrowDown className="h-3 w-3 text-green-500" />;
      case 'withdrawal':
        return <ArrowUp className="h-3 w-3 text-red-500" />;
      case 'transfer':
        return <ArrowRight className="h-3 w-3 text-blue-500" />;
      case 'game-win':
        return <Trophy className="h-3 w-3 text-amber-500" />;
      case 'bonus':
        return <Gift className="h-3 w-3 text-purple-500" />;
      case 'shared-bet':
        return <Users className="h-3 w-3 text-cyan-500" />;
      default:
        return <Clock className="h-3 w-3 text-slate-400" />;
    }
  };
  
  // Status badge
  const getStatusBadge = () => {
    switch(status) {
      case 'completed':
        return <Badge className="h-4 px-1 text-[10px] bg-green-900/30 text-green-500 border-green-900">Completed</Badge>;
      case 'pending':
        return <Badge className="h-4 px-1 text-[10px] bg-yellow-900/30 text-yellow-500 border-yellow-900">Pending</Badge>;
      case 'failed':
        return <Badge className="h-4 px-1 text-[10px] bg-red-900/30 text-red-500 border-red-900">Failed</Badge>;
      default:
        return null;
    }
  };
  
  return (
    <div className="px-2 py-1.5 border-b border-slate-800/50 hover:bg-slate-800/30">
      <div className="flex items-center justify-between mb-1">
        <div className="flex items-center">
          <div className="h-5 w-5 rounded-full bg-slate-800 flex items-center justify-center mr-2">
            {getTypeIcon()}
          </div>
          <span className="text-xs text-white">{description}</span>
        </div>
        <span className={`text-xs font-medium ${amount >= 0 ? 'text-green-500' : 'text-red-500'}`}>
          {amount >= 0 ? `+$${amount.toFixed(2)}` : `-$${Math.abs(amount).toFixed(2)}`}
        </span>
      </div>
      
      <div className="flex items-center justify-between">
        <span className="text-[10px] text-slate-400">{time}</span>
        {getStatusBadge()}
      </div>
    </div>
  );
};

// Payment Method Item Component
interface PaymentMethodItemProps {
  type: string;
  name: string;
  details: string;
  isDefault: boolean;
}

const PaymentMethodItem: React.FC<PaymentMethodItemProps> = ({ type, name, details, isDefault }) => {
  // Type-based icon
  const getTypeIcon = () => {
    switch(type) {
      case 'credit-card':
        return <CreditCard className="h-3 w-3 text-blue-500" />;
      case 'bank':
        return <Building className="h-3 w-3 text-green-500" />;
      case 'mobile':
        return <Smartphone className="h-3 w-3 text-purple-500" />;
      case 'crypto':
        return <Bitcoin className="h-3 w-3 text-orange-500" />;
      default:
        return <CreditCard className="h-3 w-3 text-slate-400" />;
    }
  };
  
  return (
    <div className="px-2 py-1.5 border-b border-slate-800/50 hover:bg-slate-800/30">
      <div className="flex items-center justify-between mb-1">
        <div className="flex items-center">
          <div className="h-5 w-5 rounded-full bg-slate-800 flex items-center justify-center mr-2">
            {getTypeIcon()}
          </div>
          <span className="text-xs text-white">{name}</span>
        </div>
        {isDefault && (
          <Badge className="h-4 px-1 text-[10px] bg-slate-800 text-slate-300">
            Default
          </Badge>
        )}
      </div>
      
      <div className="flex items-center justify-between">
        <span className="text-[10px] text-slate-400">{details}</span>
        <div className="flex space-x-1">
          <Button variant="ghost" size="sm" className="h-5 w-5 p-0.5 text-slate-400 hover:text-white">
            <Pencil className="h-3 w-3" />
          </Button>
          <Button variant="ghost" size="sm" className="h-5 w-5 p-0.5 text-slate-400 hover:text-white">
            <X className="h-3 w-3" />
          </Button>
        </div>
      </div>
    </div>
  );
};

// Scheduled Action Item Component
interface ScheduledActionItemProps {
  type: string;
  description: string;
  details: string;
  nextRun: string;
  isActive: boolean;
}

const ScheduledActionItem: React.FC<ScheduledActionItemProps> = ({ type, description, details, nextRun, isActive }) => {
  // Type-based icon
  const getTypeIcon = () => {
    switch(type) {
      case 'auto-reload':
        return <RefreshCw className="h-3 w-3 text-green-500" />;
      case 'auto-withdraw':
        return <ArrowUp className="h-3 w-3 text-blue-500" />;
      case 'recurring-transfer':
        return <Calendar className="h-3 w-3 text-purple-500" />;
      default:
        return <Clock className="h-3 w-3 text-slate-400" />;
    }
  };
  
  return (
    <div className="px-2 py-1.5 border-b border-slate-800/50 hover:bg-slate-800/30">
      <div className="flex items-center justify-between mb-1">
        <div className="flex items-center">
          <div className="h-5 w-5 rounded-full bg-slate-800 flex items-center justify-center mr-2">
            {getTypeIcon()}
          </div>
          <span className="text-xs text-white">{description}</span>
        </div>
        <div className="flex items-center">
          <label className="relative inline-flex items-center cursor-pointer">
            <input type="checkbox" checked={isActive} className="sr-only peer" readOnly />
            <div className="w-7 h-4 bg-slate-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-3 after:w-3 after:transition-all peer-checked:bg-green-600"></div>
          </label>
        </div>
      </div>
      
      <div className="flex items-center justify-between">
        <span className="text-[10px] text-slate-400">{details}</span>
        <span className="text-[10px] text-slate-400">Next: {nextRun}</span>
      </div>
    </div>
  );
};

export default VaultWalletPage;