import { useState, useEffect } from 'react';
import VaultWalletPage from './VaultWalletPage';
import MobileVaultPage from './MobileVaultPage';

const Wallet = () => {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  return isMobile ? <MobileVaultPage /> : <VaultWalletPage />;
};

export default Wallet;