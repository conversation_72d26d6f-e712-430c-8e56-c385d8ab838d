import React, { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Wallet,
  ArrowDown,
  ArrowUp,
  ArrowRight,
  CreditCard,
  DollarSign,
  Clock,
  RefreshCw,
  Shield,
  Plus,
  Eye,
  EyeOff,
  Lock,
  Gamepad,
  Trophy,
  Gift,
  Building,
  Smartphone,
  Bitcoin,
  Menu,
  X,
  MoreVertical,
  Filter,
  Search
} from 'lucide-react';

const MobileVaultPage = () => {
  const [showBalance, setShowBalance] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const [showMenu, setShowMenu] = useState(false);
  
  const tabs = [
    { id: 'overview', name: 'Overview', icon: <Wallet className="h-4 w-4" /> },
    { id: 'transactions', name: 'Transactions', icon: <Clock className="h-4 w-4" /> },
    { id: 'payments', name: 'Payments', icon: <CreditCard className="h-4 w-4" /> },
    { id: 'settings', name: 'Setting<PERSON>', icon: <Shield className="h-4 w-4" /> }
  ];
  
  return (
    <div className="min-h-screen bg-slate-950 text-white pb-20 pt-16">
      {/* Fixed Header */}
      <div className="fixed top-16 left-0 right-0 z-30 bg-slate-900 border-b border-slate-800">
        <div className="flex items-center justify-between p-3">
          <div className="flex items-center">
            <Wallet className="h-5 w-5 text-green-500 mr-2" />
            <h1 className="text-lg font-semibold text-white">Vault</h1>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowBalance(!showBalance)}
              className="h-9 w-9 p-0"
            >
              {showBalance ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowMenu(!showMenu)}
              className="h-9 w-9 p-0"
            >
              <Menu className="h-5 w-5" />
            </Button>
          </div>
        </div>
        
        {/* Tab Navigation */}
        <div className="flex px-3 pb-3 overflow-x-auto scrollbar-hide">
          {tabs.map(tab => (
            <Button
              key={tab.id}
              variant={activeTab === tab.id ? 'default' : 'ghost'}
              size="sm"
              className={`flex-1 h-9 text-sm whitespace-nowrap ${
                activeTab === tab.id 
                  ? 'bg-purple-600 text-white' 
                  : 'text-slate-400'
              }`}
              onClick={() => setActiveTab(tab.id)}
            >
              {tab.icon}
              <span className="ml-1">{tab.name}</span>
            </Button>
          ))}
        </div>
      </div>
      
      {/* Main Content */}
      <div className="pt-28">
        {activeTab === 'overview' && <OverviewSection showBalance={showBalance} />}
        {activeTab === 'transactions' && <TransactionsSection />}
        {activeTab === 'payments' && <PaymentsSection />}
        {activeTab === 'settings' && <SettingsSection />}
      </div>
      
      {/* Menu Overlay */}
      {showMenu && (
        <div className="fixed inset-0 z-40 bg-slate-900/95">
          <div className="p-4">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-lg font-semibold text-white">Wallet Actions</h2>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowMenu(false)}
                className="h-8 w-8 p-0"
              >
                <X className="h-5 w-5" />
              </Button>
            </div>
            
            <div className="space-y-2">
              <Button className="w-full h-12 justify-start bg-gradient-to-r from-green-500 to-emerald-500">
                <ArrowDown className="h-5 w-5 mr-3" />
                Deposit
              </Button>
              <Button className="w-full h-12 justify-start bg-gradient-to-r from-blue-500 to-cyan-500">
                <ArrowUp className="h-5 w-5 mr-3" />
                Withdraw
              </Button>
              <Button className="w-full h-12 justify-start bg-gradient-to-r from-purple-500 to-pink-500">
                <ArrowRight className="h-5 w-5 mr-3" />
                Transfer
              </Button>
              <Button className="w-full h-12 justify-start bg-gradient-to-r from-amber-500 to-orange-500">
                <CreditCard className="h-5 w-5 mr-3" />
                Buy Tokens
              </Button>
            </div>
          </div>
        </div>
      )}
      
      {/* Floating Action Button */}
      <Button
        className="fixed bottom-6 right-6 h-14 w-14 rounded-full bg-gradient-to-r from-green-500 to-emerald-500 shadow-lg z-20"
        onClick={() => setShowMenu(true)}
      >
        <Plus className="h-6 w-6" />
      </Button>
    </div>
  );
};

// Overview Section
const OverviewSection = ({ showBalance }: { showBalance: boolean }) => (
  <div className="p-3 space-y-4">
    {/* Total Balance Card */}
    <Card className="p-4 bg-gradient-to-r from-slate-900 to-slate-800 border-slate-700">
      <div className="text-center">
        <p className="text-sm text-slate-400 mb-2">Total Balance</p>
        {showBalance ? (
          <p className="text-3xl font-bold text-white mb-1">$1,250.00</p>
        ) : (
          <p className="text-3xl font-bold text-white mb-1">••••••</p>
        )}
        <div className="flex items-center justify-center">
          <span className="text-xs text-green-500">+$125.00 (11.1%)</span>
          <span className="text-xs text-slate-400 ml-2">Today</span>
        </div>
      </div>
    </Card>
    
    {/* Wallet Breakdown */}
    <div className="grid grid-cols-2 gap-3">
      <MobileWalletCard
        title="Main Wallet"
        amount={showBalance ? "$850.00" : "••••••"}
        icon={<Wallet className="h-4 w-4" />}
        colorClass="from-green-500 to-emerald-500"
      />
      <MobileWalletCard
        title="Game Wallet"
        amount={showBalance ? "$250.00" : "••••••"}
        icon={<Gamepad className="h-4 w-4" />}
        colorClass="from-purple-500 to-blue-500"
      />
      <MobileWalletCard
        title="Locked Funds"
        amount={showBalance ? "$100.00" : "••••••"}
        icon={<Lock className="h-4 w-4" />}
        colorClass="from-orange-500 to-red-500"
      />
      <MobileWalletCard
        title="Bonus Wallet"
        amount={showBalance ? "$50.00" : "••••••"}
        icon={<DollarSign className="h-4 w-4" />}
        colorClass="from-yellow-500 to-amber-500"
      />
    </div>
    
    {/* Risk Status */}
    <Card className="p-4 bg-slate-900 border-slate-800">
      <h3 className="text-sm font-medium text-white mb-3">Risk Status</h3>
      <div className="space-y-3">
        <div>
          <div className="flex items-center justify-between mb-1">
            <p className="text-xs text-slate-400">Daily Loss Cap</p>
            <p className="text-xs text-green-500">$25 / $50</p>
          </div>
          <Progress value={50} className="h-2" />
        </div>
        <div>
          <div className="flex items-center justify-between mb-1">
            <p className="text-xs text-slate-400">Betting Frequency</p>
            <p className="text-xs text-yellow-500">Moderate</p>
          </div>
          <Progress value={65} className="h-2" />
        </div>
        <div className="flex items-center text-xs text-green-500">
          <Shield className="h-4 w-4 mr-2" />
          <span>Stop-Loss Protection Active</span>
        </div>
      </div>
    </Card>
    
    {/* Recent Activity */}
    <Card className="p-4 bg-slate-900 border-slate-800">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-sm font-medium text-white">Recent Activity</h3>
        <Button variant="ghost" size="sm" className="text-xs text-purple-500">
          View All
        </Button>
      </div>
      
      <div className="space-y-3">
        <MobileTransactionItem
          type="deposit"
          description="Credit Card Deposit"
          amount={200}
          time="10:45 AM"
          status="completed"
        />
        <MobileTransactionItem
          type="game-win"
          description="Chess Blitz Win"
          amount={75}
          time="Yesterday"
          status="completed"
        />
        <MobileTransactionItem
          type="transfer"
          description="Transfer to Game"
          amount={-150}
          time="Yesterday"
          status="completed"
        />
      </div>
    </Card>
  </div>
);

// Transactions Section
const TransactionsSection = () => (
  <div className="p-3">
    {/* Search and Filter */}
    <div className="flex items-center space-x-2 mb-4">
      <div className="relative flex-1">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
        <input
          type="text"
          placeholder="Search transactions..."
          className="w-full h-10 pl-10 pr-3 text-sm bg-slate-900 border border-slate-700 rounded-lg text-white placeholder-slate-400"
        />
      </div>
      <Button variant="outline" size="sm" className="h-10 px-3 border-slate-700">
        <Filter className="h-4 w-4" />
      </Button>
    </div>
    
    {/* Transaction List */}
    <div className="space-y-3">
      <MobileTransactionCard
        type="deposit"
        description="Deposit via Credit Card"
        amount={200}
        time="Today, 10:45 AM"
        status="completed"
      />
      <MobileTransactionCard
        type="game-win"
        description="Chess Blitz Win"
        amount={75}
        time="Yesterday, 8:30 PM"
        status="completed"
      />
      <MobileTransactionCard
        type="transfer"
        description="Transfer to Game Wallet"
        amount={-150}
        time="Yesterday, 7:15 PM"
        status="completed"
      />
      <MobileTransactionCard
        type="withdrawal"
        description="Withdrawal to Bank"
        amount={-300}
        time="Aug 15, 2:30 PM"
        status="completed"
      />
      <MobileTransactionCard
        type="bonus"
        description="Weekly Bonus"
        amount={25}
        time="Aug 14, 12:00 PM"
        status="completed"
      />
    </div>
  </div>
);

// Payments Section
const PaymentsSection = () => (
  <div className="p-3 space-y-4">
    {/* Payment Methods */}
    <Card className="p-4 bg-slate-900 border-slate-800">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-sm font-medium text-white">Payment Methods</h3>
        <Button size="sm" className="h-8 text-xs bg-gradient-to-r from-green-500 to-emerald-500">
          <Plus className="h-3 w-3 mr-1" />
          Add
        </Button>
      </div>
      
      <div className="space-y-3">
        <MobilePaymentMethod
          type="credit-card"
          name="Visa •••• 4242"
          details="Expires 09/25"
          isDefault={true}
        />
        <MobilePaymentMethod
          type="bank"
          name="Bank Account"
          details="•••• 7890"
          isDefault={false}
        />
        <MobilePaymentMethod
          type="mobile"
          name="Mobile Money"
          details="(+254) •••• 5678"
          isDefault={false}
        />
        <MobilePaymentMethod
          type="crypto"
          name="Bitcoin Wallet"
          details="3Ft8c••••••"
          isDefault={false}
        />
      </div>
    </Card>
    
    {/* Auto Rules */}
    <Card className="p-4 bg-slate-900 border-slate-800">
      <h3 className="text-sm font-medium text-white mb-3">Auto Rules</h3>
      
      <div className="space-y-3">
        <MobileAutoRule
          type="auto-reload"
          title="Auto-reload Game Wallet"
          description="When balance < $50, add $100"
          isActive={true}
        />
        <MobileAutoRule
          type="auto-withdraw"
          title="Auto-withdraw to Bank"
          description="When Main Wallet > $1,000"
          isActive={true}
        />
        <MobileAutoRule
          type="recurring-transfer"
          title="Weekly Pool Contribution"
          description="$10 to Team Alpha"
          isActive={false}
        />
      </div>
    </Card>
  </div>
);

// Settings Section
const SettingsSection = () => (
  <div className="p-3 space-y-4">
    <Card className="p-4 bg-slate-900 border-slate-800">
      <h3 className="text-sm font-medium text-white mb-3">Risk Controls</h3>
      
      <div className="space-y-4">
        <div>
          <label className="text-xs text-slate-400">Daily Loss Cap</label>
          <div className="flex items-center space-x-3 mt-1">
            <input
              type="range"
              className="flex-1"
              value={50}
              max={100}
              readOnly
            />
            <span className="text-sm text-white">$50</span>
          </div>
        </div>
        
        <div>
          <label className="text-xs text-slate-400">Max Bet Amount</label>
          <div className="flex items-center space-x-3 mt-1">
            <input
              type="range"
              className="flex-1"
              value={25}
              max={100}
              readOnly
            />
            <span className="text-sm text-white">$25</span>
          </div>
        </div>
        
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm text-white">Stop-Loss Protection</p>
            <p className="text-xs text-slate-400">Auto-stop at loss limit</p>
          </div>
          <label className="relative inline-flex items-center cursor-pointer">
            <input type="checkbox" checked className="sr-only peer" readOnly />
            <div className="w-11 h-6 bg-slate-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
          </label>
        </div>
      </div>
    </Card>
    
    <Card className="p-4 bg-slate-900 border-slate-800">
      <h3 className="text-sm font-medium text-white mb-3">Security</h3>
      
      <div className="space-y-3">
        <Button variant="outline" className="w-full h-10 justify-between text-sm border-slate-700">
          <span>Change PIN</span>
          <MoreVertical className="h-4 w-4" />
        </Button>
        <Button variant="outline" className="w-full h-10 justify-between text-sm border-slate-700">
          <span>Two-Factor Authentication</span>
          <Badge className="text-xs bg-green-600">Active</Badge>
        </Button>
        <Button variant="outline" className="w-full h-10 justify-between text-sm border-slate-700">
          <span>Login History</span>
          <MoreVertical className="h-4 w-4" />
        </Button>
      </div>
    </Card>
  </div>
);

// Mobile Component Helpers

const MobileWalletCard = ({ title, amount, icon, colorClass }: any) => (
  <Card className="p-3 bg-slate-900 border-slate-800">
    <div className="flex items-center justify-between mb-2">
      <p className="text-xs text-slate-400">{title}</p>
      <div className={`h-6 w-6 rounded-full bg-gradient-to-r ${colorClass} flex items-center justify-center`}>
        {React.cloneElement(icon, { className: "h-3 w-3 text-white" })}
      </div>
    </div>
    <p className="text-sm font-semibold text-white">{amount}</p>
  </Card>
);

const MobileTransactionItem = ({ type, description, amount, time }: any) => {
  const getIcon = () => {
    switch(type) {
      case 'deposit':
        return <ArrowDown className="h-4 w-4 text-green-500" />;
      case 'game-win':
        return <Trophy className="h-4 w-4 text-amber-500" />;
      case 'transfer':
        return <ArrowRight className="h-4 w-4 text-blue-500" />;
      default:
        return <Clock className="h-4 w-4 text-slate-400" />;
    }
  };
  
  return (
    <div className="flex items-center justify-between">
      <div className="flex items-center">
        <div className="h-8 w-8 rounded-full bg-slate-800 flex items-center justify-center mr-3">
          {getIcon()}
        </div>
        <div>
          <p className="text-sm text-white">{description}</p>
          <p className="text-xs text-slate-400">{time}</p>
        </div>
      </div>
      <span className={`text-sm font-medium ${amount >= 0 ? 'text-green-500' : 'text-red-500'}`}>
        {amount >= 0 ? `+$${amount}` : `-$${Math.abs(amount)}`}
      </span>
    </div>
  );
};

const MobileTransactionCard = ({ type, description, amount, time }: any) => {
  const getIcon = () => {
    switch(type) {
      case 'deposit':
        return <ArrowDown className="h-4 w-4 text-green-500" />;
      case 'game-win':
        return <Trophy className="h-4 w-4 text-amber-500" />;
      case 'transfer':
        return <ArrowRight className="h-4 w-4 text-blue-500" />;
      case 'withdrawal':
        return <ArrowUp className="h-4 w-4 text-red-500" />;
      case 'bonus':
        return <Gift className="h-4 w-4 text-purple-500" />;
      default:
        return <Clock className="h-4 w-4 text-slate-400" />;
    }
  };
  
  const getStatusBadge = () => {
    switch(status) {
      case 'completed':
        return <Badge className="text-xs bg-green-900/30 text-green-500">Completed</Badge>;
      case 'pending':
        return <Badge className="text-xs bg-yellow-900/30 text-yellow-500">Pending</Badge>;
      default:
        return null;
    }
  };
  
  return (
    <Card className="p-3 bg-slate-900 border-slate-800">
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center">
          <div className="h-10 w-10 rounded-full bg-slate-800 flex items-center justify-center mr-3">
            {getIcon()}
          </div>
          <div>
            <p className="text-sm text-white">{description}</p>
            <p className="text-xs text-slate-400">{time}</p>
          </div>
        </div>
        <div className="text-right">
          <span className={`text-sm font-medium ${amount >= 0 ? 'text-green-500' : 'text-red-500'}`}>
            {amount >= 0 ? `+$${amount}` : `-$${Math.abs(amount)}`}
          </span>
          <div className="mt-1">{getStatusBadge()}</div>
        </div>
      </div>
    </Card>
  );
};

const MobilePaymentMethod = ({ type, name, details, isDefault }: any) => {
  const getIcon = () => {
    switch(type) {
      case 'credit-card':
        return <CreditCard className="h-4 w-4 text-blue-500" />;
      case 'bank':
        return <Building className="h-4 w-4 text-green-500" />;
      case 'mobile':
        return <Smartphone className="h-4 w-4 text-purple-500" />;
      case 'crypto':
        return <Bitcoin className="h-4 w-4 text-orange-500" />;
    }
  };
  
  return (
    <div className="flex items-center justify-between p-3 bg-slate-800 rounded-lg">
      <div className="flex items-center">
        <div className="h-10 w-10 rounded-full bg-slate-900 flex items-center justify-center mr-3">
          {getIcon()}
        </div>
        <div>
          <p className="text-sm text-white">{name}</p>
          <p className="text-xs text-slate-400">{details}</p>
        </div>
      </div>
      <div className="flex items-center space-x-2">
        {isDefault && <Badge className="text-xs bg-slate-700">Default</Badge>}
        <Button variant="ghost" size="sm" className="h-8 w-8 p-0 text-slate-400">
          <MoreVertical className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
};

const MobileAutoRule = ({ type, title, description, isActive }: any) => {
  const getIcon = () => {
    switch(type) {
      case 'auto-reload':
        return <RefreshCw className="h-4 w-4 text-green-500" />;
      case 'auto-withdraw':
        return <ArrowUp className="h-4 w-4 text-blue-500" />;
      case 'recurring-transfer':
        return <Clock className="h-4 w-4 text-purple-500" />;
    }
  };
  
  return (
    <div className="flex items-center justify-between p-3 bg-slate-800 rounded-lg">
      <div className="flex items-center">
        <div className="h-10 w-10 rounded-full bg-slate-900 flex items-center justify-center mr-3">
          {getIcon()}
        </div>
        <div>
          <p className="text-sm text-white">{title}</p>
          <p className="text-xs text-slate-400">{description}</p>
        </div>
      </div>
      <label className="relative inline-flex items-center cursor-pointer">
        <input type="checkbox" checked={isActive} className="sr-only peer" readOnly />
        <div className="w-11 h-6 bg-slate-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
      </label>
    </div>
  );
};

export default MobileVaultPage;