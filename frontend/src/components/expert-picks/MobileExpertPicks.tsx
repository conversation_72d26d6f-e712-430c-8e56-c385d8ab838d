import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import {
  Star,
  TrendingUp,
  Activity,
  Target,
  Trophy,
  Clock,
  DollarSign,
  Filter,
  ChevronRight,
  Eye,
  Copy,
  CheckCircle,
  AlertCircle,
  Zap,
  Crown,
  Medal,
  X
} from 'lucide-react';
import expertPicksService from '@/services/expertPicks';

interface Expert {
  id: number;
  username: string;
  full_name: string;
  bio: string;
  avatar_url?: string;
  specialties: string[];
  rating: number;
  total_picks: number;
  successful_picks: number;
  avg_roi: number;
  followers_count: number;
  subscription_price: number;
  is_verified: boolean;
  current_streak: number;
  best_streak: number;
  created_at: string;
}

interface Pick {
  id: number;
  expert_id: number;
  event_name: string;
  sport_type: string;
  pick_type: string;
  description: string;
  odds: number;
  stake_suggestion: string;
  confidence_level: number;
  reasoning?: string;
  potential_return: number;
  event_date: string;
  status: string;
  result?: string;
  actual_return?: number;
  created_at: string;
  expert?: Expert;
}

const MOCK_EXPERTS: Expert[] = [
  {
    id: 1,
    username: "ProBettor",
    full_name: "John Smith",
    bio: "20+ years betting experience. Specializing in NBA and NFL.",
    specialties: ["NBA", "NFL"],
    rating: 4.8,
    total_picks: 523,
    successful_picks: 412,
    avg_roi: 18.5,
    followers_count: 2341,
    subscription_price: 29.99,
    is_verified: true,
    current_streak: 7,
    best_streak: 15,
    created_at: "2023-01-15"
  },
  {
    id: 2,
    username: "SoccerGuru",
    full_name: "Maria Garcia",
    bio: "European football expert. Champions League specialist.",
    specialties: ["Soccer", "Tennis"],
    rating: 4.6,
    total_picks: 312,
    successful_picks: 234,
    avg_roi: 15.2,
    followers_count: 1876,
    subscription_price: 19.99,
    is_verified: true,
    current_streak: 4,
    best_streak: 12,
    created_at: "2023-03-20"
  }
];

const MOCK_PICKS: Pick[] = [
  {
    id: 1,
    expert_id: 1,
    event_name: "Lakers vs Warriors",
    sport_type: "NBA",
    pick_type: "Spread",
    description: "Lakers -3.5",
    odds: 1.91,
    stake_suggestion: "3 units",
    confidence_level: 85,
    reasoning: "Lakers have won 5 straight at home...",
    potential_return: 191,
    event_date: "2024-01-20T20:00:00",
    status: "pending",
    created_at: "2024-01-20T10:00:00"
  },
  {
    id: 2,
    expert_id: 2,
    event_name: "Real Madrid vs Barcelona",
    sport_type: "Soccer",
    pick_type: "Over/Under",
    description: "Over 2.5 goals",
    odds: 1.75,
    stake_suggestion: "2 units",
    confidence_level: 75,
    reasoning: "Both teams scoring form is excellent...",
    potential_return: 175,
    event_date: "2024-01-21T15:00:00",
    status: "pending",
    created_at: "2024-01-20T11:00:00"
  }
];

const MobileExpertPicks = () => {
  const [selectedTab, setSelectedTab] = useState('picks');
  const [selectedSport, setSelectedSport] = useState('all');
  const [showFilters, setShowFilters] = useState(false);
  const [experts, setExperts] = useState<Expert[]>(MOCK_EXPERTS);
  const [picks, setPicks] = useState<Pick[]>(MOCK_PICKS);
  const [following, setFollowing] = useState<number[]>([]);
  const [copiedPicks, setCopiedPicks] = useState<number[]>([]);

  const sports = [
    { id: 'all', name: 'All Sports', icon: '🎯' },
    { id: 'nba', name: 'NBA', icon: '🏀' },
    { id: 'nfl', name: 'NFL', icon: '🏈' },
    { id: 'soccer', name: 'Soccer', icon: '⚽' },
    { id: 'tennis', name: 'Tennis', icon: '🎾' }
  ];

  const toggleFollow = (expertId: number) => {
    setFollowing(prev => 
      prev.includes(expertId) 
        ? prev.filter(id => id !== expertId)
        : [...prev, expertId]
    );
  };

  const copyPick = (pickId: number) => {
    setCopiedPicks(prev => [...prev, pickId]);
    setTimeout(() => {
      setCopiedPicks(prev => prev.filter(id => id !== pickId));
    }, 2000);
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 80) return 'text-green-500';
    if (confidence >= 60) return 'text-yellow-500';
    return 'text-red-500';
  };

  const getExpertBadge = (expert: Expert) => {
    if (expert.rating >= 4.7) return { icon: Crown, color: 'text-yellow-500' };
    if (expert.rating >= 4.5) return { icon: Medal, color: 'text-slate-400' };
    return { icon: Star, color: 'text-amber-600' };
  };

  return (
    <div className="min-h-screen bg-slate-950 text-white pt-16 flex flex-col">
      {/* Header */}
      <div className="sticky top-16 z-20 bg-slate-900 border-b border-slate-800">
        <div className="p-3">
          <div className="flex items-center justify-between mb-3">
            <h1 className="text-lg font-semibold">Expert Picks</h1>
            <div className="flex items-center space-x-2">
              <Badge className="bg-purple-600 text-xs">
                <TrendingUp className="h-3 w-3 mr-1" />
                Hot Streak
              </Badge>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowFilters(true)}
                className="h-8 w-8 p-0"
              >
                <Filter className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Sport Pills */}
          <div className="flex space-x-2 overflow-x-auto pb-3 scrollbar-hide">
            {sports.map(sport => (
              <Button
                key={sport.id}
                variant={selectedSport === sport.id ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setSelectedSport(sport.id)}
                className={`h-8 px-3 text-xs whitespace-nowrap ${
                  selectedSport === sport.id ? 'bg-purple-600' : ''
                }`}
              >
                <span className="mr-1">{sport.icon}</span>
                {sport.name}
              </Button>
            ))}
          </div>
        </div>
      </div>

      {/* Tabs */}
      <Tabs value={selectedTab} onValueChange={setSelectedTab} className="flex-1 flex flex-col">
        <TabsList className="w-full grid grid-cols-2 bg-slate-900 border-b border-slate-800 rounded-none h-10 flex-shrink-0">
          <TabsTrigger value="picks" className="text-xs">
            <Target className="h-3 w-3 mr-1" />
            Today's Picks
          </TabsTrigger>
          <TabsTrigger value="experts" className="text-xs">
            <Trophy className="h-3 w-3 mr-1" />
            Top Experts
          </TabsTrigger>
        </TabsList>

        {/* Main Content */}
        <div className="flex-1 overflow-y-auto">
          <div className="p-3 pb-20">
            <TabsContent value="picks" className="mt-0 space-y-3">
              {/* Featured Pick */}
              <Card className="bg-gradient-to-r from-purple-900/30 to-pink-900/30 border-purple-700 p-4">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-2">
                    <div className="h-10 w-10 rounded-full bg-purple-600 flex items-center justify-center">
                      <Crown className="h-5 w-5 text-yellow-500" />
                    </div>
                    <div>
                      <p className="font-medium text-sm">ProBettor</p>
                      <div className="flex items-center space-x-1">
                        <Star className="h-3 w-3 fill-yellow-500 text-yellow-500" />
                        <span className="text-xs">4.8</span>
                        <Badge className="bg-green-500 text-white text-[10px] h-4">
                          7 WIN STREAK
                        </Badge>
                      </div>
                    </div>
                  </div>
                  <Badge className="bg-purple-600 text-xs">
                    <Zap className="h-3 w-3 mr-1" />
                    85% Conf
                  </Badge>
                </div>

                <div className="space-y-2 mb-3">
                  <p className="text-sm font-medium">Lakers vs Warriors</p>
                  <p className="text-lg font-bold">Lakers -3.5 @ 1.91</p>
                  <p className="text-xs text-slate-300">
                    Lakers have won 5 straight at home...
                  </p>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3 text-xs">
                    <span className="text-slate-400">Stake: 3 units</span>
                    <span className="text-green-500">Potential: +91%</span>
                  </div>
                  <Button 
                    size="sm" 
                    className="h-8 bg-gradient-to-r from-purple-500 to-pink-500"
                    onClick={() => copyPick(1)}
                  >
                    <Copy className="h-3 w-3 mr-1" />
                    Copy
                  </Button>
                </div>
              </Card>

              {/* Picks List */}
              {picks.map(pick => (
                <Card key={pick.id} className="bg-slate-900 border-slate-800 p-3">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <div className="h-8 w-8 rounded-full bg-slate-800 flex items-center justify-center text-xs font-bold">
                        {pick.expert_id === 1 ? 'JB' : 'MG'}
                      </div>
                      <div>
                        <p className="text-xs font-medium">
                          {pick.expert_id === 1 ? 'ProBettor' : 'SoccerGuru'}
                        </p>
                        <Badge className="bg-slate-700 text-[10px] h-4">
                          {pick.sport_type}
                        </Badge>
                      </div>
                    </div>
                    <Badge className={`text-xs ${getConfidenceColor(pick.confidence_level)}`}>
                      {pick.confidence_level}%
                    </Badge>
                  </div>

                  <div className="space-y-1 mb-3">
                    <p className="text-sm font-medium">{pick.event_name}</p>
                    <p className="text-sm">{pick.description} @ {pick.odds}</p>
                    {pick.reasoning && (
                      <p className="text-xs text-slate-400 line-clamp-2">{pick.reasoning}</p>
                    )}
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3 text-xs">
                      <span className="text-slate-400">
                        <Clock className="h-3 w-3 inline mr-1" />
                        {new Date(pick.event_date).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                      </span>
                      <span className="text-slate-400">{pick.stake_suggestion}</span>
                    </div>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className="h-7 text-xs border-slate-700"
                      onClick={() => copyPick(pick.id)}
                    >
                      {copiedPicks.includes(pick.id) ? (
                        <>
                          <CheckCircle className="h-3 w-3 mr-1 text-green-500" />
                          Copied
                        </>
                      ) : (
                        <>
                          <Copy className="h-3 w-3 mr-1" />
                          Copy
                        </>
                      )}
                    </Button>
                  </div>
                </Card>
              ))}
            </TabsContent>

            <TabsContent value="experts" className="mt-0 space-y-3">
              {experts.map(expert => {
                const BadgeIcon = getExpertBadge(expert).icon;
                const badgeColor = getExpertBadge(expert).color;
                const winRate = ((expert.successful_picks / expert.total_picks) * 100).toFixed(1);

                return (
                  <Card key={expert.id} className="bg-slate-900 border-slate-800 p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        <div className="relative">
                          <div className="h-12 w-12 rounded-full bg-gradient-to-r from-purple-600 to-pink-600 flex items-center justify-center text-lg font-bold">
                            {expert.username.substring(0, 2).toUpperCase()}
                          </div>
                          {expert.is_verified && (
                            <BadgeIcon className={`h-4 w-4 absolute -top-1 -right-1 ${badgeColor}`} />
                          )}
                        </div>
                        <div>
                          <p className="font-medium">{expert.username}</p>
                          <div className="flex items-center space-x-2">
                            <div className="flex items-center">
                              <Star className="h-3 w-3 fill-yellow-500 text-yellow-500" />
                              <span className="text-xs ml-1">{expert.rating}</span>
                            </div>
                            <span className="text-xs text-slate-400">
                              {expert.followers_count} followers
                            </span>
                          </div>
                        </div>
                      </div>
                      <Button
                        variant={following.includes(expert.id) ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => toggleFollow(expert.id)}
                        className="h-8 text-xs"
                      >
                        {following.includes(expert.id) ? 'Following' : 'Follow'}
                      </Button>
                    </div>

                    <p className="text-xs text-slate-300 mb-3 line-clamp-2">{expert.bio}</p>

                    <div className="grid grid-cols-3 gap-2 mb-3">
                      <div className="bg-slate-800 rounded p-2 text-center">
                        <p className="text-xs text-slate-400">Win Rate</p>
                        <p className="text-sm font-bold text-green-500">{winRate}%</p>
                      </div>
                      <div className="bg-slate-800 rounded p-2 text-center">
                        <p className="text-xs text-slate-400">ROI</p>
                        <p className="text-sm font-bold text-yellow-500">+{expert.avg_roi}%</p>
                      </div>
                      <div className="bg-slate-800 rounded p-2 text-center">
                        <p className="text-xs text-slate-400">Streak</p>
                        <p className="text-sm font-bold text-orange-500">{expert.current_streak} 🔥</p>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex flex-wrap gap-1">
                        {expert.specialties.slice(0, 3).map(specialty => (
                          <Badge key={specialty} className="bg-slate-700 text-[10px] h-5">
                            {specialty}
                          </Badge>
                        ))}
                      </div>
                      <Button variant="ghost" size="sm" className="h-7 text-xs">
                        View Profile
                        <ChevronRight className="h-3 w-3 ml-1" />
                      </Button>
                    </div>
                  </Card>
                );
              })}
            </TabsContent>
          </div>
        </div>
      </Tabs>

      {/* Filters Overlay */}
      {showFilters && (
        <div className="fixed inset-0 z-50 bg-slate-900 flex flex-col">
          <div className="flex items-center justify-between p-4 border-b border-slate-800">
            <h2 className="text-lg font-semibold">Filters</h2>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowFilters(false)}
              className="h-8 w-8 p-0"
            >
              <X className="h-5 w-5" />
            </Button>
          </div>

          <div className="flex-1 p-4 space-y-6">
            <div>
              <h3 className="text-sm font-medium mb-3">Sort By</h3>
              <div className="space-y-2">
                <Button variant="outline" className="w-full justify-start h-10 text-sm">
                  <TrendingUp className="h-4 w-4 mr-2" />
                  Win Rate
                </Button>
                <Button variant="outline" className="w-full justify-start h-10 text-sm">
                  <Trophy className="h-4 w-4 mr-2" />
                  Current Streak
                </Button>
                <Button variant="outline" className="w-full justify-start h-10 text-sm">
                  <Star className="h-4 w-4 mr-2" />
                  Rating
                </Button>
              </div>
            </div>

            <div>
              <h3 className="text-sm font-medium mb-3">Confidence Level</h3>
              <div className="grid grid-cols-3 gap-2">
                <Button variant="outline" className="h-10 text-sm">70%+</Button>
                <Button variant="outline" className="h-10 text-sm">80%+</Button>
                <Button variant="outline" className="h-10 text-sm">90%+</Button>
              </div>
            </div>

            <div>
              <h3 className="text-sm font-medium mb-3">Price Range</h3>
              <div className="grid grid-cols-2 gap-2">
                <Button variant="outline" className="h-10 text-sm">Free</Button>
                <Button variant="outline" className="h-10 text-sm">Under $50</Button>
              </div>
            </div>
          </div>

          <div className="p-4 border-t border-slate-800">
            <Button 
              className="w-full bg-gradient-to-r from-purple-500 to-pink-500"
              onClick={() => setShowFilters(false)}
            >
              Apply Filters
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default MobileExpertPicks;