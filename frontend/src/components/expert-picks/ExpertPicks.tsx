import { useState, useEffect } from 'react';
import {
  TrendingUp,
  BarChart3,
  Calendar,
  Clock,
  DollarSign,
  Users,
  Star,
  Trophy,
  Eye,
  Share2,
  Download,
  Filter,
  Search,
  Plus,
  Edit3,
  Settings,
  Target,
  Activity,
  LineChart,
  PieChart,
  Zap,
  Lock,
  Unlock,
  Globe,
  CheckCircle,
  AlertCircle,
  Play,
  Pause,
  RotateCcw,
  ChevronDown,
  ChevronRight,
  MessageSquare,
  Bell,
  Bookmark,
  Copy,
  ExternalLink,
  Briefcase,
  Database,
  Calculator,
  FileText,
  Upload,
  Image,
  Video,
  Mic,
  MoreHorizontal,
  X
} from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import useUserStore from '@/stores/userStore';
import expertPicksService from '@/services/expertPicks';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import useNotifications from '@/hooks/useNotifications';

const ExpertPicks = () => {
  const navigate = useNavigate();
  const { user } = useUserStore();
  const { success, error } = useNotifications();
  const [activeTab, setActiveTab] = useState('dashboard');
  const [expertMode, setExpertMode] = useState(false);
  const [expertProfile, setExpertProfile] = useState(null);
  const [expertStats, setExpertStats] = useState(null);
  const [myPicks, setMyPicks] = useState([]);
  const [pickPacks, setPickPacks] = useState([]);
  const [followers, setFollowers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedSport, setSelectedSport] = useState('football');
  const [showCreatePickModal, setShowCreatePickModal] = useState(false);

  useEffect(() => {
    if (user && user.id) {
      loadExpertData();
    }
  }, [user]);

  const loadExpertData = async () => {
    try {
      setLoading(true);
      console.log('Loading expert data for user:', user.id);

      // Try to get expert profile
      const profile = await expertPicksService.getProfile(user.id);
      console.log('Retrieved profile:', profile);
      setExpertProfile(profile);
      setExpertMode(profile.is_expert);

      if (profile.is_expert) {
        console.log('User is expert, loading additional data...');
        // Load expert stats and data
        const [stats, picks, packs] = await Promise.all([
          expertPicksService.getStats(),
          expertPicksService.getMyPicks(),
          expertPicksService.getPacks(profile.id)
        ]);

        setExpertStats(stats);
        setMyPicks(picks);
        setPickPacks(packs);
        console.log('Expert data loaded successfully');
      }
    } catch (error) {
      // Profile doesn't exist yet - this is normal for new users
      console.log('Error loading expert profile:', error);
      setExpertMode(false);
      setExpertProfile(null);
    } finally {
      setLoading(false);
    }
  };

  const enableExpertMode = async () => {
    try {
      console.log('Enabling expert mode for user:', user);
      const profile = await expertPicksService.createOrUpdateProfile(true);
      console.log('Profile created/updated:', profile);
      setExpertProfile(profile);
      setExpertMode(true);
      success('Expert mode enabled!');
      // Reload all data
      setTimeout(() => {
        loadExpertData();
      }, 500);
    } catch (err) {
      console.error('Failed to enable expert mode:', err);
      // Try to get more details about the error
      if (err.response) {
        console.error('Error response:', err.response.data);
        error(`Failed to enable expert mode: ${err.response.data.detail || 'Unknown error'}`);
      } else {
        error('Failed to enable expert mode: Network error');
      }
    }
  };

  const StatCard = ({ title, value, subtitle, icon: Icon, trend, color = 'blue' }) => {
    const iconClasses = {
      blue: 'text-blue-400',
      green: 'text-green-400',
      purple: 'text-purple-400',
      orange: 'text-orange-400'
    };

    return (
      <div className="bg-slate-900 rounded-lg p-3 border border-slate-800">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm text-slate-400">{title}</p>
            <p className="text-xl font-bold">{value}</p>
            {trend && (
              <p className="text-xs text-green-500">{trend}</p>
            )}
          </div>
          <Icon className={`h-6 w-6 ${iconClasses[color] || iconClasses.blue}`} />
        </div>
      </div>
    );
  };

  const renderDashboard = () => {
    if (!expertStats) return null;

    return (
      <>
        {/* Stats Row */}
        <div className="col-span-12 grid grid-cols-4 gap-2">
          <StatCard
            title="Total Picks"
            value={expertStats.total_picks.toLocaleString()}
            icon={Target}
            trend="+12% this month"
          />
          <StatCard
            title="Win Rate"
            value={`${expertStats.win_rate}%`}
            icon={Trophy}
            trend="+2.1% this month"
            color="green"
          />
          <StatCard
            title="Monthly Revenue"
            value={`$${expertStats.monthly_revenue.toLocaleString()}`}
            icon={DollarSign}
            trend="+18% this month"
            color="purple"
          />
          <StatCard
            title="Followers"
            value={expertStats.followers.toLocaleString()}
            icon={Users}
            trend="+156 this week"
            color="orange"
          />
        </div>

        {/* Active Picks Section */}
        <div className="col-span-8">
          <div className="bg-slate-900 rounded-lg border border-slate-800 p-3">
            <div className="flex items-center justify-between mb-2">
              <h2 className="text-base font-semibold">Active Picks</h2>
              <button className="text-sm text-blue-400 hover:text-blue-300">
                View All
              </button>
            </div>
            <div className="space-y-2">
              {myPicks.filter(pick => pick.status === 'active').slice(0, 2).map((pick) => (
                <div key={pick.id} className="bg-slate-800 rounded-lg p-2">
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <span className="text-xs bg-blue-600 text-white px-2 py-0.5 rounded">
                          {pick.sport}
                        </span>
                        <span className="text-xs text-slate-400">{pick.league}</span>
                      </div>
                      <h4 className="font-medium text-sm">{pick.match}</h4>
                      <p className="text-xs text-slate-400">
                        {pick.pick_description} @ {pick.odds}
                      </p>
                      <div className="flex items-center space-x-3 mt-1">
                        <div className="flex items-center">
                          <Activity size={12} className="text-green-400 mr-1" />
                          <span className="text-xs text-green-400">{pick.confidence}%</span>
                        </div>
                        <span className="text-xs text-slate-400">{pick.sales_count} sold</span>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-lg font-bold text-green-500">${pick.price}</p>
                      <div className="flex items-center space-x-1 mt-1">
                        <button className="p-1 text-slate-400 hover:text-white">
                          <Edit3 size={12} />
                        </button>
                        <button className="p-1 text-slate-400 hover:text-white">
                          <Share2 size={12} />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Recent Activity */}
        <div className="col-span-4">
          <div className="bg-slate-900 rounded-lg border border-slate-800 p-3">
            <h3 className="text-base font-semibold mb-2">Recent Activity</h3>
            <div className="space-y-2">
              <div className="flex items-center justify-between bg-slate-800 rounded-lg p-2">
                <div className="flex items-center space-x-3">
                  <div className="h-8 w-8 rounded-full bg-green-500/20 flex items-center justify-center">
                    <Trophy className="h-4 w-4 text-green-500" />
                  </div>
                  <div>
                    <p className="text-sm font-medium">Pick Won</p>
                    <p className="text-xs text-slate-400">2h ago</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium text-green-500">+$45</p>
                </div>
              </div>

              <div className="flex items-center justify-between bg-slate-800 rounded-lg p-2">
                <div className="flex items-center space-x-3">
                  <div className="h-8 w-8 rounded-full bg-blue-500/20 flex items-center justify-center">
                    <DollarSign className="h-4 w-4 text-blue-500" />
                  </div>
                  <div>
                    <p className="text-sm font-medium">Pick Sold</p>
                    <p className="text-xs text-slate-400">1d ago</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium text-green-500">+$25</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </>
    );
  };

  // Create Pick Modal Component
  const CreatePickModal = () => {
    if (!showCreatePickModal) return null;

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <div className="bg-slate-900 rounded-lg border border-slate-800 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
          <div className="p-6">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-semibold">Create New Pick</h2>
              <button
                onClick={() => setShowCreatePickModal(false)}
                className="text-slate-400 hover:text-white"
              >
                <X size={24} />
              </button>
            </div>

            <div className="space-y-6">
              {/* Basic Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Sport</label>
                  <select
                    value={selectedSport}
                    onChange={(e) => setSelectedSport(e.target.value)}
                    className="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="football">Football</option>
                    <option value="basketball">Basketball</option>
                    <option value="tennis">Tennis</option>
                    <option value="soccer">Soccer</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">League/Competition</label>
                  <select className="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option>Premier League</option>
                    <option>Champions League</option>
                    <option>La Liga</option>
                    <option>Serie A</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Match/Event</label>
                  <input
                    type="text"
                    placeholder="e.g., Manchester United vs Liverpool"
                    className="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Event Date & Time</label>
                  <input
                    type="datetime-local"
                    className="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>

              {/* Pick Details */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Pick Type</label>
                  <select className="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="match_winner">Match Winner</option>
                    <option value="over_under">Over/Under Goals</option>
                    <option value="both_teams_score">Both Teams to Score</option>
                    <option value="handicap">Handicap</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Odds</label>
                  <input
                    type="number"
                    step="0.01"
                    placeholder="e.g., 2.50"
                    className="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Confidence Level (%)</label>
                  <input
                    type="number"
                    min="50"
                    max="100"
                    placeholder="e.g., 85"
                    className="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>

              {/* Pricing */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Pick Price ($)</label>
                  <input
                    type="number"
                    min="1"
                    placeholder="e.g., 25"
                    className="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">VIP Price ($)</label>
                  <input
                    type="number"
                    min="1"
                    placeholder="e.g., 45"
                    className="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>

              {/* Analysis */}
              <div>
                <label className="block text-sm font-medium mb-2">Analysis & Reasoning</label>
                <textarea
                  rows={4}
                  placeholder="Provide detailed analysis explaining your pick..."
                  className="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              {/* Action Buttons */}
              <div className="flex justify-end space-x-4 pt-6 border-t border-slate-700">
                <button
                  onClick={() => setShowCreatePickModal(false)}
                  className="px-6 py-2 border border-slate-600 text-slate-300 rounded-lg hover:bg-slate-800 transition text-sm font-medium"
                >
                  Cancel
                </button>
                <button className="px-6 py-2 bg-slate-700 text-slate-300 rounded-lg hover:bg-slate-600 transition text-sm font-medium">
                  Save Draft
                </button>
                <button className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition text-sm font-medium">
                  Publish Pick
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderCreatePick = () => (
    <div className="bg-slate-900 rounded-lg border border-slate-800 p-6">
      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-2">Create New Pick</h2>
        <p className="text-sm text-slate-400">Create and sell your expert predictions with detailed analysis</p>
      </div>
      <div className="space-y-6">
        {/* Basic Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium mb-2">Sport</label>
            <select
              value={selectedSport}
              onChange={(e) => setSelectedSport(e.target.value)}
              className="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="football">Football</option>
              <option value="basketball">Basketball</option>
              <option value="tennis">Tennis</option>
              <option value="soccer">Soccer</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">League/Competition</label>
            <select className="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
              <option>Premier League</option>
              <option>Champions League</option>
              <option>La Liga</option>
              <option>Serie A</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">Match/Event</label>
            <input
              type="text"
              placeholder="e.g., Manchester United vs Liverpool"
              className="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">Event Date & Time</label>
            <input
              type="datetime-local"
              className="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
        </div>

        {/* Pick Details */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium mb-2">Pick Type</label>
            <select className="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
              <option value="match_winner">Match Winner</option>
              <option value="over_under">Over/Under Goals</option>
              <option value="both_teams_score">Both Teams to Score</option>
              <option value="handicap">Handicap</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">Odds</label>
            <input
              type="number"
              step="0.01"
              placeholder="e.g., 2.50"
              className="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">Confidence Level (%)</label>
            <input
              type="number"
              min="50"
              max="100"
              placeholder="e.g., 85"
              className="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
        </div>

        {/* Pricing */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium mb-2">Pick Price ($)</label>
            <input
              type="number"
              min="1"
              placeholder="e.g., 25"
              className="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">VIP Price ($)</label>
            <input
              type="number"
              min="1"
              placeholder="e.g., 45"
              className="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
        </div>

        {/* Analysis */}
        <div>
          <label className="block text-sm font-medium mb-2">Analysis & Reasoning</label>
          <textarea
            rows={6}
            placeholder="Provide detailed analysis explaining your pick..."
            className="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end space-x-4 pt-6 border-t border-slate-700">
          <button className="px-6 py-2 border border-slate-600 text-slate-300 rounded-lg hover:bg-slate-800 transition text-sm font-medium">
            Save Draft
          </button>
          <button className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition text-sm font-medium">
            Publish Pick
          </button>
        </div>
      </div>
    </div>
  );

  if (!user || loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading expert data...</p>
        </div>
      </div>
    );
  }

  if (!expertMode) {
    return (
      <div className="min-h-screen bg-slate-950 text-white pt-16">
        <div className="max-w-4xl mx-auto px-4 py-12">
          <div className="bg-slate-900 rounded-lg border border-slate-800 text-center p-12">
            <Lock size={64} className="mx-auto text-slate-600 mb-6" />
            <h2 className="text-3xl font-bold mb-4">Become an Expert Punter</h2>
            <p className="text-slate-400 mb-8 max-w-2xl mx-auto text-lg">
              Share your betting expertise and earn money from your predictions.
              Enable Expert Mode to access advanced analysis tools and start selling your picks.
            </p>
            <button
              onClick={enableExpertMode}
              className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-medium text-lg transition"
            >
              Enable Expert Mode
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-slate-950 text-white pt-16">
      <div className="max-w-7xl mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Expert Picks Dashboard</h1>
          <p className="text-slate-400">Manage your expert predictions and analytics</p>
        </div>

        {/* Navigation Tabs */}
        <div className="flex space-x-4 mb-8">
          <button
            onClick={() => setActiveTab('dashboard')}
            className={`px-4 py-2 rounded-lg transition-colors ${
              activeTab === 'dashboard'
                ? 'bg-blue-600 text-white'
                : 'bg-slate-800 text-slate-400 hover:bg-slate-700'
            }`}
          >
            Dashboard
          </button>
          <button
            onClick={() => setActiveTab('pick-packs')}
            className={`px-4 py-2 rounded-lg transition-colors ${
              activeTab === 'pick-packs'
                ? 'bg-blue-600 text-white'
                : 'bg-slate-800 text-slate-400 hover:bg-slate-700'
            }`}
          >
            Pick Packs
          </button>
          <button
            onClick={() => setActiveTab('analysis')}
            className={`px-4 py-2 rounded-lg transition-colors ${
              activeTab === 'analysis'
                ? 'bg-blue-600 text-white'
                : 'bg-slate-800 text-slate-400 hover:bg-slate-700'
            }`}
          >
            Analysis
          </button>
        </div>

        {/* Content Area */}
        <div className="bg-slate-900 rounded-lg border border-slate-800 p-6">
          {activeTab === 'dashboard' && renderDashboard()}

          {activeTab === 'pick-packs' && (
            <div>
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-semibold">Pick Packs</h2>
                <button className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition flex items-center">
                  <Plus size={16} className="mr-2" />
                  Create New Pack
                </button>
              </div>
              <div className="text-center py-12">
                <Briefcase size={48} className="mx-auto text-slate-600 mb-4" />
                <p className="text-slate-400">No pick packs created yet</p>
              </div>
            </div>
          )}

          {activeTab === 'analysis' && (
            <div className="text-center py-12">
              <BarChart3 size={48} className="mx-auto text-slate-600 mb-4" />
              <h3 className="text-xl font-semibold mb-2">Analysis Tools</h3>
              <p className="text-slate-400">Advanced analysis features coming soon</p>
            </div>
          )}
        </div>

        {/* Quick Actions */}
        {expertMode && (
          <div className="mt-6 flex space-x-4">
            <button
              onClick={() => setShowCreatePickModal(true)}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium flex items-center"
            >
              <Plus className="w-4 h-4 mr-2" />
              Create Pick
            </button>
            <button
              onClick={() => setActiveTab('pick-packs')}
              className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg text-sm font-medium flex items-center"
            >
              <Briefcase className="w-4 h-4 mr-2" />
              Create Pack
            </button>
          </div>
        )}

        {/* Create Pick Modal */}
        <CreatePickModal />
      </div>
    </div>
  );
};

export default ExpertPicks;