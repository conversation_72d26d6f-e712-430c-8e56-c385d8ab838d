import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { 
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
  SheetFooter,
} from '@/components/ui/sheet';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  <PERSON>,
  Star,
  Crown,
  Trophy,
  Gamepad2,
  TrendingUp,
  Clock,
  Flame,
  Zap,
  DollarSign,
  Award,
  Shield,
  Target,
  ChevronDown,
  Plus,
  Eye,
  CheckSquare,
  Filter,
  Users,
  Sword,
  Dices,
  BarChart2,
  Calendar,
  Share2,
  Menu,
  ChevronRight,
  Briefcase,
  Link
} from 'lucide-react';

// Types (same as desktop version)
interface Player {
  id: string;
  username: string;
  rank: number;
  winrate: number;
  earnings: number;
  streak: number;
  level: number;
  xp: number;
  badges: string[];
  favoriteGame: string;
  achievements: number;
  trophies: number;
  isOnline: boolean;
  country: string;
  winStreakDays: number;
  totalGamesPlayed: number;
  gamesWon: number;
  bestGame: string;
  dailyProfit: number;
  weeklyProfit: number;
  monthlyProfit: number;
  recentPerformance: 'improving' | 'declining' | 'stable';
  recentGames: {
    game: string;
    result: 'win' | 'loss';
    profit: number;
    opponent?: string;
    time: string;
  }[];
}

interface MatchPot {
  id: string;
  game: string;
  players: number;
  totalPot: number;
  entryFee: number;
  startTime: string;
  status: 'waiting' | 'in-progress' | 'completed';
  format: string;
  prizesDistribution: number[];
  currentPlayers: string[];
  gameType: string;
  duration: string;
}

// Mock Data Generators (same as desktop)
const generateMockPlayers = (): Player[] => {
  const usernames = [
    'ProGamer123', 'SkillMaster_X', 'CryptoWhale77', 'LuckyStreak99', 'NightHawk',
    'BlitzKrieg', 'QuantumPlayer', 'AceDealer', 'DragonSlayer', 'PhoenixRise',
    'SilverBullet', 'GoldRush22', 'DiamondHands', 'PlatinumPro', 'EliteSniper',
    'ThunderBolt', 'IceQueen', 'FireStorm', 'ShadowNinja', 'LightSpeed'
  ];

  const countries = ['US', 'UK', 'CA', 'AU', 'JP', 'KR', 'BR', 'DE', 'FR', 'ES'];
  const games = ['Chess 1v1', 'RPS Arena', 'Trivia Master', 'Coin Flip Pro', 'Checkers Elite'];
  const badges = ['titan', 'millionaire', 'undefeated-10', 'streaker', 'comeback-king'];

  return usernames.map((username, index) => ({
    id: `player-${index}`,
    username,
    rank: index + 1,
    winrate: Math.floor(Math.random() * 30) + 70,
    earnings: Math.floor(Math.random() * 50000) + 10000,
    streak: Math.floor(Math.random() * 15),
    level: Math.floor(Math.random() * 50) + 20,
    xp: Math.floor(Math.random() * 10000),
    badges: badges.filter(() => Math.random() > 0.6),
    favoriteGame: games[Math.floor(Math.random() * games.length)],
    achievements: Math.floor(Math.random() * 100) + 20,
    trophies: Math.floor(Math.random() * 50) + 5,
    isOnline: Math.random() > 0.3,
    country: countries[Math.floor(Math.random() * countries.length)],
    winStreakDays: Math.floor(Math.random() * 30),
    totalGamesPlayed: Math.floor(Math.random() * 1000) + 100,
    gamesWon: Math.floor(Math.random() * 800) + 50,
    bestGame: games[Math.floor(Math.random() * games.length)],
    dailyProfit: (Math.random() - 0.3) * 1000,
    weeklyProfit: (Math.random() - 0.3) * 5000,
    monthlyProfit: (Math.random() - 0.3) * 20000,
    recentPerformance: ['improving', 'declining', 'stable'][Math.floor(Math.random() * 3)] as 'improving' | 'declining' | 'stable',
    recentGames: Array(5).fill(null).map(() => ({
      game: games[Math.floor(Math.random() * games.length)],
      result: Math.random() > 0.5 ? 'win' : 'loss' as 'win' | 'loss',
      profit: (Math.random() - 0.5) * 200,
      opponent: usernames[Math.floor(Math.random() * usernames.length)],
      time: `${Math.floor(Math.random() * 24)}h ago`
    }))
  }));
};

const generateMatchPots = (): MatchPot[] => {
  const games = ['Chess Championship', 'RPS Tournament', 'Trivia Bowl', 'Checkers Masters', 'Coin Flip Frenzy'];
  const formats = ['Single Elimination', 'Round Robin', 'Swiss System', 'Double Elimination'];
  const statuses = ['waiting', 'in-progress'] as const;

  return games.map((game, index) => ({
    id: `pot-${index}`,
    game,
    players: Math.floor(Math.random() * 32) + 8,
    totalPot: Math.floor(Math.random() * 10000) + 1000,
    entryFee: Math.floor(Math.random() * 100) + 10,
    startTime: new Date(Date.now() + Math.random() * 3600000 * 24).toISOString(),
    status: statuses[Math.floor(Math.random() * statuses.length)],
    format: formats[Math.floor(Math.random() * formats.length)],
    prizesDistribution: [50, 30, 20],
    currentPlayers: Array(Math.floor(Math.random() * 20) + 5).fill(null).map((_, i) => `Player${i}`),
    gameType: ['1v1', 'FFA', 'Teams'][Math.floor(Math.random() * 3)],
    duration: `${Math.floor(Math.random() * 60) + 30} min`
  }));
};

const MobileLeaderboard = () => {
  const [selectedGame, setSelectedGame] = useState('all');
  const [selectedTimeframe, setSelectedTimeframe] = useState('alltime');
  const [selectedLeague, setSelectedLeague] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [players, setPlayers] = useState<Player[]>([]);
  const [matchPots, setMatchPots] = useState<MatchPot[]>([]);
  const [watchlist, setWatchlist] = useState<string[]>([]);
  const [selectedTab, setSelectedTab] = useState('leaderboard');
  const [showOnlineOnly, setShowOnlineOnly] = useState(false);
  const [sortBy, setSortBy] = useState('rank');
  const [selectedPlayer, setSelectedPlayer] = useState<Player | null>(null);
  const [showPlayerDialog, setShowPlayerDialog] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [autoRefresh, setAutoRefresh] = useState(true);

  useEffect(() => {
    // Initialize with mock data
    setPlayers(generateMockPlayers());
    setMatchPots(generateMatchPots());

    // Auto-refresh simulation
    if (autoRefresh) {
      const interval = setInterval(() => {
        setPlayers(currentPlayers => {
          return currentPlayers.map(player => ({
            ...player,
            isOnline: Math.random() > 0.3,
            dailyProfit: player.dailyProfit + (Math.random() - 0.5) * 100,
            weeklyProfit: player.weeklyProfit + (Math.random() - 0.5) * 500,
          }));
        });
      }, 5000);

      return () => clearInterval(interval);
    }
  }, [autoRefresh]);

  // Filter and sort players
  const filteredPlayers = players.filter(player => {
    const matchesSearch = player.username.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesOnline = !showOnlineOnly || player.isOnline;
    const matchesGame = selectedGame === 'all' || player.favoriteGame === selectedGame;
    const matchesLeague = selectedLeague === 'all' || 
      (selectedLeague === 'titan' && player.rank <= 10) ||
      (selectedLeague === 'masters' && player.rank > 10 && player.rank <= 50) ||
      (selectedLeague === 'diamond' && player.rank > 50 && player.rank <= 100);

    return matchesSearch && matchesOnline && matchesGame && matchesLeague;
  }).sort((a, b) => {
    switch (sortBy) {
      case 'rank': return a.rank - b.rank;
      case 'earnings': return b.earnings - a.earnings;
      case 'winrate': return b.winrate - a.winrate;
      case 'streak': return b.streak - a.streak;
      case 'level': return b.level - a.level;
      default: return 0;
    }
  });

  // Helper functions
  const getBadgeInfo = (badge: string) => {
    const badges: Record<string, any> = {
      'titan': { icon: <Trophy className="h-3 w-3" />, color: 'bg-gradient-to-r from-amber-600 to-yellow-500', text: 'Titan League' },
      'millionaire': { icon: <DollarSign className="h-3 w-3" />, color: 'bg-gradient-to-r from-green-600 to-emerald-500', text: 'Millionaire' },
      'undefeated-10': { icon: <Flame className="h-3 w-3" />, color: 'bg-gradient-to-r from-red-600 to-orange-500', text: 'Undefeated 10+' },
      'streaker': { icon: <Zap className="h-3 w-3" />, color: 'bg-gradient-to-r from-yellow-600 to-amber-500', text: 'Streaker' },
      'comeback-king': { icon: <Crown className="h-3 w-3" />, color: 'bg-gradient-to-r from-purple-600 to-pink-500', text: 'Comeback King' }
    };
    return badges[badge] || { icon: <Star className="h-3 w-3" />, color: 'bg-slate-600', text: badge.replace(/-/g, ' ') };
  };

  const getRankIcon = (rank: number) => {
    if (rank === 1) return <Crown className="h-4 w-4 text-yellow-500" />;
    if (rank === 2) return <Crown className="h-4 w-4 text-slate-400" />;
    if (rank === 3) return <Crown className="h-4 w-4 text-amber-600" />;
    return null;
  };

  const getLeagueInfo = (rank: number) => {
    if (rank <= 10) return { name: 'Titan', color: 'text-yellow-500', bgColor: 'bg-yellow-500/10', icon: <Crown className="h-3 w-3" /> };
    if (rank <= 50) return { name: 'Masters', color: 'text-purple-500', bgColor: 'bg-purple-500/10', icon: <Shield className="h-3 w-3" /> };
    if (rank <= 100) return { name: 'Diamond', color: 'text-blue-500', bgColor: 'bg-blue-500/10', icon: <Target className="h-3 w-3" /> };
    return { name: 'Gold', color: 'text-amber-500', bgColor: 'bg-amber-500/10', icon: <Award className="h-3 w-3" /> };
  };

  return (
    <div className="min-h-screen bg-slate-950 text-white pt-16">
      {/* Header */}
      <div className="sticky top-16 bg-slate-900 border-b border-slate-800 z-10">
        <div className="p-4">
          <div className="flex items-center justify-between mb-3">
            <h1 className="text-xl font-bold">Leaderboard</h1>
            <div className="flex items-center gap-2">
              <Button
                size="sm"
                variant="ghost"
                onClick={() => setShowFilters(true)}
                className="h-8"
              >
                <Filter className="h-4 w-4" />
              </Button>
              <Button size="sm" variant="ghost" className="h-8">
                <Share2 className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Search Bar */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-slate-400" />
            <Input
              placeholder="Search players..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-9 bg-slate-800 border-slate-700 h-9"
            />
          </div>

          {/* Quick Filters */}
          <div className="flex items-center gap-2 mt-3 overflow-x-auto pb-2">
            <Select value={selectedLeague} onValueChange={setSelectedLeague}>
              <SelectTrigger className="h-8 text-xs bg-slate-800 border-slate-700 min-w-[100px]">
                <SelectValue placeholder="League" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Leagues</SelectItem>
                <SelectItem value="titan">Titan</SelectItem>
                <SelectItem value="masters">Masters</SelectItem>
                <SelectItem value="diamond">Diamond</SelectItem>
              </SelectContent>
            </Select>

            <Select value={selectedTimeframe} onValueChange={setSelectedTimeframe}>
              <SelectTrigger className="h-8 text-xs bg-slate-800 border-slate-700 min-w-[100px]">
                <SelectValue placeholder="Time" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="daily">Today</SelectItem>
                <SelectItem value="weekly">This Week</SelectItem>
                <SelectItem value="monthly">This Month</SelectItem>
                <SelectItem value="alltime">All Time</SelectItem>
              </SelectContent>
            </Select>

            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="h-8 text-xs bg-slate-800 border-slate-700 min-w-[100px]">
                <SelectValue placeholder="Sort" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="rank">Rank</SelectItem>
                <SelectItem value="earnings">Earnings</SelectItem>
                <SelectItem value="winrate">Win Rate</SelectItem>
                <SelectItem value="streak">Streak</SelectItem>
                <SelectItem value="level">Level</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      {/* Main Content - Tabs */}
      <Tabs value={selectedTab} onValueChange={setSelectedTab} className="flex-1">
        <TabsList className="w-full justify-around bg-slate-900 border-b border-slate-800 rounded-none h-12">
          <TabsTrigger value="leaderboard" className="flex-1">
            <Trophy className="h-4 w-4 mr-1" />
            Rankings
          </TabsTrigger>
          <TabsTrigger value="watchlist" className="flex-1">
            <Eye className="h-4 w-4 mr-1" />
            Watchlist
          </TabsTrigger>
          <TabsTrigger value="pots" className="flex-1">
            <Briefcase className="h-4 w-4 mr-1" />
            Match Pots
          </TabsTrigger>
        </TabsList>

        {/* Leaderboard Tab */}
        <TabsContent value="leaderboard" className="mt-0">
          <div className="p-4 pt-5 space-y-3">
            {filteredPlayers.slice(0, 20).map((player) => (
              <Card
                key={player.id}
                className="bg-slate-900 border-slate-800 cursor-pointer"
                onClick={() => {
                  setSelectedPlayer(player);
                  setShowPlayerDialog(true);
                }}
              >
                <CardContent className="p-3">
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex items-center gap-3">
                      <div className="text-center">
                        <div className="flex items-center gap-1">
                          {getRankIcon(player.rank)}
                          <span className="font-bold text-lg">#{player.rank}</span>
                        </div>
                      </div>
                      <div>
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{player.username}</span>
                          {player.isOnline && (
                            <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse" />
                          )}
                        </div>
                        <div className="flex items-center gap-2 mt-0.5">
                          <span className="text-xs text-slate-400">{player.country}</span>
                          <span className={`px-1.5 py-0.5 rounded-full text-[10px] font-medium ${getLeagueInfo(player.rank).bgColor} ${getLeagueInfo(player.rank).color}`}>
                            {getLeagueInfo(player.rank).name}
                          </span>
                        </div>
                      </div>
                    </div>
                    <Button
                      size="sm"
                      variant={watchlist.includes(player.id) ? "secondary" : "ghost"}
                      className="h-8 w-8 p-0"
                      onClick={(e) => {
                        e.stopPropagation();
                        setWatchlist(prev => 
                          prev.includes(player.id) 
                            ? prev.filter(id => id !== player.id)
                            : [...prev, player.id]
                        );
                      }}
                    >
                      {watchlist.includes(player.id) ? (
                        <CheckSquare className="h-4 w-4" />
                      ) : (
                        <Eye className="h-4 w-4" />
                      )}
                    </Button>
                  </div>

                  <div className="grid grid-cols-3 gap-2 text-center">
                    <div>
                      <p className="text-xs text-slate-400">Win Rate</p>
                      <p className="font-bold text-green-400">{player.winrate}%</p>
                    </div>
                    <div>
                      <p className="text-xs text-slate-400">Earnings</p>
                      <p className="font-bold text-yellow-400">${(player.earnings / 1000).toFixed(1)}K</p>
                    </div>
                    <div>
                      <p className="text-xs text-slate-400">Streak</p>
                      <p className="font-bold text-orange-400">{player.streak} 🔥</p>
                    </div>
                  </div>

                  {player.badges.length > 0 && (
                    <div className="flex gap-1 mt-2">
                      {player.badges.slice(0, 3).map((badge, idx) => {
                        const badgeInfo = getBadgeInfo(badge);
                        return (
                          <div key={idx} className={`p-1 rounded-full ${badgeInfo.color}`}>
                            {badgeInfo.icon}
                          </div>
                        );
                      })}
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Watchlist Tab */}
        <TabsContent value="watchlist" className="mt-0">
          <div className="p-4 pt-5">
            {watchlist.length === 0 ? (
              <div className="text-center py-8">
                <Eye className="h-12 w-12 text-slate-600 mx-auto mb-3" />
                <p className="text-slate-400">No players in watchlist</p>
                <p className="text-sm text-slate-500 mt-1">Add players to track their performance</p>
              </div>
            ) : (
              <div className="space-y-3">
                {players
                  .filter(p => watchlist.includes(p.id))
                  .map((player) => (
                    <Card key={player.id} className="bg-slate-900 border-slate-800">
                      <CardContent className="p-3">
                        <div className="flex items-center justify-between">
                          <div>
                            <div className="flex items-center gap-2">
                              <span className="font-medium">{player.username}</span>
                              {player.isOnline && (
                                <div className="h-2 w-2 bg-green-500 rounded-full" />
                              )}
                            </div>
                            <p className="text-xs text-slate-400 mt-0.5">
                              #{player.rank} • {player.favoriteGame}
                            </p>
                          </div>
                          <div className="text-right">
                            <p className="font-bold text-green-400">{player.winrate}%</p>
                            <p className="text-xs text-slate-400">Win Rate</p>
                          </div>
                        </div>
                        <div className="grid grid-cols-3 gap-2 mt-3 text-center">
                          <div>
                            <p className="text-xs text-slate-400">Daily P/L</p>
                            <p className={`font-bold text-sm ${player.dailyProfit >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                              {player.dailyProfit >= 0 ? '+' : ''}{player.dailyProfit.toFixed(0)}
                            </p>
                          </div>
                          <div>
                            <p className="text-xs text-slate-400">Weekly</p>
                            <p className={`font-bold text-sm ${player.weeklyProfit >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                              {player.weeklyProfit >= 0 ? '+' : ''}{(player.weeklyProfit / 1000).toFixed(1)}K
                            </p>
                          </div>
                          <div>
                            <p className="text-xs text-slate-400">Monthly</p>
                            <p className={`font-bold text-sm ${player.monthlyProfit >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                              {player.monthlyProfit >= 0 ? '+' : ''}{(player.monthlyProfit / 1000).toFixed(1)}K
                            </p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
              </div>
            )}
          </div>
        </TabsContent>

        {/* Match Pots Tab */}
        <TabsContent value="pots" className="mt-0">
          <div className="p-4 pt-5 space-y-3">
            {matchPots.map((pot) => (
              <Card key={pot.id} className="bg-slate-900 border-slate-800">
                <CardContent className="p-3">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <Badge variant={pot.status === 'waiting' ? 'secondary' : 'default'} className="text-xs">
                        {pot.status}
                      </Badge>
                      <span className="font-medium">{pot.game}</span>
                    </div>
                    <span className="font-bold text-green-400">${pot.totalPot}</span>
                  </div>
                  <div className="grid grid-cols-2 gap-2 text-xs text-slate-400">
                    <div>
                      <p>Players: {pot.currentPlayers.length}/{pot.players}</p>
                      <p>Entry: ${pot.entryFee}</p>
                    </div>
                    <div>
                      <p>Format: {pot.format}</p>
                      <p>Starts: {new Date(pot.startTime).toLocaleTimeString()}</p>
                    </div>
                  </div>
                  <Progress 
                    value={(pot.currentPlayers.length / pot.players) * 100} 
                    className="h-2 mt-3"
                  />
                  <Button 
                    className="w-full mt-3" 
                    size="sm"
                    variant={pot.status === 'waiting' ? 'default' : 'secondary'}
                  >
                    {pot.status === 'waiting' ? 'Join Tournament' : 'View Match'}
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>

      {/* Filter Sheet */}
      <Sheet open={showFilters} onOpenChange={setShowFilters}>
        <SheetContent side="right" className="bg-slate-900 border-slate-800">
          <SheetHeader>
            <SheetTitle>Filters</SheetTitle>
            <SheetDescription>Customize your leaderboard view</SheetDescription>
          </SheetHeader>

          <div className="mt-6 space-y-6">
            {/* Game Filter */}
            <div>
              <h3 className="text-sm font-medium mb-3">Game</h3>
              <div className="space-y-2">
                <Button
                  variant={selectedGame === 'all' ? 'default' : 'outline'}
                  size="sm"
                  className="w-full justify-start"
                  onClick={() => setSelectedGame('all')}
                >
                  <Gamepad2 className="h-4 w-4 mr-2" />
                  All Games
                </Button>
                <Button
                  variant={selectedGame === 'Chess 1v1' ? 'default' : 'outline'}
                  size="sm"
                  className="w-full justify-start"
                  onClick={() => setSelectedGame('Chess 1v1')}
                >
                  <Sword className="h-4 w-4 mr-2" />
                  Chess 1v1
                </Button>
                <Button
                  variant={selectedGame === 'RPS Arena' ? 'default' : 'outline'}
                  size="sm"
                  className="w-full justify-start"
                  onClick={() => setSelectedGame('RPS Arena')}
                >
                  <Dices className="h-4 w-4 mr-2" />
                  RPS Arena
                </Button>
                <Button
                  variant={selectedGame === 'Trivia Master' ? 'default' : 'outline'}
                  size="sm"
                  className="w-full justify-start"
                  onClick={() => setSelectedGame('Trivia Master')}
                >
                  <BarChart2 className="h-4 w-4 mr-2" />
                  Trivia Master
                </Button>
              </div>
            </div>

            {/* Additional Options */}
            <div>
              <h3 className="text-sm font-medium mb-3">Options</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <label htmlFor="online-only" className="text-sm">
                    Online Players Only
                  </label>
                  <Switch
                    id="online-only"
                    checked={showOnlineOnly}
                    onCheckedChange={setShowOnlineOnly}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <label htmlFor="auto-refresh" className="text-sm">
                    Auto Refresh
                  </label>
                  <Switch
                    id="auto-refresh"
                    checked={autoRefresh}
                    onCheckedChange={setAutoRefresh}
                  />
                </div>
              </div>
            </div>
          </div>

          <SheetFooter className="mt-6">
            <Button
              variant="outline"
              className="flex-1"
              onClick={() => {
                setSelectedGame('all');
                setSelectedLeague('all');
                setSelectedTimeframe('alltime');
                setShowOnlineOnly(false);
                setSortBy('rank');
              }}
            >
              Reset
            </Button>
            <Button
              className="flex-1"
              onClick={() => setShowFilters(false)}
            >
              Apply
            </Button>
          </SheetFooter>
        </SheetContent>
      </Sheet>

      {/* Player Profile Dialog */}
      <Dialog open={showPlayerDialog} onOpenChange={setShowPlayerDialog}>
        <DialogContent className="max-w-md bg-slate-900 border-slate-800">
          {selectedPlayer && (
            <>
              <DialogHeader>
                <DialogTitle className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <span>{selectedPlayer.username}</span>
                    {selectedPlayer.isOnline && (
                      <div className="h-3 w-3 bg-green-500 rounded-full animate-pulse" />
                    )}
                  </div>
                  <div className="flex items-center gap-1">
                    {getRankIcon(selectedPlayer.rank)}
                    <span>#{selectedPlayer.rank}</span>
                  </div>
                </DialogTitle>
                <DialogDescription>
                  {selectedPlayer.country} • Level {selectedPlayer.level} • {selectedPlayer.favoriteGame} Main
                </DialogDescription>
              </DialogHeader>

              <div className="space-y-4 mt-4">
                {/* Performance Stats */}
                <Card className="bg-slate-800 border-slate-700">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm">Performance Stats</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <p className="text-xs text-slate-400">Win Rate</p>
                        <p className="font-bold text-green-400">{selectedPlayer.winrate}%</p>
                      </div>
                      <div>
                        <p className="text-xs text-slate-400">Total Earnings</p>
                        <p className="font-bold text-yellow-400">${selectedPlayer.earnings.toLocaleString()}</p>
                      </div>
                      <div>
                        <p className="text-xs text-slate-400">Current Streak</p>
                        <p className="font-bold text-orange-400">{selectedPlayer.streak} 🔥</p>
                      </div>
                      <div>
                        <p className="text-xs text-slate-400">Games Played</p>
                        <p className="font-bold">{selectedPlayer.totalGamesPlayed}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Recent Performance */}
                <Card className="bg-slate-800 border-slate-700">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm">Recent Performance</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <span className="text-xs text-slate-400">Daily P/L</span>
                        <span className={`font-bold ${selectedPlayer.dailyProfit >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                          {selectedPlayer.dailyProfit >= 0 ? '+' : ''}{selectedPlayer.dailyProfit.toFixed(2)}
                        </span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-xs text-slate-400">Weekly P/L</span>
                        <span className={`font-bold ${selectedPlayer.weeklyProfit >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                          {selectedPlayer.weeklyProfit >= 0 ? '+' : ''}{selectedPlayer.weeklyProfit.toFixed(2)}
                        </span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-xs text-slate-400">Trend</span>
                        <Badge variant={
                          selectedPlayer.recentPerformance === 'improving' ? 'default' : 
                          selectedPlayer.recentPerformance === 'declining' ? 'destructive' : 
                          'secondary'
                        }>
                          {selectedPlayer.recentPerformance}
                        </Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Achievements */}
                <div>
                  <h4 className="text-sm font-medium mb-2">Achievements</h4>
                  <div className="flex gap-2 flex-wrap">
                    {selectedPlayer.badges.map((badge, idx) => {
                      const badgeInfo = getBadgeInfo(badge);
                      return (
                        <div key={idx} className={`px-3 py-1.5 rounded-full flex items-center gap-2 ${badgeInfo.color}`}>
                          {badgeInfo.icon}
                          <span className="text-xs font-medium">{badgeInfo.text}</span>
                        </div>
                      );
                    })}
                  </div>
                </div>
              </div>

              <DialogFooter className="mt-4">
                <Button variant="outline" onClick={() => setShowPlayerDialog(false)} className="flex-1">
                  Close
                </Button>
                <Button className="flex-1">
                  <Plus className="h-4 w-4 mr-2" />
                  Challenge
                </Button>
              </DialogFooter>
            </>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default MobileLeaderboard;