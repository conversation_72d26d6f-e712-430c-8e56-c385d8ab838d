import React from 'react';
import LeaderboardPage from './LeaderboardPage';
import MobileLeaderboard from './MobileLeaderboard';

const Leaderboard = () => {
  const [isMobile, setIsMobile] = React.useState(false);

  React.useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  return isMobile ? <MobileLeaderboard /> : <LeaderboardPage />;
};

export default Leaderboard;