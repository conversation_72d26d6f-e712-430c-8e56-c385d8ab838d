import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Avatar } from '@/components/ui/avatar';
import { Slider } from '@/components/ui/slider';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  Search,
  Star,
  Crown,
  Trophy,
  Gamepad2,
  TrendingUp,
  Clock,
  Flame,
  Link,
  Zap,
  DollarSign,
  Award,
  Briefcase,
  Shield,
  Target,
  Flag,
  ChevronDown,
  ChevronRight,
  ArrowRight,
  ArrowUp,
  ArrowDown,
  Clock8,
  User,
  Eye,
  MapPin,
  Plus,
  X,
  CheckSquare,
  Calendar,
  BarChart2,
  Video,
  MessageSquare,
  Share2,
  Bookmark,
  Info,
  Heart,
  ThumbsUp,
  Bell,
  Shuffle,
  Filter,
  SlidersHorizontal,
  Play,
  Users,
  Sword,
  Dices
} from 'lucide-react';

// Types
interface Player {
  id: string;
  username: string;
  rank: number;
  winrate: number;
  earnings: number;
  streak: number;
  level: number;
  xp: number;
  badges: string[];
  favoriteGame: string;
  achievements: number;
  trophies: number;
  isOnline: boolean;
  country: string;
  winStreakDays: number;
  totalGamesPlayed: number;
  gamesWon: number;
  bestGame: string;
  dailyProfit: number;
  weeklyProfit: number;
  monthlyProfit: number;
  recentPerformance: 'improving' | 'declining' | 'stable';
  recentGames: {
    game: string;
    result: 'win' | 'loss';
    profit: number;
    opponent?: string;
    time: string;
  }[];
}

interface MatchPot {
  id: string;
  game: string;
  players: number;
  totalPot: number;
  entryFee: number;
  startTime: string;
  status: 'waiting' | 'in-progress' | 'completed';
  format: string;
  prizesDistribution: number[];
  currentPlayers: string[];
  gameType: string;
  duration: string;
}

// Mock Data Generator
const generateMockPlayers = (): Player[] => {
  const usernames = [
    'ProGamer123', 'SkillMaster_X', 'CryptoWhale77', 'LuckyStreak99', 'NightHawk',
    'BlitzKrieg', 'QuantumPlayer', 'AceDealer', 'DragonSlayer', 'PhoenixRise',
    'SilverBullet', 'GoldRush22', 'DiamondHands', 'PlatinumPro', 'EliteSniper',
    'ThunderBolt', 'IceQueen', 'FireStorm', 'ShadowNinja', 'LightSpeed'
  ];

  const countries = ['US', 'UK', 'CA', 'AU', 'JP', 'KR', 'BR', 'DE', 'FR', 'ES'];
  const games = ['Chess 1v1', 'RPS Arena', 'Trivia Master', 'Coin Flip Pro', 'Checkers Elite'];
  const badges = ['titan', 'millionaire', 'undefeated-10', 'streaker', 'comeback-king'];

  return usernames.map((username, index) => ({
    id: `player-${index}`,
    username,
    rank: index + 1,
    winrate: Math.floor(Math.random() * 30) + 70,
    earnings: Math.floor(Math.random() * 50000) + 10000,
    streak: Math.floor(Math.random() * 15),
    level: Math.floor(Math.random() * 50) + 20,
    xp: Math.floor(Math.random() * 10000),
    badges: badges.filter(() => Math.random() > 0.6),
    favoriteGame: games[Math.floor(Math.random() * games.length)],
    achievements: Math.floor(Math.random() * 100) + 20,
    trophies: Math.floor(Math.random() * 50) + 5,
    isOnline: Math.random() > 0.3,
    country: countries[Math.floor(Math.random() * countries.length)],
    winStreakDays: Math.floor(Math.random() * 30),
    totalGamesPlayed: Math.floor(Math.random() * 1000) + 100,
    gamesWon: Math.floor(Math.random() * 800) + 50,
    bestGame: games[Math.floor(Math.random() * games.length)],
    dailyProfit: (Math.random() - 0.3) * 1000,
    weeklyProfit: (Math.random() - 0.3) * 5000,
    monthlyProfit: (Math.random() - 0.3) * 20000,
    recentPerformance: ['improving', 'declining', 'stable'][Math.floor(Math.random() * 3)] as 'improving' | 'declining' | 'stable',
    recentGames: Array(5).fill(null).map(() => ({
      game: games[Math.floor(Math.random() * games.length)],
      result: Math.random() > 0.5 ? 'win' : 'loss' as 'win' | 'loss',
      profit: (Math.random() - 0.5) * 200,
      opponent: usernames[Math.floor(Math.random() * usernames.length)],
      time: `${Math.floor(Math.random() * 24)}h ago`
    }))
  }));
};

const generateMatchPots = (): MatchPot[] => {
  const games = ['Chess Championship', 'RPS Tournament', 'Trivia Bowl', 'Checkers Masters', 'Coin Flip Frenzy'];
  const formats = ['Single Elimination', 'Round Robin', 'Swiss System', 'Double Elimination'];
  const statuses = ['waiting', 'in-progress'] as const;

  return games.map((game, index) => ({
    id: `pot-${index}`,
    game,
    players: Math.floor(Math.random() * 32) + 8,
    totalPot: Math.floor(Math.random() * 10000) + 1000,
    entryFee: Math.floor(Math.random() * 100) + 10,
    startTime: new Date(Date.now() + Math.random() * 3600000 * 24).toISOString(),
    status: statuses[Math.floor(Math.random() * statuses.length)],
    format: formats[Math.floor(Math.random() * formats.length)],
    prizesDistribution: [50, 30, 20],
    currentPlayers: Array(Math.floor(Math.random() * 20) + 5).fill(null).map((_, i) => `Player${i}`),
    gameType: ['1v1', 'FFA', 'Teams'][Math.floor(Math.random() * 3)],
    duration: `${Math.floor(Math.random() * 60) + 30} min`
  }));
};

const LeaderboardPage = () => {
  const [selectedGame, setSelectedGame] = useState('all');
  const [selectedTimeframe, setSelectedTimeframe] = useState('alltime');
  const [selectedLeague, setSelectedLeague] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [players, setPlayers] = useState<Player[]>([]);
  const [matchPots, setMatchPots] = useState<MatchPot[]>([]);
  const [watchlist, setWatchlist] = useState<string[]>([]);
  const [selectedTab, setSelectedTab] = useState('leaderboard');
  const [selectedCountry, setSelectedCountry] = useState('all');
  const [showOnlineOnly, setShowOnlineOnly] = useState(false);
  const [sortBy, setSortBy] = useState('rank');
  const [selectedPlayer, setSelectedPlayer] = useState<Player | null>(null);
  const [showPlayerDialog, setShowPlayerDialog] = useState(false);
  const [autoRefresh, setAutoRefresh] = useState(true);

  useEffect(() => {
    // Initialize with mock data
    setPlayers(generateMockPlayers());
    setMatchPots(generateMatchPots());

    // Auto-refresh simulation
    if (autoRefresh) {
      const interval = setInterval(() => {
        setPlayers(currentPlayers => {
          return currentPlayers.map(player => ({
            ...player,
            isOnline: Math.random() > 0.3,
            dailyProfit: player.dailyProfit + (Math.random() - 0.5) * 100,
            weeklyProfit: player.weeklyProfit + (Math.random() - 0.5) * 500,
          }));
        });
      }, 5000);

      return () => clearInterval(interval);
    }
  }, [autoRefresh]);

  // Filter and sort players
  const filteredPlayers = players.filter(player => {
    const matchesSearch = player.username.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCountry = selectedCountry === 'all' || player.country === selectedCountry;
    const matchesOnline = !showOnlineOnly || player.isOnline;
    const matchesGame = selectedGame === 'all' || player.favoriteGame === selectedGame;
    const matchesLeague = selectedLeague === 'all' || 
      (selectedLeague === 'titan' && player.rank <= 10) ||
      (selectedLeague === 'masters' && player.rank > 10 && player.rank <= 50) ||
      (selectedLeague === 'diamond' && player.rank > 50 && player.rank <= 100);

    return matchesSearch && matchesCountry && matchesOnline && matchesGame && matchesLeague;
  }).sort((a, b) => {
    switch (sortBy) {
      case 'rank': return a.rank - b.rank;
      case 'earnings': return b.earnings - a.earnings;
      case 'winrate': return b.winrate - a.winrate;
      case 'streak': return b.streak - a.streak;
      case 'level': return b.level - a.level;
      default: return 0;
    }
  });

  // Helper functions
  const getBadgeInfo = (badge: string) => {
    const badges: Record<string, any> = {
      'titan': { icon: <Trophy className="h-3.5 w-3.5" />, color: 'bg-gradient-to-r from-amber-600 to-yellow-500', text: 'Titan League' },
      'millionaire': { icon: <DollarSign className="h-3.5 w-3.5" />, color: 'bg-gradient-to-r from-green-600 to-emerald-500', text: 'Millionaire' },
      'undefeated-10': { icon: <Flame className="h-3.5 w-3.5" />, color: 'bg-gradient-to-r from-red-600 to-orange-500', text: 'Undefeated 10+' },
      'streaker': { icon: <Zap className="h-3.5 w-3.5" />, color: 'bg-gradient-to-r from-yellow-600 to-amber-500', text: 'Streaker' },
      'comeback-king': { icon: <Crown className="h-3.5 w-3.5" />, color: 'bg-gradient-to-r from-purple-600 to-pink-500', text: 'Comeback King' }
    };
    return badges[badge] || { icon: <Star className="h-3.5 w-3.5" />, color: 'bg-slate-600', text: badge.replace(/-/g, ' ') };
  };

  const getRankIcon = (rank: number) => {
    if (rank === 1) return <Crown className="h-4 w-4 text-yellow-500" />;
    if (rank === 2) return <Crown className="h-4 w-4 text-slate-400" />;
    if (rank === 3) return <Crown className="h-4 w-4 text-amber-600" />;
    return null;
  };

  const getLeagueInfo = (rank: number) => {
    if (rank <= 10) return { name: 'Titan', color: 'text-yellow-500', bgColor: 'bg-yellow-500/10', icon: <Crown className="h-4 w-4" /> };
    if (rank <= 50) return { name: 'Masters', color: 'text-purple-500', bgColor: 'bg-purple-500/10', icon: <Shield className="h-4 w-4" /> };
    if (rank <= 100) return { name: 'Diamond', color: 'text-blue-500', bgColor: 'bg-blue-500/10', icon: <Target className="h-4 w-4" /> };
    return { name: 'Gold', color: 'text-amber-500', bgColor: 'bg-amber-500/10', icon: <Award className="h-4 w-4" /> };
  };

  return (
    <div className="min-h-screen bg-slate-950 text-white pt-16">
      {/* Main Grid Layout - 12 column system */}
      <div className="grid grid-cols-12 gap-1 p-1 h-[calc(100vh-64px)]">
        
        {/* Left Sidebar - Filters & Navigation (2 cols) */}
        <div className="col-span-2 flex flex-col gap-1 overflow-hidden">
          {/* Game Categories */}
          <div className="bg-slate-900 border border-slate-800 rounded-sm p-3">
            <h3 className="text-xs font-medium text-white mb-2">Game Categories</h3>
            <div className="space-y-1">
              <Button
                variant="ghost"
                size="sm"
                className={`w-full justify-start text-xs h-8 ${selectedGame === 'all' ? 'bg-slate-800' : ''}`}
                onClick={() => setSelectedGame('all')}
              >
                <Gamepad2 className="h-3 w-3 mr-2" />
                All Games
                <span className="ml-auto text-slate-400">1,234</span>
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className={`w-full justify-start text-xs h-8 ${selectedGame === 'Chess 1v1' ? 'bg-slate-800' : ''}`}
                onClick={() => setSelectedGame('Chess 1v1')}
              >
                <Sword className="h-3 w-3 mr-2" />
                Chess 1v1
                <span className="ml-auto text-slate-400">342</span>
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className={`w-full justify-start text-xs h-8 ${selectedGame === 'RPS Arena' ? 'bg-slate-800' : ''}`}
                onClick={() => setSelectedGame('RPS Arena')}
              >
                <Dices className="h-3 w-3 mr-2" />
                RPS Arena
                <span className="ml-auto text-slate-400">286</span>
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className={`w-full justify-start text-xs h-8 ${selectedGame === 'Trivia Master' ? 'bg-slate-800' : ''}`}
                onClick={() => setSelectedGame('Trivia Master')}
              >
                <BarChart2 className="h-3 w-3 mr-2" />
                Trivia Master
                <span className="ml-auto text-slate-400">198</span>
              </Button>
            </div>
          </div>

          {/* League Filter */}
          <div className="bg-slate-900 border border-slate-800 rounded-sm p-3">
            <h3 className="text-xs font-medium text-white mb-2">Leagues</h3>
            <div className="space-y-1">
              <Button
                variant="ghost"
                size="sm"
                className={`w-full justify-start text-xs h-8 ${selectedLeague === 'all' ? 'bg-slate-800' : ''}`}
                onClick={() => setSelectedLeague('all')}
              >
                <Trophy className="h-3 w-3 mr-2" />
                All Leagues
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className={`w-full justify-start text-xs h-8 ${selectedLeague === 'titan' ? 'bg-slate-800' : ''}`}
                onClick={() => setSelectedLeague('titan')}
              >
                <Crown className="h-3 w-3 mr-2 text-yellow-500" />
                Titan League
                <Badge className="ml-auto h-4 bg-yellow-500/20 text-yellow-300">Top 10</Badge>
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className={`w-full justify-start text-xs h-8 ${selectedLeague === 'masters' ? 'bg-slate-800' : ''}`}
                onClick={() => setSelectedLeague('masters')}
              >
                <Shield className="h-3 w-3 mr-2 text-purple-500" />
                Masters League
                <Badge className="ml-auto h-4 bg-purple-500/20 text-purple-300">11-50</Badge>
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className={`w-full justify-start text-xs h-8 ${selectedLeague === 'diamond' ? 'bg-slate-800' : ''}`}
                onClick={() => setSelectedLeague('diamond')}
              >
                <Target className="h-3 w-3 mr-2 text-blue-500" />
                Diamond League
                <Badge className="ml-auto h-4 bg-blue-500/20 text-blue-300">51-100</Badge>
              </Button>
            </div>
          </div>

          {/* Timeframe Filter */}
          <div className="bg-slate-900 border border-slate-800 rounded-sm p-3">
            <h3 className="text-xs font-medium text-white mb-2">Timeframe</h3>
            <div className="space-y-1">
              <Button
                variant="ghost"
                size="sm"
                className={`w-full justify-start text-xs h-8 ${selectedTimeframe === 'daily' ? 'bg-slate-800' : ''}`}
                onClick={() => setSelectedTimeframe('daily')}
              >
                <Clock className="h-3 w-3 mr-2" />
                Today
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className={`w-full justify-start text-xs h-8 ${selectedTimeframe === 'weekly' ? 'bg-slate-800' : ''}`}
                onClick={() => setSelectedTimeframe('weekly')}
              >
                <Calendar className="h-3 w-3 mr-2" />
                This Week
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className={`w-full justify-start text-xs h-8 ${selectedTimeframe === 'monthly' ? 'bg-slate-800' : ''}`}
                onClick={() => setSelectedTimeframe('monthly')}
              >
                <Calendar className="h-3 w-3 mr-2" />
                This Month
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className={`w-full justify-start text-xs h-8 ${selectedTimeframe === 'alltime' ? 'bg-slate-800' : ''}`}
                onClick={() => setSelectedTimeframe('alltime')}
              >
                <Trophy className="h-3 w-3 mr-2" />
                All Time
              </Button>
            </div>
          </div>

          {/* Additional Filters */}
          <div className="bg-slate-900 border border-slate-800 rounded-sm p-3">
            <h3 className="text-xs font-medium text-white mb-2">Filters</h3>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Switch
                  id="online-only"
                  checked={showOnlineOnly}
                  onCheckedChange={setShowOnlineOnly}
                />
                <label htmlFor="online-only" className="text-xs text-slate-300">
                  Online Only
                </label>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="auto-refresh"
                  checked={autoRefresh}
                  onCheckedChange={setAutoRefresh}
                />
                <label htmlFor="auto-refresh" className="text-xs text-slate-300">
                  Auto Refresh
                </label>
              </div>
            </div>
          </div>
        </div>

        {/* Center Main Content (7 cols) */}
        <div className="col-span-7 bg-slate-900 border border-slate-800 rounded-sm p-3 overflow-hidden flex flex-col">
          {/* Header */}
          <div className="flex items-center justify-between mb-4">
            <div>
              <h1 className="text-2xl font-bold">Leaderboard</h1>
              <p className="text-sm text-slate-400">Top players across all games</p>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" className="text-xs">
                <Share2 className="h-3 w-3 mr-1" />
                Export
              </Button>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm" className="text-xs">
                    <SlidersHorizontal className="h-3 w-3 mr-1" />
                    View
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem>Compact View</DropdownMenuItem>
                  <DropdownMenuItem>Detailed View</DropdownMenuItem>
                  <DropdownMenuItem>Card View</DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          {/* Search and Sort Bar */}
          <div className="flex items-center gap-2 mb-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-slate-400" />
              <Input
                placeholder="Search players..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-9 bg-slate-800 border-slate-700 text-sm h-9"
              />
            </div>
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="w-[140px] bg-slate-800 border-slate-700 text-sm h-9">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="rank">Rank</SelectItem>
                <SelectItem value="earnings">Earnings</SelectItem>
                <SelectItem value="winrate">Win Rate</SelectItem>
                <SelectItem value="streak">Streak</SelectItem>
                <SelectItem value="level">Level</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Player Table */}
          <div className="flex-1 overflow-y-auto">
            <div className="grid grid-cols-1 gap-1">
              {filteredPlayers.slice(0, 20).map((player) => (
                <div
                  key={player.id}
                  className="bg-slate-800 rounded-sm p-3 hover:bg-slate-700 transition-colors cursor-pointer"
                  onClick={() => {
                    setSelectedPlayer(player);
                    setShowPlayerDialog(true);
                  }}
                >
                  <div className="flex items-center gap-3">
                    {/* Rank */}
                    <div className="w-12 text-center">
                      <div className="flex items-center justify-center gap-1">
                        {getRankIcon(player.rank)}
                        <span className="font-bold text-sm">{player.rank}</span>
                      </div>
                    </div>

                    {/* Player Info */}
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{player.username}</span>
                        {player.isOnline && (
                          <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse" />
                        )}
                        <div className={`px-2 py-0.5 rounded-full text-xs font-medium ${getLeagueInfo(player.rank).bgColor} ${getLeagueInfo(player.rank).color}`}>
                          {getLeagueInfo(player.rank).name}
                        </div>
                      </div>
                      <div className="flex items-center gap-4 mt-1 text-xs text-slate-400">
                        <span>{player.country}</span>
                        <span>{player.favoriteGame}</span>
                        <span>Level {player.level}</span>
                      </div>
                    </div>

                    {/* Stats */}
                    <div className="grid grid-cols-3 gap-4 text-center">
                      <div>
                        <p className="text-xs text-slate-400">Win Rate</p>
                        <p className="font-bold text-green-400">{player.winrate}%</p>
                      </div>
                      <div>
                        <p className="text-xs text-slate-400">Earnings</p>
                        <p className="font-bold text-yellow-400">${player.earnings.toLocaleString()}</p>
                      </div>
                      <div>
                        <p className="text-xs text-slate-400">Streak</p>
                        <p className="font-bold text-orange-400">{player.streak} 🔥</p>
                      </div>
                    </div>

                    {/* Badges */}
                    <div className="flex gap-1">
                      {player.badges.slice(0, 3).map((badge, idx) => {
                        const badgeInfo = getBadgeInfo(badge);
                        return (
                          <TooltipProvider key={idx}>
                            <Tooltip>
                              <TooltipTrigger>
                                <div className={`p-1.5 rounded-full ${badgeInfo.color}`}>
                                  {badgeInfo.icon}
                                </div>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>{badgeInfo.text}</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        );
                      })}
                    </div>

                    {/* Actions */}
                    <div className="flex items-center gap-2">
                      <Button
                        size="sm"
                        variant={watchlist.includes(player.id) ? "secondary" : "outline"}
                        className="h-7 text-xs"
                        onClick={(e) => {
                          e.stopPropagation();
                          setWatchlist(prev => 
                            prev.includes(player.id) 
                              ? prev.filter(id => id !== player.id)
                              : [...prev, player.id]
                          );
                        }}
                      >
                        {watchlist.includes(player.id) ? (
                          <>
                            <CheckSquare className="h-3 w-3 mr-1" />
                            Watching
                          </>
                        ) : (
                          <>
                            <Eye className="h-3 w-3 mr-1" />
                            Watch
                          </>
                        )}
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Right Sidebar (3 cols) */}
        <div className="col-span-3 flex flex-col gap-1 overflow-hidden">
          {/* Match Pots */}
          <div className="bg-slate-900 border border-slate-800 rounded-sm p-3 flex-1 overflow-hidden">
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-sm font-medium">Live Match Pots</h3>
              <Button variant="ghost" size="sm" className="h-6 text-xs">
                View All
              </Button>
            </div>
            <div className="space-y-2 overflow-y-auto max-h-[300px]">
              {matchPots.slice(0, 5).map((pot) => (
                <Card key={pot.id} className="bg-slate-800 border-slate-700">
                  <CardContent className="p-3">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <Badge variant={pot.status === 'waiting' ? 'secondary' : 'default'} className="text-xs">
                          {pot.status}
                        </Badge>
                        <span className="text-sm font-medium">{pot.game}</span>
                      </div>
                      <span className="text-sm font-bold text-green-400">${pot.totalPot}</span>
                    </div>
                    <div className="grid grid-cols-2 gap-2 text-xs text-slate-400">
                      <div>
                        <p>Players: {pot.currentPlayers.length}/{pot.players}</p>
                        <p>Entry: ${pot.entryFee}</p>
                      </div>
                      <div>
                        <p>Format: {pot.format}</p>
                        <p>Starts: {new Date(pot.startTime).toLocaleTimeString()}</p>
                      </div>
                    </div>
                    <Progress value={(pot.currentPlayers.length / pot.players) * 100} className="h-1 mt-2" />
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Watchlist */}
          <div className="bg-slate-900 border border-slate-800 rounded-sm p-3 flex-1 overflow-hidden">
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-sm font-medium">Your Watchlist</h3>
              <Badge variant="secondary" className="text-xs">{watchlist.length}</Badge>
            </div>
            <div className="space-y-2 overflow-y-auto max-h-[300px]">
              {watchlist.length === 0 ? (
                <p className="text-xs text-slate-400 text-center py-4">
                  No players in watchlist
                </p>
              ) : (
                players
                  .filter(p => watchlist.includes(p.id))
                  .map((player) => (
                    <div key={player.id} className="bg-slate-800 rounded-sm p-2">
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="flex items-center gap-2">
                            <span className="text-sm font-medium">{player.username}</span>
                            {player.isOnline && (
                              <div className="h-2 w-2 bg-green-500 rounded-full" />
                            )}
                          </div>
                          <p className="text-xs text-slate-400">#{player.rank} • {player.favoriteGame}</p>
                        </div>
                        <div className="text-right">
                          <p className="text-sm font-bold text-green-400">{player.winrate}%</p>
                          <p className="text-xs text-slate-400">Win Rate</p>
                        </div>
                      </div>
                    </div>
                  ))
              )}
            </div>
          </div>

          {/* Stats Summary */}
          <div className="bg-slate-900 border border-slate-800 rounded-sm p-3">
            <h3 className="text-sm font-medium mb-3">Quick Stats</h3>
            <div className="grid grid-cols-2 gap-3">
              <div className="bg-slate-800 rounded-sm p-2">
                <p className="text-xs text-slate-400">Total Players</p>
                <p className="text-lg font-bold">12,487</p>
                <p className="text-xs text-green-400">+3.2% today</p>
              </div>
              <div className="bg-slate-800 rounded-sm p-2">
                <p className="text-xs text-slate-400">Prize Pool</p>
                <p className="text-lg font-bold">$284K</p>
                <p className="text-xs text-yellow-400">This week</p>
              </div>
              <div className="bg-slate-800 rounded-sm p-2">
                <p className="text-xs text-slate-400">Active Games</p>
                <p className="text-lg font-bold">1,842</p>
                <p className="text-xs text-blue-400">Live now</p>
              </div>
              <div className="bg-slate-800 rounded-sm p-2">
                <p className="text-xs text-slate-400">Avg. Bet</p>
                <p className="text-lg font-bold">$127</p>
                <p className="text-xs text-purple-400">Per game</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Player Profile Dialog */}
      <Dialog open={showPlayerDialog} onOpenChange={setShowPlayerDialog}>
        <DialogContent className="max-w-2xl bg-slate-900 border-slate-800">
          {selectedPlayer && (
            <>
              <DialogHeader>
                <DialogTitle className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <span>{selectedPlayer.username}</span>
                    {selectedPlayer.isOnline && (
                      <div className="h-3 w-3 bg-green-500 rounded-full animate-pulse" />
                    )}
                  </div>
                  <div className="flex items-center gap-2">
                    {getRankIcon(selectedPlayer.rank)}
                    <span>#{selectedPlayer.rank}</span>
                  </div>
                </DialogTitle>
                <DialogDescription>
                  {selectedPlayer.country} • Level {selectedPlayer.level} • {selectedPlayer.favoriteGame} Specialist
                </DialogDescription>
              </DialogHeader>

              <div className="grid grid-cols-2 gap-4 mt-4">
                <Card className="bg-slate-800 border-slate-700">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm">Performance Stats</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm text-slate-400">Win Rate</span>
                        <span className="font-bold text-green-400">{selectedPlayer.winrate}%</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-slate-400">Total Earnings</span>
                        <span className="font-bold text-yellow-400">${selectedPlayer.earnings.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-slate-400">Current Streak</span>
                        <span className="font-bold text-orange-400">{selectedPlayer.streak} 🔥</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-slate-400">Games Played</span>
                        <span className="font-bold">{selectedPlayer.totalGamesPlayed}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-slate-800 border-slate-700">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm">Recent Performance</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-slate-400">Daily P/L</span>
                        <span className={`font-bold ${selectedPlayer.dailyProfit >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                          {selectedPlayer.dailyProfit >= 0 ? '+' : ''}{selectedPlayer.dailyProfit.toFixed(2)}
                        </span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-slate-400">Weekly P/L</span>
                        <span className={`font-bold ${selectedPlayer.weeklyProfit >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                          {selectedPlayer.weeklyProfit >= 0 ? '+' : ''}{selectedPlayer.weeklyProfit.toFixed(2)}
                        </span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-slate-400">Monthly P/L</span>
                        <span className={`font-bold ${selectedPlayer.monthlyProfit >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                          {selectedPlayer.monthlyProfit >= 0 ? '+' : ''}{selectedPlayer.monthlyProfit.toFixed(2)}
                        </span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-slate-400">Trend</span>
                        <Badge variant={
                          selectedPlayer.recentPerformance === 'improving' ? 'default' : 
                          selectedPlayer.recentPerformance === 'declining' ? 'destructive' : 
                          'secondary'
                        }>
                          {selectedPlayer.recentPerformance}
                        </Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div className="mt-4">
                <h4 className="text-sm font-medium mb-2">Achievements</h4>
                <div className="flex gap-2 flex-wrap">
                  {selectedPlayer.badges.map((badge, idx) => {
                    const badgeInfo = getBadgeInfo(badge);
                    return (
                      <div key={idx} className={`px-3 py-1.5 rounded-full flex items-center gap-2 ${badgeInfo.color}`}>
                        {badgeInfo.icon}
                        <span className="text-xs font-medium">{badgeInfo.text}</span>
                      </div>
                    );
                  })}
                </div>
              </div>

              <DialogFooter className="mt-4">
                <Button variant="outline" onClick={() => setShowPlayerDialog(false)}>
                  Close
                </Button>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Challenge Player
                </Button>
              </DialogFooter>
            </>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default LeaderboardPage;