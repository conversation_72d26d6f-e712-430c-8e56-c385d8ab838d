import { useEffect, useState } from 'react';
import { useParams, useSearchParams } from 'react-router-dom';
import DesktopMarketplace from './DesktopMarketplace';
import MobileMarketplace from './MobileMarketplace';

const BetMarketplace = () => {
  const params = useParams();
  const [searchParams] = useSearchParams();
  const [isMobile, setIsMobile] = useState(false);
  
  // Extract URL parameters
  const category = params.category;
  const userId = params.userId;
  const showCreate = searchParams.get('create') === 'true';
  
  useEffect(() => {
    // Check if mobile on mount
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768); // md breakpoint
    };
    
    checkMobile();
    
    // Add event listener for resize
    window.addEventListener('resize', checkMobile);
    
    // Cleanup
    return () => window.removeEventListener('resize', checkMobile);
  }, []);
  
  // Use the appropriate layout based on screen size with URL params
  return isMobile ? (
    <MobileMarketplace 
      category={category} 
      userId={userId}
      showCreate={showCreate}
    />
  ) : (
    <DesktopMarketplace 
      category={category} 
      userId={userId}
      showCreate={showCreate}
    />
  );
};

export default BetMarketplace;