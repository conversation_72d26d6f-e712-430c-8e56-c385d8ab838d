import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  ArrowLeft, Clock, Users, DollarSign, TrendingUp, Share2,
  MessageSquare, Trophy, Activity
} from 'lucide-react';
import LoadingSpinner from '../shared/LoadingSpinner';
import CounterBetDialog from './CounterBetDialog';

interface BetData {
  id: string;
  name: string;
  game: string;
  amount: number;
  time: string;
  category: string;
  creator: string;
  participants: number;
  totalPool: number;
  forPercentage: number;
  status: 'active' | 'pending' | 'completed';
}

const BetDetailsPage = () => {
  const { betId } = useParams();
  const navigate = useNavigate();
  const [bet, setBet] = useState<BetData | null>(null);
  const [loading, setLoading] = useState(true);
  const [showCounterDialog, setShowCounterDialog] = useState(false);

  useEffect(() => {
    // Mock data - in real app, fetch from API
    setTimeout(() => {
      setBet({
        id: betId || '1',
        name: 'Cristiano Ronaldo will score a hat-trick',
        game: 'Champions League - Real Madrid vs Barcelona',
        amount: 500,
        time: 'Tonight 8:00 PM',
        category: 'Sports',
        creator: 'JohnDoe123',
        participants: 24,
        totalPool: 2450,
        forPercentage: 65,
        status: 'active'
      });
      setLoading(false);
    }, 1000);
  }, [betId]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="large" text="Loading bet details..." />
      </div>
    );
  }

  if (!bet) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="p-8 text-center">
          <p className="text-slate-400 mb-4">Bet not found</p>
          <Button onClick={() => navigate('/marketplace')}>Back to Marketplace</Button>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-slate-950 pt-16 p-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="mb-6">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate('/marketplace')}
            className="mb-4 text-slate-400 hover:text-white"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Marketplace
          </Button>
          
          <div className="flex justify-between items-start">
            <div>
              <h1 className="text-2xl font-bold text-white mb-2">{bet.name}</h1>
              <div className="flex items-center gap-4 text-sm text-slate-400">
                <span className="flex items-center">
                  <Trophy className="h-4 w-4 mr-1" />
                  {bet.game}
                </span>
                <span className="flex items-center">
                  <Clock className="h-4 w-4 mr-1" />
                  {bet.time}
                </span>
                <Badge className="bg-purple-500">{bet.category}</Badge>
              </div>
            </div>
            
            <Button
              className="bg-gradient-to-r from-purple-500 to-pink-500"
              onClick={() => setShowCounterDialog(true)}
            >
              Counter Bet
            </Button>
          </div>
        </div>

        {/* Main Content */}
        <div className="grid gap-6 md:grid-cols-2">
          {/* Bet Overview */}
          <Card className="bg-slate-900 border-slate-800 p-6">
            <h2 className="text-lg font-semibold text-white mb-4">Bet Overview</h2>
            
            <div className="space-y-4">
              <div>
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm text-slate-400">Total Pool</span>
                  <span className="text-2xl font-bold text-white">${bet.totalPool}</span>
                </div>
                <Progress value={bet.forPercentage} className="h-3" />
                <div className="flex justify-between text-xs text-slate-400 mt-1">
                  <span>For ({bet.forPercentage}%)</span>
                  <span>Against ({100 - bet.forPercentage}%)</span>
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-slate-400">Participants</p>
                  <p className="text-xl font-semibold text-white flex items-center">
                    <Users className="h-5 w-5 mr-2" />
                    {bet.participants}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-slate-400">Min Stake</p>
                  <p className="text-xl font-semibold text-white flex items-center">
                    <DollarSign className="h-5 w-5 mr-2" />
                    {Math.floor(bet.amount / 10)}
                  </p>
                </div>
              </div>
              
              <div>
                <p className="text-sm text-slate-400 mb-2">Created by</p>
                <div className="flex items-center gap-2">
                  <div className="h-8 w-8 rounded-full bg-gradient-to-r from-purple-500 to-pink-500" />
                  <span className="text-white">@{bet.creator}</span>
                </div>
              </div>
            </div>
          </Card>

          {/* Recent Activity */}
          <Card className="bg-slate-900 border-slate-800 p-6">
            <h2 className="text-lg font-semibold text-white mb-4 flex items-center">
              <Activity className="h-5 w-5 mr-2" />
              Recent Activity
            </h2>
            
            <div className="space-y-3">
              {[
                { user: 'Alice123', action: 'joined FOR side', amount: 50, time: '2m ago' },
                { user: 'BobGamer', action: 'countered', amount: 75, time: '5m ago' },
                { user: 'Charlie99', action: 'joined AGAINST', amount: 30, time: '12m ago' },
                { user: 'DaveTrader', action: 'joined FOR side', amount: 100, time: '15m ago' },
              ].map((activity, idx) => (
                <div key={idx} className="flex items-center justify-between p-3 bg-slate-800 rounded">
                  <div className="flex items-center gap-2">
                    <div className="h-6 w-6 rounded-full bg-slate-700" />
                    <div>
                      <p className="text-sm text-white">{activity.user}</p>
                      <p className="text-xs text-slate-400">{activity.action}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-semibold text-green-500">+${activity.amount}</p>
                    <p className="text-xs text-slate-400">{activity.time}</p>
                  </div>
                </div>
              ))}
            </div>
          </Card>
        </div>

        {/* Share Section */}
        <Card className="bg-slate-900 border-slate-800 p-6 mt-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-white mb-1">Share this bet</h3>
              <p className="text-sm text-slate-400">Invite others to join the action</p>
            </div>
            <Button variant="outline" className="text-white">
              <Share2 className="h-4 w-4 mr-2" />
              Share Link
            </Button>
          </div>
        </Card>
      </div>

      {/* Counter Dialog */}
      {showCounterDialog && (
        <CounterBetDialog
          open={showCounterDialog}
          onOpenChange={setShowCounterDialog}
          bet={{
            name: bet.name,
            amount: bet.amount,
            game: bet.game,
            time: bet.time
          }}
        />
      )}
    </div>
  );
};

export default BetDetailsPage;