import React, { useState } from 'react';
import { Di<PERSON>, DialogContent, <PERSON>alogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Label } from '@/components/ui/label';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  DollarSign, 
  Users, 
  Clock, 
  Share2, 
  Check, 
  X, 
  ChevronRight,
  Trophy,
  BarChart,
  Target,
  Calendar,
  Activity
} from 'lucide-react';

interface BetDetailsProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  bet: any;
  onCounter: () => void;
}

interface Participant {
  id: string;
  name: string;
  amount: number;
  timestamp: string;
  side: 'for' | 'against';
}

const BetDetails: React.FC<BetDetailsProps> = ({ open, onOpenChange, bet, onCounter }) => {
  const [showShareMenu, setShowShareMenu] = useState(false);
  
  // Mock data for demonstration
  const participants: Participant[] = [
    { id: '1', name: 'John Doe', amount: 50, timestamp: '2 mins ago', side: 'for' },
    { id: '2', name: 'Jane Smith', amount: 35, timestamp: '5 mins ago', side: 'against' },
    { id: '3', name: 'Mike Wilson', amount: 15, timestamp: '10 mins ago', side: 'against' },
    { id: '4', name: 'Sarah Brown', amount: 50, timestamp: '12 mins ago', side: 'for' },
  ];
  
  const timelineEvents = [
    { time: '2 mins ago', event: 'John Doe joined the FOR side with $50' },
    { time: '5 mins ago', event: 'Jane Smith countered with $35' },
    { time: '10 mins ago', event: 'Mike Wilson joined the counter with $15' },
    { time: '12 mins ago', event: 'Sarah Brown placed the initial bet of $50' },
    { time: '15 mins ago', event: 'Bet created by Sarah Brown' },
  ];
  
  const forTotal = participants.filter(p => p.side === 'for').reduce((sum, p) => sum + p.amount, 0);
  const againstTotal = participants.filter(p => p.side === 'against').reduce((sum, p) => sum + p.amount, 0);
  const totalPool = forTotal + againstTotal;
  const forPercentage = totalPool > 0 ? (forTotal / totalPool) * 100 : 50;
  
  const handleShare = (platform: string) => {
    // Implement share functionality
    // console.log(`Sharing on ${platform}`);
    setShowShareMenu(false);
  };
  
  if (!bet) return null;
  
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="w-full sm:max-w-xl bg-slate-900 border-slate-800 max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle className="text-white">Bet Details</DialogTitle>
          <DialogDescription className="text-slate-400">
            View detailed information about this bet
          </DialogDescription>
        </DialogHeader>

        <ScrollArea className="flex-1 pr-4">
          <div className="space-y-6">
            {/* Bet Overview */}
            <Card className="bg-slate-800 border-slate-700">
              <CardContent className="p-4">
                <div className="flex items-center justify-between mb-2">
                  <Badge variant="default" className={`${bet.category === 'Sports' ? 'bg-blue-500' : 'bg-purple-500'}`}>
                    {bet.category}
                  </Badge>
                  <Badge variant="outline" className="border-green-500 text-green-500">
                    Live
                  </Badge>
                </div>
                
                <h3 className="text-lg font-semibold text-white mb-2">{bet.title}</h3>
                
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 text-sm">
                  <div className="flex items-center space-x-2">
                    <Calendar className="h-4 w-4 text-slate-400" />
                    <span className="text-slate-300">{bet.event}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Clock className="h-4 w-4 text-slate-400" />
                    <span className="text-slate-300">{bet.time}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <DollarSign className="h-4 w-4 text-slate-400" />
                    <span className="text-slate-300">${totalPool} total pool</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Users className="h-4 w-4 text-slate-400" />
                    <span className="text-slate-300">{participants.length} participants</span>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            {/* Pool Distribution */}
            <div className="space-y-3">
              <Label className="text-white text-base">Pool Distribution</Label>
              <Card className="bg-slate-800 border-slate-700">
                <CardContent className="p-4">
                  <div className="space-y-3">
                    <div className="flex justify-between mb-2">
                      <div className="flex items-center space-x-2">
                        <Check className="h-4 w-4 text-green-500" />
                        <span className="text-slate-300">For ({forPercentage.toFixed(1)}%)</span>
                      </div>
                      <span className="text-white font-medium">${forTotal}</span>
                    </div>
                    <Progress value={forPercentage} className="h-3" />
                    <div className="flex justify-between">
                      <div className="flex items-center space-x-2">
                        <X className="h-4 w-4 text-red-500" />
                        <span className="text-slate-300">Against ({(100 - forPercentage).toFixed(1)}%)</span>
                      </div>
                      <span className="text-white font-medium">${againstTotal}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
            
            {/* Tabs */}
            <Tabs defaultValue="participants" className="w-full">
              <TabsList className="grid w-full grid-cols-3 bg-slate-800">
                <TabsTrigger value="participants">Participants</TabsTrigger>
                <TabsTrigger value="timeline">Timeline</TabsTrigger>
                <TabsTrigger value="stats">Stats</TabsTrigger>
              </TabsList>
              
              <TabsContent value="participants" className="mt-4">
                <Card className="bg-slate-800 border-slate-700">
                  <CardContent className="p-4">
                    <div className="space-y-3">
                      {participants.map((participant) => (
                        <div key={participant.id} className="flex items-center justify-between p-3 bg-slate-900 rounded-lg">
                          <div className="flex items-center space-x-3">
                            <div className={`h-8 w-8 rounded-full flex items-center justify-center ${
                              participant.side === 'for' ? 'bg-green-500' : 'bg-red-500'
                            }`}>
                              {participant.side === 'for' ? 
                                <Check className="h-4 w-4 text-white" /> : 
                                <X className="h-4 w-4 text-white" />
                              }
                            </div>
                            <div>
                              <p className="text-white font-medium">{participant.name}</p>
                              <p className="text-sm text-slate-400">{participant.timestamp}</p>
                            </div>
                          </div>
                          <div className="text-right">
                            <p className="text-white font-medium">${participant.amount}</p>
                            <p className={`text-sm ${
                              participant.side === 'for' ? 'text-green-500' : 'text-red-500'
                            }`}>
                              {participant.side === 'for' ? 'For' : 'Against'}
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
              
              <TabsContent value="timeline" className="mt-4">
                <Card className="bg-slate-800 border-slate-700">
                  <CardContent className="p-4">
                    <div className="space-y-3">
                      {timelineEvents.map((event, index) => (
                        <div key={index} className="flex items-start space-x-3">
                          <div className="flex-shrink-0">
                            <div className="h-2 w-2 bg-blue-500 rounded-full mt-1.5"></div>
                          </div>
                          <div className="flex-1">
                            <p className="text-slate-400 text-sm">{event.time}</p>
                            <p className="text-white">{event.event}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
              
              <TabsContent value="stats" className="mt-4">
                <Card className="bg-slate-800 border-slate-700">
                  <CardContent className="p-4">
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <div className="flex items-center space-x-2">
                          <Trophy className="h-4 w-4 text-yellow-500" />
                          <span className="text-slate-400">Potential Return</span>
                        </div>
                        <p className="text-2xl font-bold text-white">2.8x</p>
                      </div>
                      <div className="space-y-2">
                        <div className="flex items-center space-x-2">
                          <BarChart className="h-4 w-4 text-blue-500" />
                          <span className="text-slate-400">Market Confidence</span>
                        </div>
                        <p className="text-2xl font-bold text-white">72%</p>
                      </div>
                      <div className="space-y-2">
                        <div className="flex items-center space-x-2">
                          <Target className="h-4 w-4 text-green-500" />
                          <span className="text-slate-400">Target Pool</span>
                        </div>
                        <p className="text-2xl font-bold text-white">$500</p>
                      </div>
                      <div className="space-y-2">
                        <div className="flex items-center space-x-2">
                          <Activity className="h-4 w-4 text-purple-500" />
                          <span className="text-slate-400">Activity Score</span>
                        </div>
                        <p className="text-2xl font-bold text-white">High</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </ScrollArea>
        
        {/* Action Buttons */}
        <div className="flex gap-3 mt-6">
          <Button
            variant="outline"
            className="bg-slate-800 border-slate-700 text-white hover:bg-slate-700"
            onClick={() => setShowShareMenu(!showShareMenu)}
          >
            <Share2 className="h-4 w-4 mr-2" />
            Share
          </Button>
          <Button
            className="flex-1 bg-gradient-to-r from-red-500 to-pink-500 hover:from-red-600 hover:to-pink-600"
            onClick={onCounter}
          >
            Counter This Bet
            <ChevronRight className="ml-2 h-4 w-4" />
          </Button>
        </div>
        
        {/* Share Menu */}
        {showShareMenu && (
          <div className="absolute bottom-20 right-4 bg-slate-800 border border-slate-700 rounded-lg shadow-lg p-2 z-50">
            <button
              onClick={() => handleShare('twitter')}
              className="flex items-center space-x-2 w-full px-3 py-2 hover:bg-slate-700 rounded transition-colors"
            >
              <span className="text-white">Twitter</span>
            </button>
            <button
              onClick={() => handleShare('telegram')}
              className="flex items-center space-x-2 w-full px-3 py-2 hover:bg-slate-700 rounded transition-colors"
            >
              <span className="text-white">Telegram</span>
            </button>
            <button
              onClick={() => handleShare('copy')}
              className="flex items-center space-x-2 w-full px-3 py-2 hover:bg-slate-700 rounded transition-colors"
            >
              <span className="text-white">Copy Link</span>
            </button>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default BetDetails;