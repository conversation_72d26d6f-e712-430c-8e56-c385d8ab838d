import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>ontent, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Trophy, Copy, Check, Link, MessageSquare } from 'lucide-react';

interface ShareBetProps {
  betId?: string;
}

const ShareBet: React.FC<ShareBetProps> = ({ betId = "BET-2024-0542" }) => {
  const [shareUrl] = useState(`https://betbet.com/bet/${betId}`);
  const [copied, setCopied] = useState(false);

  const handleCopy = () => {
    navigator.clipboard.writeText(shareUrl);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  // Placeholder component for QR code - in a real app, this would be a real QR code
  const QrCode = ({ className }: { className?: string }) => {
    return (
      <div className={`flex items-center justify-center ${className}`}>
        <div className="grid grid-cols-5 grid-rows-5 gap-1">
          {[...Array(25)].map((_, i) => (
            <div 
              key={i} 
              className={`w-4 h-4 rounded-sm ${Math.random() > 0.7 ? 'bg-slate-900' : 'bg-transparent'}`}
            />
          ))}
        </div>
      </div>
    );
  };

  return (
    <Card className="bg-slate-900 border-slate-800 max-w-md mx-auto">
      <CardHeader>
        <CardTitle className="text-white">Share This Bet</CardTitle>
        <CardDescription className="text-slate-400">
          Send this link to challenge others
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Share Preview */}
        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="p-4">
            <div className="flex items-start space-x-3">
              <div className="h-10 w-10 rounded-lg bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center flex-shrink-0">
                <Trophy className="h-5 w-5 text-white" />
              </div>
              <div>
                <p className="text-white font-medium">Ronaldo will score vs Barcelona</p>
                <p className="text-sm text-slate-400">$100 stake • Champions League</p>
                <p className="text-sm text-green-500 mt-1">Open for counters</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Share URL */}
        <div className="space-y-2">
          <Label className="text-white">Share Link</Label>
          <div className="flex gap-2">
            <Input
              readOnly
              value={shareUrl}
              className="bg-slate-800 border-slate-700 text-white"
            />
            <Button
              variant="outline"
              className="bg-slate-800 border-slate-700 text-white hover:bg-slate-700"
              onClick={handleCopy}
            >
              {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
            </Button>
          </div>
        </div>

        {/* Share Options */}
        <div className="space-y-3">
          <Label className="text-white">Share via</Label>
          <div className="grid grid-cols-2 gap-3">
            <Button variant="outline" className="bg-slate-800 border-slate-700 text-white hover:bg-slate-700">
              <MessageSquare className="h-4 w-4 mr-2" />
              WhatsApp
            </Button>
            <Button variant="outline" className="bg-slate-800 border-slate-700 text-white hover:bg-slate-700">
              <Link className="h-4 w-4 mr-2" />
              Telegram
            </Button>
          </div>
        </div>

        {/* QR Code */}
        <div className="text-center">
          <p className="text-sm text-slate-400 mb-3">Or scan QR code</p>
          <div className="h-32 w-32 mx-auto bg-white rounded-lg flex items-center justify-center">
            <QrCode className="h-24 w-24 text-slate-900" />
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default ShareBet;