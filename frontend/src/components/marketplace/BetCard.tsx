import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>ontent, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { UserX, Share2, Lock, Timer, Calendar, Clock } from 'lucide-react';
import CounterBetDialog from './CounterBetDialog';

interface BetCardProps {
  status?: 'open' | 'partially-filled' | 'locked' | 'expiring-soon' | 'popular' | 'completed';
  userPosition?: 'for' | 'against' | null;
  result?: 'won' | 'lost' | null;
}

const BetCard: React.FC<BetCardProps> = ({ 
  status = 'open', 
  userPosition = null, 
  result = null 
}) => {
  const [showCounterDialog, setShowCounterDialog] = useState(false);

  const getStatusBadge = () => {
    switch(status) {
      case 'partially-filled':
        return <Badge className="bg-yellow-500 text-white">Filling</Badge>;
      case 'locked':
        return <Badge className="bg-blue-500 text-white">Locked</Badge>;
      case 'expiring-soon':
        return <Badge className="bg-red-500 text-white animate-pulse">Expiring Soon</Badge>;
      case 'popular':
        return <Badge className="bg-purple-500 text-white">🔥 Popular</Badge>;
      case 'completed':
        return result === 'won'
          ? <Badge className="bg-green-500 text-white">Won</Badge>
          : <Badge className="bg-red-500 text-white">Lost</Badge>;
      default:
        return <Badge className="bg-green-500 text-white">Open</Badge>;
    }
  };

  return (
    <>
      <Card className="bg-slate-900 border-slate-800 hover:border-slate-700 transition-all cursor-pointer">
        <CardHeader className="pb-4">
          <div className="flex items-start justify-between mb-2">
            <div className="flex items-center space-x-2">
              <div className="h-8 w-8 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center">
                <UserX className="h-4 w-4 text-white" />
              </div>
              <div>
                <p className="text-sm text-slate-400">Player A • 5 mins ago</p>
              </div>
            </div>
            {getStatusBadge()}
          </div>

          <CardTitle className="text-white text-lg">
            Cristiano Ronaldo will score vs Barcelona
          </CardTitle>

          <div className="flex items-center space-x-4 mt-2">
            <div className="flex items-center space-x-1">
              <Calendar className="h-4 w-4 text-slate-400" />
              <span className="text-sm text-slate-400">Champions League</span>
            </div>
            <div className="flex items-center space-x-1">
              <Clock className="h-4 w-4 text-slate-400" />
              <span className="text-sm text-slate-400">Tonight 8:00 PM</span>
            </div>
          </div>
        </CardHeader>

        <CardContent>
          {/* Stake Information */}
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-slate-400">Total Stake</span>
              <span className="text-2xl font-bold text-white">$100</span>
            </div>

            {status === 'partially-filled' && (
              <>
                <Progress value={65} className="h-2" />
                <div className="flex justify-between text-sm">
                  <span className="text-slate-400">$65 / $100 filled</span>
                  <span className="text-purple-500">3 counters</span>
                </div>
              </>
            )}

            {status === 'locked' && (
              <div className="flex items-center justify-between p-3 bg-slate-800 rounded-lg">
                <div className="flex items-center space-x-2">
                  <Lock className="h-4 w-4 text-blue-500" />
                  <span className="text-sm text-white">Bet Locked</span>
                </div>
                <Badge variant="outline" className={userPosition === 'for' ? 'border-green-500 text-green-500' : 'border-red-500 text-red-500'}>
                  {userPosition === 'for' ? 'Supporting' : 'Against'}
                </Badge>
              </div>
            )}

            {status === 'completed' && (
              <div className={`p-3 rounded-lg ${result === 'won' ? 'bg-green-900/20' : 'bg-red-900/20'}`}>
                <div className="flex items-center justify-between">
                  <span className="text-white font-medium">
                    {result === 'won' ? 'You Won!' : 'You Lost'}
                  </span>
                  <span className={`text-xl font-bold ${result === 'won' ? 'text-green-500' : 'text-red-500'}`}>
                    {result === 'won' ? '+$195' : '-$50'}
                  </span>
                </div>
              </div>
            )}

            {/* Expiry Timer for Open Bets */}
            {status === 'open' || status === 'partially-filled' || status === 'expiring-soon' ? (
              <div className="flex items-center justify-between pt-2">
                <div className="flex items-center space-x-2">
                  <Timer className="h-4 w-4 text-slate-400" />
                  <span className="text-sm text-slate-400">Expires in</span>
                </div>
                <span className={`text-sm font-medium ${status === 'expiring-soon' ? 'text-red-500' : 'text-white'}`}>
                  {status === 'expiring-soon' ? '10 mins' : '2h 45m'}
                </span>
              </div>
            ) : null}
          </div>

          {/* Action Buttons */}
          {(status === 'open' || status === 'partially-filled') && (
            <div className="mt-4 space-y-2">
              <Button
                className="w-full bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600"
                onClick={() => setShowCounterDialog(true)}
              >
                Counter Bet
              </Button>
              <Button variant="outline" className="w-full bg-slate-800 border-slate-700 text-white hover:bg-slate-700">
                <Share2 className="h-4 w-4 mr-2" />
                Share
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Counter Bet Dialog */}
      <CounterBetDialog open={showCounterDialog} onOpenChange={setShowCounterDialog} bet={{
        title: 'Cristiano Ronaldo will score vs Barcelona',
        event: 'Champions League',
        time: 'Tonight 8:00 PM',
        total: 100,
        category: 'Sports'
      }} />
    </>
  );
};

export default BetCard;