import { useState } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Clock,
  Search,
  Plus,
  Share2,
  UserX,
  Lock,
  ChevronDown,
  Tag,
  Flame,
  Sliders,
  Star
} from 'lucide-react';
import CreateBetDialog from './CreateBetDialog';
import CounterBetDialog from './CounterBetDialog';
import BetDetails from './BetDetails';

interface DesktopMarketplaceProps {
  category?: string;
  userId?: string;
  showCreate?: boolean;
}

// Helper function to get bet details
const getBetDetails = (idx: number) => {
  const types = [
    { name: '<PERSON><PERSON><PERSON> will score a hat-trick', game: 'Champions League', amount: 50 + (idx * 25), time: 'Tonight 8:00 PM', category: 'Sports' },
    { name: 'Lakers to win by 10+ points', game: 'NBA Game', amount: 100 + (idx * 50), time: 'Tomorrow 9:00 PM', category: 'Sports' },
    { name: 'Liverpool vs Arsenal - Over 3.5 goals', game: 'Premier League', amount: 75 + (idx * 30), time: 'Sunday 4:00 PM', category: 'Sports' },
    { name: 'Djokovic to win Wimbledon 2024', game: 'Tennis', amount: 200 + (idx * 75), time: 'July 2024', category: 'Sports' }
  ];
  
  return types[idx % types.length];
};

// Desktop Marketplace Component with Proper Scrolling
const DesktopMarketplace = ({ category = 'all', userId, showCreate = false }: DesktopMarketplaceProps) => {
  const [activeFilter, setActiveFilter] = useState(category);
  const [expandedFilters, setExpandedFilters] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [viewMode, setViewMode] = useState('grid'); // 'grid' or 'list'
  const [showCreateDialog, setShowCreateDialog] = useState(showCreate);
  const [showCounterDialog, setShowCounterDialog] = useState(false);
  const [showDetailsDialog, setShowDetailsDialog] = useState(false);
  const [selectedBet, setSelectedBet] = useState<any>(null);
  
  return (
    <div className="h-full bg-slate-950 text-white pt-16">
      <div className="grid grid-cols-12 gap-1 p-1 h-full">
        {/* Column 1: Filters */}
        <div className="col-span-2 flex flex-col gap-1">
          {/* Category Filter - Fixed Height */}
          <div className="bg-slate-900 border border-slate-800 p-2 rounded-sm">
            <div className="flex items-center justify-between mb-2">
              <span className="text-xs font-medium text-white">Categories</span>
              <Button 
                variant="ghost" 
                size="sm" 
                className="h-5 w-5 p-0.5"
                onClick={() => setExpandedFilters(!expandedFilters)}
              >
                <ChevronDown className={`h-3 w-3 text-slate-400 ${expandedFilters ? 'rotate-180 transform' : ''}`} />
              </Button>
            </div>
            
            <div className="space-y-1">
              {[
                { id: 'all', name: 'All Bets', count: 468 },
                { id: 'sports', name: 'Sports Bets', count: 285 },
                { id: 'skill', name: 'Skill Games', count: 124 },
                { id: 'trivia', name: 'Trivia Bets', count: 59 },
              ].map(category => (
                <Button 
                  key={category.id}
                  variant="ghost"
                  className={`w-full justify-between h-6 px-2 text-xs rounded-sm ${
                    activeFilter === category.id 
                      ? 'bg-slate-800 text-white' 
                      : 'text-slate-400 hover:text-white hover:bg-slate-800/50'
                  }`}
                  onClick={() => setActiveFilter(category.id)}
                >
                  <span>{category.name}</span>
                  <Badge className="h-4 px-1 text-[10px] bg-slate-800 text-slate-400">
                    {category.count}
                  </Badge>
                </Button>
              ))}
            </div>
          </div>
          
          {/* Advanced Filters - SCROLLABLE */}
          <div className="bg-slate-900 border border-slate-800 rounded-sm flex-1 overflow-hidden flex flex-col">
            <div className="p-1 border-b border-slate-800">
              <div className="flex items-center justify-between">
                <span className="text-xs font-medium text-white">Filters</span>
                <Button variant="ghost" size="sm" className="h-5 w-5 p-0.5">
                  <Sliders className="h-3 w-3 text-slate-400" />
                </Button>
              </div>
            </div>
            
            <div className="flex-1 overflow-auto p-2 space-y-3">
              {/* Amount Filter */}
              <div>
                <span className="text-xs text-slate-400 mb-1 block">Amount</span>
                <div className="flex flex-wrap gap-1">
                  {['Any', '$1-50', '$50-200', '$200+'].map(amount => (
                    <Button 
                      key={amount}
                      variant="outline"
                      size="sm"
                      className="h-5 px-2 py-0 text-[10px] rounded-sm bg-slate-800 border-slate-700 text-white hover:bg-slate-700"
                    >
                      {amount}
                    </Button>
                  ))}
                </div>
              </div>
              
              {/* Status Filter */}
              <div>
                <span className="text-xs text-slate-400 mb-1 block">Status</span>
                <div className="flex flex-wrap gap-1">
                  {[
                    { name: 'Open', color: 'bg-green-500' },
                    { name: 'Filling', color: 'bg-yellow-500' },
                    { name: 'Expiring', color: 'bg-red-500' },
                    { name: 'All', color: 'bg-purple-500' }
                  ].map(status => (
                    <Button 
                      key={status.name}
                      variant="outline"
                      size="sm"
                      className="h-5 px-2 py-0 text-[10px] rounded-sm bg-slate-800 border-slate-700 text-white hover:bg-slate-700"
                    >
                      <span className={`h-1.5 w-1.5 rounded-full ${status.color} mr-1`}></span>
                      {status.name}
                    </Button>
                  ))}
                </div>
              </div>
              
              {/* Game Type Filter */}
              <div>
                <span className="text-xs text-slate-400 mb-1 block">Game Type</span>
                <div className="space-y-1">
                  {[
                    { id: 'word', name: 'Word Games', icon: '📝' },
                    { id: 'trivia', name: 'Trivia', icon: '🧠' },
                    { id: 'logic', name: 'Logic Puzzles', icon: '🧩' },
                    { id: 'sports', name: 'Sports', icon: '⚽' },
                    { id: 'reaction', name: 'Reaction Games', icon: '⚡' }
                  ].map(game => (
                    <div key={game.id} className="flex items-center">
                      <input 
                        type="checkbox" 
                        id={game.id} 
                        className="h-3 w-3 rounded-sm border-slate-700"
                      />
                      <label htmlFor={game.id} className="ml-1.5 text-xs text-slate-300 flex items-center hover:text-white cursor-pointer">
                        <span className="mr-1">{game.icon}</span>
                        {game.name}
                      </label>
                    </div>
                  ))}
                </div>
              </div>
              
              {/* Time Filter */}
              <div>
                <span className="text-xs text-slate-400 mb-1 block">Created</span>
                <div className="flex flex-wrap gap-1">
                  {['Today', '3 Days', 'Week', 'Any'].map(time => (
                    <Button 
                      key={time}
                      variant="outline"
                      size="sm"
                      className="h-5 px-2 py-0 text-[10px] rounded-sm bg-slate-800 border-slate-700 text-white hover:bg-slate-700"
                    >
                      {time}
                    </Button>
                  ))}
                </div>
              </div>
              
              <Button 
                className="w-full h-6 text-xs bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 rounded-sm"
              >
                Apply Filters
              </Button>
            </div>
          </div>
        </div>
        
        {/* Main Content Area */}
        <div className="col-span-10 flex flex-col h-full overflow-hidden">
          {/* Quick filters bar with search - Fixed Height */}
          <div className="h-8 bg-slate-900 border border-slate-800 rounded-sm px-2 mb-1 flex items-center flex-shrink-0">
            {/* Search field */}
            <div className="relative mr-3">
              <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-3 w-3 text-slate-400" />
              <Input 
                placeholder="Search bets..." 
                className="h-6 pl-7 pr-2 text-xs bg-slate-800 border-slate-700 rounded-sm w-48"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            
            <div className="flex items-center space-x-2">
              <span className="text-xs text-slate-400">Sort:</span>
              <Button 
                variant="ghost"
                size="sm"
                className="h-5 px-2 rounded-sm text-[10px] bg-slate-800 text-white"
              >
                Newest
                <ChevronDown className="h-3 w-3 ml-1" />
              </Button>
              
              <div className="h-4 w-px bg-slate-700 mx-1"></div>
              
              <Button 
                variant="ghost"
                size="sm"
                className="h-5 px-2 rounded-sm text-[10px] text-yellow-500 hover:bg-slate-800 hover:text-yellow-400"
              >
                <Star className="h-3 w-3 mr-1 text-yellow-500" />
                Favorites
              </Button>
              
              <Button 
                variant="ghost"
                size="sm"
                className="h-5 px-2 rounded-sm text-[10px] text-orange-500 hover:bg-slate-800 hover:text-orange-400"
              >
                <Flame className="h-3 w-3 mr-1 text-orange-500" />
                Trending
              </Button>
              
              <Button 
                variant="ghost"
                size="sm"
                className="h-5 px-2 rounded-sm text-[10px] text-red-500 hover:bg-slate-800 hover:text-red-400"
              >
                <Clock className="h-3 w-3 mr-1 text-red-500" />
                Expiring
              </Button>
            </div>
            
            <div className="ml-auto flex items-center space-x-2">
              <Button
                size="sm" 
                className="h-6 px-2 text-xs bg-gradient-to-r from-purple-500 to-pink-500 rounded-sm"
                onClick={() => setShowCreateDialog(true)}
              >
                <Plus className="h-3 w-3 mr-1" />
                New Bet
              </Button>
              
              <div className="flex items-center">
                <Button
                  variant="ghost"
                  size="sm"
                  className={`h-5 w-5 p-0.5 rounded-sm ${viewMode === 'grid' ? 'bg-slate-800 text-white' : 'text-slate-400 hover:bg-slate-800'}`}
                  onClick={() => setViewMode('grid')}
                >
                  <div className="grid grid-cols-2 gap-px h-full w-full">
                    {[...Array(4)].map((_, i) => (
                      <div key={i} className="bg-current rounded-sm" />
                    ))}
                  </div>
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className={`h-5 w-5 p-0.5 rounded-sm ${viewMode === 'list' ? 'bg-slate-800 text-white' : 'text-slate-400 hover:bg-slate-800'}`}
                  onClick={() => setViewMode('list')}
                >
                  <div className="flex flex-col justify-between h-full">
                    {[...Array(3)].map((_, i) => (
                      <div key={i} className="h-0.5 w-full bg-current rounded-sm" />
                    ))}
                  </div>
                </Button>
              </div>
            </div>
          </div>
          
          {/* Grid or List view of bets - SCROLLABLE */}
          <div className="bg-slate-900 border border-slate-800 rounded-sm flex-1 overflow-hidden">
            {viewMode === 'grid' ? (
              <div className="overflow-auto h-full bg-slate-800/50">
                <div className="grid grid-cols-4 gap-2 p-2">
                  {/* Generate filtered bet cards */}
                  {Array.from({ length: 12 })
                    .map((_, index) => ({ id: index, ...getBetDetails(index) }))
                    .filter(bet => 
                      searchQuery === '' || 
                      bet.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                      bet.game.toLowerCase().includes(searchQuery.toLowerCase())
                    )
                    .map((bet) => (
                      <CompactBetCard 
                        key={bet.id} 
                        index={bet.id}
                        onClick={() => {
                          setSelectedBet({
                            ...bet,
                            title: bet.name,
                            event: bet.game,
                            total: bet.amount
                          });
                          setShowDetailsDialog(true);
                        }}
                        onCounter={() => {
                          setSelectedBet({
                            ...bet,
                            title: bet.name,
                            event: bet.game,
                            total: bet.amount
                          });
                          setShowCounterDialog(true);
                        }}
                      />
                    ))}
                </div>
              </div>
            ) : (
              <div className="overflow-auto h-full bg-slate-800/50">
                <div className="p-2">
                  {/* List view */}
                  {Array.from({ length: 12 })
                    .map((_, index) => ({ id: index, ...getBetDetails(index) }))
                    .filter(bet => 
                      searchQuery === '' || 
                      bet.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                      bet.game.toLowerCase().includes(searchQuery.toLowerCase())
                    )
                    .map((bet) => (
                      <div key={bet.id} className="mb-1">
                        <BetListItem 
                          index={bet.id}
                          onClick={() => {
                            setSelectedBet({
                              ...bet,
                              title: bet.name,
                              event: bet.game,
                              total: bet.amount
                            });
                            setShowDetailsDialog(true);
                          }}
                          onCounter={() => {
                            setSelectedBet({
                              ...bet,
                              title: bet.name,
                              event: bet.game,
                              total: bet.amount
                            });
                            setShowCounterDialog(true);
                          }}
                        />
                      </div>
                    ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
      
      {/* Dialogs */}
      <CreateBetDialog 
        open={showCreateDialog} 
        onOpenChange={setShowCreateDialog}
      />
      
      <CounterBetDialog 
        open={showCounterDialog} 
        onOpenChange={setShowCounterDialog}
        bet={selectedBet}
      />
      
      <BetDetails 
        open={showDetailsDialog} 
        onOpenChange={setShowDetailsDialog}
        bet={selectedBet}
        onCounter={() => {
          setShowDetailsDialog(false);
          setShowCounterDialog(true);
        }}
      />
    </div>
  );
};

// Compact Bet Card Component
const CompactBetCard = ({ index, onClick, onCounter }: { index: number; onClick: () => void; onCounter: () => void; }) => {
  // Generate different statuses based on index for variety
  const getStatusFromIndex = (idx: number) => {
    const statuses = ['open', 'partially-filled', 'expiring-soon', 'locked'];
    return statuses[idx % statuses.length];
  };
  
  const status = getStatusFromIndex(index);
  const betDetails = getBetDetails(index);
  
  const getStatusBadge = (s: string) => {
    switch(s) {
      case 'partially-filled':
        return <Badge className="text-[10px] h-4 px-1 bg-yellow-500 text-white">Filling</Badge>;
      case 'locked':
        return <Badge className="text-[10px] h-4 px-1 bg-blue-500 text-white">Locked</Badge>;
      case 'expiring-soon':
        return <Badge className="text-[10px] h-4 px-1 bg-red-500 text-white animate-pulse">Expiring</Badge>;
      default:
        return <Badge className="text-[10px] h-4 px-1 bg-green-500 text-white">Open</Badge>;
    }
  };
  
  // Different fill levels for progress bars
  const getFillLevel = (idx: number) => {
    const levels = [0, 25, 45, 65, 85, 100];
    return levels[idx % levels.length];
  };
  
  return (
    <div className="bg-slate-900 p-2.5 hover:bg-slate-800/70 cursor-pointer rounded-sm border border-slate-800/50 hover:border-slate-700 transition-all" onClick={onClick}>
      <div className="flex items-start justify-between mb-2">
        <div className="flex items-center space-x-1.5">
          <div className="h-6 w-6 rounded-full bg-gradient-to-r from-slate-700 to-slate-600 flex items-center justify-center">
            <UserX className="h-3 w-3 text-white" />
          </div>
          <span className="text-[10px] text-slate-400">Player {String.fromCharCode(65 + (index % 26))}</span>
        </div>
        {getStatusBadge(status)}
      </div>
      
      <div className="mb-2">
        <h3 className="text-xs font-medium text-white mb-1 line-clamp-2" style={{minHeight: "2rem"}}>
          {betDetails.name}
        </h3>
        <div className="flex items-center">
          <Tag className="h-2.5 w-2.5 text-slate-400 mr-1" />
          <span className="text-[10px] text-slate-400">{betDetails.game}</span>
        </div>
      </div>
      
      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <span className="text-[10px] text-slate-400">Total Stake</span>
          <span className="text-xs font-bold text-white">${betDetails.amount}</span>
        </div>
        
        {status === 'partially-filled' && (
          <>
            <Progress value={getFillLevel(index)} className="h-1.5" />
            <div className="flex justify-between items-center text-[10px]">
              <span className="text-slate-400">${Math.floor(betDetails.amount * getFillLevel(index) / 100)} filled</span>
              <span className="text-purple-400">{index + 1} counters</span>
            </div>
          </>
        )}
        
        {status === 'expiring-soon' && (
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Clock className="h-2.5 w-2.5 text-red-500 mr-1" />
              <span className="text-[10px] text-red-500">Expires in {10 - (index % 10)}m</span>
            </div>
            <Flame className="h-3 w-3 text-orange-500" />
          </div>
        )}
        
        {status === 'locked' && (
          <div className="flex items-center justify-between p-1.5 bg-slate-800 rounded">
            <div className="flex items-center">
              <Lock className="h-2.5 w-2.5 text-blue-500 mr-1" />
              <span className="text-[10px] text-white">Bet Locked</span>
            </div>
            <span className="text-[10px] text-blue-400">{index % 2 === 0 ? 'Supporting' : 'Against'}</span>
          </div>
        )}
        
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-1.5">
            <Button 
              variant="ghost" 
              size="sm" 
              className="h-5 px-1.5 py-0 text-[10px] rounded-sm bg-slate-800 text-white hover:bg-slate-700"
            >
              {(1 + (index % 3)).toFixed(2)}
            </Button>
            <Button 
              variant="ghost" 
              size="sm" 
              className="h-5 px-1.5 py-0 text-[10px] rounded-sm bg-slate-800 text-white hover:bg-slate-700"
            >
              {(2 + (index % 2)).toFixed(2)}
            </Button>
          </div>
          
          <Button 
            variant="ghost" 
            size="sm" 
            className="h-5 w-5 p-0.5 rounded-sm text-slate-400 hover:text-white"
          >
            <Share2 className="h-3 w-3" />
          </Button>
        </div>
      </div>
      
      {/* Quick actions on hover */}
      <div className="mt-2 pt-2 border-t border-slate-800 grid grid-cols-2 gap-1">
        <Button 
          className="h-6 text-[10px] rounded-sm bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600"
          onClick={(e) => {
            e.stopPropagation();
            onCounter();
          }}
        >
          Counter Bet
        </Button>
        <Button 
          variant="outline"
          className="h-6 text-[10px] rounded-sm bg-slate-800 border-slate-700 text-white hover:bg-slate-700"
          onClick={(e) => {
            e.stopPropagation();
            onClick();
          }}
        >
          View Details
        </Button>
      </div>
    </div>
  );
};

// List view item component
const BetListItem = ({ index, onClick, onCounter }: { index: number; onClick: () => void; onCounter: () => void; }) => {
  const getStatusFromIndex = (idx: number) => {
    const statuses = ['open', 'partially-filled', 'expiring-soon', 'locked'];
    return statuses[idx % statuses.length];
  };
  
  const status = getStatusFromIndex(index);
  const betDetails = getBetDetails(index);
  
  const getStatusBadge = (s: string) => {
    switch(s) {
      case 'partially-filled':
        return <Badge className="text-[10px] h-4 px-1 bg-yellow-500 text-white">Filling</Badge>;
      case 'locked':
        return <Badge className="text-[10px] h-4 px-1 bg-blue-500 text-white">Locked</Badge>;
      case 'expiring-soon':
        return <Badge className="text-[10px] h-4 px-1 bg-red-500 text-white animate-pulse">Expiring</Badge>;
      default:
        return <Badge className="text-[10px] h-4 px-1 bg-green-500 text-white">Open</Badge>;
    }
  };
  
  const getFillLevel = (idx: number) => {
    const levels = [0, 25, 45, 65, 85, 100];
    return levels[idx % levels.length];
  };
  
  return (
    <div className="flex items-center p-3 bg-slate-900 border border-slate-800/50 rounded-sm hover:bg-slate-800/70 hover:border-slate-700 cursor-pointer transition-all" onClick={onClick}>
      {/* Avatar */}
      <div className="h-6 w-6 rounded-full bg-gradient-to-r from-slate-700 to-slate-600 flex items-center justify-center mr-2">
        <UserX className="h-3 w-3 text-white" />
      </div>
      
      {/* Bet details */}
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-2">
          <h3 className="text-xs font-medium text-white truncate max-w-[300px]">{betDetails.name}</h3>
          {getStatusBadge(status)}
        </div>
        <div className="flex items-center text-[10px] text-slate-400">
          <Tag className="h-2.5 w-2.5 mr-1" />
          <span>{betDetails.game}</span>
          <span className="mx-1.5">•</span>
          <span>Player {String.fromCharCode(65 + (index % 26))}</span>
        </div>
      </div>
      
      {/* Progress for partially filled */}
      {status === 'partially-filled' && (
        <div className="w-24 mx-3">
          <Progress value={getFillLevel(index)} className="h-1.5" />
          <div className="text-[10px] text-slate-400 mt-0.5">
            ${Math.floor(betDetails.amount * getFillLevel(index) / 100)}/{betDetails.amount}
          </div>
        </div>
      )}
      
      {/* Expiring timer */}
      {status === 'expiring-soon' && (
        <div className="flex items-center mx-3">
          <Clock className="h-3 w-3 text-red-500 mr-1" />
          <span className="text-xs text-red-500">{10 - (index % 10)}m</span>
        </div>
      )}
      
      {/* Amount and odds */}
      <div className="flex items-center space-x-3">
        <div className="text-right">
          <div className="text-xs font-bold text-white">${betDetails.amount}</div>
          <div className="text-[10px] text-slate-400">Stake</div>
        </div>
        
        <div className="flex items-center space-x-1">
          <Button 
            variant="ghost" 
            size="sm" 
            className="h-5 px-1.5 py-0 text-[10px] rounded-sm bg-slate-800 text-white hover:bg-slate-700"
          >
            {(1 + (index % 3)).toFixed(2)}
          </Button>
          <Button 
            variant="ghost" 
            size="sm" 
            className="h-5 px-1.5 py-0 text-[10px] rounded-sm bg-slate-800 text-white hover:bg-slate-700"
          >
            {(2 + (index % 2)).toFixed(2)}
          </Button>
        </div>
        
        {/* Action buttons */}
        <div className="flex items-center space-x-1">
          <Button 
            className="h-5 px-2 text-[10px] rounded-sm bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600"
            onClick={(e) => {
              e.stopPropagation();
              onCounter();
            }}
          >
            Counter
          </Button>
          <Button 
            variant="ghost" 
            size="sm" 
            className="h-5 w-5 p-0.5 rounded-sm text-slate-400 hover:text-white"
          >
            <Share2 className="h-3 w-3" />
          </Button>
        </div>
      </div>
    </div>
  );
};

export default DesktopMarketplace;