import React, { useState } from 'react';
import { <PERSON><PERSON>, Dialog<PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Card, CardContent } from '@/components/ui/card';
import { 
  DollarSign, 
  Globe, 
  Users,
  Lock,
  Percent,
  Plus,
  Gamepad2,
  Trophy,
  Clock,
  AlertCircle,
  Check,
  ChevronRight,
  ChevronLeft,
  Eye,
  Calendar,
  TrendingUp
} from 'lucide-react';

interface CreateBetDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

// Step interface
interface Step {
  id: string;
  title: string;
  icon: React.ReactNode;
  isComplete: boolean;
}

const CreateBetDialog: React.FC<CreateBetDialogProps> = ({ open, onOpenChange }) => {
  const [activeStep, setActiveStep] = useState(0);
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [category, setCategory] = useState('sports');
  const [gameType, setGameType] = useState('');
  const [betAmount, setBetAmount] = useState('50');
  const [odds, setOdds] = useState({ for: '1.50', against: '2.50' });
  const [visibility, setVisibility] = useState('public');
  const [duration, setDuration] = useState('24');
  const [isCreating, setIsCreating] = useState(false);

  // Step metadata
  const steps: Step[] = [
    { 
      id: 'bet-details', 
      title: 'Bet Details', 
      icon: <Calendar className="h-4 w-4" />, 
      isComplete: false 
    },
    { 
      id: 'stake-odds', 
      title: 'Stake & Odds', 
      icon: <TrendingUp className="h-4 w-4" />, 
      isComplete: false 
    },
    { 
      id: 'visibility-duration', 
      title: 'Visibility & Duration', 
      icon: <Eye className="h-4 w-4" />, 
      isComplete: false 
    },
    { 
      id: 'review-confirm', 
      title: 'Review & Confirm', 
      icon: <Check className="h-4 w-4" />, 
      isComplete: false 
    }
  ];

  // Helper to advance to next step
  const nextStep = () => {
    if (activeStep < steps.length - 1) {
      setActiveStep(activeStep + 1);
      // Mark the current step as complete
      const updatedSteps = [...steps];
      updatedSteps[activeStep].isComplete = true;
    }
  };

  // Helper to go back to previous step
  const prevStep = () => {
    if (activeStep > 0) {
      setActiveStep(activeStep - 1);
    }
  };

  const categories = [
    { value: 'sports', label: 'Sports Bets', icon: Trophy },
    { value: 'games', label: 'Skill Games', icon: Gamepad2 },
    { value: 'trivia', label: 'Trivia Bets', icon: Users },
    { value: 'custom', label: 'Custom Bet', icon: Plus }
  ];

  const gameTypes: Record<string, string[]> = {
    sports: ['Soccer', 'Basketball', 'Tennis', 'NFL', 'Cricket'],
    games: ['Word Challenge', 'Logic Puzzle', 'Pattern Master', 'Quick Reflexes'],
    trivia: ['Sports Trivia', 'General Knowledge', 'History', 'Science'],
    custom: ['Custom Event']
  };

  const handleCreate = async () => {
    setIsCreating(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Reset form
    setTitle('');
    setDescription('');
    setBetAmount('50');
    setOdds({ for: '1.50', against: '2.50' });
    setActiveStep(0);
    setIsCreating(false);
    onOpenChange(false);
  };

  const calculateReturns = () => {
    const amount = parseFloat(betAmount) || 0;
    const forOdds = parseFloat(odds.for) || 1;
    const againstOdds = parseFloat(odds.against) || 1;
    
    return {
      forReturn: (amount * forOdds).toFixed(2),
      againstReturn: (amount * againstOdds).toFixed(2)
    };
  };

  const returns = calculateReturns();

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="w-full sm:max-w-lg bg-slate-900 border-slate-800 max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-white">Create New Bet</DialogTitle>
          <DialogDescription className="text-slate-400">
            Define your bet and let others counter it
          </DialogDescription>
        </DialogHeader>

        {/* Steps indicator */}
        <div className="flex justify-between mb-6 mt-2">
          {steps.map((step, index) => (
            <div key={step.id} className="flex items-center">
              <div 
                className={`flex items-center justify-center h-8 w-8 rounded-full 
                  ${index === activeStep ? 'bg-gradient-to-r from-purple-500 to-pink-500' : 
                    step.isComplete ? 'bg-green-500' : 'bg-slate-700'} 
                  text-white font-medium`}
              >
                {step.isComplete ? <Check className="h-4 w-4" /> : index + 1}
              </div>
              <div 
                className={`ml-2 ${index === activeStep ? 'text-white' : 
                  step.isComplete ? 'text-green-400' : 'text-slate-500'}`}
              >
                {index < steps.length - 1 && (
                  <div className={`h-px w-8 ${step.isComplete ? 'bg-green-500' : 'bg-slate-700'} 
                    ml-2 hidden sm:inline-block`}
                  />
                )}
              </div>
            </div>
          ))}
        </div>

        <div className="py-2">
          {/* Step 1: Bet Details */}
          {activeStep === 0 && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-white flex items-center">
                <Calendar className="h-5 w-5 mr-2 text-purple-500" />
                Bet Details
              </h3>
              
              {/* Category Selection */}
              <div className="space-y-3">
                <Label className="text-white">Category</Label>
                <RadioGroup value={category} onValueChange={setCategory}>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                    {categories.map((cat) => (
                      <div key={cat.value}>
                        <RadioGroupItem value={cat.value} id={cat.value} className="sr-only" />
                        <Label
                          htmlFor={cat.value}
                          className={`flex items-center justify-center p-3 rounded-lg border cursor-pointer transition-all ${
                            category === cat.value
                              ? 'bg-purple-500/20 border-purple-500 text-purple-500'
                              : 'bg-slate-800 border-slate-700 text-slate-300 hover:border-slate-600'
                          }`}
                        >
                          <cat.icon className="h-5 w-5 mr-2" />
                          {cat.label}
                        </Label>
                      </div>
                    ))}
                  </div>
                </RadioGroup>
              </div>

              {/* Game Type */}
              <div className="space-y-3">
                <Label className="text-white">Game/Event Type</Label>
                <Select value={gameType} onValueChange={setGameType}>
                  <SelectTrigger className="bg-slate-800 border-slate-700 text-white">
                    <SelectValue placeholder="Select game or event type" />
                  </SelectTrigger>
                  <SelectContent>
                    {gameTypes[category]?.map((type) => (
                      <SelectItem key={type} value={type.toLowerCase()}>
                        {type}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Bet Title */}
              <div className="space-y-3">
                <Label className="text-white">Bet Title</Label>
                <Input
                  placeholder="e.g., Liverpool to win by 2+ goals"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  className="bg-slate-800 border-slate-700 text-white"
                />
              </div>

              {/* Description */}
              <div className="space-y-3">
                <Label className="text-white">Description (Optional)</Label>
                <Textarea
                  placeholder="Add any additional details about your bet..."
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  className="bg-slate-800 border-slate-700 text-white h-20 resize-none"
                />
              </div>
              
              <div className="flex justify-end mt-6">
                <Button
                  className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600"
                  onClick={nextStep}
                  disabled={!title || !gameType}
                >
                  Next
                  <ChevronRight className="ml-2 h-4 w-4" />
                </Button>
              </div>
            </div>
          )}
          
          {/* Step 2: Stake & Odds */}
          {activeStep === 1 && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-white flex items-center">
                <TrendingUp className="h-5 w-5 mr-2 text-green-500" />
                Stake & Odds
              </h3>
              
              {/* Bet Amount */}
              <div className="space-y-3">
                <Label className="text-white">Your Stake</Label>
                <div className="relative">
                  <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                  <Input
                    type="number"
                    value={betAmount}
                    onChange={(e) => setBetAmount(e.target.value)}
                    className="pl-10 bg-slate-800 border-slate-700 text-white"
                  />
                </div>
                
                {/* Quick amount buttons */}
                <div className="flex gap-2">
                  {['25', '50', '100', '250'].map((amount) => (
                    <Button
                      key={amount}
                      variant="outline"
                      size="sm"
                      className="bg-slate-800 border-slate-700 text-white hover:bg-slate-700"
                      onClick={() => setBetAmount(amount)}
                    >
                      ${amount}
                    </Button>
                  ))}
                </div>
              </div>

              {/* Odds */}
              <div className="space-y-3">
                <Label className="text-white">Set Odds</Label>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div>
                    <Label className="text-sm text-slate-400 mb-2">For (Your Side)</Label>
                    <div className="relative">
                      <Percent className="absolute left-3 top-1/2 transform -translate-y-1/2 h-3 w-3 text-slate-400" />
                      <Input
                        type="number"
                        step="0.01"
                        value={odds.for}
                        onChange={(e) => setOdds({ ...odds, for: e.target.value })}
                        className="pl-8 bg-slate-800 border-slate-700 text-white"
                      />
                    </div>
                  </div>
                  <div>
                    <Label className="text-sm text-slate-400 mb-2">Against (Counter Side)</Label>
                    <div className="relative">
                      <Percent className="absolute left-3 top-1/2 transform -translate-y-1/2 h-3 w-3 text-slate-400" />
                      <Input
                        type="number"
                        step="0.01"
                        value={odds.against}
                        onChange={(e) => setOdds({ ...odds, against: e.target.value })}
                        className="pl-8 bg-slate-800 border-slate-700 text-white"
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Potential Returns Preview */}
              <Card className="bg-slate-800 border-slate-700">
                <CardContent className="p-4">
                  <h4 className="text-white font-medium mb-3">Potential Returns</h4>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-slate-400">If you win (For)</span>
                      <span className="text-green-500 font-medium">${returns.forReturn}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-slate-400">If counters win (Against)</span>
                      <span className="text-red-500 font-medium">${returns.againstReturn}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              <div className="flex justify-between mt-6">
                <Button
                  variant="outline"
                  className="bg-slate-800 border-slate-700 text-white hover:bg-slate-700"
                  onClick={prevStep}
                >
                  <ChevronLeft className="mr-2 h-4 w-4" />
                  Back
                </Button>
                <Button
                  className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600"
                  onClick={nextStep}
                  disabled={!betAmount || !odds.for || !odds.against}
                >
                  Next
                  <ChevronRight className="ml-2 h-4 w-4" />
                </Button>
              </div>
            </div>
          )}
          
          {/* Step 3: Visibility & Duration */}
          {activeStep === 2 && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-white flex items-center">
                <Eye className="h-5 w-5 mr-2 text-blue-500" />
                Visibility & Duration
              </h3>
              
              {/* Visibility */}
              <div className="space-y-3">
                <Label className="text-white">Who can see this bet?</Label>
                <RadioGroup value={visibility} onValueChange={setVisibility}>
                  <div className="space-y-3">
                    <div className="flex items-start space-x-3">
                      <RadioGroupItem value="public" id="public" />
                      <div className="flex-1">
                        <Label htmlFor="public" className="flex items-center cursor-pointer text-white">
                          <Globe className="h-4 w-4 mr-2 text-green-500" />
                          <span className="font-medium">Public</span>
                        </Label>
                        <p className="text-sm text-slate-400">Anyone can see and counter this bet</p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3">
                      <RadioGroupItem value="private" id="private" />
                      <div className="flex-1">
                        <Label htmlFor="private" className="flex items-center cursor-pointer text-white">
                          <Lock className="h-4 w-4 mr-2 text-amber-500" />
                          <span className="font-medium">Private</span>
                        </Label>
                        <p className="text-sm text-slate-400">Only people with the link can see this bet</p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3">
                      <RadioGroupItem value="friends" id="friends" />
                      <div className="flex-1">
                        <Label htmlFor="friends" className="flex items-center cursor-pointer text-white">
                          <Users className="h-4 w-4 mr-2 text-blue-500" />
                          <span className="font-medium">Friends Only</span>
                        </Label>
                        <p className="text-sm text-slate-400">Only your friends can see and counter</p>
                      </div>
                    </div>
                  </div>
                </RadioGroup>
              </div>

              {/* Duration */}
              <div className="space-y-3">
                <Label className="text-white">How long should this bet stay active?</Label>
                <div className="relative">
                  <Clock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                  <Select value={duration} onValueChange={setDuration}>
                    <SelectTrigger className="pl-10 bg-slate-800 border-slate-700 text-white">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1">1 hour</SelectItem>
                      <SelectItem value="6">6 hours</SelectItem>
                      <SelectItem value="12">12 hours</SelectItem>
                      <SelectItem value="24">24 hours</SelectItem>
                      <SelectItem value="48">48 hours</SelectItem>
                      <SelectItem value="72">3 days</SelectItem>
                      <SelectItem value="168">1 week</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Info Card */}
              <Card className="bg-blue-900/20 border-blue-800">
                <CardContent className="p-4">
                  <div className="flex items-start space-x-3">
                    <AlertCircle className="h-5 w-5 text-blue-500 mt-0.5" />
                    <div>
                      <p className="text-sm text-blue-500 font-medium">Visibility Note</p>
                      <p className="text-sm text-slate-300 mt-1">
                        You can share private bets with specific people using a unique link. 
                        Public bets appear in the marketplace immediately.
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              <div className="flex justify-between mt-6">
                <Button
                  variant="outline"
                  className="bg-slate-800 border-slate-700 text-white hover:bg-slate-700"
                  onClick={prevStep}
                >
                  <ChevronLeft className="mr-2 h-4 w-4" />
                  Back
                </Button>
                <Button
                  className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600"
                  onClick={nextStep}
                >
                  Next
                  <ChevronRight className="ml-2 h-4 w-4" />
                </Button>
              </div>
            </div>
          )}
          
          {/* Step 4: Review & Confirm */}
          {activeStep === 3 && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-white flex items-center">
                <Check className="h-5 w-5 mr-2 text-green-500" />
                Review & Confirm
              </h3>
              
              {/* Bet Summary */}
              <Card className="bg-slate-800 border-slate-700">
                <CardContent className="p-4 space-y-4">
                  <div>
                    <h4 className="text-white font-medium mb-2">Bet Details</h4>
                    <div className="text-sm space-y-1">
                      <div className="flex justify-between">
                        <span className="text-slate-400">Category:</span>
                        <span className="text-white capitalize">{category}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-slate-400">Type:</span>
                        <span className="text-white">{gameType}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-slate-400">Title:</span>
                        <span className="text-white">{title}</span>
                      </div>
                      {description && (
                        <div className="mt-2">
                          <span className="text-slate-400">Description:</span>
                          <p className="text-white text-xs mt-1">{description}</p>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div className="h-px bg-slate-700"></div>
                  
                  <div>
                    <h4 className="text-white font-medium mb-2">Stake & Odds</h4>
                    <div className="text-sm space-y-1">
                      <div className="flex justify-between">
                        <span className="text-slate-400">Your Stake:</span>
                        <span className="text-white">${betAmount}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-slate-400">Odds For:</span>
                        <span className="text-green-500">{odds.for}x</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-slate-400">Odds Against:</span>
                        <span className="text-red-500">{odds.against}x</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="h-px bg-slate-700"></div>
                  
                  <div>
                    <h4 className="text-white font-medium mb-2">Settings</h4>
                    <div className="text-sm space-y-1">
                      <div className="flex justify-between">
                        <span className="text-slate-400">Visibility:</span>
                        <span className="text-white capitalize">{visibility}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-slate-400">Duration:</span>
                        <span className="text-white">
                          {duration === '1' ? '1 hour' : 
                           duration === '6' ? '6 hours' :
                           duration === '12' ? '12 hours' :
                           duration === '24' ? '24 hours' :
                           duration === '48' ? '48 hours' :
                           duration === '72' ? '3 days' : '1 week'}
                        </span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              {/* Warning */}
              <div className="flex items-start space-x-2 p-3 bg-yellow-900/20 rounded-lg">
                <AlertCircle className="h-5 w-5 text-yellow-500 mt-0.5" />
                <div className="flex-1">
                  <p className="text-sm text-yellow-500 font-medium">Important</p>
                  <p className="text-sm text-slate-300">
                    Your ${betAmount} will be locked until this bet is resolved. 
                    Make sure you're confident in your position before creating this bet.
                  </p>
                </div>
              </div>
              
              <div className="flex justify-between mt-6">
                <Button
                  variant="outline"
                  className="bg-slate-800 border-slate-700 text-white hover:bg-slate-700"
                  onClick={prevStep}
                  disabled={isCreating}
                >
                  <ChevronLeft className="mr-2 h-4 w-4" />
                  Back
                </Button>
                <Button
                  className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600"
                  onClick={handleCreate}
                  disabled={isCreating}
                >
                  {isCreating ? 'Creating...' : `Create Bet for $${betAmount}`}
                </Button>
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default CreateBetDialog;