import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  Clock,
  Search,
  Plus,
  UserX,
  Tag,
  Flame,
  Sliders,
  Star,
  X
} from 'lucide-react';
import CreateBetDialog from './CreateBetDialog';
import CounterBetDialog from './CounterBetDialog';
import BetDetails from './BetDetails';

// Helper function to get bet details
const getBetDetails = (idx: number) => {
  const types = [
    { name: '<PERSON><PERSON><PERSON> will score a hat-trick', game: 'Champions League', amount: 50 + (idx * 25), time: 'Tonight 8:00 PM', category: 'Sports' },
    { name: 'Lakers to win by 10+ points', game: 'NBA Game', amount: 100 + (idx * 50), time: 'Tomorrow 9:00 PM', category: 'Sports' },
    { name: 'Liverpool vs Arsenal - Over 3.5 goals', game: 'Premier League', amount: 75 + (idx * 30), time: 'Sunday 4:00 PM', category: 'Sports' },
    { name: '<PERSON><PERSON><PERSON> to win Wimbledon 2024', game: 'Tennis', amount: 200 + (idx * 75), time: 'July 2024', category: 'Sports' }
  ];
  
  return types[idx % types.length];
};

interface MobileMarketplaceProps {
  category?: string;
  userId?: string;
  showCreate?: boolean;
}

const MobileMarketplace = ({ category = 'all', userId, showCreate = false }: MobileMarketplaceProps) => {
  const [activeFilter, setActiveFilter] = useState(category);
  const [searchQuery, setSearchQuery] = useState('');
  const [showCreateDialog, setShowCreateDialog] = useState(showCreate);
  const [showCounterDialog, setShowCounterDialog] = useState(false);
  const [showDetailsDialog, setShowDetailsDialog] = useState(false);
  const [selectedBet, setSelectedBet] = useState<any>(null);
  const [showFilters, setShowFilters] = useState(false);
  const [showSearch, setShowSearch] = useState(false);
  
  return (
    <div className="h-screen bg-slate-950 overflow-hidden flex flex-col pt-16">
      {/* Fixed Header */}
      <div className="flex-none bg-slate-900 border-b border-slate-800">
        <div className="flex items-center justify-between p-3">
          <h1 className="text-lg font-semibold text-white">Marketplace</h1>
          
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowSearch(!showSearch)}
              className="h-9 w-9 p-0"
            >
              <Search className="h-5 w-5 text-white" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowFilters(true)}
              className="h-9 w-9 p-0"
            >
              <Sliders className="h-5 w-5 text-white" />
            </Button>
          </div>
        </div>
        
        {/* Expandable Search */}
        {showSearch && (
          <div className="px-3 pb-3">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
              <Input 
                placeholder="Search bets..." 
                className="h-10 pl-10 pr-3 text-sm bg-slate-800 border-slate-700 rounded-lg w-full"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                autoFocus
              />
            </div>
          </div>
        )}
        
        {/* Quick Filters */}
        <div className="flex space-x-2 px-3 pb-3 overflow-x-auto">
          {[
            { id: 'all', name: 'All Bets', icon: null },
            { id: 'trending', name: 'Trending', icon: <Flame className="h-4 w-4" /> },
            { id: 'expiring', name: 'Expiring', icon: <Clock className="h-4 w-4" /> },
            { id: 'favorites', name: 'Favorites', icon: <Star className="h-4 w-4" /> }
          ].map(filter => (
            <Button
              key={filter.id}
              variant={activeFilter === filter.id ? 'default' : 'ghost'}
              size="sm"
              className={`h-8 px-3 text-xs whitespace-nowrap ${
                activeFilter === filter.id 
                  ? 'bg-purple-600 text-white' 
                  : 'text-slate-400'
              }`}
              onClick={() => setActiveFilter(filter.id)}
            >
              {filter.icon && <span className="mr-1">{filter.icon}</span>}
              {filter.name}
            </Button>
          ))}
        </div>
      </div>
      
      {/* Main Content */}
      <div className="flex-1 overflow-y-auto px-3 pb-20">
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
          {/* Bet Cards */}
          {Array.from({ length: 12 })
            .map((_, index) => ({ id: index, ...getBetDetails(index) }))
            .filter(bet => 
              searchQuery === '' || 
              bet.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
              bet.game.toLowerCase().includes(searchQuery.toLowerCase())
            )
            .map((bet) => (
              <MobileBetCard 
                key={bet.id} 
                bet={bet}
                onClick={() => {
                  setSelectedBet({
                    ...bet,
                    title: bet.name,
                    event: bet.game,
                    total: bet.amount
                  });
                  setShowDetailsDialog(true);
                }}
                onCounter={() => {
                  setSelectedBet({
                    ...bet,
                    title: bet.name,
                    event: bet.game,
                    total: bet.amount
                  });
                  setShowCounterDialog(true);
                }}
              />
            ))}
        </div>
      </div>
      
      {/* Floating Action Button */}
      <Button
        className="fixed bottom-6 right-6 h-14 w-14 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 shadow-lg z-20"
        onClick={() => setShowCreateDialog(true)}
      >
        <Plus className="h-6 w-6" />
      </Button>
      
      {/* Filters Overlay */}
      {showFilters && (
        <div className="fixed inset-0 z-40 bg-slate-900 flex flex-col">
          <div className="flex items-center justify-between p-4 border-b border-slate-800">
            <h2 className="text-lg font-semibold text-white">Filters</h2>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowFilters(false)}
              className="h-8 w-8 p-0"
            >
              <X className="h-5 w-5 text-white" />
            </Button>
          </div>
          
          <div className="flex-1 overflow-y-auto p-4 space-y-6">
            {/* Categories */}
            <div>
              <h3 className="text-sm font-medium text-white mb-3">Categories</h3>
              <div className="space-y-2">
                {[
                  { id: 'all', name: 'All Bets', count: 468 },
                  { id: 'sports', name: 'Sports Bets', count: 285 },
                  { id: 'skill', name: 'Skill Games', count: 124 },
                  { id: 'trivia', name: 'Trivia Bets', count: 59 }
                ].map(category => (
                  <Button
                    key={category.id}
                    variant="outline"
                    className="w-full justify-between h-12 px-4 text-sm border-slate-700 text-white"
                    onClick={() => {
                      setActiveFilter(category.id);
                      setShowFilters(false);
                    }}
                  >
                    <span>{category.name}</span>
                    <Badge className="bg-slate-800 text-slate-400">
                      {category.count}
                    </Badge>
                  </Button>
                ))}
              </div>
            </div>
            
            {/* Amount Range */}
            <div>
              <h3 className="text-sm font-medium text-white mb-3">Amount</h3>
              <div className="grid grid-cols-2 gap-2">
                {['Any', '$1-50', '$50-200', '$200+'].map(amount => (
                  <Button
                    key={amount}
                    variant="outline"
                    className="h-10 text-sm border-slate-700 text-white"
                  >
                    {amount}
                  </Button>
                ))}
              </div>
            </div>
            
            {/* Status */}
            <div>
              <h3 className="text-sm font-medium text-white mb-3">Status</h3>
              <div className="grid grid-cols-2 gap-2">
                {[
                  { name: 'Open', color: 'bg-green-500' },
                  { name: 'Filling', color: 'bg-yellow-500' },
                  { name: 'Expiring', color: 'bg-red-500' },
                  { name: 'All', color: 'bg-purple-500' }
                ].map(status => (
                  <Button
                    key={status.name}
                    variant="outline"
                    className="h-10 text-sm border-slate-700 text-white"
                  >
                    <span className={`h-2 w-2 rounded-full ${status.color} mr-2`} />
                    {status.name}
                  </Button>
                ))}
              </div>
            </div>
            
            <Button
              className="w-full h-12 text-sm bg-gradient-to-r from-purple-500 to-pink-500"
              onClick={() => setShowFilters(false)}
            >
              Apply Filters
            </Button>
          </div>
        </div>
      )}
      
      {/* Dialogs */}
      <CreateBetDialog 
        open={showCreateDialog} 
        onOpenChange={setShowCreateDialog}
      />
      
      <CounterBetDialog 
        open={showCounterDialog} 
        onOpenChange={setShowCounterDialog}
        bet={selectedBet}
      />
      
      <BetDetails 
        open={showDetailsDialog} 
        onOpenChange={setShowDetailsDialog}
        bet={selectedBet}
        onCounter={() => {
          setShowDetailsDialog(false);
          setShowCounterDialog(true);
        }}
      />
    </div>
  );
};

// Mobile Bet Card Component
const MobileBetCard = ({ bet, onClick, onCounter }: { bet: any; onClick: () => void; onCounter: () => void; }) => {
  const getStatusFromIndex = (idx: number) => {
    const statuses = ['open', 'partially-filled', 'expiring-soon', 'locked'];
    return statuses[idx % statuses.length];
  };
  
  const status = getStatusFromIndex(bet.id);
  
  const getStatusBadge = (s: string) => {
    switch(s) {
      case 'partially-filled':
        return <Badge className="text-xs h-5 px-2 bg-yellow-500 text-white">Filling</Badge>;
      case 'locked':
        return <Badge className="text-xs h-5 px-2 bg-blue-500 text-white">Locked</Badge>;
      case 'expiring-soon':
        return <Badge className="text-xs h-5 px-2 bg-red-500 text-white animate-pulse">Expiring</Badge>;
      default:
        return <Badge className="text-xs h-5 px-2 bg-green-500 text-white">Open</Badge>;
    }
  };
  
  const getFillLevel = (idx: number) => {
    const levels = [0, 25, 45, 65, 85, 100];
    return levels[idx % levels.length];
  };
  
  return (
    <div 
      className="bg-slate-900 p-4 rounded-lg border border-slate-800 hover:border-slate-700 transition-all cursor-pointer"
      onClick={onClick}
    >
      {/* Header */}
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center space-x-2">
          <div className="h-10 w-10 rounded-full bg-gradient-to-r from-slate-700 to-slate-600 flex items-center justify-center">
            <UserX className="h-5 w-5 text-white" />
          </div>
          <div>
            <p className="text-sm font-medium text-white">Player {String.fromCharCode(65 + (bet.id % 26))}</p>
            <p className="text-xs text-slate-400">{bet.time}</p>
          </div>
        </div>
        {getStatusBadge(status)}
      </div>
      
      {/* Bet Details */}
      <div className="mb-3">
        <h3 className="text-sm font-medium text-white mb-1 line-clamp-2">
          {bet.name}
        </h3>
        <div className="flex items-center text-xs text-slate-400">
          <Tag className="h-3 w-3 mr-1" />
          <span>{bet.game}</span>
        </div>
      </div>
      
      {/* Stake and Progress */}
      <div className="mb-4">
        <div className="flex justify-between items-center mb-2">
          <span className="text-sm text-slate-400">Total Stake</span>
          <span className="text-lg font-bold text-white">${bet.amount}</span>
        </div>
        
        {status === 'partially-filled' && (
          <>
            <Progress value={getFillLevel(bet.id)} className="h-2 mb-2" />
            <div className="flex justify-between text-xs">
              <span className="text-slate-400">
                ${Math.floor(bet.amount * getFillLevel(bet.id) / 100)} of ${bet.amount}
              </span>
              <span className="text-purple-400">{bet.id + 1} counters</span>
            </div>
          </>
        )}
        
        {status === 'expiring-soon' && (
          <div className="flex items-center text-xs text-red-500">
            <Clock className="h-3 w-3 mr-1" />
            <span>Expires in {10 - (bet.id % 10)}m</span>
          </div>
        )}
      </div>
      
      {/* Action Buttons */}
      <div className="grid grid-cols-2 gap-2">
        <Button
          className="h-10 text-sm bg-gradient-to-r from-purple-500 to-pink-500"
          onClick={(e) => {
            e.stopPropagation();
            onCounter();
          }}
        >
          Counter Bet
        </Button>
        <Button
          variant="outline"
          className="h-10 text-sm border-slate-700 text-white"
          onClick={(e) => {
            e.stopPropagation();
            onClick();
          }}
        >
          View Details
        </Button>
      </div>
    </div>
  );
};

export default MobileMarketplace;