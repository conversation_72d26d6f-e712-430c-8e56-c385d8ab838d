import { useState, useEffect } from 'react';
import { X, Trash, ShoppingBag, ChevronDown, AlertCircle } from 'lucide-react';

interface BetSlipItem {
  id: string;
  eventName: string;
  selection: string;
  odds: number;
  stake: number;
  potentialWin: number;
  type: 'single' | 'multiple';
  eventTime?: string;
  sport?: string;
}

interface BetSlipProps {
  isOpen: boolean;
  onClose: () => void;
  items?: BetSlipItem[];
  balance?: number;
}

const BetSlip = ({ isOpen, onClose, items = [], balance = 1250.00 }: BetSlipProps) => {
  const [betItems, setBetItems] = useState<BetSlipItem[]>(items);
  const [isMinimized, setIsMinimized] = useState(false);
  const [placingBet, setPlacingBet] = useState(false);
  const [unitStake, setUnitStake] = useState(10);
  
  // Calculate totals
  const totalStake = betItems.reduce((sum, item) => sum + item.stake, 0);
  const totalPotentialWin = betItems.reduce((sum, item) => sum + item.potentialWin, 0);
  const totalOdds = betItems.reduce((product, item) => product * item.odds, 1);
  
  const updateStake = (itemId: string, newStake: number) => {
    setBetItems(prev => prev.map(item => 
      item.id === itemId 
        ? { ...item, stake: newStake, potentialWin: newStake * item.odds }
        : item
    ));
  };
  
  const removeItem = (itemId: string) => {
    setBetItems(prev => prev.filter(item => item.id !== itemId));
  };
  
  const clearAll = () => {
    setBetItems([]);
  };
  
  const applyUnitStake = () => {
    setBetItems(prev => prev.map(item => ({
      ...item, 
      stake: unitStake, 
      potentialWin: unitStake * item.odds
    })));
  };
  
  const placeBet = async () => {
    setPlacingBet(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    setPlacingBet(false);
    clearAll();
  };


  if (!isOpen) return null;

  return (
    <>
      {/* Betslip panel */}
      <div className={`fixed right-0 bottom-0 top-16 w-72 bg-slate-900 border-l border-slate-800 transition-all z-40 ${
        isMinimized ? 'translate-y-[calc(100%-3rem)]' : 'translate-y-0'
      }`}>
        {/* Header */}
        <div className="flex items-center justify-between p-2 border-b border-slate-800 cursor-pointer"
          onClick={() => setIsMinimized(!isMinimized)}
        >
          <div className="flex items-center">
            <ShoppingBag className="h-4 w-4 text-yellow-500 mr-1.5" />
            <span className="text-white text-sm font-medium">
              Betslip ({betItems.length})
            </span>
          </div>
          <div className="flex items-center space-x-1">
            {betItems.length > 0 && (
              <button 
                className="p-1 text-slate-400 hover:text-white"
                onClick={(e) => {
                  e.stopPropagation();
                  clearAll();
                }}
                title="Clear all"
              >
                <Trash className="h-3 w-3" />
              </button>
            )}
            <button 
              className="p-1 text-slate-400 hover:text-white"
              onClick={(e) => {
                e.stopPropagation();
                setIsMinimized(!isMinimized);
              }}
            >
              <ChevronDown className={`h-3 w-3 transition-transform ${isMinimized ? 'rotate-180' : ''}`} />
            </button>
            <button 
              className="p-1 text-slate-400 hover:text-white"
              onClick={(e) => {
                e.stopPropagation();
                onClose();
              }}
            >
              <X className="h-3 w-3" />
            </button>
          </div>
        </div>
        
        {/* Content */}
        {!isMinimized && (
          <>
            {/* Quick stake selector */}
            {betItems.length > 0 && (
              <div className="p-2 border-b border-slate-800">
                <div className="flex items-center justify-between mb-1">
                  <span className="text-xxs text-slate-400">Unit Stake</span>
                  <div className="flex items-center space-x-1">
                    {[5, 10, 20, 50].map(amount => (
                      <button
                        key={amount}
                        className={`px-1.5 h-5 rounded text-xxs font-medium transition-colors ${
                          unitStake === amount
                            ? 'bg-slate-700 text-white'
                            : 'bg-slate-800 text-slate-400 hover:text-white'
                        }`}
                        onClick={() => setUnitStake(amount)}
                      >
                        ${amount}
                      </button>
                    ))}
                  </div>
                </div>
                <button
                  className="w-full h-6 bg-slate-800 hover:bg-slate-700 text-xs text-white rounded transition-colors"
                  onClick={applyUnitStake}
                >
                  Apply to All
                </button>
              </div>
            )}
            
            {/* Bet items */}
            <div className="overflow-auto max-h-[calc(100%-10rem)]">
              {betItems.length === 0 ? (
                <div className="p-4 text-center text-slate-400">
                  <ShoppingBag className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p className="text-sm">Your betslip is empty</p>
                  <p className="text-xs mt-1">Add selections to get started</p>
                </div>
              ) : (
                betItems.map(item => (
                  <div key={item.id} className="p-2 border-b border-slate-800 hover:bg-slate-800/30">
                    <div className="flex justify-between items-start mb-1">
                      <div className="flex-1 mr-2">
                        <p className="text-white text-xs font-medium">{item.eventName}</p>
                        <p className="text-slate-400 text-xxs">{item.selection}</p>
                        {item.eventTime && (
                          <p className="text-slate-500 text-xxs mt-0.5">
                            {item.sport} • {item.eventTime}
                          </p>
                        )}
                      </div>
                      <div className="flex items-center space-x-1">
                        <span className="text-white text-sm font-medium">{item.odds.toFixed(2)}</span>
                        <button
                          className="p-0.5 text-slate-400 hover:text-red-400"
                          onClick={() => removeItem(item.id)}
                        >
                          <X className="h-3 w-3" />
                        </button>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="flex-1">
                        <input
                          type="number"
                          value={item.stake}
                          onChange={(e) => updateStake(item.id, Number(e.target.value))}
                          className="w-full bg-slate-800 border border-slate-700 rounded px-2 h-6 text-xs text-white focus:border-slate-600 focus:outline-none"
                          min="0"
                          step="5"
                        />
                      </div>
                      <div className="text-right">
                        <p className="text-xxs text-slate-400">Potential Win</p>
                        <p className="text-xs text-green-500 font-medium">
                          ${item.potentialWin.toFixed(2)}
                        </p>
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
            
            {/* Footer */}
            {betItems.length > 0 && (
              <div className="absolute bottom-0 left-0 right-0 p-2 bg-slate-900 border-t border-slate-800">
                <div className="space-y-1 mb-2">
                  <div className="flex justify-between text-xs">
                    <span className="text-slate-400">Total Stake:</span>
                    <span className="text-white font-medium">${totalStake.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between text-xs">
                    <span className="text-slate-400">Total Odds:</span>
                    <span className="text-white font-medium">{totalOdds.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-slate-400">Potential Win:</span>
                    <span className="text-green-500 font-bold">${totalPotentialWin.toFixed(2)}</span>
                  </div>
                </div>
                
                {totalStake > balance && (
                  <div className="flex items-center text-xxs text-red-400 mb-1">
                    <AlertCircle className="h-3 w-3 mr-1" />
                    Insufficient balance
                  </div>
                )}
                
                <button
                  className={`w-full h-8 rounded text-sm font-medium transition-all ${
                    totalStake > balance || totalStake === 0 || placingBet
                      ? 'bg-slate-800 text-slate-500 cursor-not-allowed'
                      : 'bg-gradient-to-r from-green-600 to-green-500 hover:from-green-700 hover:to-green-600 text-white'
                  }`}
                  disabled={totalStake > balance || totalStake === 0 || placingBet}
                  onClick={placeBet}
                >
                  {placingBet ? 'Placing Bet...' : `Place $${totalStake.toFixed(2)} Bet`}
                </button>
              </div>
            )}
          </>
        )}
      </div>
      
      {/* Mini betslip when closed */}
      {!isOpen && betItems.length > 0 && (
        <button 
          className="fixed right-0 bottom-24 bg-slate-900 border border-slate-800 border-r-0 rounded-l-md px-2 py-1.5 z-40 shadow-lg hover:bg-slate-800 transition-colors"
          onClick={() => onClose()}
        >
          <div className="flex items-center">
            <ShoppingBag className="h-4 w-4 text-yellow-500 mr-1.5" />
            <div className="text-left">
              <p className="text-xxs text-slate-400">Betslip</p>
              <p className="text-xs text-white font-medium">
                {betItems.length} • ${totalStake.toFixed(2)}
              </p>
            </div>
          </div>
        </button>
      )}
    </>
  );
};

export default BetSlip;