import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Progress } from '@/components/ui/progress';
import { 
  DollarSign, 
  AlertCircle, 
  X, 
  Check, 
  ArrowRight, 
  ArrowLeft, 
  ThumbsUp, 
  Percent, 
  Users 
} from 'lucide-react';

interface CounterBetDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  bet: any;
}

// Step interface
interface Step {
  id: string;
  title: string;
  icon: React.ReactNode;
  isComplete: boolean;
}

const CounterBetDialog: React.FC<CounterBetDialogProps> = ({ open, onOpenChange, bet }) => {
  const [counterAmount, setCounterAmount] = useState('35');
  const [joinPool, setJoinPool] = useState(false);
  const [activeStep, setActiveStep] = useState(0);
  
  // Step metadata
  const steps: Step[] = [
    { 
      id: 'bet-overview', 
      title: 'Bet Overview', 
      icon: <X className="h-4 w-4" />, 
      isComplete: false 
    },
    { 
      id: 'counter-settings', 
      title: 'Counter Settings', 
      icon: <DollarSign className="h-4 w-4" />, 
      isComplete: false 
    },
    { 
      id: 'review-confirm', 
      title: 'Review & Confirm', 
      icon: <Check className="h-4 w-4" />, 
      isComplete: false 
    }
  ];

  // Helper to advance to next step
  const nextStep = () => {
    if (activeStep < steps.length - 1) {
      setActiveStep(activeStep + 1);
      // Mark the current step as complete
      const updatedSteps = [...steps];
      updatedSteps[activeStep].isComplete = true;
    }
  };

  // Helper to go back to previous step
  const prevStep = () => {
    if (activeStep > 0) {
      setActiveStep(activeStep - 1);
    }
  };
  
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="w-full sm:max-w-lg bg-slate-900 border-slate-800 max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-white">Counter This Bet</DialogTitle>
          <DialogDescription className="text-slate-400">
            Join the opposing side of this bet
          </DialogDescription>
        </DialogHeader>

        {/* Steps indicator */}
        <div className="flex justify-between mb-6 mt-2">
          {steps.map((step, index) => (
            <div key={step.id} className="flex items-center">
              <div 
                className={`flex items-center justify-center h-8 w-8 rounded-full 
                  ${index === activeStep ? 'bg-gradient-to-r from-red-500 to-pink-500' : 
                    step.isComplete ? 'bg-green-500' : 'bg-slate-700'} 
                  text-white font-medium`}
              >
                {step.isComplete ? <Check className="h-4 w-4" /> : index + 1}
              </div>
              <div 
                className={`ml-2 ${index === activeStep ? 'text-white' : 
                  step.isComplete ? 'text-green-400' : 'text-slate-500'}`}
              >
                {index < steps.length - 1 && (
                  <div className={`h-px w-8 ${step.isComplete ? 'bg-green-500' : 'bg-slate-700'} 
                    ml-2 hidden sm:inline-block`}
                  />
                )}
              </div>
            </div>
          ))}
        </div>

        <div className="py-2">
          {/* Step 1: Bet Overview */}
          {activeStep === 0 && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-white flex items-center">
                <X className="h-5 w-5 mr-2 text-red-500" />
                Bet Overview
              </h3>
              
              {/* Bet Summary */}
              <Card className="bg-slate-800 border-slate-700">
                <CardContent className="p-4">
                  <p className="text-white font-medium mb-2">
                    {bet?.title || 'Cristiano Ronaldo will score vs Barcelona'}
                  </p>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-slate-400">{bet?.event || 'Champions League'} • {bet?.time || 'Tonight 8:00 PM'}</span>
                    <span className="text-purple-500 font-medium">${bet?.total || 100} total</span>
                  </div>
                </CardContent>
              </Card>

              {/* Your Position */}
              <div className="space-y-3">
                <Label className="text-white">Your Position</Label>
                <Card className="bg-red-900/20 border-red-800">
                  <CardContent className="p-4">
                    <div className="flex items-center space-x-2">
                      <X className="h-5 w-5 text-red-500" />
                      <p className="text-white font-medium">
                        Against: {bet?.title ? `NOT: ${bet.title}` : 'Ronaldo will NOT score'}
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Current Pool Status */}
              <div className="space-y-3">
                <Label className="text-white">Current Pool Status</Label>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-slate-400">Amount needed</span>
                    <span className="text-white font-medium">${bet?.needed || 35}</span>
                  </div>
                  <Progress value={bet?.progress || 65} className="h-2" />
                  <div className="flex justify-between text-sm">
                    <span className="text-slate-400">${bet?.filled || 65} / ${bet?.total || 100} filled</span>
                    <span className="text-purple-500">{bet?.counters || 3} counters joined</span>
                  </div>
                </div>
              </div>
              
              <div className="flex justify-end mt-6">
                <Button
                  className="bg-gradient-to-r from-red-500 to-pink-500 hover:from-red-600 hover:to-pink-600"
                  onClick={nextStep}
                >
                  Next
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </div>
            </div>
          )}
          
          {/* Step 2: Counter Settings */}
          {activeStep === 1 && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-white flex items-center">
                <DollarSign className="h-5 w-5 mr-2 text-green-500" />
                Counter Settings
              </h3>
              
              {/* Counter Amount */}
              <div className="space-y-3">
                <Label className="text-white">Your Counter Amount</Label>
                <div className="relative">
                  <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                  <Input
                    type="number"
                    value={counterAmount}
                    onChange={(e) => setCounterAmount(e.target.value)}
                    className="pl-10 bg-slate-800 border-slate-700 text-white"
                    max={bet?.needed || "35"}
                  />
                </div>
                <p className="text-sm text-slate-400">
                  Maximum: ${bet?.needed || 35} (remaining to fill the bet)
                </p>
                
                <div className="flex gap-2 mt-2">
                  {['5', '10', '20', '35'].map((amount) => (
                    <Button
                      key={amount}
                      variant="outline"
                      size="sm"
                      className="bg-slate-800 border-slate-700 text-white hover:bg-slate-700"
                      onClick={() => setCounterAmount(amount)}
                    >
                      ${amount}
                    </Button>
                  ))}
                </div>
              </div>

              {/* Join Pool Option */}
              <div className="flex items-center space-x-2 mt-4">
                <input
                  type="checkbox"
                  id="join-pool"
                  checked={joinPool}
                  onChange={(e) => setJoinPool(e.target.checked)}
                  className="rounded border-slate-700"
                />
                <Label htmlFor="join-pool" className="text-slate-300 cursor-pointer">
                  Allow others to join my counter (create a pool)
                </Label>
              </div>
              
              <Card className="bg-slate-800 border-slate-700 mt-4">
                <CardContent className="p-4">
                  <div className="flex items-center space-x-2 mb-3">
                    <ThumbsUp className="h-4 w-4 text-blue-400" />
                    <p className="text-sm text-blue-400 font-medium">Why create a pool?</p>
                  </div>
                  <p className="text-sm text-slate-400">
                    Creating a pool allows other users to join your side of the bet. This can help fill larger bets more quickly and share the risk among multiple participants.
                  </p>
                </CardContent>
              </Card>
              
              <div className="flex justify-between mt-6">
                <Button
                  variant="outline"
                  className="bg-slate-800 border-slate-700 text-white hover:bg-slate-700"
                  onClick={prevStep}
                >
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Back
                </Button>
                <Button
                  className="bg-gradient-to-r from-red-500 to-pink-500 hover:from-red-600 hover:to-pink-600"
                  onClick={nextStep}
                >
                  Next
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </div>
            </div>
          )}
          
          {/* Step 3: Review & Confirm */}
          {activeStep === 2 && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-white flex items-center">
                <Check className="h-5 w-5 mr-2 text-blue-500" />
                Review & Confirm
              </h3>
              
              {/* Summary */}
              <Card className="bg-slate-800 border-slate-700">
                <CardContent className="p-4 space-y-4">
                  <div>
                    <h4 className="text-white font-medium mb-2">Bet Overview</h4>
                    <div className="text-sm space-y-1">
                      <div className="flex justify-between">
                        <span className="text-slate-400">Bet:</span>
                        <span className="text-white">{bet?.title || 'Ronaldo will score vs Barcelona'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-slate-400">Event:</span>
                        <span className="text-white">{bet?.event || 'Champions League'} • {bet?.time || 'Tonight 8:00 PM'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-slate-400">Your Position:</span>
                        <span className="text-red-400">Against (Will NOT happen)</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="h-px bg-slate-700"></div>
                  
                  <div>
                    <h4 className="text-white font-medium mb-2">Your Counter</h4>
                    <div className="text-sm space-y-1">
                      <div className="flex justify-between">
                        <span className="text-slate-400">Amount:</span>
                        <span className="text-white">${counterAmount}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-slate-400">Pool:</span>
                        <span className="text-white">{joinPool ? 'Allowing others to join' : 'Private (just you)'}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-slate-400">Your Share:</span>
                        <div className="flex items-center text-white">
                          <Percent className="h-3 w-3 mr-1" />
                          {((parseFloat(counterAmount) / (bet?.total || 100)) * 100).toFixed(1)}%
                        </div>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-slate-400">Other Counters:</span>
                        <div className="flex items-center text-white">
                          <Users className="h-3 w-3 mr-1" />
                          {bet?.participants || 3} participants
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="h-px bg-slate-700"></div>
                  
                  <div>
                    <h4 className="text-white font-medium mb-2">Potential Returns</h4>
                    <div className="text-sm space-y-2">
                      <div className="flex justify-between">
                        <span className="text-slate-400">Your Stake</span>
                        <span className="text-white font-medium">${counterAmount}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-slate-400">Potential Win</span>
                        <span className="text-green-500 font-bold">
                          ${((parseFloat(counterAmount) / (bet?.needed || 35)) * (bet?.total || 100) * 0.98).toFixed(2)}
                        </span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              {/* Warning */}
              <div className="flex items-start space-x-2 p-3 bg-yellow-900/20 rounded-lg">
                <AlertCircle className="h-5 w-5 text-yellow-500 mt-0.5" />
                <div className="flex-1">
                  <p className="text-sm text-yellow-500 font-medium">Important</p>
                  <p className="text-sm text-slate-300">
                    Your funds will be locked once you counter this bet. You cannot cancel after joining.
                  </p>
                </div>
              </div>
              
              <div className="flex justify-between mt-6">
                <Button
                  variant="outline"
                  className="bg-slate-800 border-slate-700 text-white hover:bg-slate-700"
                  onClick={prevStep}
                >
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Back
                </Button>
                <Button
                  className="bg-gradient-to-r from-red-500 to-pink-500 hover:from-red-600 hover:to-pink-600"
                >
                  Counter for ${counterAmount}
                </Button>
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default CounterBetDialog;