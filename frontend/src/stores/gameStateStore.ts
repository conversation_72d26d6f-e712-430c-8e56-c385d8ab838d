import { create } from 'zustand';
import { gameStateService } from '../services/gameState';
import type { GameInstance } from '../services/gameState';
import { toast } from '../components/ui/use-toast';

interface GameStateStore {
  currentGame: GameInstance | null;
  activeGames: GameInstance[];
  loading: boolean;
  error: string | null;

  // Actions
  createGame: (gameType: string, stakeAmount: number) => Promise<GameInstance | null>;
  loadGame: (gameId: string) => Promise<void>;
  joinGame: (gameId: string, stakeAmount: number) => Promise<void>;
  makeMove: (gameId: string, moveData: any) => Promise<void>;
  loadActiveGames: (gameType?: string) => Promise<void>;
  updateGameFromWebSocket: (game: GameInstance) => void;
  clearCurrentGame: () => void;
}

export const useGameStateStore = create<GameStateStore>((set, get) => ({
  currentGame: null,
  activeGames: [],
  loading: false,
  error: null,

  createGame: async (gameType: string, stakeAmount: number) => {
    set({ loading: true, error: null });
    try {
      const game = await gameStateService.createGame({ game_type: gameType, wager_amount: stakeAmount });
      set({ currentGame: game, loading: false });
      toast({
        title: 'Game Created',
        description: 'Waiting for opponent to join...',
      });
      return game;
    } catch (error: any) {
      set({ loading: false, error: error.response?.data?.detail || 'Failed to create game' });
      toast({
        title: 'Error',
        description: error.response?.data?.detail || 'Failed to create game',
        variant: 'destructive',
      });
      return null;
    }
  },

  loadGame: async (gameId: string) => {
    set({ loading: true, error: null });
    try {
      const game = await gameStateService.getGame(gameId);
      set({ currentGame: game, loading: false });
    } catch (error: any) {
      set({ loading: false, error: error.response?.data?.detail || 'Failed to load game' });
      toast({
        title: 'Error',
        description: error.response?.data?.detail || 'Failed to load game',
        variant: 'destructive',
      });
    }
  },

  joinGame: async (gameId: string, stakeAmount: number) => {
    set({ loading: true, error: null });
    try {
      const game = await gameStateService.joinGame(gameId, { stake_amount: stakeAmount });
      set({ currentGame: game, loading: false });
      toast({
        title: 'Game Joined',
        description: 'You have successfully joined the game!',
      });
    } catch (error: any) {
      set({ loading: false, error: error.response?.data?.detail || 'Failed to join game' });
      toast({
        title: 'Error',
        description: error.response?.data?.detail || 'Failed to join game',
        variant: 'destructive',
      });
    }
  },

  makeMove: async (gameId: string, moveData: any) => {
    set({ loading: true, error: null });
    try {
      const game = await gameStateService.makeMove(gameId, { move_data: moveData });
      set({ currentGame: game, loading: false });
    } catch (error: any) {
      set({ loading: false, error: error.response?.data?.detail || 'Failed to make move' });
      toast({
        title: 'Error',
        description: error.response?.data?.detail || 'Failed to make move',
        variant: 'destructive',
      });
    }
  },

  loadActiveGames: async (gameType?: string) => {
    set({ loading: true, error: null });
    try {
      const games = await gameStateService.getActiveGames(gameType);
      set({ activeGames: games, loading: false });
    } catch (error: any) {
      set({ loading: false, error: error.response?.data?.detail || 'Failed to load games' });
    }
  },

  updateGameFromWebSocket: (game: GameInstance) => {
    const { currentGame, activeGames } = get();
    
    // Update current game if it matches
    if (currentGame?.id === game.id) {
      set({ currentGame: game });
    }

    // Update in active games list
    const updatedActiveGames = activeGames.map(g => 
      g.id === game.id ? game : g
    );
    set({ activeGames: updatedActiveGames });
  },

  clearCurrentGame: () => {
    set({ currentGame: null });
  },
}));