import { create } from 'zustand';

interface Match {
  id: string;
  homeTeam: string;
  awayTeam: string;
  homeScore?: number;
  awayScore?: number;
  startTime: Date;
  status: 'scheduled' | 'live' | 'completed';
  odds: {
    home: number;
    draw: number;
    away: number;
  };
}

interface Bet {
  id?: string;
  matchId: string;
  type: 'home' | 'draw' | 'away';
  amount: number;
  odds: number;
  timestamp?: Date;
  status?: 'pending' | 'won' | 'lost';
}

interface SportsState {
  liveMatches: Match[];
  upcomingMatches: Match[];
  activeBets: Bet[];
  isLoading: boolean;
  error: string | null;
  setLiveMatches: (matches: Match[]) => void;
  setUpcomingMatches: (matches: Match[]) => void;
  setActiveBets: (bets: Bet[]) => void;
  placeBet: (bet: Bet) => void;
  setLoading: (isLoading: boolean) => void;
  setError: (error: string | null) => void;
  updateMatchOdds: (matchId: string, odds: { home: number; draw: number; away: number }) => void;
  updateMatchScore: (matchId: string, homeScore: number, awayScore: number) => void;
}

const useSportsStore = create<SportsState>((set) => ({
  liveMatches: [],
  upcomingMatches: [],
  activeBets: [],
  isLoading: false,
  error: null,
  setLiveMatches: (matches) => set({ liveMatches: matches }),
  setUpcomingMatches: (matches) => set({ upcomingMatches: matches }),
  setActiveBets: (bets) => set({ activeBets: bets }),
  placeBet: (bet) =>
    set((state) => ({
      activeBets: [...state.activeBets, { ...bet, timestamp: new Date(), status: 'pending' }],
    })),
  setLoading: (isLoading) => set({ isLoading }),
  setError: (error) => set({ error }),
  updateMatchOdds: (matchId, newOdds) =>
    set((state) => ({
      liveMatches: state.liveMatches.map((match) =>
        match.id === matchId ? { ...match, odds: newOdds } : match
      ),
      upcomingMatches: state.upcomingMatches.map((match) =>
        match.id === matchId ? { ...match, odds: newOdds } : match
      ),
    })),
  updateMatchScore: (matchId, homeScore, awayScore) =>
    set((state) => ({
      liveMatches: state.liveMatches.map((match) =>
        match.id === matchId ? { ...match, homeScore, awayScore } : match
      ),
    })),
}));

export default useSportsStore;
