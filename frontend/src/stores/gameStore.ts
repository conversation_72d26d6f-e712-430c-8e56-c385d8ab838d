import { create } from 'zustand';

interface Game {
  id: string;
  name: string;
  type: string;
  status: 'waiting' | 'active' | 'completed';
  players: number;
  maxPlayers: number;
  startTime?: Date;
}

interface Category {
  id: string;
  name: string;
  description: string;
  imageUrl?: string;
}

interface Answer {
  gameId: string;
  questionId: string;
  answer: string | number | boolean;
}

interface GameState {
  activeGames: Game[];
  currentGame: Game | null;
  categories: Category[];
  setActiveGames: (games: Game[]) => void;
  setCurrentGame: (game: Game | null) => void;
  setCategories: (categories: Category[]) => void;
  joinGame: (gameId: string) => void;
  submitAnswer: (answer: Answer) => void;
}

const useGameStore = create<GameState>((set, get) => ({
  activeGames: [],
  currentGame: null,
  categories: [],
  setActiveGames: (games) => set({ activeGames: games }),
  setCurrentGame: (game) => set({ currentGame: game }),
  setCategories: (categories) => set({ categories }),
  joinGame: (gameId) => {
    // This would typically make an API call
    const game = get().activeGames.find((g) => g.id === gameId);
    if (game) {
      set({ currentGame: game });
    }
  },
  submitAnswer: (answer) => {
    // This would typically make an API call
    // console.log('Submitting answer:', answer);
  },
}));

export default useGameStore;
