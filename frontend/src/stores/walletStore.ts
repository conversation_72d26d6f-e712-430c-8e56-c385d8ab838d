import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface Transaction {
  id: string;
  type: 'deposit' | 'withdrawal' | 'bet' | 'win';
  amount: number;
  timestamp: Date;
  status: 'pending' | 'completed' | 'failed';
  description?: string;
  created_at?: string;
}

interface WalletState {
  balance: number;
  transactions: Transaction[];
  isLoading: boolean;
  error: string | null;
  username: string;
  userId: string;
  updateBalance: (amount: number) => void;
  setBalance: (balance: number) => void;
  addTransaction: (transaction: Transaction) => void;
  setTransactions: (transactions: Transaction[]) => void;
  setLoading: (isLoading: boolean) => void;
  setError: (error: string | null) => void;
  setUser: (username: string, userId: string) => void;
  reset: () => void;
}

const useWalletStore = create<WalletState>()(
  persist(
    (set) => ({
      balance: 0,
      transactions: [],
      isLoading: false,
      error: null,
      username: '',
      userId: '',
      updateBalance: (amount) => set((state) => ({ balance: state.balance + amount })),
      setBalance: (balance) => set({ balance }),
      addTransaction: (transaction) =>
        set((state) => ({ transactions: [transaction, ...state.transactions] })),
      setTransactions: (transactions) => set({ transactions }),
      setLoading: (isLoading) => set({ isLoading }),
      setError: (error) => set({ error }),
      setUser: (username, userId) => set({ username, userId }),
      reset: () => set({ 
        balance: 0, 
        transactions: [], 
        isLoading: false, 
        error: null,
        username: '',
        userId: ''
      }),
    }),
    {
      name: 'wallet-storage',
    }
  )
);

export default useWalletStore;
