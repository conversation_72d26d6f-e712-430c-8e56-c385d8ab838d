import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface User {
  id: string;
  username: string;
  email: string;
  is_superuser?: boolean;
}

interface UserState {
  user: User | null;
  isAuthenticated: boolean;
  token: string | null;
  refreshToken: string | null;
  setUser: (user: User) => void;
  setTokens: (token: string, refreshToken: string) => void;
  logout: () => void;
}

const useUserStore = create<UserState>()(
  persist(
    (set) => ({
      user: null,
      isAuthenticated: false,
      token: null,
      refreshToken: null,
      setUser: (user) => set({ user, isAuthenticated: true }),
      setTokens: (token, refreshToken) => {
        // console.log('UserStore - Setting tokens:', { token: !!token, refreshToken: !!refreshToken });
        // console.log('UserStore - Token first 20 chars:', token?.substring(0, 20) + '...');
        set({ token, refreshToken });
      },
      logout: () => set({ user: null, isAuthenticated: false, token: null, refreshToken: null }),
    }),
    {
      name: 'user-storage',
    }
  )
);

export default useUserStore;
