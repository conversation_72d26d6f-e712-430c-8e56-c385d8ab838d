export interface Wallet {
  id: string;
  userId: string;
  balance: number;
  currency: string;
  updatedAt: string;
}

export interface Transaction {
  id: string;
  userId: string;
  type: TransactionType;
  amount: number;
  currency: string;
  status: TransactionStatus;
  reference?: string;
  description?: string;
  metadata?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

export type TransactionType = 'deposit' | 'withdrawal' | 'bet' | 'win' | 'refund' | 'bonus';

export type TransactionStatus = 'pending' | 'completed' | 'failed' | 'cancelled';

export interface PaymentMethod {
  id: string;
  userId: string;
  type: PaymentMethodType;
  name: string;
  isDefault: boolean;
  details: PaymentMethodDetails;
  createdAt: string;
  updatedAt: string;
}

export type PaymentMethodType = 'card' | 'bank_account' | 'paypal' | 'crypto';

export interface PaymentMethodDetails {
  last4?: string;
  brand?: string;
  expiryMonth?: string;
  expiryYear?: string;
  accountNumber?: string;
  routingNumber?: string;
  bankName?: string;
  email?: string;
  address?: string;
}

export interface DepositRequest {
  amount: number;
  currency: string;
  paymentMethodId?: string;
  paymentMethodType?: PaymentMethodType;
  paymentMethodDetails?: PaymentMethodDetails;
}

export interface WithdrawalRequest {
  amount: number;
  currency: string;
  paymentMethodId?: string;
  paymentMethodType?: PaymentMethodType;
  paymentMethodDetails?: PaymentMethodDetails;
}
