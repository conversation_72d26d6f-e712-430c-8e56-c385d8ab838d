export interface User {
  id: string;
  username: string;
  email: string;
  is_superuser?: boolean;
  createdAt?: string;
  updatedAt?: string;
}

export interface UserProfile extends User {
  firstName?: string;
  lastName?: string;
  bio?: string;
  avatarUrl?: string;
  phoneNumber?: string;
}

export interface UserStats {
  gamesPlayed: number;
  gamesWon: number;
  totalWinnings: number;
  winRate: number;
  rank?: number;
  level?: number;
  points?: number;
}

export interface Achievement {
  id: string;
  name: string;
  description: string;
  iconUrl?: string;
  unlockedAt?: string;
  progress?: number;
  maxProgress?: number;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  username: string;
  email: string;
  password: string;
  confirmPassword?: string;
}

export interface AuthResponse {
  user: User;
  access_token: string;
  refresh_token: string;
  expires_in?: number;
}
