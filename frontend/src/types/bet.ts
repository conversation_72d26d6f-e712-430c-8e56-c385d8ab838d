export interface BetSlipItem {
  id: string;
  eventName: string;
  selection: string;
  odds: number;
  stake: number;
  potentialWin: number;
  type: 'single' | 'multiple';
  eventTime?: string;
  sport?: string;
}

export interface Bet {
  id: string;
  title: string;
  description: string;
  category: string;
  amount: number;
  odds: number;
  creatorId: string;
  creatorName: string;
  timestamp: Date;
  status: 'open' | 'matched' | 'completed' | 'cancelled';
  participants: {
    id: string;
    name: string;
    amount: number;
    side: 'for' | 'against';
  }[];
}

export interface BetCategory {
  id: string;
  name: string;
  icon: string;
  color: string;
}