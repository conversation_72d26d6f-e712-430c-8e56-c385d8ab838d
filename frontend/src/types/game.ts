export interface Game {
  id: string;
  name: string;
  description?: string;
  type: GameType;
  category: string;
  status: GameStatus;
  players: number;
  maxPlayers: number;
  entryFee?: number;
  prize?: number;
  startTime?: string;
  endTime?: string;
  createdAt: string;
  updatedAt: string;
}

export type GameType = 
  | "checkers"
  | "rock_paper_scissors"
  | "trivia"
  | 'puzzle'
  | 'word'
  | 'card'
  | 'custom'
  | 'chess';

export type GameStatus = 
  | "waiting"
  | "in_progress"
  | 'active'
  | "completed"
  | "cancelled";

export interface GameCategory {
  id: string;
  name: string;
  description: string;
  imageUrl?: string;
  gamesCount?: number;
}

export interface GameQuestion {
  id: string;
  gameId: string;
  text: string;
  type: QuestionType;
  options?: string[];
  correctAnswer?: string | number | boolean;
  points: number;
  timeLimit?: number;
}

export type QuestionType = 'multiple_choice' | 'true_false' | 'text' | 'number';

export interface GameAnswer {
  gameId: string;
  questionId: string;
  userId: string;
  answer: string | number | boolean;
  isCorrect?: boolean;
  points?: number;
  answeredAt: string;
}

export interface GameResult {
  gameId: string;
  userId: string;
  score: number;
  rank: number;
  prize?: number;
  completedAt: string;
}

export interface GameLobby {
  gameId: string;
  players: GamePlayer[];
  startCountdown?: number;
  chatMessages?: ChatMessage[];
}

export interface GamePlayer {
  userId: string;
  username: string;
  avatarUrl?: string;
  isReady: boolean;
  joinedAt: string;
}

export interface ChatMessage {
  id: string;
  userId: string;
  username: string;
  message: string;
  timestamp: string;
}

export interface GameState {
  gameId: string;
  gameType: GameType;
  status: GameStatus;
  player1Id: string;
  player2Id?: string;
  currentPlayerId?: string;
  winnerId?: string;
  state: any;  // Game-specific state
  settings: any;  // Game-specific settings
  isPlayer: boolean;
  yourPlayerNumber?: 1 | 2;
}

export interface GameMove {
  type: string;
  data: any;
}

export interface GameMessage {
  type: 'game_state' | 'move_made' | 'user_joined' | 'user_disconnected' | 'chat' | 'error' | 'player_joined' | 'game_cancelled';
  data?: any;
  message?: string;
  userId?: string;
  username?: string;
  isPlayer?: boolean;
  playerId?: string;
  move?: any;
  newState?: any;
  currentPlayerId?: string;
  gameStatus?: GameStatus;
  winnerId?: string;
  player2Id?: string;
  player2Username?: string;
  leavingPlayerId?: string;
}
