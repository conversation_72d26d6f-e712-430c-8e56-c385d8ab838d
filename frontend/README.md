# BetBet Frontend

This is the frontend for the BetBet platform, a gaming and sports betting application.

## Technologies Used

- React 18+ with Vite
- TypeScript
- Tailwind CSS
- shadcn/ui for UI components
- React Router for navigation
- Axios for API calls
- Socket.io-client for WebSocket connections
- React Query for server state management
- <PERSON>ustand for client state management
- <PERSON>act Hook Form for form handling
- Zod for schema validation

## Getting Started

### Prerequisites

- Node.js (v16+)
- npm or yarn

### Installation

1. Clone the repository
2. Navigate to the frontend directory
3. Install dependencies:

```bash
npm install
```

### Development

To start the development server:

```bash
npm run dev
```

This will start the Vite development server at `http://localhost:5173`.

### Building for Production

To build the application for production:

```bash
npm run build
```

The build output will be in the `dist` directory.

### Environment Variables

Create a `.env` file in the root of the frontend directory with the following variables:

```
VITE_API_BASE_URL=http://localhost:8000
VITE_WS_URL=ws://localhost:8000/ws
VITE_ENVIRONMENT=development
```

## Project Structure

```
src/
├── components/
│   ├── auth/
│   │   ├── LoginForm.tsx
│   │   ├── RegisterForm.tsx
│   │   └── ProtectedRoute.tsx
│   ├── dashboard/
│   ├── games/
│   ├── sports/
│   ├── wallet/
│   └── shared/
│       ├── Header.tsx
│       ├── Navigation.tsx
│       └── LoadingSpinner.tsx
├── hooks/
│   ├── useAuth.ts
│   ├── useWebSocket.ts
│   ├── useApi.ts
│   └── useNotifications.ts
├── services/
│   ├── api.ts
│   ├── websocket.ts
│   └── auth.ts
├── stores/
│   ├── userStore.ts
│   ├── gameStore.ts
│   ├── walletStore.ts
│   └── sportsStore.ts
├── types/
│   ├── user.ts
│   ├── game.ts
│   ├── sports.ts
│   └── wallet.ts
├── utils/
├── App.tsx
└── main.tsx
```

## Features

- Authentication (Login/Register)
- User Dashboard
- Gaming Module
- Sports Betting Module
- Wallet System
- Social Features

## License

This project is proprietary and confidential.
