"""Manual test for Sports API - demonstrates exactly how to make requests with API key."""
import httpx
import asyncio

# Configuration
API_URL = "https://api.sportsgameodds.com/v2"
API_KEY = "3bfb7e94f274e7389b28f2d9573fb5f7"  # Replace with your actual API key

async def manual_test():
    """Manually test the sports API."""
    
    print("🏈 Manual Sports API Test")
    print("=" * 50)
    print(f"API URL: {API_URL}")
    print(f"API Key: {'*' * 20 if API_KEY != 'your_actual_api_key_here' else 'NOT SET'}")
    print("=" * 50)
    
    if API_KEY == "your_actual_api_key_here":
        print("\n⚠️  Please replace 'your_actual_api_key_here' with your actual API key in this file!")
        return
    
    # Create headers with API key
    headers = {
        "X-API-Key": API_KEY,
        "Content-Type": "application/json"
    }
    
    async with httpx.AsyncClient() as client:
        try:
            # Test 1: Get sports
            print("\n1. Testing GET /sports/")
            response = await client.get(
                f"{API_URL}/sports/",
                headers=headers,
                timeout=30.0
            )
            
            print(f"   Status: {response.status_code}")
            print(f"   Headers: {dict(response.headers)[:200]}...")  # First 200 chars
            
            if response.status_code == 200:
                data = response.json()
                print(f"   ✅ Success! Found {len(data)} sports")
                print(f"   Sample data: {data[:2]}...")  # First 2 items
            else:
                print(f"   ❌ Error: {response.text[:200]}...")
                
        except Exception as e:
            print(f"   ❌ Exception: {type(e).__name__}: {e}")
        
        # Test 2: Get upcoming games
        try:
            print("\n2. Testing GET /games/upcoming")
            response = await client.get(
                f"{API_URL}/games/upcoming",
                headers=headers,
                params={"limit": 5},
                timeout=30.0
            )
            
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"   ✅ Success! Found {len(data)} upcoming games")
                print(f"   Sample data: {data[:1]}...")  # First item
            else:
                print(f"   ❌ Error: {response.text[:200]}...")
                
        except Exception as e:
            print(f"   ❌ Exception: {type(e).__name__}: {e}")
        
        # Test 3: Get live games
        try:
            print("\n3. Testing GET /games/live")
            response = await client.get(
                f"{API_URL}/games/live",
                headers=headers,
                timeout=30.0
            )
            
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"   ✅ Success! Found {len(data)} live games")
                if data:
                    print(f"   Sample data: {data[:1]}...")  # First item
            else:
                print(f"   ❌ Error: {response.text[:200]}...")
                
        except Exception as e:
            print(f"   ❌ Exception: {type(e).__name__}: {e}")

if __name__ == "__main__":
    print("\nTo run this test:")
    print("1. Edit this file and replace 'your_actual_api_key_here' with your actual API key")
    print("2. Run: python manual_test_sports.py")
    print()
    asyncio.run(manual_test())